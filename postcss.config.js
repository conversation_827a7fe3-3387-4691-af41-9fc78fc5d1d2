export default {
  plugins: {
    autoprefixer: {
      overrideBrowserslist: [
        "Android 4.1",
        "iOS 7.1",
        "Chrome > 31",
        "ff > 31",
        "ie >= 8",
        "last 10 versions" // 所有主流浏览器最近10版本用
      ],
      grid: true
    },
    // 除了以下三个常用参数 unitPrecision ：rem的小数位数  replace 是否替换原始值 mediaQuery 是否在媒体查询中转换px  minPixelValue 小于或者等于该值的单位不被转换等参数
    // 配置完成后，postcss-pxtorem会在构建时自动将CSS中的像素单位转换为rem
    "postcss-pxtorem": {
      // rootValue做了一些变化，为了适配移动端的显示，这里将rootValue设为1，在main.ts中根据设备类型动态设置根元素字体大小
      rootValue: 1, // 根元素字体大小，用于将像素转换为rem的基准值,一般1920*1080基础字体是16px，这边就是设置192
      propList: ["*", "!border", "no-rem"], // 除 border 外所有px 转 rem
      selectorBlackList: [".print", ".no-scale", ".cell"], // 过滤掉.el-开头的class，不进行rem转换
      replace: true,
      minPixelValue: 2, // 设置要替换的最小像素
      mediaQuery: false,
      minFontSize: 14
    }
  }
}
