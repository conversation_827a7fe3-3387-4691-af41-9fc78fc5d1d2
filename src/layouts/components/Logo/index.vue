<script lang="ts" setup>
import { useUserStore } from "@/store/modules/user"

const userStore = useUserStore()

interface Props {
  collapse?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  collapse: true
})
</script>

<template>
  <div class="layout-logo-container no-scale" :class="{ collapse: props.collapse }">
    <!-- <transition name="layout-logo-fade"> 
       <router-link v-if="props.collapse" key="collapse" to="/">
        <img :src="logo" class="layout-logo" />
      </router-link>
      <router-link v-else key="expand" to="/">
        <img :src="logoText1" class="layout-logo-text" />
      </router-link> 
     </transition> -->
    <img :src="userStore.getLogoImg" v-if="userStore.getLogoImg" class="w-200px h-60px m-l-1px m-t-2px" />
    <div v-else>食堂资金监管平台</div>

    <div class="logo-bottom-line" />
  </div>
</template>

<style lang="scss" scoped>
.layout-logo-container {
  min-width: 204px;
  position: relative;
  width: 100%;
  height: var(--v3-header-height);
  line-height: var(--v3-header-height);
  background-color: var(--el-color-menu-bg);
  text-align: center;
  overflow: hidden;
  color: #fff;
  font-size: 22px;
  font-weight: 700;
  .layout-logo {
    display: none;
  }
  .layout-logo-text {
    vertical-align: middle;
    width: 152px;
    height: 32px;
    &img {
      width: 152px;
      height: 32px;
    }
  }
  .logo-bottom-line {
    width: 0.90365rem;
    height: 0;
    border-radius: 0.01563rem 0px 0px 0px;
    border: 1px dashed #505050;
    position: absolute;
    bottom: -1px;
    left: 7%;
  }
}

.layout-mode-top {
  height: var(--v3-navigationbar-height);
  line-height: var(--v3-navigationbar-height);
}

.collapse {
  .layout-logo {
    width: 32px;
    height: 32px;
    vertical-align: middle;
    display: inline-block;
  }
  .layout-logo-text {
    display: none;
  }
}
</style>
