<template>
  <div v-loading="loadingStatus">
    <el-dropdown
      class="right-status-item"
      @visible-change="dropDownChange"
      @command="handleDropdownItem"
      ref="dropdown"
      :hide-on-click="false"
    >
      <div class="right-status-avatar">
        <div :class="['doit', 'm-r-5', userStore.statusLabel == 'online' ? 'doit-active' : 'doit-noactive']" />
        <span class="m-r-5">{{ userStore.statusLabel == "online" ? "在线" : "离线" }}</span>
        <el-icon>
          <ArrowDown v-if="isShowArrowDown" />
          <ArrowUp v-if="!isShowArrowDown" />
        </el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="online" class="m-auto">在线</el-dropdown-item>
          <el-dropdown-item command="offline">离线</el-dropdown-item>
          <el-dropdown-item divided
            >默认在线 <el-switch class="m-l-10" v-model="userStore.default_online" @change="changeDefaultStatus" />
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch } from "vue"
import { useUserStore } from "@/store/modules/user"
import {
  apiBackgroundNutritionistAccountUpdateLineStatusPost,
  apiBackgroundNutritionistAccountUpdateDefaultOnlinePost
} from "@/api/healthy"
import { to } from "@/utils"
import { ElMessage } from "element-plus"
import useMqttClient from "@/service/mqtt/useMqttService.hooks"
import { useMqttClientStore } from "@/store/modules/mqttClientStore"
import mqttConfigDefault from "../../../config/mqtt.d"
import { QosEnum } from "@/enums/qosEnum"
import { Buffer } from "buffer"
import type { DropdownInstance } from "element-plus"
import { getMqttInfo } from "@/api/healthy"

/** 初始化mqtt连接 */
let mqttUrl = mqttConfigDefault.host + ":" + mqttConfigDefault.port
const { connect, disconnect } = useMqttClient()
const useMqttStore = useMqttClientStore()

const isShowArrowDown = ref(true)
const userStore = useUserStore()
const loadingStatus = ref(false)
const dropdown = ref<DropdownInstance>()

// 是否显示下拉菜单
const dropDownChange = (value: boolean) => {
  console.log("dropDownClose")
  isShowArrowDown.value = !value
}
// 下拉菜单点击事件 离线在线切换
const handleDropdownItem = (value: string) => {
  console.log("handleDropdownItem", value)
  if (value === "offline" || value === "online") {
    handlerVisible(false)
    updateLineStatus(value)
  }
}

const initMqttInfo = async () => {
  const [err, res] = await to(getMqttInfo())
  console.log("initMqttInfo", err, res)
  if (err) {
    return
  }
  if (res && res.code === 0) {
    const data = res.data || {}
    const mqttInfo = data.mqtt_info || {}
    if (typeof mqttInfo === "object") {
      // 登录成功 根据用户的id 去配置mqtt遗嘱
      const userInfo: any = userStore.getUserInfo || {}
      const accountId: number = userInfo.account_id
      // const topicYizhu = `background_nutritionist/${accountId}/ToBN`
      // const payload = Buffer.from(
      //   JSON.stringify({
      //     line_status: "offline"
      //   })
      // )
      // const options = {
      //   will: {
      //     topic: topicYizhu,
      //     payload: payload,
      //     qos: QosEnum.Qos2, // 质量等级
      //     retain: false // 保留消息
      //   }
      // }
      const options = {
        clientId: mqttInfo.client_id, // 客户端ID，如果不传进去的话mqtt 会自动给你生成一个
        username: mqttInfo.username, // 用户名
        password: mqttInfo.password // 密码
      }
      mqttUrl = mqttInfo.host + ":" + mqttInfo.port
      const topic = `consult/nutritionist/${accountId}/ToN`
      console.log("initMqtt", topic, mqttUrl)
      connect(mqttUrl, topic, options)
      // connect(mqttUrl, topic)
    }
  }
}
// 加载
onMounted(() => {
  console.log("statusBar onMounted")

  initMqttInfo()
})
// 销毁
onUnmounted(() => {
  console.log("statusBar onUnmounted")
  disconnect()
})
// 更新状态
const updateLineStatus = async (status: string) => {
  const userInfo: any = userStore.getUserInfo
  const id = userInfo.account_id || ""
  const params = {
    id: id,
    line_status: status
  }
  loadingStatus.value = true
  const [err, res] = await to(apiBackgroundNutritionistAccountUpdateLineStatusPost(params))
  loadingStatus.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    ElMessage.success("修改状态成功")
    userStore.updateStatus(status)
  } else {
    ElMessage.error(res.msg || "修改状态失败")
  }
}
// 更新默认在线状态
const changeDefaultStatus = async (status: boolean | string | number) => {
  const userInfo: any = userStore.getUserInfo
  const id = userInfo.account_id || ""
  const params = {
    id: id,
    default_online: status
  }
  loadingStatus.value = true
  const [err, res] = await to(apiBackgroundNutritionistAccountUpdateDefaultOnlinePost(params))
  loadingStatus.value = false
  if (err) {
    userStore.setDefaltOnline(!(status as boolean))
    return
  }
  if (res && res.code === 0) {
    userStore.setDefaltOnline(status as boolean)
    ElMessage.success("修改默认状态成功")
  } else {
    userStore.setDefaltOnline(!(status as boolean))
    ElMessage.error(res.msg || "修改默认状态失败")
  }
}
// 设置显示隐藏
const handlerVisible = (visible: any) => {
  if (!dropdown.value) {
    return
  }
  if (visible) {
    dropdown.value.handleOpen()
  } else {
    dropdown.value.handleClose()
  }
}
// 信息监听
watch(
  () => useMqttStore.getReConnectMessage,
  (newValue: any) => {
    console.log("newValue useMqttStore.getReConnectMessage", newValue)
    if (newValue && Array.isArray(newValue) && newValue.length > 0) {
      const tag = newValue[0] || {}
      console.log("调用上线", tag)
      if (tag && tag.isReconnet) {
        updateLineStatus("online")
      }
    }
  },
  {
    immediate: true,
    deep: true
  }
)
</script>

<style lang="scss" scoped>
.right-status-item {
  width: 100px;
  padding: 0 10px;
  cursor: pointer;

  .right-status-avatar {
    display: flex;
    align-items: center;

    .doit {
      width: 12px;
      height: 12px;
      border-radius: 6px;
    }

    .doit-active {
      background-color: #0dc195;
    }

    .doit-noactive {
      background-color: gray;
    }

    span {
      font-size: 14px;
    }
  }
}
</style>
