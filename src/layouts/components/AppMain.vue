<script lang="ts" setup>
import { ref, watch } from "vue"
import { useTagsViewStore } from "@/store/modules/tags-view"
import { useSettingsStore } from "@/store/modules/settings"
import { useAppStore } from "@/store/modules/app"
import img from "@/assets/dataCenter/customer_service.png"
import Footer from "./Footer/index.vue"
import { storeToRefs } from "pinia"

const tagsViewStore = useTagsViewStore()
const settingsStore = useSettingsStore()
const appStore = useAppStore()
const { showBreadcrumb } = storeToRefs(settingsStore)

// 存储高度
import { useTableHeightStore } from "@/store/modules/tableHeight"
const useTableHeight = useTableHeightStore()
const appMainContentRef = ref<HTMLDivElement | null>()
watch(
  () => appMainContentRef.value?.offsetHeight,
  (newVal, oldVal) => {
    if (newVal && newVal !== oldVal) {
      useTableHeight.setMainContainHeight(newVal)
    }
  }
)
</script>

<template>
  <div ref="appMainContentRef">
    <section :class="['app-main-content', showBreadcrumb ? 'main-height' : 'main-height-no-breadcrumb']">
      <div class="app-scrollbar">
        <!-- key 采用 route.path 和 route.fullPath 有着不同的效果，大多数时候 path 更通用 -->
        <router-view v-slot="{ Component, route }">
          <transition name="el-fade-in" mode="out-in">
            <div v-if="!route.path" />
            <div v-else>
              <keep-alive :include="tagsViewStore.cachedViews">
                <component :is="Component" :key="route.path" class="app-container-grow" />
              </keep-alive>
            </div>
          </transition>
        </router-view>
        <!-- 页脚 -->
        <Footer v-if="settingsStore.showFooter" />
        <!-- 联系客服弹窗 -->
        <el-drawer v-model="appStore.showDrawer" direction="rtl" size="25%">
          <template #header>
            <span style="font-size: 16px">主题设置</span>
          </template>
          <template #default>
            <div class="drawer-content">
              <el-image style="width: 50%" :src="img" :fit="'contain'" />
              <div class="drawer-content-text">
                <span>扫码联系我们</span>
                <span>欢迎您的咨询，我们会及时响应，提供贴心服务！</span>
              </div>
            </div>
          </template>
        </el-drawer>
      </div>
      <!-- 返回顶部 -->
      <el-backtop />
      <!-- 返回顶部（固定 Header 情况下） -->
      <el-backtop target=".app-scrollbar" />
    </section>
  </div>
</template>

<style lang="scss" scoped>
@import "@/styles/mixins.scss";
// 抽屉的样式
.drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding-bottom: 40px;
  &-text {
    margin-top: 20px;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    & span:first-child {
      font-size: 26px;
    }
    & span:last-child {
      font-size: 16px;
    }
  }
}

// 内容的高度应该是100vh 减去头部的高度--v3-header-height  减去标签栏的高度(--v3-tagsview-height) 减去 标签栏距离底部的距离 --v3-tagsview-margin-bottom  如果有面包屑的话，需要减去面包屑的高度
.app-main-content {
  width: 100%;
  display: flex;
}
.main-height {
  height: calc(
    100vh - var(--v3-header-height) - var(--v3-tagsview-height) - var(--v3-tagsview-margin-bottom) - var(
        --v3-breadcrumb-height
      )
  );
}
.main-height-no-breadcrumb {
  height: calc(100vh - var(--v3-header-height) - var(--v3-tagsview-height) - var(--v3-tagsview-margin-bottom));
}

.app-scrollbar {
  flex-grow: 1;
  overflow: auto;
  @extend %scrollbar;
  display: flex;
  flex-direction: column;

  .app-container-grow {
    flex-grow: 1;
  }
}
</style>
