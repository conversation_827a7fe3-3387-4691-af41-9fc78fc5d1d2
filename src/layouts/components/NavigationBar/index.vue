<script lang="ts" setup>
import { useRouter } from "vue-router"
import { storeToRefs } from "pinia"
import { useAppStore } from "@/store/modules/app"
import { useSettingsStore } from "@/store/modules/settings"
import { useUserStore } from "@/store/modules/user"
// import StatusBar from "../StatusBar/index.vue"
import ThemeSwitch from "@/components/ThemeSwitch/index.vue"
import Screenfull from "@/components/Screenfull/index.vue"
import Hamburger from "../Hamburger/index.vue"
import TopMenu from "../TopMenu/index.vue"
import { useDevice } from "@/hooks/useDevice"
// import { useLayoutMode } from "@/hooks/useLayoutMode"
import { onMounted, ref, watch } from "vue"
import { ElMessageBox } from "element-plus"
const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()
const settingsStore = useSettingsStore()
const { showThemeSwitch, showScreenfull } = storeToRefs(settingsStore)
const isShowArrowDown = ref(true)
import IcMenuDownLoad from "@/assets/layouts/ic_menu_download.png"
import IcMenuNotice from "@/assets/layouts/ic_menu_notice.png"
import { getImgByName, useTheme } from "@/hooks/useTheme"
import ThemeChooseDialog from "@/components/ThemeChooseDialog/index.vue"
import UserInfoDialog from "./UserInfoDialog.vue"

const icHead = ref(getImgByName("ic_head_default"))
const activeThemeName = useTheme().activeThemeName
const isShowTheme = ref(false) // 是否显示主题设置
const isShowUser = ref(false) // 是否显示用户信息
const { isMobile } = useDevice()
// 是否显示下拉菜单
const dropDownChange = (value: boolean) => {
  console.log("dropDownClose")
  isShowArrowDown.value = !value
}

/** 切换侧边栏 */
const toggleSidebar = () => {
  appStore.toggleSidebar(false)
}

/** 登出 */

import useGlobalMessageReminder from "@/hooks/useGlobalMessageReminder"
const { onlyCloseDialog } = useGlobalMessageReminder()
const logout = async () => {
  onlyCloseDialog()
  if (userStore.getIsAdmin) {
    ElMessageBox.confirm("确定退出系统么？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(() => {
        userStore.resetUserData()
        userStore.resetTagsView()
        const userAgent = navigator.userAgent
        if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
          // @ts-ignore
          window.open("about:blank", "_self").close()
          window.opener.closeCurrentWindow()
          window.location.href = "about:blank"
        } else {
          window.opener = null
          window.open("about:blank", "_self")
          window.close()
        }
      })
      .catch(() => {
        // ElMessage("已取消删除")
      })
  } else {
    await userStore.logout()
    router.push("/login")
  }
}
// 显示用户信息
const handlerShowUser = () => {
  isShowUser.value = true
}

const errorHandler = () => {
  return true
}
const getUserName = () => {
  const userInfo: any = userStore.userInfo || {}
  return userInfo.name || ""
}
// 取消
const closeDialogTheme = () => {
  isShowTheme.value = false
}

// 取消用户弹窗
const closeDialogUser = () => {
  isShowUser.value = false
}

// 保存确认主题弹窗
const confirmDialogTheme = async (data: any) => {
  console.log("confirmDialogTheme", data)
  isShowTheme.value = false
}

// 确认保存
const confirmDialogUser = async (data: any) => {
  console.log("confirmDialogUser", data)
  isShowUser.value = false
}
// 跳转页面
const handlerGoto = (type: string) => {
  router.push({ name: type })
}

watch(
  () => activeThemeName.value,
  (newVal) => {
    console.log("activeThemeName", newVal)
    icHead.value = getImgByName("ic_head_default")
  }
)

// 显示主题弹窗
const showThemeChooseDialog = () => {
  console.log("打开弹窗")
  isShowTheme.value = true
}

// 获取公告
import { apiBackgroundSupervisionSupervisionMessagesGetQuestionnaireMsgNumPost } from "@/api/system"
import to from "await-to-js"

const unReadList = ref<any>([])
const unReadCount = ref(0)
const getMsgNum = async () => {
  const [err, res]: any[] = await to(apiBackgroundSupervisionSupervisionMessagesGetQuestionnaireMsgNumPost())
  if (err) return
  if (res.code === 0) {
    if (res.data) {
      unReadCount.value = res.data.unread_count
      unReadList.value = res.data.questionnaire_popup_message
    }
  }
}
onMounted(() => {
  getMsgNum()
})
const gotoMessage = () => {
  router.push({ path: "/notification_management/system_notification" })
}

const { getQuestionnaireMsgList } = useGlobalMessageReminder()
watch(
  () => unReadList.value,
  (newVal) => {
    if (newVal && newVal.length) {
      getQuestionnaireMsgList(newVal)
    }
  }
)
</script>

<template>
  <div class="navigation-bar">
    <Hamburger :is-active="appStore.sidebar.opened" class="hamburger" @toggle-click="toggleSidebar" v-if="false" />
    <!-- <Breadcrumb v-if="!isTop || isMobile" class="breadcrumb" /> -->
    <!-- <Sidebar v-if="isTop && !isMobile" class="sidebar" /> -->
    <TopMenu />
    <div class="right-menu">
      <!-- <SearchMenu v-if="showSearchMenu" class="right-menu-item" /> -->

      <!-- <ThemeSwitch v-if="showThemeSwitch" class="right-menu-item" /> -->
      <!-- <Notify v-if="showNotify" class="right-menu-item" /> -->
      <el-badge :value="unReadCount" :hidden="!unReadCount" class="item m-r-10 pointer">
        <el-icon size="18" @click="gotoMessage"><BellFilled /></el-icon>
      </el-badge>
      <Screenfull v-if="showScreenfull" class="right-menu-item" />
      <!-- <StatusBar v-if="!userStore.isAdmin" /> -->
      <!-- <el-badge :value="userStore.getUnReadCount" :max="99" type="danger" class="m-r-20px badge-tag" :offset="[-4, 5]">
        <img :src="IcMenuNotice" @click="handlerGoto('SuspendQuery')" />
      </el-badge>
      <el-badge
        :value="userStore.getDownloadCount"
        :max="99"
        type="danger"
        class="m-r-20px badge-tag"
        :offset="[-4, 5]"
      >
        <img :src="IcMenuDownLoad" @click="handlerGoto('DownloadRecord')" />
      </el-badge> -->
      <el-dropdown class="right-menu-item" @visible-change="dropDownChange">
        <div class="right-menu-avatar">
          <el-avatar :src="userStore.getHeadImg" :size="30" @error="errorHandler">
            <img :src="icHead" class="w-30 h-30" />
          </el-avatar>
          <span class="m-r-5">{{ userStore.isAdmin ? userStore.username || "超级管理员" : getUserName() }}</span>
          <el-icon>
            <ArrowDown v-if="isShowArrowDown" />
            <ArrowUp v-if="!isShowArrowDown" />
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <div @click="handlerShowUser" v-if="!userStore.isAdmin">
              <el-dropdown-item>账号信息</el-dropdown-item>
            </div>
            <div @click="showThemeChooseDialog" v-if="!userStore.isAdmin">
              <el-dropdown-item>主题设置</el-dropdown-item>
            </div>
            <el-dropdown-item divided @click="logout" v-if="!userStore.isAdmin">
              <span class="block color-red">退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <!--弹窗-->
    <theme-choose-dialog :visible="isShowTheme" @close-dialog="closeDialogTheme" @confirm-dialog="confirmDialogTheme" />
    <user-info-dialog :visible="isShowUser" @close-dialog="closeDialogUser" @confirm-dialog="confirmDialogUser" />
  </div>
</template>

<style lang="scss" scoped>
.navigation-bar {
  height: var(--v3-navigationbar-height);
  overflow: hidden;
  color: var(--v3-navigationbar-text-color);
  display: flex;
  justify-content: space-between;
  border-bottom: var(--v3-header-border-bottom);
  .hamburger {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 15px;
    cursor: pointer;
    width: 10px;
  }

  .breadcrumb {
    flex: 1;

    // 参考 Bootstrap 的响应式设计将宽度设置为 576
    @media screen and (max-width: 576px) {
      display: none;
    }
  }

  .sidebar {
    flex: 1;
    // 设置 min-width 是为了让 Sidebar 里的 el-menu 宽度自适应
    min-width: 0px;

    :deep(.el-menu) {
      background-color: transparent;
    }

    :deep(.el-sub-menu) {
      &.is-active {
        .el-sub-menu__title {
          color: var(--el-color-primary) !important;
        }
      }
    }
  }

  .right-menu {
    margin-right: 10px;
    height: 100%;
    display: flex;
    align-items: center;

    .right-menu-item {
      padding: 0 10px;
      cursor: pointer;

      .right-menu-avatar {
        display: flex;
        align-items: center;

        .el-avatar {
          margin-right: 10px;
        }

        span {
          font-size: 16px;
        }
      }
    }
    .badge-tag {
      cursor: pointer;
      &:hover {
        transform: scale(1.03);
      }
    }
  }
}
</style>
