<template>
  <div>
    <el-drawer
      v-model="dialogVisible"
      :title="dialogTitle"
      size="408px"
      direction="rtl"
      @close="closeDialog"
      class="ps-drawer"
      :close-on-click-modal="false"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="ruleFormRef"
        label-width="auto"
        class="from-container"
        label-position="right"
        v-loading="loading"
      >
        <div class="flex items-center">
          <div class="left-item">姓名</div>
          <div class="right-item flex items-center">
            <el-input v-model="formData.name" placeholder="请输入姓名" class="input-item" v-if="type == 'edit'" />
            <div v-else>
              <span class="text-ellipsis">{{ formData.name }}</span>
            </div>
            <el-icon class="icon-tag" @click="changeEditType" v-if="type !== 'edit'">
              <Edit />
            </el-icon>
          </div>
        </div>
        <div class="flex">
          <div class="left-item">账号</div>
          <div class="right-item">
            {{ formData.username }}
          </div>
        </div>
        <div class="flex items-center">
          <div class="left-item">手机号</div>
          <div class="right-item flex items-center">
            {{ formData.mobile }}
            <el-icon class="icon-tag" v-if="type !== 'edit'" @click="showWarnDialog('mobile')">
              <Edit />
            </el-icon>
          </div>
        </div>
        <div class="flex items-center">
          <div class="left-item">密码</div>
          <div class="right-item flex items-center">
            {{ formData.password }}
            <el-icon class="icon-tag" v-if="type !== 'edit'" @click="showWarnDialog('password')">
              <Edit />
            </el-icon>
          </div>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="cancelDialog">关闭</el-button>
          <el-button plain type="primary" @click="confirmDialog" v-if="type === 'edit'">保存</el-button>
        </div>
      </template>
      <!--弹窗-->
      <el-dialog
        v-model="warnDialogShow"
        :title="warnDialogTitle"
        width="300px"
        :close-on-click-modal="false"
        @close="handlerWarnClose"
        class="ps-dialog"
      >
        <div class="tip-top">
          {{
            warnDialogType === "password"
              ? "修改密码成功后将自动退出系统，需要重新登录"
              : "修改手机号成功后将自动退出系统，需要重新登录"
          }}
        </div>
        <el-form
          :model="formData"
          :rules="rules"
          ref="warnFormRef"
          label-width="auto"
          class="from-container"
          label-position="right"
          v-loading="loading"
        >
          <el-form-item prop="newMobile" v-if="warnDialogType === 'mobile'" class="m-t-20px">
            <el-input v-model="formData.newMobile" placeholder="请输入新的手机号" class="input-item" />
          </el-form-item>
          <el-form-item prop="vrcode" v-if="warnDialogType === 'mobile'">
            <el-input v-model="formData.vrcode" placeholder="请输入验证码" class="input-item" style="width: 160px" />
            <el-button
              @click="getVerCode(false)"
              class="w-90px m-l-18px ps-origin-btn-plain"
              :disabled="tipBtnDisabled"
              >{{ tipVrcode }}</el-button
            >
          </el-form-item>
          <el-form-item prop="newPass" v-if="warnDialogType === 'password'" class="m-t-20px">
            <el-input v-model="formData.newPass" placeholder="请输入新密码" class="input-item" type="password" />
          </el-form-item>
          <el-form-item prop="confirnPass" v-if="warnDialogType === 'password'">
            <el-input
              v-model="formData.confirmPass"
              placeholder="请再次输入新密码"
              class="input-item"
              type="password"
            />
          </el-form-item>
        </el-form>
        <!-- 点文字认证-->
        <div class="verify-style">
          <verify-code
            ref="verifyCode"
            :visible="isShowVerify"
            @success="verifySuccess"
            :is-number="true"
            @refresh="verifyRefresh"
          />
        </div>
        <template #footer>
          <div class="">
            <el-button @click="handlerWarnClose">取消</el-button>
            <el-button type="primary" @click="confirmModify"> 修改 </el-button>
          </div>
        </template>
      </el-dialog>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, reactive, onUnmounted } from "vue"
import { apiBackgroundFundSupervisionAuditAccountModifyPost } from "@/api/login"
import to from "await-to-js"
import type { FormRules } from "element-plus"
import { ElMessage } from "element-plus"
import { useUserStore } from "@/store/modules/user"
import { TUserInfo } from "../../../../types/constant"
import { Edit } from "@element-plus/icons-vue"
import { Md5 } from "ts-md5"
import VerifyCode from "@/components/VerifyCode/index.vue"
import { apiBackgroundGetSmsVerifyCodePost, apiBackgroundVerificationCodePost } from "@/api/login"
import { Base64 } from "js-base64"

const userStore = useUserStore()

const prop = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dialogData: {
    type: Object,
    default: () => {
      return {}
    }
  },
  dialogTitle: {
    type: String,
    default: "系统设置"
  },
  direction: {
    type: String,
    default: "rtl"
  },
  dialogType: {
    type: String,
    default: "add"
  }
})

// 表单ref
const ruleFormRef = ref()
// 表单数据
const formData = reactive<TUserInfo>({
  username: "",
  account_id: 0,
  mobile: "",
  password: "",
  name: ""
})
// 表单校验
const rules = reactive<FormRules>({
  username: [{ required: true, message: "请填写姓名", trigger: "change" }],
  newMobile: [{ required: true, message: "请填写手机号", trigger: "blur" }],
  vrcode: [{ required: true, message: "请输入验证码", trigger: "blur" }],
  newPass: [{ required: true, message: "请输入新密码", trigger: "blur" }],
  confirmPass: [{ required: true, message: "请输入新密码", trigger: "blur" }]
})
const type = ref("info")
// 加载中
const loading = ref(false)
// 是否显示弹窗
const dialogVisible = ref(false)
// 弹窗事件
const emit = defineEmits([
  "closeDialog", // 弹窗关闭
  "confirmDialog" // 弹窗确认
])
// 警告弹窗
const warnDialogShow = ref(false)
const warnDialogType = ref("password")
const warnDialogTitle = ref("修改密码")
const warnFormRef = ref()

// 点文字
const isShowVerify = ref(false)
const answerList = ref<any[]>([])
const answer = ref("")
const verifyCode = ref()
const tipVrcode = ref("获取验证码")
const tipBtnDisabled = ref(false)
const countDown = ref(0)
const authTimetimer = ref() // 验证码倒计时线程

// 关闭弹窗
const closeDialog = () => {
  console.log("closeDialog 111111")
  dialogVisible.value = false
  type.value = "info"
  emit("closeDialog")
}
// 取消
const cancelDialog = () => {
  closeDialog()
}
// 确认弹窗
const confirmDialog = async () => {
  console.log("confirmDialog", ruleFormRef.value)
  if (ruleFormRef.value) {
    ruleFormRef.value.validate(async (valid: any) => {
      if (valid) {
        saveInfo(formData)
      }
    })
  }
}

// 保存信息
const saveInfo = async (data: TUserInfo) => {
  console.log("saveTheme", data)
  const params = {
    id: data.account_id,
    name: data.name,
    username: data.username
  }
  if (warnDialogType.value === "password" && data.newPass) {
    const md5: any = new Md5()
    md5.appendAsciiStr(data.newPass)
    const newPass = md5.end()
    Reflect.set(params, "password", newPass)
  }
  console.log("params", params)
  const [err, res] = await to(apiBackgroundFundSupervisionAuditAccountModifyPost(params))
  if (err) {
    ElMessage.error(err.message || "保存失败")
    return
  }
  if (res && res.code === 0) {
    ElMessage.success("保存成功")
    const data = res.data || {}
    if (data) {
      userStore.setUserInfo(res.data)
    }
    emit("confirmDialog", true)
  } else {
    ElMessage.error(res.msg || "保存失败")
  }
}

// 初始化数据
const initData = () => {
  const userInfo = userStore.getUserInfo
  formData.username = userInfo?.username
  formData.account_id = userInfo?.account_id
  formData.name = userInfo?.name
  formData.mobile = userInfo?.mobile
  formData.password = "* * * * * *"
  console.log("userInfo", userInfo)
}
// 修改编辑模式
const changeEditType = () => {
  type.value = "edit"
}
// 警告弹窗关闭
const handlerWarnClose = () => {
  // 关闭弹窗的时候清空缓存
  formData.confirmPass = ""
  formData.newPass = ""
  formData.vrcode = ""
  formData.newMobile = ""
  if (warnFormRef.value) {
    warnFormRef.value.clearValidate()
  }
  warnDialogShow.value = false
}
// 显示警告弹窗
const showWarnDialog = (value: string) => {
  warnDialogType.value = value
  warnDialogTitle.value = value === "password" ? "修改密码" : "修改手机号"
  warnDialogShow.value = true
}
// 确认修改
const confirmModify = () => {
  if (warnFormRef.value) {
    warnFormRef.value.validate(async (valid: any) => {
      if (valid) {
        if (warnDialogType.value === "password" && formData.newPass !== formData.confirmPass) {
          return ElMessage.error("两次输入的密码不一致")
        }
        saveInfo(formData)
      }
    })
  }
}

// 验证成功
const verifySuccess = (value: any) => {
  console.log("verifySuccess", value)
  isShowVerify.value = false
  getPhoneCode()
}
// 刷新验证码
const verifyRefresh = () => {
  getVerCode(true)
}
// 获取短信验证码
const getPhoneCode = async () => {
  const choices = 0
  const params = {
    phone: formData.newMobile,
    choices: choices,
    code: answer.value
  }
  console.log("getPhoneCode", params)
  const [err, res] = await to(apiBackgroundVerificationCodePost(params))
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res.code === 0) {
    // 开始倒计时
    countDownHandle()
    tipBtnDisabled.value = true
    ElMessage.success("发送成功")
  } else {
    ElMessage.error(res.msg)
  }
}

// 获取图形验证码
const getVerCode = async (flag: boolean) => {
  if (!formData.newMobile || !/^1[3456789]\d{9}$/.test(formData.newMobile)) {
    return ElMessage.error("请输入正确的手机号")
  }
  const [err, res] = await to(
    apiBackgroundGetSmsVerifyCodePost({
      phone: formData.newMobile
    })
  )
  if (err) {
    return ElMessage.error("获取失败")
  }
  if (res && res.code === 0) {
    const data = res.data || {}
    answer.value = data.key || ""
    formData.code = data.key || ""
    const keys = Base64.decode(data.key) ? JSON.parse(Base64.decode(data.key)) : ""
    console.log("getLoginVerifyCode", keys)
    answerList.value = []
    if (keys && typeof keys === "object") {
      for (const keyName in keys) {
        answerList.value.push(keys[keyName])
      }
    }
    if (verifyCode.value) {
      verifyCode.value.setAnswerList(answerList.value)
      if (flag) {
        verifyCode.value.reset()
      }
    }
    isShowVerify.value = true
    console.log("result", answerList.value)
  } else {
    ElMessage.error(res.msg)
  }
}

// 倒计时
const countDownHandle = () => {
  console.log("倒计时")
  countDown.value = 60
  setCountDownText()
  authTimetimer.value = setInterval(() => {
    countDown.value--
    setCountDownText()
    if (countDown.value <= 0) {
      tipVrcode.value = "重新获取"
      tipBtnDisabled.value = false
      if (authTimetimer.value) {
        clearInterval(authTimetimer.value)
      }
    }
  }, 1000)
}
// 设置倒计时的文字
const setCountDownText = () => {
  const ONE_MINUTE = 60
  const minute = Math.floor(countDown.value / ONE_MINUTE)
  let countText = ""
  if (minute > 1) {
    // 1 分钟就不要显示这个啦，显示60秒多好
    countText = `${minute}分`
    const second = Math.floor(countDown.value % ONE_MINUTE)
    if (second > 0) {
      countText += `${second}秒`
    } else {
      countText += `钟`
    }
    tipVrcode.value = countText
  } else {
    tipVrcode.value = `${countDown.value}秒`
  }
  console.log("countDownText", tipVrcode.value)
}

watch([() => prop.visible, () => prop.dialogData], ([newVisible, newData]) => {
  console.log("UserInfoDialog", newVisible, newData)
  dialogVisible.value = newVisible
  if (newVisible) {
    // 如果显示获取一下最新的数据
    initData()
  }
  console.log("UserInfoDialog", newVisible)
})
// 页面销毁
onUnmounted(() => {
  if (authTimetimer.value) {
    clearInterval(authTimetimer.value)
  }
})
</script>
<style lang="scss" scoped>
.left-item {
  width: 120px;
  height: 40px;
  line-height: 40px;
  background-color: #f4f6fc;
  border: 1px solid #e9ecf1;
  padding-left: 16px;
  font-size: 14px;
  color: #363636;
}

.right-item {
  width: 370px;
  height: 40px;
  line-height: 40px;
  padding-left: 16px;
  font-size: 14px;
  border-top: 1px solid #e9ecf1;
  border-right: 1px solid #e9ecf1;
  border-bottom: 1px solid #e9ecf1;
}

.icon-tag {
  width: 18px;
  height: 18px;
  color: var(--el-color-primary);
  cursor: pointer;
}

.icon-tag:hover {
  transform: scale(1.1);
}

.ps-dialog {
  position: relative;
  .verify-style {
    position: absolute;
    top: 18px;
    left: 0;
  }
}
</style>
