<script lang="ts" setup>
import { computed } from "vue"
import { type RouteRecordRaw } from "vue-router"
import SidebarItemLink from "./SidebarItemLink.vue"
import { isExternal } from "@/utils/validate"
import path from "path-browserify"

interface Props {
  item: RouteRecordRaw
  basePath?: string
}

const props = withDefaults(defineProps<Props>(), {
  basePath: ""
})

/** 是否始终显示根菜单 */
const alwaysShowRootMenu = computed(() => props.item.meta?.alwaysShow)

/** 显示的子菜单 */
const showingChildren = computed(() => {
  return props.item.children?.filter((child) => !child.meta?.hidden) ?? []
})

/** 显示的子菜单数量 */
const showingChildNumber = computed(() => {
  return showingChildren.value.length
})

/** 唯一的子菜单项 */
const theOnlyOneChild = computed(() => {
  const number = showingChildNumber.value
  switch (true) {
    case number > 1:
      return null
    case number === 1:
      return showingChildren.value[0]
    default:
      return { ...props.item, path: "" }
  }
})

/** 解析路径 */
const resolvePath = (routePath: string) => {
  switch (true) {
    case isExternal(routePath):
      console.log("routePath", routePath)
      return routePath
    case isExternal(props.basePath):
      console.log("basePath", props.basePath)
      return props.basePath
    default:
      console.log("path.resolve(props.basePath, routePath)", props.basePath, routePath)
      return path.resolve(props.basePath, routePath)
  }
}

const getLangName = (name: string) => {
  console.log("name", name)
  // @ts-ignore
  return window["$t"]("route." + name)
}
//  检测层级
// const checkLevelOne =(item:any) => {
//   var level = item.meta && item.meta.level ? item.meta.level : 0
//   if(level >= 1){
//     return true
//   }
//  return false
// }
</script>

<template>
  <!--只有一个子菜单的时候-->
  <template v-if="!alwaysShowRootMenu && theOnlyOneChild && !theOnlyOneChild.children">
    <SidebarItemLink v-if="theOnlyOneChild.meta" :to="resolvePath(theOnlyOneChild.path ? theOnlyOneChild.path : '')">
      <!--:class="[checkLevelOne(theOnlyOneChild)?'el-menu-level-1':'']"-->
      <el-menu-item :index="resolvePath(theOnlyOneChild.path ? theOnlyOneChild.path : '')">
        <SvgIcon v-if="theOnlyOneChild.meta.svgIcon" :name="theOnlyOneChild.meta.svgIcon" />
        <component v-else-if="theOnlyOneChild.meta.elIcon" :is="theOnlyOneChild.meta.elIcon" class="el-icon" />
        <template v-if="theOnlyOneChild.meta.title" #title>
          {{ getLangName(theOnlyOneChild.meta.title) }}
        </template>
      </el-menu-item>
    </SidebarItemLink>
  </template>
  <!--多个子菜单的时候-->
  <el-sub-menu v-else :index="resolvePath(props.item.path ? props.item.path : '')" teleported>
    <template #title>
      <SvgIcon v-if="props.item.meta?.svgIcon" :name="props.item.meta.svgIcon" />
      <component v-else-if="props.item.meta?.elIcon" :is="props.item.meta.elIcon" class="el-icon" />
      <span v-if="props.item.meta?.title">{{ getLangName(props.item.meta.title) }}</span>
    </template>
    <template v-if="props.item.children">
      <SidebarItem
        v-for="child in showingChildren"
        :key="child.path"
        :item="child"
        :base-path="resolvePath(child.path ? child.path : '')"
      />
    </template>
  </el-sub-menu>
</template>

<style lang="scss" scoped>
.svg-icon {
  min-width: 1em;
  margin-right: 12px;
  font-size: 18px;
}

.el-icon {
  width: 1em;
  margin-right: 12px;
  font-size: 18px;
}
.el-menu-level-1 {
  margin: 0 40px;
  padding: 0 20px !important;
}
.left-border {
  height: 100%;
  width: 1px;
}
</style>
