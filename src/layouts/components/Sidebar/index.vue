<script lang="ts" setup>
import { computed, ref, watchEffect } from "vue"
import { useRoute } from "vue-router"
import { useAppStore } from "@/store/modules/app"
import { usePermissionStore } from "@/store/modules/permission"
import { useSettingsStore } from "@/store/modules/settings"
import SidebarItem from "./SidebarItem.vue"
import { getCssVariableValue } from "@/utils"
import { useUserStore } from "@/store/modules/user"

const v3SidebarMenuBgColor = getCssVariableValue("--v3-sidebar-menu-bg-color")
const v3SidebarMenuTextColor = getCssVariableValue("--v3-sidebar-menu-text-color")
const v3SidebarMenuActiveTextColor = getCssVariableValue("--v3-sidebar-menu-active-text-color")

const route = useRoute()
const appStore = useAppStore()
const permissionStore = usePermissionStore()
const settingsStore = useSettingsStore()
const userStore = useUserStore()
const currentRoutePath = ref(route.path)

watchEffect(() => {
  currentRoutePath.value = route.path
  console.log("currentRouteMenu", currentRoutePath.value)
})

const noHiddenRoutes = computed(() => {
  const activeMenu = userStore.getActiveMenu
  console.log("activeMenu 12312312312", activeMenu, permissionStore.getDyNamicRoutesBykey(activeMenu))
  return activeMenu ? permissionStore.getDyNamicRoutesBykey(activeMenu) : []
})
const isCollapse = computed(() => !appStore.sidebar.opened)
const isLogo = computed(() => settingsStore.showLogo)
const backgroundColor = computed(() => v3SidebarMenuBgColor)
const textColor = computed(() => v3SidebarMenuTextColor)
const activeTextColor = computed(() => v3SidebarMenuActiveTextColor)
const sidebarMenuItemHeight = computed(() => {
  return "var(--v3-sidebar-menu-item-height)"
})
const sidebarMenuHoverBgColor = computed(() => {
  return "var(--v3-sidebar-menu-hover-bg-color)"
})
const tipLineWidth = computed(() => {
  return "3px"
})
</script>

<template>
  <div :class="{ 'has-logo': isLogo }">
    <!-- <Logo v-if="isLogo" :collapse="isCollapse" /> -->
    <el-scrollbar wrap-class="scrollbar-wrapper" :always="false">
      <el-menu
        :default-active="currentRoutePath"
        :collapse="isCollapse"
        :background-color="backgroundColor"
        :text-color="textColor"
        :active-text-color="activeTextColor"
        :unique-opened="true"
        :collapse-transition="true"
        mode="vertical"
      >
        <SidebarItem v-for="route in noHiddenRoutes" :key="route.path" :item="route" :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<style lang="scss" scoped>
%tip-line {
  &::before {
    content: "";
    position: absolute;
    top: 30%;
    left: -20px;
    width: v-bind(tipLineWidth);
    height: 16px;
    background-color: var(--v3-sidebar-menu-tip-line-bg-color);
  }
}

.has-logo {
  .el-scrollbar {
    // 多 1% 是为了在左侧模式时侧边栏最底部不显示 1px 左右的白色线条
    height: 101%;
  }
}

.el-scrollbar {
  // 多 1% 是为了在顶部模式时防止垂直滚动
  height: 101%;
  background-color: v-bind(backgroundColor);

  :deep(.scrollbar-wrapper) {
    // 限制水平宽度
    overflow-x: hidden !important;

    .el-scrollbar__view {
      height: 100%;
    }
  }

  // 滚动条
  :deep(.el-scrollbar__bar) {
    &.is-horizontal {
      // 隐藏水平滚动条
      display: none;
    }

    &.is-vertical {
      // 当为顶部模式时隐藏垂直滚动条
      display: none;
    }
  }
}

.el-menu {
  border: none;
  min-height: 100%;
  width: 100% !important;
  padding-left: 20px;
}

.el-menu--horizontal {
  height: v-bind(sidebarMenuItemHeight);
}

:deep(.el-menu-item),
:deep(.el-sub-menu__title),
:deep(.el-sub-menu .el-menu-item),
:deep(.el-menu--horizontal .el-menu-item) {
  height: v-bind(sidebarMenuItemHeight);
  line-height: v-bind(sidebarMenuItemHeight);
  border-top-left-radius: 24px;
  border-bottom-left-radius: 24px;
  margin-top: 1px;
  &.is-active,
  &:hover {
    background-color: v-bind(sidebarMenuHoverBgColor);
  }
}

:deep(.el-sub-menu) {
  &.is-active {
    > .el-sub-menu__title {
      color: v-bind(activeTextColor) !important;
    }
  }
}

:deep(.el-menu-item.is-active) {
  @extend %tip-line;
}

.el-menu--collapse {
  :deep(.el-sub-menu.is-active) {
    .el-sub-menu__title {
      @extend %tip-line;
    }
  }
}
</style>
