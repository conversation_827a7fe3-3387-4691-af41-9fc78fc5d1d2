<script lang="ts" setup>
import { ref, nextTick } from "vue"
import { RouterLink, useRoute, useRouter } from "vue-router"
import { useSettingsStore } from "@/store/modules/settings"
import { useRouteListener } from "@/hooks/useRouteListener"
import Screenfull from "@/components/Screenfull/index.vue"
import { ElScrollbar } from "element-plus"
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue"
import imgArrowLeft from "@/assets/layouts/ic_arraw_left_gray.png?url"
import imgArrowRight from "@/assets/layouts/ic_arraw_right_gray.png?url"
import { useTagsViewStore } from "@/store/modules/tags-view"
import { useUserStore } from "@/store/modules/user"
import { adminRoutes, healthyRoutes } from "@/router/modules"

interface Props {
  tagRefs: InstanceType<typeof RouterLink>[]
}

const props = defineProps<Props>()

const route = useRoute()
const router = useRouter()
const settingsStore = useSettingsStore()
const { listenerRouteChange } = useRouteListener()
const tagsViewStore = useTagsViewStore()
const userStore = useUserStore()

/** 滚动条组件元素的引用 */
const scrollbarRef = ref<InstanceType<typeof ElScrollbar>>()
/** 滚动条内容元素的引用 */
const scrollbarContentRef = ref<HTMLDivElement>()

/** 当前滚动条距离左边的距离 */
let currentScrollLeft = 0
/** 每次滚动距离 */
const translateDistance = 200

/** 滚动时触发 */
const scroll = ({ scrollLeft }: { scrollLeft: number }) => {
  currentScrollLeft = scrollLeft
}

/** 鼠标滚轮滚动时触发 */
const wheelScroll = ({ deltaY }: WheelEvent) => {
  if (/^-/.test(deltaY.toString())) {
    scrollTo("left")
  } else {
    scrollTo("right")
  }
}

/** 获取可能需要的宽度 */
const getWidth = () => {
  /** 可滚动内容的长度 */
  const scrollbarContentRefWidth = scrollbarContentRef.value!.clientWidth
  /** 滚动可视区宽度 */
  const scrollbarRefWidth = scrollbarRef.value!.wrapRef!.clientWidth
  /** 最后剩余可滚动的宽度 */
  const lastDistance = scrollbarContentRefWidth - scrollbarRefWidth - currentScrollLeft

  return { scrollbarContentRefWidth, scrollbarRefWidth, lastDistance }
}

/** 左右滚动 */
const scrollTo = (direction: "left" | "right", distance: number = translateDistance) => {
  let scrollLeft = 0
  const { scrollbarContentRefWidth, scrollbarRefWidth, lastDistance } = getWidth()
  // 没有横向滚动条，直接结束
  if (scrollbarRefWidth > scrollbarContentRefWidth) return
  if (direction === "left") {
    scrollLeft = Math.max(0, currentScrollLeft - distance)
  } else {
    scrollLeft = Math.min(currentScrollLeft + distance, currentScrollLeft + lastDistance)
  }
  scrollbarRef.value!.setScrollLeft(scrollLeft)
}

/** 移动到目标位置 */
const moveTo = () => {
  const tagRefs = props.tagRefs
  for (let i = 0; i < tagRefs.length; i++) {
    // @ts-ignore
    if (route.path === tagRefs[i].$props.to.path) {
      // @ts-ignore
      const el: HTMLElement = tagRefs[i].$el
      const offsetWidth = el.offsetWidth
      const offsetLeft = el.offsetLeft
      const { scrollbarRefWidth } = getWidth()
      // 当前 tag 在可视区域左边时
      if (offsetLeft < currentScrollLeft) {
        const distance = currentScrollLeft - offsetLeft
        scrollTo("left", distance)
        return
      }
      // 当前 tag 在可视区域右边时
      const width = scrollbarRefWidth + currentScrollLeft - offsetWidth
      if (offsetLeft > width) {
        const distance = offsetLeft - width
        scrollTo("right", distance)
        return
      }
    }
  }
}

/** 监听路由变化，移动到目标位置 */
listenerRouteChange(() => {
  nextTick(moveTo)
})
const isShowArrowDown = ref(true)
// 是否显示下拉菜单
const dropDownChange = (value: boolean) => {
  console.log("dropDownClose")
  isShowArrowDown.value = !value
}
// 监听右侧更多下拉菜单
const dropDownCommand = (command: string) => {
  console.log(command)
  const tagView = tagsViewStore.getActiveTagView()
  console.log("tagView", tagView)
  switch (command) {
    case "refresh":
      console.log("refresh")
      if (tagView != undefined) {
        tagsViewStore.delCachedView(tagView)
        router.replace({ path: "/redirect" + tagView.path, query: tagView.query })
      }
      break
    case "closeRight":
      if (tagView != undefined) {
        tagsViewStore.delRightOther(tagView)
      }
      console.log("closeRight")
      break
    case "closeOther":
      if (tagView != undefined) {
        tagsViewStore.delOthersVisitedViews(tagView)
        tagsViewStore.delOthersCachedViews(tagView)
      }
      console.log("closeOther")
      break
    case "closeAll":
      tagsViewStore.delAllVisitedViews()
      tagsViewStore.delAllCachedViews()
      const isAdmin = userStore.getIsAdmin
      if (isAdmin) {
        // 如果管理员
        if (adminRoutes && adminRoutes.length > 0) {
          router.push(adminRoutes[0].path)
        }
      } else {
        if (healthyRoutes && healthyRoutes.length > 0) router.push(healthyRoutes[0].path)
      }
      console.log("closeAll")
      break
    default:
      break
  }
}
</script>

<template>
  <div class="scroll-container">
    <el-icon class="arrow left" @click="scrollTo('left')">
      <img :src="imgArrowLeft" class="w-24 h-24" />
    </el-icon>
    <el-scrollbar ref="scrollbarRef" @wheel.passive="wheelScroll" @scroll="scroll">
      <div ref="scrollbarContentRef" class="scrollbar-content">
        <slot />
      </div>
    </el-scrollbar>
    <el-icon class="arrow right" @click="scrollTo('right')">
      <img :src="imgArrowRight" class="w-24 h-24" />
    </el-icon>

    <!-- <Screenfull v-if="settingsStore.showScreenfull" :content="true" class="screenfull" /> -->
    <el-dropdown class="right-menu-item" @visible-change="dropDownChange" @command="dropDownCommand">
      <div class="ps-flex flex-center">
        <div class="gray-line" />
        <div class="">{{ $t("global.more") }}</div>
        <el-icon><ArrowDown v-if="isShowArrowDown" /><ArrowUp v-if="!isShowArrowDown" /></el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="refresh">刷新当前标签</el-dropdown-item>
          <el-dropdown-item command="closeRight">关闭右侧标签</el-dropdown-item>
          <el-dropdown-item command="closeOther">关闭其他标签</el-dropdown-item>
          <el-dropdown-item command="closeAll">关闭所有标签</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<style lang="scss" scoped>
.scroll-container {
  height: 100%;
  width: 100%;
  user-select: none;
  display: flex;
  justify-content: space-between;
  .arrow {
    width: 40px;
    height: 100%;
    font-size: 18px;
    cursor: pointer;
    &:hover {
      transform: scale(1.05);
    }
    &.left {
      // box-shadow: 5px 0 5px -6px var(--el-border-color-darker);
    }
    &.right {
      // box-shadow: -5px 0 5px -6px var(--el-border-color-darker);
    }
    &.down {
      width: 20px;
      margin-right: 20px;
    }
  }
  .el-scrollbar {
    flex: 1;
    // 防止换行（超出宽度时，显示滚动条）
    white-space: nowrap;
    .scrollbar-content {
      display: inline-block;
    }
  }
  .screenfull {
    width: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  .right-menu-item {
    padding-right: 15px;
    cursor: pointer;
  }
  .gray-line {
    margin-left: 5px;
    margin-right: 10px;
    width: 1px;
    height: 14px;
    background: #e9ecf1;
  }
}
</style>
