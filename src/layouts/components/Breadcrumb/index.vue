<script lang="ts" setup>
import { ref, computed, watch } from "vue"
import { type RouteLocationMatched, useRoute, useRouter } from "vue-router"
import { useRouteListener } from "@/hooks/useRouteListener"
import { compile } from "path-to-regexp"
import { renderLang } from "@/utils"
import { ArrowRight } from "@element-plus/icons-vue"
import imgDateRight from "@/assets/layouts/ic_date_right_gray.png"
import { useUserStore } from "@/store/modules/user"
import { formatDateTimeByType } from "@/utils/index"

const route = useRoute()
const router = useRouter()
const { listenerRouteChange } = useRouteListener()
const userStore = useUserStore()

/** 定义响应式数据 breadcrumbs，用于存储面包屑导航信息 */
const breadcrumbs = ref<RouteLocationMatched[]>([])

/** 获取面包屑导航信息 */
const getBreadcrumb = () => {
  breadcrumbs.value = route.matched.filter((item) => item.meta?.title && item.meta?.breadcrumb !== false)
  console.log("breadcrumbs", breadcrumbs.value)
}

/** 编译路由路径 */
const pathCompile = (path: string) => {
  const toPath = compile(path)
  return toPath(route.params)
}

/** 处理面包屑导航点击事件 */
const handleLink = (item: RouteLocationMatched) => {
  const { redirect, path } = item
  if (redirect) {
    router.push(redirect as string)
    return
  }
  router.push(pathCompile(path))
}

/** 监听路由变化，更新面包屑导航信息 */
listenerRouteChange((route) => {
  if (route.path.startsWith("/redirect/")) return
  getBreadcrumb()
}, true)

const getLangName = (name: string | undefined) => {
  if (!name) return ""
  console.log("name", name)
  return renderLang("route." + name)
}

const props = userStore.getBreadCrumbDate
const dateValue = ref()
const dateVisableIcon = ref(true)
const nowRouteTitle = ref()

const startTime = () => {
  const date = new Date()
  date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
  return formatDateTimeByType(date, "YYYY-MM-DD")
}
const endTime = () => {
  return formatDateTimeByType(new Date(), "YYYY-MM-DD")
}
// 设置默认值
dateValue.value = props.defaultTime && props.defaultTime.length > 0 ? props.defaultTime : [startTime(), endTime()]

// 监听路由的值变化
const isShowDate = computed(() => {
  const { meta } = route
  console.log("isShowDate", meta)
  return meta ? meta.showBeadcrumbRightDate : false
})
// 设置date图标icon
const setDateIcon = (): boolean => {
  if (!dateValue.value || dateValue.value.length === 0) {
    dateVisableIcon.value = true
    return true
  }
  dateVisableIcon.value = false
  return false
}
watch(
  [() => dateValue, () => route.meta],
  ([newValue, newMeta]) => {
    console.log("newValue", newValue.value)
    userStore.setBreakCrumbDate({ dateValue: newValue.value })
    if (newMeta.title) {
      nowRouteTitle.value = newMeta.title
    }
  },
  {
    deep: true,
    immediate: true
  }
)
</script>

<template>
  <div class="breadcrumb-container ps-flex row-between p-r-24">
    <el-breadcrumb :separator-icon="ArrowRight">
      <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="item.path">
        <span
          v-if="item.redirect || item.redirect === 'noRedirect' || item.meta.title === nowRouteTitle"
          :class="[index !== breadcrumbs.length - 1 ? 'no-redirect' : '']"
        >
          {{ getLangName(item.meta?.title) }}
        </span>
        <a v-else @click.prevent="handleLink(item)">
          {{ getLangName(item.meta?.title) }}
        </a>
      </el-breadcrumb-item>
    </el-breadcrumb>
    <!-- 时间控件-->
    <div
      v-if="isShowDate"
      class="ps-date-picker ps-flex flex-center"
      @mouseover="setDateIcon"
      @mouseleave="dateVisableIcon = true"
    >
      <el-date-picker
        v-model="dateValue"
        :type="props.dateType ? props.dateType : 'daterange'"
        :start-placeholder="props.startPlaceholder ? props.startPlaceholder : '开始时间'"
        :end-placeholder="props.endPlaceholder ? props.endPlaceholder : '结束时间'"
        value-format="YYYY-MM-DD"
        :range-separator="props.rangeSeparator ? props.rangeSeparator : '至'"
        prefix-icon="none"
      />
      <img :src="imgDateRight" class="w-16 h-16 img-right-icon" v-if="dateVisableIcon" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.breadcrumb-container {
  width: 100%;
}

.el-breadcrumb {
  // line-height: var(--v3-navigationbar-height);
  margin: 0 20px;
  height: var(--v3-breadcrumb-height);

  .no-redirect {
    color: var(--el-text-color-placeholder);
  }

  a {
    font-weight: normal;
  }
}

.ps-date-picker {
  position: relative;

  .img-right-icon {
    position: absolute;
    right: 10px;
    top: 10px;
  }
}
</style>
