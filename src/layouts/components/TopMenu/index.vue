<template>
  <div class="top-menu ps-flex col-center m-l-24 m-r-24">
    <div
      v-for="(item, index) in noHiddenRoutes"
      :key="index"
      @click="handleOpen(item, index)"
      :class="[
        'menu-item',
        'ps-flex',
        'flex-center',
        isActive(item) ? 'active' : 'noactive',
        'm-r-20',
        'pointer',
        'font-size-14'
      ]"
    >
      <!-- <SvgIcon v-if="item.svgIconName" :name="item.svgIconName" /> -->
      <div>{{ item.verbose_name }}</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { toRef, ref, watchEffect, onMounted, watch } from "vue"
import { useUserStore } from "@/store/modules/user"
import { usePermissionStore } from "@/store/modules/permission"
import { useRoute, useRouter } from "vue-router"
import cloneDeep from "lodash/cloneDeep"

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const permissionStore = usePermissionStore()
const activeMenu = toRef(userStore.getActiveMenu)
const currentRoutePath = ref(route.name)
const noHiddenRoutes = ref<Array<any>>()

onMounted(() => {
  const list = permissionStore.getDyNamicRoutes
  if (!list || list.length === 0) {
    return
  }
  if (!activeMenu.value || activeMenu.value.length === 0) {
    userStore.setActiveMenu(list[0].key || "")
    activeMenu.value = list[0].key || ""
    const childrenList = list[0].children || []
    if (childrenList && childrenList.length > 0) {
      list[0].activeMenu = childrenList[0].path
    }
  }
  noHiddenRoutes.value = cloneDeep(list)
})

// 设置保存激活的路由
const setActiveByRoute = (currentRouteMenu: string, currentRoutePath: string) => {
  if (noHiddenRoutes.value) {
    const list = cloneDeep(noHiddenRoutes.value)
    list.forEach((item) => {
      if (item.key === currentRouteMenu) {
        item.activeMenu = currentRoutePath
      }
    })
    noHiddenRoutes.value = cloneDeep(list)
  }
}

watchEffect(() => {
  currentRoutePath.value = route.name
  const currentRouteMenu = permissionStore.getActiveMenuByRouteName(route.name)
  if (currentRouteMenu !== activeMenu.value) {
    activeMenu.value = currentRouteMenu
    userStore.setActiveMenu(currentRouteMenu)
  }
  setActiveByRoute(currentRouteMenu, route.path)
})

// 是否激活
const isActive = (item: any) => {
  const activePath = item.key
  return activePath === activeMenu.value
}

// 点击路由跳转
const handleOpen = (item: any, index: number) => {
  activeMenu.value = item.key
  userStore.setActiveMenu(item.key)
  const list = cloneDeep(noHiddenRoutes.value)
  if (!item.activeMenu) {
    item.activeMenu = item.children ? item.children[0].path : ""
  }
  // item.activeMenu = item.children ? item.children[0].path : ""
  router.push({ path: item.activeMenu })
}
</script>

<style lang="scss" scoped>
.top-menu {
  width: 100%;
  height: 100%;
  flex: 1;
}

.menu-item {
  min-width: 108px;
  height: 32px;
  border-radius: 20px;
  &:hover {
    background-color: var(--el-color-primary);
    color: #fff;
    transform: scale(1.05);
  }
}

.active {
  background-color: var(--el-color-primary);
  color: #fff;
}

.noactive {
  background-color: #f2f5fa;
  color: #808488;
}
</style>
