<script lang="ts" setup>
import { computed, ref, watch, onMounted } from "vue"
import { storeToRefs } from "pinia"
import { useAppStore } from "@/store/modules/app"
import { useSettingsStore } from "@/store/modules/settings"
import { AppMain, NavigationBar, Sidebar, TagsView, Logo } from "./components"
import Breadcrumb from "./components/Breadcrumb/index.vue"
const appStore = useAppStore()
const settingsStore = useSettingsStore()
const { showTagsView, showLogo, showBreadcrumb } = storeToRefs(settingsStore)

/** 定义计算属性 layoutClasses，用于控制布局的类名 */
const layoutClasses = computed(() => {
  return {
    hideSidebar: !appStore.sidebar.opened
  }
})

// 在这里获取消息通知接口
import useGlobalMessageReminder from "@/hooks/useGlobalMessageReminder"
const { getGlobalWarningMessageList } = useGlobalMessageReminder()
import { useUserStoreHook } from "@/store/modules/user"
const { roles } = useUserStoreHook()

watch(
  () => roles,
  async (newVal) => {
    if (newVal) {
      await getGlobalWarningMessageList()
    }
  },
  {
    immediate: true
  }
)
</script>

<template>
  <div :class="layoutClasses" class="app-wrapper">
    <!-- 头部导航栏和标签栏 -->
    <div class="fixed-header layout-header">
      <!-- logo -->
      <Logo v-if="showLogo" :collapse="false" class="logo" />
      <!-- 顶部菜单 包含一级路由，在线状态，人员信息等-->
      <div class="content">
        <NavigationBar />
      </div>
    </div>
    <!-- 主容器 -->
    <div :class="{ hasTagsView: showTagsView }" class="main-container">
      <!-- 左侧边菜单栏 -->
      <Sidebar class="sidebar-container" />
      <!-- 页面主体内容 -->
      <div class="app-main">
        <!-- 页面标签栏 -->
        <TagsView v-show="showTagsView" />
        <!-- 面包屑导航栏 -->
        <Breadcrumb class="breadcrumb" v-show="showBreadcrumb" />
        <!-- 页面内容 -->
        <AppMain />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import "@/styles/mixins.scss";
$transition-time: 0.35s;

.app-wrapper {
  @extend %clearfix;
  width: 100%;
}

.fixed-header {
  position: fixed;
  top: 0;
  z-index: 1002;
  width: 100%;
  display: flex;
  .logo {
    width: var(--v3-sidebar-width);
  }
  .content {
    flex: 1;
    position: relative;
    background-color: var(--v3-header-bg-color);
    // box-shadow: var(--v3-header-box-shadow);
  }
}

.layout-header {
  background-color: var(--v3-header-bg-color);
}

.main-container {
  height: calc(100vh - var(--v3-header-height));
}

.sidebar-container {
  transition: width $transition-time;
  width: var(--v3-sidebar-width) !important;
  height: 100%;
  position: fixed;
  left: 0;
  z-index: 1001;
  overflow: hidden;
}

.app-main {
  transition: padding-left $transition-time;
  padding-left: var(--v3-sidebar-width);
  height: 100vh;
  overflow: auto;
  font-size: 14px;
}

.hideSidebar {
  .sidebar-container {
    width: var(--v3-sidebar-hide-width) !important;
  }
  .app-main {
    padding-left: var(--v3-sidebar-hide-width);
  }
}

.hasTagsView {
  .sidebar-container {
    padding-top: var(--v3-header-height);
  }
  .app-main {
    padding-top: var(--v3-header-height);
  }
}
</style>
