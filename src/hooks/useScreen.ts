import { nextTick } from "process"
import { onBeforeMount, onMounted, onBeforeUnmount, ref, Ref } from "vue"

const useScreenHook = (containerRef?: Ref<HTMLElement | null>, topHeight?: number) => {
  const mainContainerRef = ref<HTMLElement | null>(null)
  const maxHeight = ref(0)
  // const maxWidth = ref(0)

  const setHeight = () => {
    // 页面整体高度 =   页面整体占据屏幕高度- 顶部栏高度- (tags栏高度) -  面包屑高度
    const containerHeight = window.innerHeight - 64 - 42 - 20
    console.log("containerHeight 123", containerHeight)
    maxHeight.value = topHeight ? containerHeight - topHeight : containerHeight
    if (maxHeight.value <= 300) {
      maxHeight.value = 300
    }
    mainContainerRef.value && (mainContainerRef.value.style.height = `${maxHeight.value}px`)
    console.log("maxHeight 123", maxHeight.value)
  }

  /** 在组件挂载前添加窗口大小变化事件监听器 */
  onBeforeMount(() => {
    window.addEventListener("resize", setHeight)
  })

  /** 在组件挂载后根据窗口大小判断设备类型并调整布局 */
  onMounted(() => {
    nextTick(() => {
      if (containerRef?.value) {
        mainContainerRef.value = containerRef.value
      }
      setHeight()
    })
  })

  /** 在组件卸载前移除窗口大小变化事件监听器 */
  onBeforeUnmount(() => {
    window.removeEventListener("resize", setHeight)
  })

  return {
    containerRef,
    maxHeight,
    setHeight
  }
}

export default useScreenHook
