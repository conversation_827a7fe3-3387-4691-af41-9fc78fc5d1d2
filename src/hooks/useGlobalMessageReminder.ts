import { h, ref, nextTick } from "vue"
import to from "await-to-js"
import {
  apiBackgroundFundSupervisionWarnDispositionWarnNoticeTagListPost,
  apiBackgroundFundSupervisionWarnDispositionWarnNoticeTagReadPost,
  apiBackgroundFundSupervisionWarnDispositionWarnNoticeRedListPost,
  apiBackgroundFundSupervisionWarnDispositionWarnNoticeRedReadPost
} from "@/api/warning"
import {
  apiBackgroundSupervisionSupervisionMessagesBulkQuestionnaireMsgCancelPopPost,
  apiBackgroundSupervisionSupervisionMessagesBulkQuestionnaireMsgReadPost
} from "@/api/system"
import { ElMessage, ElNotification, ElButton } from "element-plus"
import { cloneDeep } from "lodash"
import useGoToPage from "@/hooks/useGoToPage"

import { useRouter } from "vue-router"
import { useUserStoreHook } from "@/store/modules/user"

let notificationInstances: any[] = [] // 存储通知实例
let questionnaireInstances: any[] = []
const useGlobalMessageReminder = () => {
  const { roles } = useUserStoreHook()

  // 获取消息列表
  const globalWarningMessageList = ref<any[]>([])
  const getGlobalWarningMessageList = async () => {
    const [err, res]: any[] = await to(apiBackgroundFundSupervisionWarnDispositionWarnNoticeTagListPost())
    if (err) {
      return ElMessage.error(err.msg)
    }
    if (res && res.code === 0) {
      globalWarningMessageList.value = cloneDeep(res.data)
      // 清空之前的实例
      notificationInstances.forEach((instance: any) => instance.close())
      notificationInstances = []
      // 生成全局消息
      nextTick(() => {
        globalWarningMessageList.value.forEach((item: any) => {
          const instance = ElNotification({
            title: "提示",
            offset: 110,
            message: h(
              "div",
              {
                style: {
                  display: "flex",
                  flexDirection: "column",
                  gap: "12px"
                }
              },
              [
                h(
                  "div",
                  {
                    style: {
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "flex-start",
                      fontSize: "14px"
                    }
                  },
                  `有${item.count}条新的${item.name}`
                ),
                h(
                  "div",
                  {
                    style: {
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "flex-end",
                      gap: "8px"
                    }
                  },
                  [
                    h(
                      ElButton,
                      {
                        size: "small",
                        onClick: () => closeNotification("warnType", instance, item)
                      },
                      "关闭"
                    ),
                    h(
                      ElButton,
                      {
                        type: "primary",
                        size: "small",
                        onClick: () => readNotification("warnType", instance, item),
                        disabled: !roles.includes("background_fund_supervision.warn_manage.warning_message_list")
                      },
                      "查看详情"
                    )
                  ]
                )
              ]
            ),
            customClass: "ps-notification-custom-class",
            duration: 0,
            onClose: () => {
              instance.close()
            }
          })
          notificationInstances.push(instance)
        })
      })
    } else {
      ElMessage.error(res.msg)
    }
    console.log("调用了，看看存了什么", notificationInstances)
  }

  const questionnaireMessageList = ref<any[]>([])
  const getQuestionnaireMsgList = (unReadList: any[]) => {
    questionnaireMessageList.value = cloneDeep(unReadList)

    // 清空之前的实例
    questionnaireInstances.forEach((instance: any) => instance.close())
    questionnaireInstances = []
    // 生成全局消息
    questionnaireMessageList.value.forEach((item: any) => {
      const instance = ElNotification({
        title: "提示",
        offset: 110,
        message: h(
          "div",
          {
            style: {
              display: "flex",
              flexDirection: "column",
              gap: "12px"
            }
          },
          [
            h(
              "div",
              {
                style: {
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "flex-start",
                  fontSize: "14px"
                }
              },
              `你有一份${item.title}问卷待填写`
            ),
            h(
              "div",
              {
                style: {
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "flex-end",
                  gap: "8px"
                }
              },
              [
                h(
                  ElButton,
                  {
                    size: "small",
                    onClick: () => closeNotification("survey", instance, item)
                  },
                  "关闭"
                ),
                h(
                  ElButton,
                  {
                    type: "primary",
                    size: "small",
                    onClick: () => readNotification("survey", instance, item)
                  },
                  "去填写"
                )
              ]
            )
          ]
        ),
        customClass: "ps-notification-custom-class",
        duration: 5000,
        onClose: () => {
          closeNotification("survey", instance, item)
        }
      })
      questionnaireInstances.push(instance)
    })
  }

  // 关闭方法
  const closeNotification = async (type: string, instance: any, data?: any) => {
    if (type === "survey") {
      const [err, res]: any[] = await to(
        apiBackgroundSupervisionSupervisionMessagesBulkQuestionnaireMsgCancelPopPost({
          msg_nos: [data.id]
        })
      )
      if (err) {
        return ElMessage.error(err.msg)
      }
      if (res && res.code === 0) {
        instance.close()
      } else {
        ElMessage.error(res.msg)
      }
      return
    } else {
      const [err, res]: any[] = await to(
        apiBackgroundFundSupervisionWarnDispositionWarnNoticeTagReadPost({ data: data, red_state: false })
      )
      if (err) {
        return ElMessage.error(err.msg)
      }
      if (res && res.code === 0) {
        instance.close()
      } else {
        ElMessage.error(res.msg)
      }
    }
  }

  // 关闭方法
  const onlyCloseDialog = () => {
    // 清空之前的实例
    console.log("调用了，看看这里面是什么", notificationInstances)
    // 遍历并关闭所有实例
    notificationInstances.forEach((instance) => {
      if (instance && instance.close) {
        instance.close()
      }
    })
    // 清空实例数组避免内存泄漏
    notificationInstances = []
  }

  // 已读通知
  const { goToPage } = useGoToPage()
  const gotoPath = (type: string) => {
    goToPage({
      path: "/ai_warning_management/warning_information",
      query: { type: type }
    })
  }

  const router = useRouter()
  const readNotification = async (type: string, instance: any, data?: any) => {
    if (type === "survey") {
      const [err, res]: any[] = await to(
        apiBackgroundSupervisionSupervisionMessagesBulkQuestionnaireMsgReadPost({ msg_nos: [data.id] })
      )
      if (err) {
        return ElMessage.error(err.msg)
      }
      if (res && res.code === 0) {
        instance.close()
        const { href } = router.resolve({
          path: "/questionnaire-detail",
          query: {
            id: parseInt(data.questionnaire_id)
          }
        })
        window.open(href, "_blank")
      } else {
        ElMessage.error(res.msg)
      }
    } else {
      const [err, res]: any[] = await to(
        apiBackgroundFundSupervisionWarnDispositionWarnNoticeTagReadPost({ data: data, red_state: true })
      )
      if (err) {
        return ElMessage.error(err.msg)
      }
      if (res && res.code === 0) {
        instance.close()
        gotoPath(data.date_type)
      } else {
        ElMessage.error(res.msg)
      }
    }
  }

  // 获取小红点
  const redPointList = ref<any[]>([])
  const getRedPoint = async () => {
    const [err, res]: any[] = await to(apiBackgroundFundSupervisionWarnDispositionWarnNoticeRedListPost())
    if (err) {
      return ElMessage.error(err.msg)
    }
    if (res && res.code === 0) {
      redPointList.value = cloneDeep(res.data || [])
    } else {
      ElMessage.error(res.msg)
    }
  }

  // 已读小红点
  const readRedPoint = async (data: any) => {
    const [err, res]: any[] = await to(apiBackgroundFundSupervisionWarnDispositionWarnNoticeRedReadPost({ data: data }))
    if (err) {
      return ElMessage.error(err.msg)
    }
    if (res && res.code === 0) {
      let index = redPointList.value.findIndex((item: any) => item.id === data.id)
      redPointList.value.splice(index, 1)
      getRedPoint()
    } else {
      ElMessage.error(res.msg)
    }
  }

  return {
    getGlobalWarningMessageList,
    getQuestionnaireMsgList,
    redPointList,
    getRedPoint,
    readRedPoint,
    onlyCloseDialog
  }
}

export default useGlobalMessageReminder
