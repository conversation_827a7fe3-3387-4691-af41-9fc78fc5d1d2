import { ref } from "vue"
import { ElMessage, ElLoading, ElMessageBox } from "element-plus"
import { sleep } from "@/utils"
import { request } from "@/utils/service"

type ExportOptions = {
  type: string
  url?: string
  immediate: boolean
  params: any
}

const useExportExcel = () => {
  const random = ref(new Date().getTime())
  const isExportLoading = ref(false) // 调用接口
  const exportQueryId = ref("") // 导出的query_id
  const exportShow = ref(false)

  const requestFun = (params: any, url: string) => {
    return request({
      url: url,
      method: "post",
      data: params
    })
  }

  /**
   * @param {*} option { type: '类型' // required, url: 'API方法', immediate: false // 是否立即执行，不显示弹窗, params: {} // 请求参数 required }
   * @returns
   */
  const exportHandle = async (options: ExportOptions) => {
    if (isExportLoading.value) {
      ElMessage.error("请勿重复点击！")
      return
    }
    isExportLoading.value = true
    // 如果立即执行
    if (options.immediate) {
      const loading = ElLoading.service({
        target: ".el-main",
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(255, 255, 255, 0.8)"
      })
      await sleep(1000)
      if (options.url) {
        requestFun(options.params, options.url).then((res: any) => {
          loading.close()
          if (res.code === 0) {
            exportQueryId.value = res.data.query_id
            exportShow.value = true
          } else {
            ElMessage.error(res.msg)
          }
        })
      }
      return
    } else {
      // 不立即执行的
      ElMessageBox.confirm("确认导出？", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === "confirm") {
            instance.confirmButtonLoading = true
            await sleep(1000)
            if (options.url) {
              requestFun(options.params, options.url).then((res: any) => {
                if (res.code === 0) {
                  instance.confirmButtonLoading = false
                  exportQueryId.value = res.data.query_id
                  exportShow.value = true
                  done()
                } else {
                  ElMessage.error(res.msg)
                }
              })
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(() => {
          isExportLoading.value = false
        })
        .catch(() => {
          isExportLoading.value = false
        })
    }
  }

  return {
    random,
    exportQueryId,
    exportShow,
    exportHandle
  }
}

export default useExportExcel
