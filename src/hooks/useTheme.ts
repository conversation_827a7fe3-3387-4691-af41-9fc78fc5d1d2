import { ref, watchEffect } from "vue"
import { getActiveThemeName, setActiveThemeName } from "@/utils/cache/local-storage"
import { IMG_LIST } from "@/constants/img-cons"

const DEFAULT_THEME_NAME = "normal"
type DefaultThemeName = typeof DEFAULT_THEME_NAME

/** 注册的主题名称, 其中 DefaultThemeName 是必填的 */
export type ThemeName = DefaultThemeName | "blue" | "green" | "purple" | "orange" | "dark-blue" | "dark"

interface ThemeList {
  title: string
  name: ThemeName
}

/** 主题列表 */
const themeList: ThemeList[] = [
  {
    title: "默认",
    name: DEFAULT_THEME_NAME
  },
  {
    title: "蓝色",
    name: "blue"
  },
  {
    title: "绿色",
    name: "green"
  },
  {
    title: "紫色",
    name: "purple"
  },
  {
    title: "橙色",
    name: "orange"
  },
  {
    title: "黑暗",
    name: "dark"
  },
  {
    title: "深蓝",
    name: "dark-blue"
  }
]

/** 正在应用的主题名称 */
const activeThemeName = ref<ThemeName>(getActiveThemeName() || DEFAULT_THEME_NAME)

/** 主题图片列表 */
const commonImgList = ref<Array<any>>()
const modules = ref<Record<string, any>>()

/** 设置主题 */
const setTheme = (value: ThemeName) => {
  activeThemeName.value = value
}

/** 在 html 根元素上挂载 class */
const setHtmlRootClassName = (value: ThemeName) => {
  document.documentElement.className = value
  console.log("setHtmlRootClassName", value)
}

/** 初始化 */
const initTheme = () => {
  // watchEffect 来收集副作用
  watchEffect(() => {
    const value = activeThemeName.value
    setHtmlRootClassName(value)
    setActiveThemeName(value)
  })
}

// 获取图片根据名字
// export const getImgByName = (name: string, theme?: ThemeName): string => {
//   let themeName: ThemeName | null = getActiveThemeName()
//   if (!commonImgList.value || commonImgList.value.length === 0) {
//     modules.value = import.meta.glob(
//       [
//         "../assets/blue/*.{png,svg,jpg,jpeg}",
//         "../assets/green/*.{png,svg,jpg,jpeg}",
//         "../assets/orange/*.{png,svg,jpg,jpeg}",
//         "../assets/purple/*.{png,svg,jpg,jpeg}",
//         "../assets/dark/*.{png,svg,jpg,jpeg}",
//         "../assets/dark-blue/*.{png,svg,jpg,jpeg}"
//       ],
//       { eager: true }
//     )
//   }
//   // 如果有传参，则使用传参
//   if (theme) {
//     themeName = theme
//   }
//   if (themeName && IMG_LIST[themeName] && modules.value) {
//     return (modules.value[IMG_LIST[themeName][name]] as { default: string }).default
//   }
//   return ""
// }

// 预先导入所有图片模块
const imgModules = import.meta.glob(
  [
    "../assets/blue/*.{png,svg,jpg,jpeg}",
    "../assets/green/*.{png,svg,jpg,jpeg}",
    "../assets/orange/*.{png,svg,jpg,jpeg}",
    "../assets/purple/*.{png,svg,jpg,jpeg}",
    "../assets/dark/*.{png,svg,jpg,jpeg}",
    "../assets/dark-blue/*.{png,svg,jpg,jpeg}"
  ],
  { eager: true }
)

// 获取图片根据名字
export const getImgByName = (name: string, theme?: ThemeName): string => {
  let themeName: ThemeName | null = getActiveThemeName()
  if (!commonImgList.value || commonImgList.value.length === 0) {
    modules.value = imgModules
  }
  // 如果有传参，则使用传参
  if (theme) {
    themeName = theme
  }
  if (themeName && IMG_LIST[themeName] && modules.value) {
    return (modules.value[IMG_LIST[themeName][name]] as { default: string }).default
  }
  return ""
}

/** 主题 hook */
export function useTheme() {
  return { themeList, activeThemeName, initTheme, setTheme, getImgByName }
}
