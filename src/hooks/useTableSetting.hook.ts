import { getTreeDeepkeyList, findChildSetting } from "@/utils/index"
import to from "await-to-js"
import { ref } from "vue"
import { getAccountPrintInfo, setAccountPrintInfo } from "@/api/index"
import { useUserStoreHook } from "@/store/modules/user"
import { ElMessage } from "element-plus"
import type { TUserInfo } from "../../types/constant.d"
const useTabelSettingHook = (tableSetting: Array<any>, printType: string) => {
  const currentTableSetting = ref()
  const userStore = useUserStoreHook()
  const userInfo: TUserInfo = userStore.getUserInfo
  const accountId = userInfo.account_id
  // 读取设置设置默认
  const initPrintSetting = async () => {
    let defaultKeys = []
    defaultKeys = await getPrintSettingInfo()
    try {
      // defaultKeys = JSON.parse(getLocalStorage(this.$store.getters.userInfo.account_id + this.printType))
      if (!defaultKeys) {
        // null的情况为没设置过，设置过后默认是数组可为空数组
        defaultKeys = getTreeDeepkeyList(tableSetting, "prop", "childern", 0)
      } else {
        defaultKeys = getTreeDeepkeyList(defaultKeys, "prop", "childern", 0)
      }
    } catch (error) {
      defaultKeys = getTreeDeepkeyList(tableSetting, "prop", "childern", 0)
    }
    if (defaultKeys.length < 12) {
      let setting = findChildSetting(tableSetting, defaultKeys, "prop")
      setting = deleteWidthKey(setting)
      currentTableSetting.value = setting
    } else {
      currentTableSetting.value = findChildSetting(tableSetting, defaultKeys, "prop")
    }
  }
  const deleteWidthKey = (data: any, child = "children") => {
    function traversal(data: any) {
      data.map((item: any) => {
        if (item.width) {
          delete item.width
        }
        if (item.minWidth) {
          delete item.minWidth
        }

        if (item[child]) {
          if (item[child].length > 0) {
            traversal(item[child])
          }
        }
      })
    }
    traversal(data)
    return data
  }
  // 获取设置数据
  const getPrintSettingInfo = async () => {
    let defaultKeys = null
    const [err, res]: any[] = await to(
      getAccountPrintInfo({
        id: accountId,
        print_key: printType
      })
    )
    if (err) {
      ElMessage.error(err.message)
      return defaultKeys
    }
    if (res.code === 0) {
      console.log(res)
      defaultKeys = res.data
    } else {
      ElMessage.error(res.msg)
    }
    return defaultKeys
  }
  // 获取设置数据
  const setPrintSettingInfo = async (list: Array<any>) => {
    const [err, res]: any[] = await to(
      setAccountPrintInfo({
        id: accountId,
        print_key: printType,
        print_list: list
      })
    )
    if (err) {
      ElMessage.error(err.message)
      return
    }
    if (res.code === 0) {
      // console.log(res)
      ElMessage.success("设置成功")
    } else {
      ElMessage.error(res.msg)
    }
  }
  return {
    currentTableSetting,
    initPrintSetting,
    getPrintSettingInfo,
    setPrintSettingInfo
  }
}

export default useTabelSettingHook
