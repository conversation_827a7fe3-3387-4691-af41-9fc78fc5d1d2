import { onBeforeMount, onMounted, onBeforeUnmount, ref, Ref, nextTick } from "vue"

const useTableHeightHook = (
  tableTopHeight = 72,
  tableHeadHeight = 64,
  psTableRef?: Ref<HTMLElement | null>,
  topHeight?: number
) => {
  const searchRef = ref<HTMLElement | null>(null)
  const tableWrapperRef = ref<HTMLElement | null>(null)
  const tableRef = ref<HTMLElement | null>(null)
  const tabHeight = ref(0)
  const maxHeight = ref(0)

  const setTableHeight = () => {
    // 页面整体高度 =   页面整体占据屏幕高度- 顶部栏高度- (tags栏高度+margin-top)-  面包屑高度
    const containerHeight = window.innerHeight - 64 - 42 - 20
    console.log("containerHeight", containerHeight)
    // 表格总体布局高度 = 页面整体高度-搜索栏高度-margin-top的高度
    const searchHeight = searchRef.value?.offsetHeight
    console.log("searchHeight", searchHeight, searchRef.value?.clientHeight)
    tabHeight.value = containerHeight - (searchHeight || 0) - 40 - (topHeight || 0)
    if (tabHeight.value <= 300) {
      tabHeight.value = 300
    }
    console.log("tabHeight", tabHeight.value)
    tableWrapperRef.value && (tableWrapperRef.value.style.height = `${tabHeight.value}px`)
    // 表格高度= 表格总体布局高度-表格顶部标题与按钮层高度 - 表格头部高度
    maxHeight.value = tabHeight.value - tableTopHeight - tableHeadHeight
    tableRef.value && (tableRef.value.style.height = `${maxHeight.value}px`)
    console.log("maxHeight", maxHeight.value)
  }

  /** 在组件挂载前添加窗口大小变化事件监听器 */
  onBeforeMount(() => {
    window.addEventListener("resize", setTableHeight)
  })

  /** 在组件挂载后根据窗口大小判断设备类型并调整布局 */
  onMounted(() => {
    nextTick(() => {
      if (psTableRef?.value) {
        tableRef.value = Reflect.get(psTableRef.value, "element")
      }
      waitForSearchHeight()
    })
  })
  // 递归获取
  const waitForSearchHeight = async (maxTry = 10) => {
    await nextTick()
    const height = searchRef.value?.offsetHeight || 0
    if (height <= 1 && maxTry > 0) {
      // 递归等待，直到高度正常或达到最大尝试次数
      setTimeout(() => waitForSearchHeight(maxTry - 1), 30)
    } else {
      setTableHeight()
    }
  }

  /** 在组件卸载前移除窗口大小变化事件监听器 */
  onBeforeUnmount(() => {
    window.removeEventListener("resize", setTableHeight)
  })

  return {
    searchRef,
    tableWrapperRef,
    tableRef,
    tabHeight,
    maxHeight,
    setTableHeight
  }
}

export default useTableHeightHook
