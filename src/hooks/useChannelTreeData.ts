import { apiBackgroundFundSupervisionSupervisionChannelListPost } from "@/api/supervision/index"
import { onMounted, ref } from "vue"
import { useUserStore } from "@/store/modules/user"
import to from "await-to-js"
import { cloneDeep } from "lodash"
// 获取渠道树数据
const useChannelTreeDataHook = () => {
  // 树形组织列表
  const treeData = ref<Array<any>>([])

  // 获取树形数据
  const getTreeOrgsData = async () => {
    const userStore = useUserStore()
    treeData.value = userStore.getChannelTreeData
    if (treeData.value && treeData.value.length > 0) {
      return
    }
    console.log("organvalue")
    let params = {
      page: 1,
      page_size: 9999
    }
    const userInfo = userStore.getUserInfo
    if (userInfo && userInfo.supervision_channel_id) {
      Reflect.set(params, "parent__in", userInfo.supervision_channel_id)
    } else {
      Reflect.set(params, "parent__is_null", "1")
    }
    const [err, res]: any[] = await to(apiBackgroundFundSupervisionSupervisionChannelListPost(params))
    if (err) {
      return
    }
    if (res && res.code === 0) {
      let data: any = res.data || {}
      let results: Array<any> = data.results || []
      userStore.setChannelTreeData(results)
      treeData.value = cloneDeep(results)
      console.log("data", data)
    }
  }

  // 获取父级的绑定渠道Id
  const getParantBindSuperVision = (id: any, list: Array<any>): any => {
    if (list && list.length > 0) {
      for (let index = 0; index < list.length; index++) {
        const element: any = list[index]
        const bindedOrgInfo = element.binded_org_info || []
        const childRenList = element.children_list
        if (bindedOrgInfo && bindedOrgInfo.length > 0) {
          const findItem = bindedOrgInfo.find((item: any) => item.org_id === id)
          if (findItem) {
            return element.id
          }
        }
        if (childRenList && childRenList.length > 0) {
          let resultSurpervison = getParantBindSuperVision(id, childRenList)
          if (resultSurpervison) {
            return resultSurpervison
          }
        }
      }
    }
    return ""
  }

  onMounted(() => {
    getTreeOrgsData()
  })
  return {
    treeData,
    getTreeOrgsData,
    getParantBindSuperVision
  }
}

export default useChannelTreeDataHook
