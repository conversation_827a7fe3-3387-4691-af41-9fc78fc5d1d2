import { type App } from "vue"
import ElementPlus from "element-plus"
import dayjs, { Dayjs } from "dayjs"

// 使用 dayjs 插件来设置日期选择器的起始星期几
import "dayjs/locale/zh-cn"

export function loadElementPlus(app: App) {
  /** Element Plus 组件完整引入 */
  app.use(ElementPlus)

  // 设置日期选择器的起始星期几
  app.config.globalProperties.$dayjs = (date?: string | Date | Dayjs) => {
    return dayjs(date).locale("zh-cn").startOf("week").add(1, "day")
  }
}
