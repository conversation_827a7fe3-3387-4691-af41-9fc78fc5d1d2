//语言
import { lang } from "@/setting/systemSetting"
import { createI18n } from "vue-i18n" //引入vue-i18n组件
import { getLocalLang } from "@/utils/cache/local-storage"
import { LangEnum } from "@/enums/styleEnum"
import zh from "./zh/index"
import en from "./en/index"

const langStorage = getLocalLang()

// 语言数组
export const langList = [
  {
    label: "中文",
    key: LangEnum.ZH
  },
  {
    label: "English",
    key: LangEnum.EN
  }
]

const i18n = createI18n({
  legacy: false,
  globalInjection: true,
  locale: langStorage ? langStorage : lang,
  fallbackLocale: langStorage ? langStorage : lang,
  messages: {
    [LangEnum.ZH]: zh,
    [LangEnum.EN]: en
  }
})

export default i18n
