// core
import { createApp } from "vue"
import App from "@/App.vue"
import { setupStore } from "@/store"
import router, { setupRouter } from "@/router"
import "@/router/permission"
import i18n from "@/i18n/index"
import { installComponents } from "@/components/index"
import VueDOMPurifyHTML from "vue-dompurify-html"
import vue3SeamlessScroll from "vue3-seamless-scroll"
import "@/utils/hm_baidu"

// load
import { loadSvg } from "@/icons"
import { loadPlugins, setInitFunction } from "@/plugins"
import { loadDirectives } from "@/directives"

// css
import "uno.css"
import "normalize.css"
import "element-plus/dist/index.css"
import "element-plus/theme-chalk/dark/css-vars.css"
import "vxe-table/lib/style.css"
import "vxe-table-plugin-element/dist/style.css"
import "@/styles/index.scss"
// 适配
import "amfe-flexible/index.js"

const setRootFontSize = () => {
  const width = window.innerWidth
  let baseSize = 16 // 默认 PC 端

  // 根据设备类型调整基准值（示例逻辑）
  if (/iPhone|iPad|iPod|Android|Windows Phone|Mobile/i.test(navigator.userAgent)) {
    baseSize = (width / 37500) * 100 // 移动端按 375 设计稿缩放
  } else {
    baseSize = (width / 192000) * 100 // PC 端按 1920 设计稿缩放
  }

  document.documentElement.style.fontSize = `${baseSize}px`
}

// 初始化APP
async function appInit() {
  const app = createApp(App)
  /** 加载插件 */
  loadPlugins(app)
  /** 加载全局 SVG */
  loadSvg(app)
  /** 加载自定义指令 */
  loadDirectives(app)
  /** 设置全局方法 */
  setInitFunction(app)
  // 挂载状态管理
  setupStore(app)
  // 挂载路由
  setupRouter(app)
  // 全局组件挂载
  installComponents(app)
  // 初始化 & 监听窗口变化
  setRootFontSize()
  window.addEventListener("resize", setRootFontSize)
  // 路由准备就绪后挂载APP实例
  await router.isReady()
  // 语言注册
  app.use(i18n)
  // 全局注册 DOMPurify
  app.use(VueDOMPurifyHTML)
  // 列表滑动
  app.use(vue3SeamlessScroll, { name: "seamless-scroll" })
  // 挂载到页面
  app.mount("#app", true)
  // 挂载到 window
  // @ts-ignore
  window["$vue"] = app
}

// 执行初始化 app
appInit().then(() => {})
