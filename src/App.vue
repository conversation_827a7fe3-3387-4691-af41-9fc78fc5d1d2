<script lang="ts" setup>
import { onUnmounted, onMounted, onBeforeUnmount } from "vue"
import { useTheme } from "@/hooks/useTheme"
// 将 Element Plus 的语言设置为中文

import zhCn from "element-plus/es/locale/lang/zh-cn"
import en from "element-plus/es/locale/lang/en"
import { I18n } from "@/components/I18n"
import { useLang } from "@/hooks/useLang.hook"
import mqttConfigDefault from "./config/mqtt.d"
import BaseSetting from "@/components/BaseSetting/index.vue"
import settingImg from "@/assets/layouts/icon6.png"
import { ref } from "vue"
const { initTheme } = useTheme()
/** 初始化主题 */
initTheme()
// 全局语言
const { locale } = useLang()
/** 初始化mqtt连接 */
const mqttUrl = mqttConfigDefault.host + ":" + mqttConfigDefault.port
const isShowBaseSetting = ref(false)
const isShowSetting = ref(import.meta.env.VITE_NODE_ENV == "staging")

onMounted(() => {
  console.log("App onMounted", mqttUrl, locale.value)

  // consult/user/105/ToU
  // const topic = "consult/nutritionist/20/ToN"
  // console.log(topic, 666666)
  // connect(mqttUrl, topic)
})
onUnmounted(() => {
  console.log("App onUnmounted")
  // disconnect()
})

// 测试是否能监听页面切换
const handleVisibilityChange = () => {
  if (document.visibilityState === "hidden") {
    console.log("页面已隐藏")
  } else {
    console.log("页面当前可见")
  }
}
// 关闭弹窗
const closeDialog = () => {
  isShowBaseSetting.value = false
}

onMounted(() => {
  document.addEventListener("visibilitychange", handleVisibilityChange)
})

onBeforeUnmount(() => {
  document.removeEventListener("visibilitychange", handleVisibilityChange)
})
</script>

<template>
  <el-config-provider :locale="locale === 'ZH' ? zhCn : en">
    <I18n />
    <router-view />
    <div class="setting-tag" v-if="isShowSetting">
      <img :src="settingImg" class="w-30px h-30px" @click="isShowBaseSetting = true" />
    </div>
    <base-setting :visible="isShowBaseSetting" @close-dialog="closeDialog" @confirm-dialog="closeDialog" />
  </el-config-provider>
</template>
<style lang="scss" scoped>
.setting-tag {
  position: fixed;
  right: 20px;
  bottom: 0;
  cursor: pointer;
}
</style>
