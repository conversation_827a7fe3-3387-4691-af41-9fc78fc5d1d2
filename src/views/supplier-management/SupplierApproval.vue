<template>
  <div class="supplier-approval-container">
    <el-radio-group v-model="tabType">
      <el-radio-button value="pending_approval">待审批</el-radio-button>
      <el-radio-button value="agreed">已同意</el-radio-button>
      <el-radio-button value="rejected">已拒绝</el-radio-button>
      <el-radio-button value="repealed">已撤销</el-radio-button>
    </el-radio-group>
    <div class="m-t-20px">
      <pending-approval v-if="tabType === 'pending_approval'" />
      <agreed v-if="tabType === 'agreed'" />
      <rejected v-if="tabType === 'rejected'" />
      <repealed v-if="tabType === 'repealed'" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { PendingApproval, Agreed, Rejected, Repealed } from "./components/index"
import { ref } from "vue"
import { useRouter, useRoute, type LocationQueryRaw } from "vue-router"

// 路由实例
const router = useRouter()
// 路由对象
const route = useRoute()

const tabType = ref<any>("pending_approval")

// 初始化
function initLoad() {
  if (route.query.tab_type) {
    tabType.value = route.query.tab_type
  }
}
// 当tab切换时同步下参数到路由中，下次f5刷新防止还原
function changeHash(val: string) {
  router.replace({
    name: "DistributionManagement",
    query: {
      tab_type: val
    }
  })
}

function changeTabHandle(val: any) {
  changeHash(val)
}

// 初始化时要调下
initLoad()
</script>
<style lang="scss" scoped>
.supplier-approval-container {
  padding: 0 20px;
}
</style>
