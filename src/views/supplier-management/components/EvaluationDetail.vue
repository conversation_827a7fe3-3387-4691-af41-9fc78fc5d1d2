<template>
  <!-- 查看 -->
  <el-drawer
    v-model="detailDrawerShow"
    :title="title"
    :direction="'rtl'"
    :show-close="false"
    class="ps-drawer"
    :close-on-click-modal="true"
    size="40%"
    @close="closeDialog"
  >
    <div class="divider">
      <h3>评价信息</h3>
      <el-form :model="evaluationInfo">
        <el-form-item label="供应学校：">
          {{ evaluationInfo.company_name }}
        </el-form-item>
        <el-form-item label="人员名称：">
          {{ evaluationInfo.name }}
        </el-form-item>
        <el-form-item label="评价内容：">
          {{ evaluationInfo.remark }}
        </el-form-item>
        <el-form-item label="日期：">
          {{ evaluationInfo.create_time }}
        </el-form-item>
      </el-form>
    </div>
    <div class="divider" v-if="evaluationInfo.status !== '1'">
      <h3>处理结果</h3>
      <el-form :model="evaluationInfo">
        <el-form-item label="供应学校：">
          {{ evaluationInfo.source_organization_name }}
        </el-form-item>
        <el-form-item label="处理说明：">
          {{ evaluationInfo.reply_remark }}
        </el-form-item>
        <el-form-item label="附件：">
          <div class="flex">
            <el-image
              class="w-200px h-100px mr-10px"
              :src="item"
              :fit="'contain'"
              v-for="(item, index) in evaluationInfo.images"
              :key="index"
            />
          </div>
        </el-form-item>
        <el-form-item label="驳回原因：">
          {{ evaluationInfo.reason }}
        </el-form-item>
      </el-form>
    </div>
    <div class="dialog-footer m-t-20px">
      <el-button @click="closeDialog"> 关闭 </el-button>
      <!-- <el-button type="primary" @click="closeDialog" v-if="!evaluationInfo.isHandle"> 去处理 </el-button>
      <el-button type="primary" @click="rejectThis" v-if="evaluationInfo.isHandle && !processingResult.reason">
        驳回
      </el-button>
      <el-button type="primary" @click="closeDialog" v-if="processingResult.reason"> 确定 </el-button> -->
    </div>
  </el-drawer>
</template>
<script setup lang="ts">
import { ref } from "vue"
import { useVModel } from "@vueuse/core"

const emit = defineEmits(["closeDialog", "update:isShow"])
const props = defineProps({
  isShow: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: "查看"
  }
})

const detailDrawerShow = useVModel(props, "isShow", emit)

const evaluationInfo = ref<any>({
  company_name: "",
  name: "",
  remark: "",
  create_time: "",
  status: "",
  reply_remark: "",
  images: [],
  reason: "",
  source_organization_name: ""
})

// const rejectThis = () => {
//   ElMessageBox.prompt("拒绝原因", "提示", {
//     confirmButtonText: "确定",
//     cancelButtonText: "取消",
//     dangerouslyUseHTMLString: true,
//     message: `<div><div class="mb-5px">确定驳回该处理结果？</div><div><span style="color: red;">*</span>驳回原因</div></div>`,
//     inputType: "textarea",
//     inputPattern: /^.+$/,
//     inputPlaceholder: "请输入驳回原因",
//     inputErrorMessage: "驳回原因不能为空"
//   })
//     .then(({ value }) => {
//       rowData.value.reason = value
//       detailDrawerShow.value = false
//       ElMessage({
//         type: "success",
//         message: "操作成功"
//       })
//     })
//     .catch(() => {
//       ElMessage({
//         type: "info",
//         message: "已取消操作"
//       })
//     })
// }
// 设置弹窗数据
const setDialogData = (data: any) => {
  if (!data) {
    return
  }
  evaluationInfo.value = { ...data }
}
// 关闭弹窗
const closeDialog = () => {
  detailDrawerShow.value = false
  emit("closeDialog")
}

defineExpose({
  setDialogData
})
</script>
<style scoped lang="scss">
.divider {
  border-bottom: 1px solid #dcdfe6;
}
:deep(.el-divider--horizontal) {
  margin: 0px !important;
}
</style>
