<template>
  <div>
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
      <div class="pl-20px pr-20px">
        <el-divider />
      </div>
    </div>
    <div class="container-wrapper">
      <div class="table-wrapper" ref="tableWrapperRef">
        <div class="table-content">
          <ps-table
            :tableData="tableData"
            ref="psTable2Ref"
            v-loading="tableLoading"
            :show-pagination="true"
            @pagination-change="handleCurrentChange"
            :pageConfig="pageConfig"
            :max-height="maxHeight"
          >
            <ps-column :table-headers="tableSetting">
              <template #refUnitPrice="{ row }">
                <div>{{ getUnitPrice(row) }}</div>
              </template>
              <template #specification="{ row }">
                <div class="ps-origin-txt" @click="handlerShowDialog('specification', row)">查看</div>
              </template>
              <template #operation="{ row }">
                <el-button
                  plain
                  link
                  size="small"
                  type="primary"
                  @click="handlerShowDialog('detail', row, row.production_source_alias)"
                >
                  详情
                </el-button>
              </template>
            </ps-column>
          </ps-table>
        </div>
      </div>
    </div>
    <specification-dialog
      ref="specificationDialogRef"
      :is-show="detailDrawerShow"
      :type="detailDrawerType"
      :title="detailDrawerTitle"
      :source="detailDrawerSource"
      @cancelDialog="closeDialog"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue"
import { cloneDeep } from "lodash"
import { SEARCH_FORM_SETTING_SUPPLIES, TABLE_SETTING_SUPPLIES, img1, img2, img3 } from "./constants"
import useTableHeightHook from "@/hooks/useTableHeight"
import { apiBackgroundFundSupervisionSupplierManageSupplierManageApplyMaterialsPost } from "@/api/supplier/index"
import { ElMessage } from "element-plus"
import to from "await-to-js"
import SpecificationDialog from "./SpecificationDialog.vue"

const props = defineProps({
  treeNodeData: {
    type: Object,
    default: () => {
      return {}
    }
  },
  currentTree: {
    type: String || Number,
    default: ""
  }
})

const psTable2Ref = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 0, psTable2Ref, 90)
// 供应商Id
const supplierId = ref()
// 来源
const detailDrawerSource = ref("")

const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_SUPPLIES))
const loading = ref(false)
// table数据
const tableData = ref<any>([])
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_SUPPLIES)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 格式化从参数
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value && data[key].value !== "" && data[key].value !== "全部" && data[key].value.length > 0) {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}

// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 抽屉
const specificationDialogRef = ref()
const detailDrawerShow = ref(false)
const detailDrawerType = ref("detail")
const detailDrawerTitle = ref("")
const defaultData = {
  productionSource: "上游供货",
  supplierName: "食佳集团冷冻肉专供",
  name: "蒋惠文",
  phone: "***********",
  address: "广州市海珠区赤岗街道101号",
  businessQualification: {
    label: "经营资质",
    type: "农产品供应",
    qualificationList: [
      {
        url: img1,
        name: "营业执照",
        timeRange: ["2023-10-01", "2033-09-30"]
      },
      {
        url: img2,
        name: "食品生产许可证",
        timeRange: ["2023-10-01", "2033-09-30"]
      },
      {
        url: img3,
        name: "食品经营许可证",
        timeRange: ["2023-10-01", "2033-09-30"]
      }
    ]
  }
}
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionSupplierManageSupplierManageApplyMaterialsPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize,
      supplier_id: supplierId.value
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    let results = data.results || []
    tableData.value = cloneDeep(results)
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
// 获取最小单位，因为是列表，所以那个产品鸿宇说，用逗号隔开
const getUnitPrice = (data: any) => {
  let unitList = data.limit_unit_management || []
  if (unitList && unitList.length > 0) {
    return unitList
      .map((item: any) => {
        return item.net_content_unit_alias
      })
      .join(",")
  }
  return ""
}
// 查看详情
const handlerShowDialog = (type: string, row: any, source?: string) => {
  console.log("handlerShowDialog", type, row)
  detailDrawerType.value = type
  detailDrawerTitle.value = type === "detail" ? "详情" : "规格信息"
  if (source) {
    detailDrawerSource.value = source
  }
  if (specificationDialogRef.value) {
    specificationDialogRef.value.setDialogData(type === "detail" ? props.treeNodeData : row, type)
  }
  detailDrawerShow.value = true
}
// 关闭弹窗
const closeDialog = () => {
  detailDrawerShow.value = false
}
onMounted(() => {
  if (props.treeNodeData) {
    supplierId.value = props.treeNodeData.id
  }
  getDataList()
})

watch(
  () => props.treeNodeData,
  (newVal) => {
    supplierId.value = newVal.id
  }
)
</script>

<style lang="scss" scoped>
.table-box {
  border-bottom: 1px solid #dcdfe6;
}
:deep(.el-divider--horizontal) {
  margin: 0px !important;
}
.container-wrapper .table-wrapper {
  box-shadow: none;
  -webkit-box-shadow: none;
}
</style>
