<template>
  <div class="container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper pt-20px" ref="tableWrapperRef">
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #apply_time="{ row }">
              {{ getNameByKey(row, "apply_time") }}
            </template>
            <template #update_time="{ row }">
              {{ getNameByKey(row, "update_time") }}
            </template>
            <template #supplier_name="{ row }">
              {{ getNameBy<PERSON>ey(row, "supplier_name") }}
            </template>
            <template #contact_name="{ row }">
              {{ getNameByKey(row, "contact_name") }}
            </template>
            <template #contact_phone="{ row }">
              {{ getNameByKey(row, "contact_phone") }}
            </template>
            <template #address="{ row }">
              {{ getNameByKey(row, "address") }}
            </template>
            <template #operation="{ row }">
              <el-button plain link size="small" type="primary" @click="openHandle(row)"> 详情 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!-- 详情 -->
    <approval-detail ref="approvalDetailRepealedRef" />
    <!-- 详情 -->
    <el-drawer
      v-model="detailDrawerShow"
      title="详情"
      :direction="'rtl'"
      :show-close="false"
      class="ps-drawer"
      size="40%"
    >
      <el-form :model="formData" label-width="auto" label-position="left">
        <el-form-item label="联系人：">
          {{ getNameByKey(formData, "contact_name") }}
        </el-form-item>
        <el-form-item label="联系电话：">
          {{ getNameByKey(formData, "contact_phone") }}
        </el-form-item>
        <el-form-item label="地址：">
          {{ getNameByKey(formData, "address") }}
        </el-form-item>
        <!-- 经营资质 -->
        <!-- <div>
          <h3>{{ formData.businessQualification.label }}</h3>
          <div class="flex flex-col pl-20px">
            <el-form-item label="类别：">
              {{ formData.businessQualification.type }}
            </el-form-item>
            <div class="flex flex-items-center">
              <div
                v-for="(item, index) in formData.businessQualification.qualificationList"
                :key="index"
                class="mr-20px flex flex-col flex-justify-between flex-items-center h-150px"
              >
                <el-image class="w-full h-100" :src="item.url" :fit="'contain'" />
                <div>{{ item.name }}</div>
                <div>{{ item.timeRange[0] + " 至 " + item.timeRange[1] }}</div>
              </div>
            </div>
          </div>
        </div> -->
        <el-divider />
        <!-- 合同信息 -->
        <!-- <div>
          <h3>{{ formData.contractInfo.label }}</h3>
          <div class="flex flex-col pl-20px">
            <el-form-item label="合同年限："> {{ formData.contractInfo.timeLimit }}年 </el-form-item>
            <el-form-item label="合同有效期：">
              自{{ formData.contractInfo.validity[0] }}起至{{ formData.contractInfo.validity[0] }}止
            </el-form-item>
            <el-form-item label="合同信息：">
              <div class="flex flex-items-center">
                <div
                  v-for="(item, index) in formData.contractInfo.infoList"
                  :key="index"
                  class="mr-20px flex flex-col flex-justify-between flex-items-center h-150px"
                >
                  <el-image class="w-full h-100" :src="item.url" :fit="'contain'" />
                </div>
              </div>
            </el-form-item>
          </div>
        </div>
        <el-divider />
         -->
        <!-- 车辆信息 -->
        <div class="">
          <h3>车辆信息</h3>
          <div v-for="(vehicle, index) in formData.vehicle_info" :key="index" class="flex flex-col pl-20px">
            <el-form-item label="车辆号：" class="mr-10px">
              {{ vehicle.plate_number }}
            </el-form-item>
            <el-form-item label="车辆类型：">
              {{ vehicle.car_type_alias }}
            </el-form-item>
            <div class="flex flex-items-center">
              <div
                v-for="(item, index) in vehicle.car_img"
                :key="item"
                class="mr-34px flex flex-col flex-justify-between flex-items-center h-150px"
              >
                <el-image class="w-full h-100" :src="item" :fit="'contain'" />
              </div>
            </div>
          </div>
        </div>
        <el-divider />
        <!-- 司机信息 -->
        <div>
          <h3>司机信息</h3>
          <div v-for="(driver, index) in formData.driver_info" :key="index" class="flex flex-col pl-20px">
            <el-form-item label="司机姓名：">
              {{ driver.name }}
            </el-form-item>
            <el-form-item label="联系方式：">
              {{ driver.number }}
            </el-form-item>
            <el-form-item label="证件信息：">
              <div class="flex flex-items-center">
                <div
                  v-for="(item, index) in driver.driving_licence"
                  :key="item"
                  class="mr-20px flex flex-col flex-justify-between flex-items-center h-150px"
                >
                  <el-image class="w-full h-100" :src="item" :fit="'contain'" />
                  <div>驾驶证件</div>
                </div>
                <div
                  v-for="(item, index) in driver.health_certificate"
                  :key="item"
                  class="mr-20px flex flex-col flex-justify-between flex-items-center h-150px"
                >
                  <el-image class="w-full h-100" :src="item" :fit="'contain'" />
                  <div>健康证</div>
                </div>
              </div>
            </el-form-item>
          </div>
        </div>
        <!-- 审批流程 -->
        <div>
          <h3>审批流程</h3>
          <el-timeline style="max-width: 600px">
            <el-timeline-item
              v-for="(activity, index) in formData.approve_account_info"
              :key="index"
              :icon="activity.icon"
              type="primary"
              :size="'large'"
              placement="top"
              :timestamp="`${activity.approve_status_alias + ' ' + (activity.approve_time ? activity.approve_time : '')}`"
            >
              {{ activity.username + " （" + activity.account_name + "）" }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-form>
      <div class="dialog-footer m-t-20px">
        <el-button @click="detailDrawerShow = false"> 关闭 </el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import debounce from "lodash/debounce"
import { cloneDeep } from "lodash"
import { ref, reactive } from "vue"
import { SEARCH_FORM_PENDINGAPPROVAL, TABLE_SETTING_REPEALED, REPEALED_TABLE_DATA } from "./constants"
import useTableHeightHook from "@/hooks/useTableHeight"
import { apiBackgroundFundSupervisionSupplierManageSupplierManageApplyData } from "@/api/supplier_manage/index"
import ApprovalDetail from "./ApprovalDetail.vue"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(20, 64, psTableRef, 50)

// table数据
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_PENDINGAPPROVAL))
const loading = ref(false)
const tableData = ref<any>([])
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_REPEALED)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
const approvalDetailRepealedRef = ref()

// search参数格式化
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    // 默认参数为空不传，false要传，空数组不传
    if (data[key].value !== "" && data[key].value !== null && data[key].value.length !== 0) {
      if (key !== "select_time") {
        params[key] = data[key].value
      } else if (data[key].value.length > 0) {
        params.apply_start_time = data[key].value[0]
        params.apply_end_time = data[key].value[1]
      }
    }
  }
  return params
}

// 获取类型名称
const getNameByKey = (data: any, key: string) => {
  const shortlistdApplicationData = data.shortlistd_application_data || {}
  const name = shortlistdApplicationData[key] || "--"
  return name
}

const getDataList = async () => {
  loading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionSupplierManageSupplierManageApplyData({
      ...formatQueryParams(searchFormSetting.value),
      apply_result: "cnacel",
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  loading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    let results = data.results || []
    tableData.value = results
    pageConfig.total = data.count
  }
}

// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 改变页面
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 抽屉
const detailDrawerShow = ref(false)
const formData = ref<any>({})
const openHandle = (data: any) => {
  if (!data) {
    return
  }
  if (approvalDetailRepealedRef.value) {
    approvalDetailRepealedRef.value.setFormData(data)
  }
  approvalDetailRepealedRef.value.visible = true
}
getDataList()
</script>

<style scoped></style>
