<template>
  <div>
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
      <div class="pl-20px pr-20px">
        <el-divider />
      </div>
    </div>
    <div class="container-wrapper">
      <div class="table-wrapper" ref="tableWrapperRef">
        <div class="flex flex-justify-between flex-item-center mb-10px pr-20px pl-20px">
          <div class="flex flex-item-center head-setting">
            <div>结算金额：¥{{ totalSettledFee }}</div>
            <div class="m-l-20">待结算金额：¥{{ totalSettlingFee }}</div>
          </div>
          <div class="table-button">
            <el-button type="primary" @click="showSchoolDialog">分配学校</el-button>
            <el-button type="primary" @click="goToPrint">打印</el-button>
            <!-- <el-button type="primary" @click="goToExport">导出</el-button> -->
          </div>
        </div>
        <div class="table-content">
          <ps-table
            :tableData="tableData"
            ref="psTable1Ref"
            v-loading="tableLoading"
            :show-pagination="true"
            @pagination-change="handleCurrentChange"
            :pageConfig="pageConfig"
            :max-height="maxHeight"
          >
            <ps-column :table-headers="tableSetting">
              <template #operation="{ row }">
                <el-button plain link size="small" type="primary" @click="showDetail(row)"> 结算明细 </el-button>
                <el-button plain link size="small" type="primary" @click="handlerUnbinding(row)"> 解除绑定 </el-button>
              </template>
            </ps-column>
          </ps-table>
        </div>
      </div>
    </div>

    <!-- 分配学校 -->
    <el-drawer
      v-model="drawerShow"
      title="分配学校"
      :direction="'rtl'"
      :show-close="false"
      class="ps-drawer"
      size="40%"
      @close="closeDrawer"
    >
      <!-- <span>供应商分配学校进行配送</span> -->
      <el-form
        :model="manageAssignForm"
        :rules="manageAssignFormRules"
        label-width="auto"
        class="pt-20px"
        ref="manageAssignFormRef"
      >
        <el-form-item label="组织名称" prop="org_ids">
          <div class="w-200px" v-loading="orgsLoading">
            <el-select
              v-model="manageAssignForm.org_ids"
              multiple
              clearable
              collapseTags
              placeholder="请选择"
              size="default"
              @change="handleOrgChange"
            >
              <el-option v-for="(item, index) in orgList" :key="index" :label="item.name" :value="item.id" />
            </el-select>
          </div>
        </el-form-item>
      </el-form>
      <div class="dialog-footer m-t-20px">
        <el-button @click="drawerShow = false"> 取消 </el-button>
        <el-button type="primary" :disabled="drawerLoading" @click="saveSetSchool" v-loading="drawerLoading">
          保存
        </el-button>
      </div>
    </el-drawer>

    <supplies-settlement-detail-dialog
      ref="suppliesRef"
      :is-show="detailDrawerShow"
      :type="detailDrawerType"
      :title="detailDrawerTitle"
      :supplier-manage-id="treeNodeData.id"
      :start-time="startTime"
      :end-time="endTime"
      :orgId="chooseOrg"
      @cancelDialog="closeDialog"
    />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import { ref, reactive, onMounted, nextTick } from "vue"
import { cloneDeep } from "lodash"
import { SEARCH_FORM_SETTING_SUPPLY_RELATIONSHIP, TABLE_SETTING_SUPPLY_RELATIONSHIP } from "./constants"
import useTableHeightHook from "@/hooks/useTableHeight"
import { ElMessage, FormRules, ElMessageBox } from "element-plus"
import {
  apiBackgroundFundSupervisionSupplierManageSupplierManageAssignPost,
  apiBackgroundFundSupervisionSupplierManageSupplyRelationshipInfoPost,
  apiBackgroundFundSupervisionSupplierManageSupplyRelationshipInfoExportPost,
  apiBackgroundFundSupervisionSupplierGetChannelOrgBindListPost,
  apiBackgroundFundSupervisionSupplierManageSupplierManageUnbindPost
} from "@/api/supplier"
import { divide } from "@/utils"
import SuppliesSettlementDetailDialog from "./SuppliesSettlementDetailDialog.vue"
import { exportHandle } from "@/utils/exportExcel"
import { setLocalStorage } from "@/utils/storage"
import { useRouter } from "vue-router"
// import useSearch from "../hooks/useSearch"

const props = defineProps({
  treeNodeData: {
    type: Object,
    default: () => {
      return {}
    }
  },
  currentTree: {
    type: String || Number,
    default: ""
  }
})
// 路由
const router = useRouter()
// 表格数据
const psTable1Ref = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTable1Ref, 90)
// 筛选数据
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_SUPPLY_RELATIONSHIP))
const loading = ref(false)
// table数据
// const { searchHandle, defaultData, tableData } = useSearch(searchFormSetting.value)
const tableData = ref<any>([])
const tableLoading = ref(false)
const drawerLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_SUPPLY_RELATIONSHIP))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 表单校验
const manageAssignFormRules = reactive<FormRules>({
  org_ids: [{ required: true, message: "请选择", trigger: "change" }]
})
// 导出
const printType = "SupplyRelationshipExport"
// 开始日期
const startTime = ref()
// 结束日期
const endTime = ref()
// 选择的orgs
const chooseOrg = ref<Array<any>>()
// 分配的组织
const orgList = ref<any>([])
// 获取数据loading
const orgsLoading = ref(false)
// 待结算汇总
const totalSettlingFee = ref()
// 已结算汇总
const totalSettledFee = ref()
// 弹窗
const detailDrawerShow = ref(false) // 结算明细弹窗
const detailDrawerType = ref("detail") // 结算弹窗类型
const detailDrawerTitle = ref("结算明细") // 结算弹窗标题
const suppliesRef = ref() //  结算弹窗ref
// 抽屉 原来的分配学校弹窗
const drawerShow = ref(false)
const manageAssignFormRef = ref()
const manageAssignForm = ref({
  org_ids: [] as any,
  orgData: [] as Array<any>
})
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionSupplierManageSupplyRelationshipInfoPost({
      ...formatQueryParams(searchFormSetting.value),
      supplier_manage_id: props.treeNodeData.id,
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    let results = data.results || []
    tableData.value = cloneDeep(results)
    let collect = data.collect || {}
    totalSettledFee.value = collect.total_settled_fee ? divide(collect.total_settled_fee) : 0
    totalSettlingFee.value = collect.total_unliquidated_fee ? divide(collect.total_unliquidated_fee) : 0
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
// 获取监管渠道下面的组织数据
const getChannelGetChannelOrg = async () => {
  orgsLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionSupplierGetChannelOrgBindListPost({
      id: props.treeNodeData.id
    })
  )
  orgsLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    const data = res.data || {}
    const results = data.results || []
    orgList.value = results
  } else {
    ElMessage.error(res.msg)
  }
}
//保存分配学校
const saveSetSchool = () => {
  manageAssignFormRef.value.validate(async (valid: any) => {
    if (valid) {
      setManageAssign()
    }
  })
}
// 分配学校
const setManageAssign = async () => {
  drawerLoading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionSupplierManageSupplierManageAssignPost({
      org_ids: manageAssignForm.value.orgData,
      supplier_manage_id: props.treeNodeData.id
    })
  )
  drawerLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    drawerShow.value = false
    getDataList()
  } else {
    ElMessage.error(res.msg)
  }
}
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "" && data[key].value !== "全部" && data[key].value.length > 0) {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value && value !== "全部") {
          params[key] = data[key].value
        }
        if (key === "org_ids") {
          chooseOrg.value = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.start_time = data[key].value[0]
        params.end_time = data[key].value[1]
        startTime.value = data[key].value[0]
        endTime.value = data[key].value[1]
      }
    } else if (key === "org_ids") {
      chooseOrg.value = []
    }
  }
  return params
}
// 显示分配学校弹窗
const showSchoolDialog = () => {
  drawerShow.value = true
  getChannelGetChannelOrg()
}
// 关闭分配学校弹窗
const closeDrawer = () => {
  manageAssignForm.value.org_ids = []
  if (manageAssignFormRef.value) {
    setTimeout(() => {
      manageAssignFormRef.value.clearValidate()
    }, 100)
    console.log("manageAssignFormRef.value", manageAssignFormRef.value)
  }
}
onMounted(() => {
  getDataList()
  getChannelGetChannelOrg()
})

// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 显示详情
const showDetail = (data: any) => {
  console.log("data", data)
  if (suppliesRef.value) {
    suppliesRef.value.setDialogData(data)
  }
  detailDrawerShow.value = true
}
// 关闭弹窗
const closeDialog = () => {
  detailDrawerShow.value = false
}
// 导出
const goToExport = () => {
  console.log("goToExport")
  const option = {
    type: printType,
    api: apiBackgroundFundSupervisionSupplierManageSupplyRelationshipInfoExportPost,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      supplier_manage_id: props.treeNodeData.id,
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    }
  }
  exportHandle(option)
}

// 打印
const goToPrint = () => {
  let tabbleSetting = cloneDeep(tableSetting.value)
  tabbleSetting = tabbleSetting.filter((item: any) => item.prop !== "operation")
  setLocalStorage("print_setting", tabbleSetting)
  let collectList = [
    {
      label: "结算金额：¥",
      value: totalSettledFee.value
    },
    {
      label: "待结算金额：¥",
      value: totalSettlingFee.value
    }
  ]
  setLocalStorage("collect" + printType, collectList)
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: printType,
      print_title: "供应关系",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiBackgroundFundSupervisionSupplierManageSupplyRelationshipInfoPost",
      params: JSON.stringify({
        ...formatQueryParams(searchFormSetting.value),
        supplier_manage_id: props.treeNodeData.id,
        page: 1,
        page_size: 9999
      }),
      isShowCollect: 1
    }
  })
  window.open(href, "_blank")
}
// 解除绑定
const handlerUnbinding = (row: any) => {
  let orgId = row.organization_id ? parseInt(row.organization_id) : -1
  ElMessageBox.confirm("确定要解除该绑定关系吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    const [err, res]: any[] = await to(
      apiBackgroundFundSupervisionSupplierManageSupplierManageUnbindPost({
        supplier_manage_id: props.treeNodeData.id,
        org_id: orgId
      })
    )
    if (err) {
      return
    }
    if (res && res.code === 0) {
      ElMessage.success("解除成功")
      pageConfig.currentPage = 1
      getDataList()
    } else {
      ElMessage.error(res.msg || "解除失败")
    }
  })
}
// 组织选择更改
const handleOrgChange = (value: Array<any>) => {
  console.log("handleOrgChange", value)
  if (value) {
    let findItems = orgList.value.filter((item: any) => {
      return value.includes(item.id)
    })
    console.log("findItems", findItems)
    manageAssignForm.value.orgData = findItems.map((item: any) => {
      return {
        org_id: item.id,
        company_id: item.company
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.table-box {
  border-bottom: 1px solid #dcdfe6;
}

:deep(.el-divider--horizontal) {
  margin: 0px !important;
}
.head-setting {
  background: #f4f6fc;
  border-radius: 10px;
  width: fit-content;
  padding: 10px;
  margin-bottom: 10px;
}
.container-wrapper .table-wrapper {
  box-shadow: none;
  -webkit-box-shadow: none;
}
</style>
