<template>
  <el-drawer v-model="visible" title="详情" :direction="'rtl'" :show-close="false" class="ps-drawer" size="40%">
    <el-form :model="formData" label-width="auto" label-position="left">
      <el-form-item label="供应商名称：">
        {{ getName<PERSON>y<PERSON>ey(formData, "supplier_name") }}
      </el-form-item>
      <el-form-item label="联系人：">
        {{ getNameByKey(formData, "contact_name") }}
      </el-form-item>
      <el-form-item label="联系电话：">
        {{ getNameByKey(formData, "contact_phone") }}
      </el-form-item>
      <el-form-item label="地址：">
        {{ getNameByKey(formData, "address") }}
      </el-form-item>
      <!-- 经营资质 -->
      <div>
        <h3>经营资质</h3>
        <div class="flex flex-col pl-20px">
          <div class="flex flex-items-center">
            <div
              v-for="(item, index) in formData.shortlistd_application_data.certification_info"
              :key="index"
              class="mr-20px flex flex-col flex-justify-between flex-items-center h-150px"
            >
              <el-image
                class="w-full h-100"
                :src="item.imgUrl && item.imgUrl.length > 0 ? item.imgUrl[0] : ''"
                :fit="'contain'"
              />
              <div>{{ item.name }}</div>
              <div v-if="item.expiry_date && item.expiry_date.length > 1">
                {{ item.expiry_date[0] + " 至 " + item.expiry_date[1] }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-divider />
      <!-- 车辆信息 -->
      <div class="">
        <h3>车辆信息</h3>
        <div
          v-for="(vehicle, index) in formData.shortlistd_application_data.vehicle_info"
          :key="index"
          class="flex flex-col pl-20px"
        >
          <el-form-item label="车辆号：" class="mr-10px">
            {{ vehicle.plate_number }}
          </el-form-item>
          <el-form-item label="车辆类型：">
            {{ vehicle.car_type_alias }}
          </el-form-item>
          <div class="flex flex-items-center">
            <div
              v-for="(item, index) in vehicle.car_img"
              :key="index"
              class="mr-34px flex flex-col flex-justify-between flex-items-center h-150px"
            >
              <el-image class="w-full h-100" :src="item" :fit="'contain'" />
            </div>
          </div>
        </div>
      </div>
      <el-divider />
      <!-- 司机信息 -->
      <div>
        <h3>司机信息</h3>
        <div
          v-for="(driver, index) in formData.shortlistd_application_data.driver_info"
          :key="index"
          class="flex flex-col pl-20px"
        >
          <el-form-item label="司机姓名：">
            {{ driver.name }}
          </el-form-item>
          <el-form-item label="联系方式：">
            {{ driver.number }}
          </el-form-item>
          <el-form-item label="证件信息：">
            <div class="flex flex-items-center">
              <div
                v-for="(item, index) in driver.driving_licence"
                :key="index"
                class="mr-20px flex flex-col flex-justify-between flex-items-center h-150px"
              >
                <el-image class="w-full h-100" :src="item" :fit="'contain'" />
                <div>驾驶证件</div>
              </div>
              <div
                v-for="(item, index) in driver.health_certificate"
                :key="index"
                class="mr-20px flex flex-col flex-justify-between flex-items-center h-150px"
              >
                <el-image class="w-full h-100" :src="item" :fit="'contain'" />
                <div>健康证</div>
              </div>
            </div>
          </el-form-item>
        </div>
      </div>
      <el-divider />
      <!-- 审批流程 -->
      <div>
        <h3>审批流程</h3>
        <el-form-item>
          <el-timeline class="p-t-10 m-l-35">
            <el-timeline-item
              :icon="item.icon"
              :color="item.color"
              :size="'large'"
              v-for="(item, index) in formData.approveAccountInfo"
              :key="index"
              :timestamp="item.status_alias"
              :placement="'top'"
            >
              <div
                v-for="(itemIn, indexIn) in item.data || []"
                :key="indexIn"
                :class="[approveMethod === 'and_approve' && index !== 0 ? 'bg-grey' : '', 'm-b-10']"
              >
                <!--这里做个区别，会签和其他两个区别显示-->
                <div v-if="approveMethod !== 'and_approve'" class="flex flex-col">
                  <div class="w-350 flex flex-items-center flex-justify-between" :class="indexIn > 0 ? 'm-t-10' : ''">
                    <div>{{ itemIn?.operator || "--" }}</div>
                    <div class="w-150 flex flex-items-center flex-justify-between" v-if="itemIn?.status !== 'PENDING'">
                      <div v-if="itemIn?.status !== 'PENDING'">{{ itemIn?.timestamp || "--" }}</div>
                      <el-icon :color="itemIn?.color || '#909399'" size="18px">
                        <CircleCheckFilled v-if="itemIn?.icon === 'el-icon-success'" />
                        <CircleCloseFilled v-if="itemIn?.icon === 'el-icon-error'" />
                      </el-icon>
                    </div>
                  </div>
                  <div
                    v-if="index > 0 && item.status !== 'REVOKE' && itemIn?.reason"
                    style="color: #000"
                    class="text-break"
                  >
                    审批意见：{{ itemIn.reason }}
                  </div>
                </div>
                <div v-else>
                  <div v-for="(childItem, childIndex) in itemIn || []" :key="childIndex" class="flex flex-col">
                    <div
                      class="w-350 flex flex-items-center flex-justify-between"
                      :class="childIndex > 0 ? 'm-t-10' : ''"
                    >
                      <div>{{ childItem?.operator || "--" }}</div>
                      <div
                        class="w-150 flex flex-items-center flex-justify-between"
                        v-if="childItem?.status !== 'PENDING'"
                      >
                        <div v-if="childItem?.status !== 'PENDING'">{{ childItem?.timestamp || "--" }}</div>
                        <el-icon :color="childItem?.color || '#909399'" size="18px">
                          <CircleCheckFilled v-if="childItem?.icon === 'el-icon-success'" />
                          <CircleCloseFilled v-if="childItem?.icon === 'el-icon-error'" />
                        </el-icon>
                      </div>
                    </div>
                    <div
                      v-if="index > 0 && childItem?.status !== 'REVOKE' && childItem?.reason"
                      style="color: #000"
                      class="text-break"
                    >
                      审批意见：{{ childItem.reason }}
                    </div>
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-form-item>
      </div>
    </el-form>
    <div class="dialog-footer m-t-20px">
      <el-button @click="closeDrawer"> 关闭 </el-button>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { cloneDeep } from "lodash"
import { MoreFilled, CloseBold, Check, CircleCheckFilled, CircleCloseFilled } from "@element-plus/icons-vue"
import { apiBackgroundFundSupervisionFinanceApproveApproveAccountsPost } from "@/api/supplier_manage"

const visible = ref(false)
const formData = ref<any>({
  shortlistd_application_data: {
    certification_info: [],
    vehicle_info: [],
    driver_info: []
  },
  approveAccountInfo: [] as any[]
})

const closeDrawer = () => {
  visible.value = false
  formData.value = {
    shortlistd_application_data: {
      certification_info: [],
      vehicle_info: [],
      driver_info: []
    },
    approveAccountInfo: [] as any[]
  }
}
// 获取类型名称
const getNameByKey = (data: any, key: string) => {
  const shortlistdApplicationData = data.shortlistd_application_data || {}
  const name = shortlistdApplicationData[key] || "--"
  return name
}
const approveMethod = ref("")

// 设置
const setFormData = async (data: any) => {
  formData.value = data
  approveMethod.value = data.approve_method
  let approveAccountInfo = await getApproveAccountInfo({ id: data.shortlistd_application_data.id })
  getApproveList(approveAccountInfo, data)
}
// 获取审批账号信息
const getApproveAccountInfo = (data: any) => {
  return new Promise((resolve) => {
    apiBackgroundFundSupervisionFinanceApproveApproveAccountsPost({
      id: data.id
    })
      .then((res: any) => {
        console.log("res", res)
        if (res && res.code === 0) {
          resolve(res.data || [])
        } else {
          resolve([])
        }
      })
      .catch((error) => {
        console.log("error", error)
        resolve([])
      })
  })
}
// 获取审批列表
const getApproveList = (data: any, info: any) => {
  console.log("getApproveList", data, info)
  // 这个也是仿造财务审批的流程，有问题问何甘荣
  let shortlistdApplicationData = info.shortlistd_application_data || {}
  let approveInfo = [
    {
      icon: Check,
      color: "#14ce84",
      status_alias: "提交申请",
      status: "pending",
      data: [
        {
          icon: Check,
          color: "#14ce84",
          status_alias: "提交申请",
          status: "pending",
          account_id: "",
          timestamp: shortlistdApplicationData.create_time || "",
          operator: shortlistdApplicationData.contact_name || ""
        }
      ]
    }
  ]
  formData.value.approveAccountInfo = approveInfo
  let result = data
  let newStatus = []
  // 先判断一下是不是或签，或签的话全部账号显示在一个流程里就可以了
  switch (approveMethod.value) {
    case "one_by_one_approve": {
      // 依次审批还是拿回approve_account_info组成数组显示吧
      // 先循环res.data
      result.forEach((item: any) => {
        let obj = {
          icon: Check,
          color: "#ff9b45",
          status_alias: "待审批",
          status: "pending",
          data: [] as any[]
        }
        let statusList: any[] = []
        if (item.approve_account_info && item.approve_account_info.length) {
          // 将这个审批账号里的全部账号拉出来组成目标数据丢尽obj.data
          item.approve_account_info.forEach((itemIn: any) => {
            let childStatus = itemIn.approve_status === "PENDING" || itemIn.approve_status === "AGREE"
            console.log("childStatus", childStatus)
            let child = {
              icon: childStatus ? "el-icon-success" : "el-icon-error",
              color: switchColor(itemIn.approve_status),
              status_alias: itemIn.approve_status_alias,
              status: itemIn.approve_status,
              account_id: itemIn.account_id,
              timestamp: itemIn.approve_time,
              operator: `${itemIn.account_name}`,
              reason: itemIn.approve_reason
            }
            statusList.push(itemIn.approve_status)
            obj.data.push(child)
          })
          let agreeFlag = statusList.some((item) => item === "AGREE")
          let rejectFlag = statusList.some((item) => item === "REJECT")
          // 把上传的obj根据里面的内容重新赋值一下
          obj.icon = agreeFlag ? Check : rejectFlag ? CloseBold : MoreFilled
          obj.color = agreeFlag ? "#14ce84" : rejectFlag ? "#fd594e" : "#ff9b45"
          obj.status_alias = agreeFlag ? "审批通过" : rejectFlag ? "拒绝审批" : "待审批"
          obj.status = agreeFlag ? "AGREE" : rejectFlag ? "REJECT" : "PENDING"
        }
        newStatus.push(obj)
      })
      // 判断是否需要到资金平台
      if (result[0].approve_platform && result[0].approve_platform === "zj") {
        let obj = {
          icon: Check,
          color: "#14ce84",
          status_alias: "待资金监管平台审批",
          status: "pending",
          data: []
        }
        newStatus.push(obj)
      }
      break
    }
    case "and_approve": {
      // 如果是会签，将每个审批账号做成一个数组塞到data里面
      let obj = {
        icon:
          result[0].approve_status === "agree"
            ? Check
            : result[0].approve_status === "pending"
              ? MoreFilled
              : CloseBold,
        color: switchColor(result[0].approve_status),
        status_alias: result[0].approve_status_alias,
        status: result[0].approve_status,
        data: [] as any[]
      }
      if (result[0].approve_account_info && result[0].approve_account_info.length) {
        result[0].approve_account_info.forEach((item: any) => {
          if (item.length) {
            let arr: any = []
            item.forEach((itemIn: any) => {
              let childStatus = itemIn.approve_status === "PENDING" || itemIn.approve_status === "AGREE"
              let child = {
                icon: childStatus ? "el-icon-success" : "el-icon-error",
                color: switchColor(itemIn.approve_status),
                status_alias: itemIn.approve_status_alias,
                status: itemIn.approve_status,
                account_id: itemIn.account_id,
                timestamp: itemIn.approve_time,
                operator: `${itemIn.account_name}`,
                reason: itemIn.approve_reason
              }
              arr.push(child)
            })
            obj.data.push(arr)
          }
        })
        newStatus.push(obj)
      }
      // 判断是否需要到资金平台
      if (result[0].approve_platform && result[0].approve_platform === "zj") {
        let obj = {
          icon: Check,
          color: "#14ce84",
          status_alias: "待资金监管平台审批",
          status: "pending",
          data: []
        }
        newStatus.push(obj)
      }
      break
    }
    case "or_approve": {
      // 如果是或签，将所有账号放在一个流程内
      console.log("obj.data or_approve", result)
      let obj = {
        icon:
          result[0].approve_status === "agree"
            ? Check
            : result[0].approve_status === "pending"
              ? MoreFilled
              : CloseBold,
        color: switchColor(result[0].approve_status),
        status_alias: result[0].approve_status_alias,
        status: result[0].approve_status,
        data: [] as any[]
      }
      if (result[0].approve_account_info && result[0].approve_account_info.length) {
        result[0].approve_account_info.forEach((item: any) => {
          if (item.length) {
            item.forEach((itemIn: any) => {
              let childStatus = itemIn.approve_status === "PENDING" || itemIn.approve_status === "AGREE"
              let child = {
                icon: childStatus ? "el-icon-success" : "el-icon-error",
                color: switchColor(itemIn.approve_status),
                status_alias: itemIn.approve_status_alias,
                status: itemIn.approve_status,
                account_id: itemIn.account_id,
                timestamp: itemIn.approve_time,
                operator: `${itemIn.account_name}`,
                reason: itemIn.approve_reason
              }
              obj.data.push(child)
            })
          }
        })
        newStatus.push(obj)
        console.log("obj.data", obj.data)
        console.log("formData.value.approveAccountInfo 0000", cloneDeep(newStatus))
      }
      // 判断是否需要到资金平台
      if (result[0].approve_platform && result[0].approve_platform === "zj") {
        let obj = {
          icon: Check,
          color: "#14ce84",
          status_alias: "待资金监管平台审批",
          status: "pending",
          data: []
        }
        newStatus.push(obj)
      }
      break
    }
  }
  console.log("formData.value.approveAccountInfo 1111", cloneDeep([...newStatus]))
  addRejectStatus(result, newStatus)
  // 如果这时没撤回，再往里塞待拨款状态节点
  if (formData.value.appropriation_status !== "revoked" && formData.value.appropriation_status !== "pending") {
    setAppropriationProcess(newStatus)
    console.log("formData.value.approveAccountInfo 2222", cloneDeep([...newStatus]))
  }
  if (approveMethod.value !== "and_approve") {
    Reflect.set(formData.value, "approveAccountInfo", formData.value.approveAccountInfo.concat([...newStatus]))
    console.log("formData.value.approveAccountInfo 3333", cloneDeep([...newStatus]))
  } else {
    let obj = cloneDeep(formData.value.approveAccountInfo[0])
    obj.data = [[obj.data[0]]]
    Reflect.set(formData.value, "approveAccountInfo", [obj, ...newStatus])
    console.log("formData.value.approveAccountInfo22222", formData.value.approveAccountInfo)
  }
}
// 选择状态颜色
const switchColor = (status: string) => {
  let color = ""
  switch (status) {
    case "PENDING":
      color = "#ff9b45"
      break
    case "AGREE":
      color = "#14ce84"
      break
    case "REJECT":
      color = "#fd594e"
      break
    case "pending":
      color = "#ff9b45"
      break
    case "agree":
      color = "#14ce84"
      break
    case "reject":
      color = "#fd594e"
      break
    default:
      color = "#909399"
  }
  return color
}

const addRejectStatus = (data: any, statusArr: any[]) => {
  // 处理状态
  let shortlistdApplicationData = formData.value?.shortlistd_application_data
  if (data && data.length > 0 && data[0].approve_status === "cnacel") {
    let obj = {
      icon: "el-icon-error",
      color: "#909399",
      status_alias: "撤销申请",
      status: "REVOKE",
      timestamp: shortlistdApplicationData?.update_time,
      operator: `${formData.value.operator}`
    }
    let status = {
      icon: CloseBold,
      color: "#909399",
      status_alias: "撤销申请",
      status: "REVOKE",
      data: [] as any[]
    }
    // 用历史操作处理旧数据
    let record = []
    if (data[0].approve_record && data[0].approve_record.record && data[0].approve_record.record.length) {
      record = cloneDeep(data[0].approve_record.record)
    }
    // 如果是撤销的，直接塞
    switch (data[0].approve_method) {
      case "one_by_one_approve": {
        // 先把最后一个干掉
        statusArr.pop()
        statusArr.forEach((item) => {
          let approvalStatusArr: any[] = []
          item.data.forEach((itemIn: any) => {
            let obj = record.filter((recordItem: any) => recordItem.account_id === itemIn.account_id)
            if (obj.length) {
              // 如果有就改
              let childStatus = obj[0].status === "PENDING" || obj[0].status === "AGREE"
              itemIn.icon = childStatus ? "el-icon-success" : "el-icon-error"
              itemIn.color = switchColor(obj[0].status)
              itemIn.status_alias = obj[0].content
              itemIn.status = obj[0].status
              itemIn.timestamp = obj[0].time
            } else {
              // 没有就置空
              itemIn.icon = ""
              itemIn.timestamp = ""
            }
            approvalStatusArr.push(itemIn.status)
          })
          // 根据statusArr里的状态去判断
          let flag = approvalStatusArr.some((item) => item === "REJECT")
          // 审批账号里面的改好了，轮到该审批账号本身的状态了
          item.icon = flag ? CloseBold : Check
          item.color = flag ? switchColor("") : switchColor("AGREE")
          item.status_alias = flag ? "" : "审批通过"
          item.status = flag ? "" : "AGREE"
        })
        // 依次审批的话因为最后一个审批账号没审就撤销了，把最后那个干掉
        status.data = [{ ...obj }]
        statusArr.push(status)
        break
      }
      case "and_approve": {
        statusArr[0].data.forEach((item: any) => {
          item.forEach((itemIn: any) => {
            let obj = record.filter((recordItem: any) => recordItem.account_id === itemIn.account_id)
            if (obj.length) {
              // 如果有就改
              itemIn.icon = obj[0].status === "AGREE" ? "el-icon-success" : "el-icon-error"
              itemIn.color = switchColor(obj[0].status)
              itemIn.status_alias = obj[0].content
              itemIn.status = obj[0].status
              itemIn.timestamp = obj[0].time
            } else {
              // 没有就置空
              itemIn.icon = ""
              itemIn.timestamp = ""
            }
          })
        })
        // 审批账号里面的改好了，轮到该审批账号本身的状态了
        statusArr[0].icon = "el-icon-more"
        statusArr[0].color = switchColor("PENDING")
        statusArr[0].status_alias = "待审批"
        statusArr[0].status = "PENDING"
        status.data = [[{ ...obj }]]
        statusArr.push(status)
        break
      }
      case "or_approve": {
        // 先把最后一个干掉
        statusArr.pop()
        status.data = [{ ...obj }]
        statusArr.push(status)
        break
      }
    }
  }
}
// 设置待拨款流程
const setAppropriationProcess = (arr: any[]) => {
  console.log("看看待拨款的情况", arr, formData.value)
  if (formData.value.zj_approve_status === "agree") {
    let obj = {
      icon: "el-icon-check",
      color: "#14ce84",
      status_alias: "待拨款",
      status: "agree",
      data:
        formData.value.approve_method === "and_approve"
          ? [
              [
                {
                  icon: "el-icon-success",
                  color: "#14ce84",
                  status_alias: "",
                  status: "agree",
                  account_id: "",
                  timestamp: new Date(),
                  operator: ``
                }
              ]
            ]
          : [
              {
                icon: "el-icon-success",
                color: "#14ce84",
                status_alias: "",
                status: "agree",
                account_id: "",
                timestamp: new Date(),
                operator: ``
              }
            ]
    }
    arr.push(obj)
    if (formData.value.zj_appropriation_status === "appropriated") {
      // 如果是已拨款，再插入已拨款的状态
      let obj = {
        icon: "el-icon-check",
        color: "#14ce84",
        status_alias: "已拨款",
        status: "agree",
        data:
          formData.value.approve_method === "and_approve"
            ? [
                [
                  {
                    icon: "el-icon-success",
                    color: "#14ce84",
                    status_alias: "",
                    status: "agree",
                    account_id: "",
                    timestamp: formData.value.appropriation_time
                  }
                ]
              ]
            : [
                {
                  icon: "el-icon-success",
                  color: "#14ce84",
                  status_alias: "",
                  status: "agree",
                  account_id: "",
                  timestamp: formData.value.appropriation_time
                }
              ]
      }
      arr.push(obj)
    }
  }
}

// 对外暴露方法
defineExpose({
  visible,
  setFormData
})
</script>

<style lang="scss" scoped>
.bg-grey {
  padding: 10px 20px;
  border: 1px solid #e7e9ef;
  border-radius: 4px;
}
.text-break {
  max-width: 350px;
  word-break: break-all;
}
</style>
