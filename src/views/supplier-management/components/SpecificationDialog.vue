<template>
  <div class="specification-detail container-wrapper">
    <el-drawer
      v-model="dialogFormVisible"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div v-if="type === 'specification'">
        <el-table
          :data="tableData"
          stripe
          header-row-class-name="ps-table-header-row"
          border
          :span-method="arraySpanMethod"
        >
          <el-table-column prop="name" label="最小单位" />
          <el-table-column prop="unit_management_name" label="规格" />
          <el-table-column prop="count" label="数量">
            <template #default="scope">
              {{ scope.row.count + scope.row.name }}
            </template>
          </el-table-column>
          <el-table-column prop="unit_price" label="单价">
            <template #default="scope">
              {{ scope.row.unit_price ? "¥" + scope.row.unit_price : "--" }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else>
        <el-form :model="detailData">
          <el-form-item label="生产来源：">
            {{ source }}
          </el-form-item>
          <el-form-item label="供货商名称：">
            {{ detailData.name }}
          </el-form-item>
          <el-form-item label="联系人：">
            {{ detailData.contact_name }}
          </el-form-item>
          <el-form-item label="联系电话：">
            {{ detailData.contact_phone }}
          </el-form-item>
          <el-form-item label="地址：">
            {{ detailData.address }}
          </el-form-item>
          <div>
            <h3>经营资质</h3>
            <el-form-item label="类别：">
              {{ detailData.supply_category }}
            </el-form-item>
            <div class="flex flex-wrap">
              <div
                class="flex flex-col flex-justify-between flex-items-center"
                v-for="(item, index) in detailData.certification_info"
                :key="index"
              >
                <el-image class="w-200px h-100px mr-10px mb-10px m-t-10px" :src="item.imgUrl" :fit="'contain'" />
                <span>{{ item.name }}</span>
                <span v-if="item.expiry_date && item.expiry_date.length > 1"
                  >{{ item.expiry_date[0] }}至{{ item.expiry_date[1] }}</span
                >
              </div>
            </div>
          </div>
        </el-form>
      </div>
      <div class="dialog-footer m-t-20px">
        <!-- <el-button type="primary" class="ps-origin-btn-plain" @click="closeDialog" v-loading="confirmLoading">
          取消
        </el-button> -->
        <el-button type="primary" @click="closeDialog" v-loading="confirmLoading"> 关闭 </el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { cloneDeep } from "lodash"
import { ref, watch } from "vue"
import { divide } from "@/utils"
import type { TableColumnCtx } from "element-plus"

// import { confirm } from "@/utils/message"
// import { cloneDeep } from "lodash"
const props = defineProps({
  title: {
    type: String,
    default: "详情"
  },
  width: {
    type: String,
    default: "548px"
  },
  isShow: {
    type: Boolean,
    default: false
  },
  type: {
    type: String, // detail 详情  specification 规格
    default: "detail"
  },
  orgId: {
    type: Number,
    default: -1
  },
  source: {
    type: String,
    default: ""
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const dialogFormVisible = ref(false)
const confirmLoading = ref(false)
const detailData = ref()

// table数据
const tableData = ref<Array<any>>()
// const tableLoading = ref(false)
// const tableSetting = ref()
// const cloneTableData = cloneDeep(tableData)
// const pageConfig = reactive({
//   total: 0,
//   currentPage: 1,
//   pageSize: 10
// })

// 弹窗确认
const confirmDialog = () => {
  emit("confirmDialog")
}

// 弹窗关闭
const closeDialog = () => {
  tableData.value = []
  emit("cancelDialog")
}
// 设置数据
const setDialogData = (data: any, type: string) => {
  console.log("setDialogData", type, data)
  if (type === "detail" && data && typeof data === "object") {
    detailData.value = cloneDeep(data)
  } else {
    let limitUnitManagement = data.limit_unit_management || []
    let materialSpecification = data.material_specification || []
    // 根据这两个组成一个数据
    let newList: any[] = []
    if (limitUnitManagement && limitUnitManagement.length > 0) {
      limitUnitManagement.forEach((item: any) => {
        let id = item.id
        let childTag: any = {
          id: item.id,
          name: item.unit_management_name
        }
        console.log("childTag", childTag, id)

        if (materialSpecification && materialSpecification.length > 0) {
          let specList = materialSpecification.filter((subItem: any) => {
            return subItem.specification === id
          })
          console.log("specList", specList)
          if (specList && specList.length > 0) {
            specList.forEach((specItem: any) => {
              Reflect.set(childTag, "count", specItem.count)
              Reflect.set(childTag, "unit_management_name", specItem.unit_management_name)
              Reflect.set(childTag, "unit_price", divide(specItem.unit_price))
              newList.push(cloneDeep(childTag))
            })
          }
        }
      })
    }
    tableData.value = cloneDeep(newList || [])
    console.log("tableData", tableData.value)
  }
}
// 合并表格 类型
interface SpanMethodProps {
  row: any
  column: TableColumnCtx<any>
  rowIndex: number
  columnIndex: number
}
// 合并表格
const arraySpanMethod = ({ row, column, rowIndex, columnIndex }: SpanMethodProps) => {
  if (columnIndex === 0) {
    // 假设我们要合并第一列，即ID列 判断Id一样就合并
    if (rowIndex > 0 && tableData.value && row.id === tableData.value[rowIndex - 1].id) {
      return {
        rowspan: 0,
        colspan: 0
      }
    } else {
      const count = (tableData.value && tableData.value.filter((item) => item.id === row.id).length) || 0
      return {
        rowspan: count,
        colspan: 1
      }
    }
  }
}

// 页码改变
// const handleCurrentChange = (page: number, pageSize: number) => {
//   pageConfig.currentPage = page
//   pageConfig.pageSize = pageSize
// }
watch(
  () => ({
    isShow: props.isShow
  }),
  (newValue) => {
    console.log("watch dialog", newValue)
    dialogFormVisible.value = newValue.isShow
    if (newValue.isShow) {
      // initData()
    }
  }
)
defineExpose({ setDialogData })
</script>
<style lang="scss" scoped>
.container-wrapper .table-content {
  padding: 0 !important;
  position: relative;
}
.btn-layout {
  position: absolute;
  top: -40px;
  right: 20px;
  z-index: 111;
}
.specification-detail {
  .tag-title {
    width: 120px;
    min-width: 120px;
    background: #f4f6fc;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-left: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .tag-content {
    min-width: 300px;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .border-bottom {
    border-bottom: 1px solid #e9ecf1;
  }
}
</style>
