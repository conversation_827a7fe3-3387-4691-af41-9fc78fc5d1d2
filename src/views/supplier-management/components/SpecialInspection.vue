<template>
  <div ref="searchRef">
    <search-form
      :form-setting="searchFormSetting"
      @change-search="changeSearch"
      @reset="handlerReset"
      v-loading="loading"
      search-mode="normal"
    />
    <div class="pl-20px pr-20px">
      <el-divider />
    </div>
    <div class="container-wrapper">
      <div class="table-wrapper" ref="tableWrapperRef">
        <div class="flex flex-justify-end flex-item-center mb-10px pr-20px pl-20px">
          <div class="table-button">
            <el-button type="primary" @click="drawerShow = true">检查项模板</el-button>
          </div>
        </div>
        <div class="table-content">
          <ps-table
            :tableData="tableData"
            ref="psTableRef"
            v-loading="tableLoading"
            :show-pagination="true"
            @pagination-change="handleCurrentChange"
            :pageConfig="pageConfig"
            :max-height="maxHeight"
          >
            <ps-column :table-headers="tableSetting">
              <template #operation="{ row }">
                <el-button plain link size="small" type="primary" @click="showDetailDrawer('handle', row.detail)">
                  处理
                </el-button>
                <el-button plain link size="small" type="primary" @click="showDetailDrawer('detail', row.detail)">
                  详情
                </el-button>
              </template>
            </ps-column>
          </ps-table>
        </div>
      </div>
    </div>

    <!-- 检查项模板 -->
    <el-drawer
      v-model="drawerShow"
      title="检查项模板"
      :direction="'rtl'"
      :show-close="false"
      class="ps-drawer"
      size="40%"
    >
      <div class="template-box flex flex-col" v-for="(item, index) in templateData" :key="index">
        <div class="template-box-head flex flex-items-center">
          <h3>{{ item.title }}</h3>
          <el-checkbox-group v-model="item.selectHandle" class="ml-20px" @change="selectHandleChange(item)">
            <el-checkbox label="一键全选" value="isSelectAll" />
            <el-checkbox label="是否必填" value="isRequired" />
          </el-checkbox-group>
        </div>
        <div class="template-box-content">
          <div v-for="(itemIn, indexIn) in item.content" :key="indexIn">
            <div class="template-box-content-up">
              <el-checkbox v-model="itemIn.isSelect" :label="itemIn.label" size="large" />
            </div>
            <div class="template-box-content-down">
              <div class="flex flex-items-center">
                <el-checkbox v-model="itemIn.isFillInInfo" label="填写信息" size="large" />
                <el-switch class="ml-10px" v-model="itemIn.isRequiredForFillInInfo" active-text="是否必填" />
              </div>
              <div class="flex flex-items-center">
                <el-checkbox v-model="itemIn.isUpLoadFile" label="上传附件" size="large" />
                <el-switch class="ml-10px" v-model="itemIn.isRequiredForUpLoadFile" active-text="是否必填" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="dialog-footer m-t-20px">
        <el-button @click="drawerShow = false"> 取消 </el-button>
        <el-button type="primary" @click="setTemplateDataHandle"> 确定 </el-button>
      </div>
    </el-drawer>

    <!-- 详情与处理 -->
    <el-drawer
      v-model="detailDrawerShow"
      :title="selectType === 'handle' ? '处理' : '详情'"
      :direction="'rtl'"
      :show-close="false"
      class="ps-drawer"
      size="40%"
    >
      <div class="flex flex-col" v-for="(item, index) in detailData" :key="index">
        <div class="flex flex-items-center">
          <h3>{{ item.title }}</h3>
          <div v-show="selectType === 'handle'">（合格项：2项，不合格项：1项）</div>
        </div>
        <div class="flex flex-items-start p-10px" v-for="(itemIn, indexIn) in item.content" :key="indexIn">
          <el-icon v-show="selectType === 'handle'" :color="itemIn.isUpToStandard ? '#00b482' : '#ff5656'" size="18"
            ><CircleCheckFilled v-if="itemIn.isUpToStandard" /><CircleCloseFilled v-else
          /></el-icon>
          <div class="ml-10px">
            <div class="font-size-16px mb-10px">{{ itemIn.label }}</div>
            <div class="font-size-16px mb-10px">{{ itemIn.UpToStandardText }}</div>
            <div class="font-size-16px mb-10px color-gray-5">附件： {{ itemIn.file }}</div>
            <div class="font-size-16px mb-10px color-gray-5" v-show="!itemIn.isUpToStandard">
              整改信息： {{ itemIn.abarbeitungInfo }}
            </div>
            <div class="font-size-16px mb-10px color-gray-5" v-show="!itemIn.isUpToStandard">
              <div v-if="selectType === 'detail'">是否整改： {{ itemIn.isAbarbeitungAlias }}</div>
              <div v-else>
                <span>是否整改：</span>
                <el-radio-group v-model="itemIn.isAbarbeitung">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="dialog-footer m-t-20px">
        <el-button @click="detailDrawerShow = false"> 取消 </el-button>
        <el-button type="primary" @click="detailDrawerShow = false"> 确定 </el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue"
import { cloneDeep } from "lodash"
import {
  SEARCH_FORM_SETTING_SPECIAL_INSPECTION,
  TABLE_SETTING_SPECIAL_INSPECTION,
  ContentType,
  TemplateDataProps,
  DetailContentType
} from "./constants"
import useTableHeightHook from "@/hooks/useTableHeight"
import { useSupplierManagement } from "@/store/modules/supplierManagement"

const supplierManagementStore = useSupplierManagement()

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_SPECIAL_INSPECTION))
const loading = ref(false)
// table数据
const tableData = ref<any>([])
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_SPECIAL_INSPECTION)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
const getDataList = () => {
  tableData.value = supplierManagementStore.specialInspectionData
}
// watch(() => )
onMounted(() => {
  getDataList()
})

// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 抽屉
const drawerShow = ref(false)
const detailDrawerShow = ref(false)
const templateData = ref<TemplateDataProps[]>([
  {
    title: "第一项：资质文件",
    selectHandle: [],
    content: [
      {
        isSelect: false,
        label: "营业执照",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      },
      {
        isSelect: false,
        label: "食品经营许可证",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      },
      {
        isSelect: false,
        label: "生产许可证",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      }
    ]
  },
  {
    title: "第二项：产品质量检查",
    selectHandle: [],
    content: [
      {
        isSelect: false,
        label: "外观检查",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      },
      {
        isSelect: false,
        label: "规格检查",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      },
      {
        isSelect: false,
        label: "质量检测报告检查",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      },
      {
        isSelect: false,
        label: "产品标签检查",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      }
    ]
  },
  {
    title: "第三项：仓储和物流检查",
    selectHandle: [],
    content: [
      {
        isSelect: false,
        label: "仓储条件检查",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      },
      {
        isSelect: false,
        label: "物流配送车辆检查",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      },
      {
        isSelect: false,
        label: "运输过程中的包装检查",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      }
    ]
  },
  {
    title: "第四项：供应能力检查",
    selectHandle: [],
    content: [
      {
        isSelect: false,
        label: "生产能力检查",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      },
      {
        isSelect: false,
        label: "库存水平检查",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      },
      {
        isSelect: false,
        label: "补货能力检查",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      }
    ]
  },
  {
    title: "第五项：价格和成本检查",
    selectHandle: [],
    content: [
      {
        isSelect: false,
        label: "价格合理性检查",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      },
      {
        isSelect: false,
        label: "成本稳定性检查",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      },
      {
        isSelect: false,
        label: "费用和结算检查",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      }
    ]
  },
  {
    title: "第六项：服务质量检查",
    selectHandle: [],
    content: [
      {
        isSelect: false,
        label: "订单响应速度检查",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      },
      {
        isSelect: false,
        label: "交货准时性检查",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      },
      {
        isSelect: false,
        label: "售后服务检查",
        isFillInInfo: false,
        isRequiredForFillInInfo: false,
        isUpLoadFile: false,
        isRequiredForUpLoadFile: false
      }
    ]
  }
])
const detailData = ref<any[]>([
  {
    title: "第一项：资质文件",
    content: [
      {
        label: "营业执照",
        isUpToStandard: true,
        UpToStandardText: "合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      },
      {
        label: "营业执照",
        isUpToStandard: true,
        UpToStandardText: "合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      },
      {
        label: "营业执照",
        isUpToStandard: false,
        UpToStandardText: "不合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      }
    ]
  },
  {
    title: "第二项：产品质量检查",
    content: [
      {
        label: "外观检查",
        isUpToStandard: true,
        UpToStandardText: "合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      },
      {
        label: "规格检查",
        isUpToStandard: true,
        UpToStandardText: "合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      },
      {
        label: "质量检测报告检查",
        isUpToStandard: false,
        UpToStandardText: "不合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      },
      {
        label: "产品标签检查",
        isUpToStandard: false,
        UpToStandardText: "不合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      }
    ]
  },
  {
    title: "第三项：仓储和物流检查",
    content: [
      {
        label: "仓储条件检查",
        isUpToStandard: true,
        UpToStandardText: "合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      },
      {
        label: "物流配送车辆检查",
        isUpToStandard: true,
        UpToStandardText: "合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      },
      {
        label: "运输过程中的包装检查",
        isUpToStandard: false,
        UpToStandardText: "不合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      }
    ]
  },
  {
    title: "第四项：供应能力检查",
    content: [
      {
        label: "生产能力检查",
        isUpToStandard: true,
        UpToStandardText: "合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      },
      {
        label: "库存水平检查",
        isUpToStandard: true,
        UpToStandardText: "合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      },
      {
        label: "补货能力检查",
        isUpToStandard: false,
        UpToStandardText: "不合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      }
    ]
  },
  {
    title: "第五项：价格和成本检查",
    content: [
      {
        label: "价格合理性检查",
        isUpToStandard: true,
        UpToStandardText: "合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      },
      {
        label: "成本稳定性检查",
        isUpToStandard: true,
        UpToStandardText: "合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      },
      {
        label: "费用和结算检查",
        isUpToStandard: false,
        UpToStandardText: "不合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      }
    ]
  },
  {
    title: "第六项：服务质量检查",
    content: [
      {
        label: "订单响应速度检查",
        isUpToStandard: true,
        UpToStandardText: "合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      },
      {
        label: "交货准时性检查",
        isUpToStandard: true,
        UpToStandardText: "合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      },
      {
        label: "售后服务检查",
        isUpToStandard: false,
        UpToStandardText: "不合格",
        file: "无",
        abarbeitungInfo: "无",
        isAbarbeitung: false,
        isAbarbeitungAlias: "否"
      }
    ]
  }
])

const setTemplateDataHandle = () => {
  console.log("data", templateData.value)
  supplierManagementStore.setTemplateData(templateData.value)
  drawerShow.value = false
}
const selectHandleChange = (data: TemplateDataProps) => {
  console.log("data.selectHandle", data.selectHandle)
  if (data.selectHandle.length === 1 && data.selectHandle.includes("isSelectAll")) {
    data.content.forEach((item: ContentType) => {
      item.isSelect = true
      item.isFillInInfo = true
      item.isRequiredForFillInInfo = false
      item.isUpLoadFile = true
      item.isRequiredForUpLoadFile = false
    })
  } else if (data.selectHandle.length === 1 && data.selectHandle.includes("isRequired")) {
    data.selectHandle = ["isSelectAll", "isRequired"]
    data.content.forEach((item: ContentType) => {
      item.isSelect = true
      item.isFillInInfo = true
      item.isRequiredForFillInInfo = true
      item.isUpLoadFile = true
      item.isRequiredForUpLoadFile = true
    })
  } else if (data.selectHandle.length === 2) {
    data.content.forEach((item: ContentType) => {
      item.isSelect = true
      item.isFillInInfo = true
      item.isRequiredForFillInInfo = true
      item.isUpLoadFile = true
      item.isRequiredForUpLoadFile = true
    })
  } else {
    data.content.forEach((item: ContentType) => {
      item.isSelect = false
      item.isFillInInfo = false
      item.isRequiredForFillInInfo = false
      item.isUpLoadFile = false
      item.isRequiredForUpLoadFile = false
    })
  }
}
const selectType = ref<string>("")
const showDetailDrawer = (type: string, data: ContentType[]) => {
  console.log("data", data)
  selectType.value = type
  detailData.value = [...data]
  detailDrawerShow.value = true
}
</script>

<style lang="scss" scoped>
.table-box {
  border-bottom: 1px solid #dcdfe6;
}
.template-box {
  &-content {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    &-down {
      padding: 0px 20px;
    }
  }
}
:deep(.el-divider--horizontal) {
  margin: 0px !important;
}
</style>
