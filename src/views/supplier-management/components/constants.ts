import { getPreDate } from "@/utils/date"

import car1 from "../../document-management/img/car1.png"
import car2 from "../../document-management/img/car2.png"
import card1 from "../../document-management/img/card1.png"
import card2 from "../../document-management/img/card2.png"
import document from "../../document-management/img/document.png"
import document1 from "../../document-management/img/document1.png"
import document2 from "../../document-management/img/document2.png"
import document3 from "../../document-management/img/document3.png"
import document4 from "../../document-management/img/document4.png"
import document5 from "../../document-management/img/document5.png"
import document6 from "../../document-management/img/document6.png"

export type ContentType = {
  isSelect: boolean
  label: string
  isFillInInfo: boolean
  isRequiredForFillInInfo: boolean
  isUpLoadFile: boolean
  isRequiredForUpLoadFile: boolean
}
export interface TemplateDataProps {
  title: string
  selectHandle: Array<string>
  content: Array<ContentType>
}

export type DetailContentType = {
  label: string
  isUpToStandard: boolean
  UpToStandardText: string
  file: string | any[]
  abarbeitungInfo: string
  isAbarbeitung: boolean | null
  isAbarbeitungAlias: string
}

export const defaultImg = "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg"

export const img1 = document1
export const img2 = document2
export const img3 = document3

export const SEARCH_FORM_SETTING_SUPPLY_RELATIONSHIP = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "日期",
    labelWidth: "40px",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  // org_ids: {
  //   type: "supervisionOrgs",
  //   label: "组织名称",
  //   value: [],
  //   placeholder: "请选择",
  //   multiple: true,
  //   clearable: true,
  //   collapseTags: true
  // }，
  org_name: {
    type: "input",
    label: "组织名称",
    value: "",
    placeholder: "请输入组织名称",
    clearable: true,
    maxlength: 20
  }
}

export const TABLE_SETTING_SUPPLY_RELATIONSHIP = [
  {
    label: "组织名称",
    prop: "organization_name"
  },
  {
    label: "结算金额",
    prop: "total_settled_fee",
    type: "price"
  },
  {
    label: "待结算金额",
    prop: "total_unliquidated_fee",
    type: "price"
  },
  // {
  //   label: "状态",
  //   prop: "status_alias"
  // },
  {
    label: "操作",
    prop: "operation",
    slot: "operation"
  }
]

export const ORG_LIST = [
  {
    label: "英格利英文学院",
    value: "1"
  },
  {
    label: "学泰一中",
    value: "2"
  },
  {
    label: "学泰二中",
    value: "3"
  }
]

export const DETAIL_DRAWER_DATA = [{}]

export const SEARCH_FORM_SETTING_SUPPLIER_EVALUATION = {
  select_time: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "日期",
    labelWidth: "40px",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  // source_organization_ids: {
  //   type: "supervisionOrgs",
  //   label: "监管组织",
  //   value: [],
  //   placeholder: "请选择",
  //   multiple: true,
  //   clearable: true,
  //   collapseTags: true
  // },
  org_name: {
    type: "input",
    label: "监管组织",
    value: "",
    placeholder: "请输入组织名称",
    clearable: true,
    maxlength: 20
  }
}
export const TABLE_SETTING_SUPPLIER_EVALUATION = [
  {
    label: "监管组织",
    prop: "source_organization_name"
  },
  {
    label: "人员名称",
    prop: "name"
  },
  {
    label: "评价时间",
    prop: "create_time"
  },
  {
    label: "服务质量",
    prop: "service_score",
    slot: "service_score"
  },
  {
    label: "产品质量",
    prop: "hygiene_score",
    slot: "hygiene_score"
  },
  {
    label: "物资价格",
    prop: "food_score",
    slot: "food_score"
  },
  {
    label: "评价内容",
    prop: "remark",
    slot: "remark"
  },
  {
    label: "附件",
    prop: "imgs",
    slot: "imgs"
  }
  // {
  //   label: "操作",
  //   prop: "operation",
  //   slot: "operation"
  // }
]

export const SEARCH_FORM_SETTING_SPECIAL_INSPECTION = {
  select_time: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "日期",
    labelWidth: "40px",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  }
}
export const TABLE_SETTING_SPECIAL_INSPECTION = [
  {
    label: "检查日期",
    prop: "date"
  },
  {
    label: "名称",
    prop: "name"
  },
  {
    label: "状态",
    prop: "status_alias"
  },
  {
    label: "备注",
    prop: "remark"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operation"
  }
]
export const SEARCH_FORM_SETTING_SUPPLIES = {
  materila_name: {
    type: "input",
    label: "物资名称",
    labelWidth: "80px",
    value: "" as string,
    placeholder: "请输入物资名称",
    clearable: true
  }
}
export const TABLE_SETTING_SUPPLIES = [
  {
    label: "物资名称",
    prop: "name"
  },
  {
    label: "最小单位",
    prop: "ref_unit_price",
    slot: "refUnitPrice"
  },
  {
    label: "规格",
    prop: "specification",
    slot: "specification"
  },
  {
    label: "分类",
    prop: "vendor_materail_classification_name"
  },
  {
    label: "生产来源",
    prop: "production_source_alias"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operation"
  }
]
// 供应商待审批
export const SEARCH_FORM_PENDINGAPPROVAL = {
  select_time: {
    type: "daterange",
    clearable: false,
    label: "申请日期",
    labelWidth: "80px",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择"
  }
}
export const TABLE_SETTING_PENDINGAPPROVAL = [
  {
    label: "申请时间",
    prop: "apply_time",
    slot: "apply_time"
  },
  {
    label: "供应商名称",
    prop: "supplier_name",
    slot: "supplier_name"
  },
  {
    label: "负责人",
    prop: "contact_name",
    slot: "contact_name"
  },
  {
    label: "联系电话",
    prop: "contact_phone",
    slot: "contact_phone"
  },
  {
    label: "地址",
    prop: "address",
    slot: "address"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operation"
  }
]
export const TABLE_SETTING_AGREED = [
  {
    label: "申请时间",
    prop: "apply_time",
    slot: "apply_time"
  },
  {
    label: "审批时间",
    prop: "update_time",
    slot: "update_time"
  },
  {
    label: "供应商名称",
    prop: "supplier_name",
    slot: "supplier_name"
  },
  {
    label: "负责人",
    prop: "contact_name",
    slot: "contact_name"
  },
  {
    label: "联系电话",
    prop: "contact_phone",
    slot: "contact_phone"
  },
  {
    label: "地址",
    prop: "address",
    slot: "address"
  },
  {
    label: "操作人",
    prop: "operator"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operation"
  }
]
export const TABLE_SETTING_REJECTED = [
  {
    label: "申请时间",
    prop: "apply_time",
    slot: "apply_time"
  },
  {
    label: "审批时间",
    prop: "update_time",
    slot: "update_time"
  },
  {
    label: "供应商名称",
    prop: "supplier_name",
    slot: "supplier_name"
  },
  {
    label: "负责人",
    prop: "contact_name",
    slot: "contact_name"
  },
  {
    label: "联系电话",
    prop: "contact_phone",
    slot: "contact_phone"
  },
  {
    label: "地址",
    prop: "address",
    slot: "address"
  },
  {
    label: "拒绝原因",
    prop: "reject_reason"
  },
  {
    label: "操作人",
    prop: "operator"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operation"
  }
]
export const TABLE_SETTING_REPEALED = [
  {
    label: "申请时间",
    prop: "apply_time",
    slot: "apply_time"
  },
  {
    label: "撤销时间",
    prop: "update_time",
    slot: "update_time"
  },
  {
    label: "供应商名称",
    prop: "supplier_name",
    slot: "supplier_name"
  },
  {
    label: "负责人",
    prop: "contact_name",
    slot: "contact_name"
  },
  {
    label: "联系电话",
    prop: "contact_phone",
    slot: "contact_phone"
  },
  {
    label: "地址",
    prop: "address",
    slot: "address"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operation"
  }
]
export const PENDING_APPROVAL_TABLE_DATA = [
  {
    applicationTime: "2024年11月22日17:52:07",
    supplier: "同和谷仓批发有限公司",
    name: "刘宜庆",
    phone: "***********",
    address: "广州市同和街道88号",
    businessQualification: {
      label: "经营资质",
      type: "农产品供应",
      qualificationList: [
        {
          url: document1,
          name: "营业执照",
          timeRange: ["2013-10-01", "2033-09-30"]
        },
        {
          url: document2,
          name: "食品生产许可证",
          timeRange: ["2020-08-10", "2030-08-09"]
        },
        {
          url: document3,
          name: "食品经营许可证",
          timeRange: ["2020-09-01", "2030-08-31"]
        }
      ]
    },
    contractInfo: {
      label: "合同信息",
      timeLimit: 2,
      validity: ["2024-11-16", "2026-11-15"],
      infoList: [
        {
          url: document6
        },
        {
          url: document6
        },
        {
          url: document6
        }
      ]
    },
    carInfo: {
      label: "车辆信息",
      carNo: "粤A·OU955",
      carType: "轻型厢式货车",
      imgList: [car1, car2]
    },
    driverInfo: {
      label: "司机信息",
      name: "郑越进",
      phone: "***********",
      certificateInfo: [
        {
          url: card1,
          name: "驾驶证件"
        },
        {
          url: card2,
          name: "健康证件"
        }
      ]
    }
  }
]
export const AGREED_TABLE_DATA = [
  {
    applicationTime: "2024年11月22日17:52:07",
    approvalTime: "2024年11月22日19:42:00",
    supplier: "安格斯畜禽肉供货商",
    name: "周首钢",
    phone: "***********",
    address: "广州市同和街道88号",
    operator: "刘局长",
    businessQualification: {
      label: "经营资质",
      type: "畜禽肉及制品",
      qualificationList: [
        {
          url: document1,
          name: "营业执照",
          timeRange: ["2013-10-01", "2033-09-30"]
        },
        {
          url: document2,
          name: "食品生产许可证",
          timeRange: ["2020-08-10", "2030-08-09"]
        },
        {
          url: document3,
          name: "食品经营许可证",
          timeRange: ["2020-09-01", "2030-08-31"]
        }
      ]
    },
    contractInfo: {
      label: "合同信息",
      timeLimit: 2,
      validity: ["2024-11-16", "2026-11-15"],
      infoList: [
        {
          url: document6
        },
        {
          url: document6
        },
        {
          url: document6
        }
      ]
    },
    carInfo: {
      label: "车辆信息",
      carNo: "粤A·OU955",
      carType: "轻型厢式货车",
      imgList: [car1, car2]
    },
    driverInfo: {
      label: "司机信息",
      name: "赖清泉",
      phone: "13032322254",
      certificateInfo: [
        {
          url: card1,
          name: "驾驶证件"
        },
        {
          url: card2,
          name: "健康证件"
        }
      ]
    }
  }
]
export const REJECTED_TABLE_DATA = [
  {
    applicationTime: "2024年11月22日 08:45:06",
    approvalTime: "2024年11月22日 19:42:00",
    supplier: "园心粗粮全国供应",
    name: "李自成",
    phone: "***********",
    address: "广州市车陂街道80号",
    rejectReason: "资质不合格，请重新整理",
    operator: "刘局长",
    businessQualification: {
      label: "经营资质",
      type: "谷类及制品",
      qualificationList: [
        {
          url: document1,
          name: "营业执照",
          timeRange: ["2013-10-01", "2033-09-30"]
        },
        {
          url: document2,
          name: "食品生产许可证",
          timeRange: ["2020-08-10", "2030-08-09"]
        },
        {
          url: document3,
          name: "食品经营许可证",
          timeRange: ["2020-09-01", "2030-08-31"]
        }
      ]
    },
    contractInfo: {
      label: "合同信息",
      timeLimit: 2,
      validity: ["2024-11-16", "2026-11-15"],
      infoList: [
        {
          url: document6
        },
        {
          url: document6
        },
        {
          url: document6
        }
      ]
    },
    carInfo: {
      label: "车辆信息",
      carNo: "粤A·QL945",
      carType: "轻型厢式货车",
      imgList: [car1, car2]
    },
    driverInfo: {
      label: "司机信息",
      name: "林晓平",
      phone: "13032989950",
      certificateInfo: [
        {
          url: card1,
          name: "驾驶证件"
        },
        {
          url: card2,
          name: "健康证件"
        }
      ]
    }
  }
]
export const REPEALED_TABLE_DATA = [
  {
    applicationTime: "2024年11月22日 09:45:06",
    revocationTime: "2024年11月22日 14:58:00",
    supplier: "喜膳来果蔬批发",
    name: "张远祥",
    phone: "***********",
    address: "永泰大街兴业大厦102",
    businessQualification: {
      label: "经营资质",
      type: "蔬菜类及制品",
      qualificationList: [
        {
          url: document1,
          name: "营业执照",
          timeRange: ["2013-10-01", "2033-09-30"]
        },
        {
          url: document2,
          name: "食品生产许可证",
          timeRange: ["2020-08-10", "2030-08-09"]
        },
        {
          url: document3,
          name: "食品经营许可证",
          timeRange: ["2020-09-01", "2030-08-31"]
        }
      ]
    },
    contractInfo: {
      label: "合同信息",
      timeLimit: 2,
      validity: ["2024-11-16", "2026-11-15"],
      infoList: [
        {
          url: document6
        },
        {
          url: document6
        },
        {
          url: document6
        }
      ]
    },
    carInfo: {
      label: "车辆信息",
      carNo: "粤A·KK945",
      carType: "轻型厢式货车",
      imgList: [car1, car2]
    },
    driverInfo: {
      label: "司机信息",
      name: "张雷",
      phone: "13032336566",
      certificateInfo: [
        {
          url: card1,
          name: "驾驶证件"
        },
        {
          url: card2,
          name: "健康证件"
        }
      ]
    }
  }
]
