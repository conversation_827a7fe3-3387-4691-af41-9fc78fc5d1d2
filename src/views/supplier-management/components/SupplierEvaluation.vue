<template>
  <div class="supplier-evaluation-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
      <div class="pl-20px pr-20px">
        <el-divider />
      </div>
    </div>
    <div class="container-wrapper">
      <div class="table-wrapper" ref="tableWrapperRef">
        <div class="table-content">
          <ps-table
            :tableData="tableData"
            ref="psTableEvaRef"
            v-loading="tableLoading"
            :show-pagination="true"
            @pagination-change="handleCurrentChange"
            :pageConfig="pageConfig"
            :max-height="maxHeight"
          >
            <ps-column :table-headers="tableSetting">
              <template #service_score="{ row }">
                <el-rate v-model="row.service_score" :disabled="true" />
              </template>
              <template #hygiene_score="{ row }">
                <el-rate v-model="row.hygiene_score" :disabled="true" />
              </template>
              <template #food_score="{ row }">
                <el-rate v-model="row.food_score" :disabled="true" />
              </template>
              <template #imgs="{ row }">
                <el-button
                  plain
                  link
                  size="small"
                  @click="handlerShowDetail(row)"
                  type="primary"
                  :disabled="!row.images || row.images.length === 0"
                >
                  查看
                </el-button>
              </template>
              <template #remark="{ row }">
                <!-- 评价内容 最多显示两行，超出显示省略号-->
                <el-tooltip placement="top" trigger="hover">
                  <template #content>
                    <div class="tooltip-content">{{ row.remark }}</div>
                  </template>
                  <div class="text-ellipsis-2">
                    {{ row.remark }}
                  </div>
                </el-tooltip>
              </template>
              <template #operation="{ row }">
                <!-- <el-button v-if="row.status === '1'" plain link size="small" type="primary" @click="showDrawer(row)">
                  转代办
                </el-button> -->
                <el-button plain link size="small" type="primary" @click="showDetailDrawer(row)"> 详情 </el-button>
              </template>
            </ps-column>
          </ps-table>
        </div>
      </div>
    </div>

    <!-- 操作 这个东西保留，现在版本没有用到-->
    <el-drawer v-model="drawerShow" title="操作" :direction="'rtl'" :show-close="false" class="ps-drawer" size="40%">
      <el-form :model="form" label-width="auto" class="pt-20px">
        <el-form-item label="截至处理时间">
          <div class="w-200px">
            <el-date-picker
              v-model="form.time"
              type="date"
              placeholder="请选择日期"
              size="default"
              value-format="YYYY-MM-DD"
            />
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer m-t-20px">
          <el-button @click="drawerShowHandle(false)">取消</el-button>
          <el-button type="primary" @click="drawerShowHandle(true)">确定</el-button>
        </div>
      </template>
    </el-drawer>
    <!-- 详情弹窗 -->
    <evaluation-detail ref="evaluationDetailRef" @close-dialog="closeDetailDialog" :is-show="detailDrawerShow" />
    <!-- 图片预览-->
    <image-preview-dialog v-model="imageVisible" :imgs="imageList" :currentIndex="0" :title="dialogTitle" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue"
import { cloneDeep } from "lodash"
import { SEARCH_FORM_SETTING_SUPPLIER_EVALUATION, TABLE_SETTING_SUPPLIER_EVALUATION } from "./constants"
import useTableHeightHook from "@/hooks/useTableHeight"
import { ElMessage, ElMessageBox } from "element-plus"
import { to } from "await-to-js"
import { apiBackgroundFundSupervisionSupplierChannelDemocraticFeedbackListPost } from "@/api/supplier"
import EvaluationDetail from "./EvaluationDetail.vue"

const rtl = "rtl" // 抽屉方向常量

const props = defineProps({
  treeNodeData: {
    type: Object,
    default: () => {
      return {}
    }
  },
  currentTree: {
    type: String || Number,
    default: ""
  }
})

const psTableEvaRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 0, psTableEvaRef, 90)

const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_SUPPLIER_EVALUATION))
const loading = ref(false)
// table数据
const tableData = ref<any>([])
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_SUPPLIER_EVALUATION)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])
const dialogTitle = ref("图片详情")
// 获取列表
const getDataList = async () => {
  console.log("props.treeNodeData", props.treeNodeData)
  tableLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionSupplierChannelDemocraticFeedbackListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize,
      supplier_manage_id: props.treeNodeData.id
    })
  )
  tableLoading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    let results = data.results || []
    tableData.value = results || []
    pageConfig.total = data.count || 0
  } else {
    ElMessage.error(res.msg)
  }
}
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value && data[key].value !== "" && data[key].value !== "全部" && data[key].value.length > 0) {
      if (key !== "select_time") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0] || ""
        params.end_date = data[key].value[1] || ""
      }
    }
  }
  return params
}
// 反馈图片
const handlerShowDetail = (row: any) => {
  console.log(row)
  const img = row ? row.images : []
  if (!img || img.length === 0) {
    return ElMessage.error("图片不存在")
  }
  console.log("handlerShowDetail", img)
  imageList.value = img
  imageVisible.value = true
}
// 保留原来人员写的弹窗处理，现在的版本没有用到  -----start
// 抽屉
const drawerShow = ref(false)
const form = ref({
  time: "" as string
})
const rowData = ref<any>({})
// const showDrawer = (data: any) => {
//   rowData.value = data
//   drawerShow.value = true
// }
const drawerShowHandle = (type: boolean) => {
  if (type) {
    rowData.value.status = "2"
    rowData.value.status_alias = "待处理"
  }
  drawerShow.value = false
}
// 保留原来人员写的弹窗处理，现在的版本没有用到  -----end

// 详情弹窗
const detailDrawerShow = ref(false)
const evaluationDetailRef = ref()
const showDetailDrawer = (data: any) => {
  rowData.value = data
  if (evaluationDetailRef.value) {
    evaluationDetailRef.value.setDialogData(data)
  }

  detailDrawerShow.value = true
}
// 关闭详情弹窗
const closeDetailDialog = () => {
  detailDrawerShow.value = false
}

onMounted(() => {
  getDataList()
})

watch(
  () => props.currentTree,
  (newValue: any) => {
    if (newValue) {
      getDataList()
    }
  }
)
// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}
</script>

<style lang="scss" scoped>
.table-box {
  border-bottom: 1px solid #dcdfe6;
}
:deep(.el-divider--horizontal) {
  margin: 0px !important;
}
.container-wrapper .table-wrapper {
  box-shadow: none;
  -webkit-box-shadow: none;
}
.tooltip-content {
  white-space: normal;
  word-break: break-all;
  max-width: 300px;
}
</style>
