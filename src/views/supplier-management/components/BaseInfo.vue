<template>
  <div class="pt-20px">
    <el-form :model="formData" label-width="100px" label-position="right">
      <el-form-item label="联系人：">
        {{ formData.contact_name }}
      </el-form-item>
      <el-form-item label="联系电话：">
        {{ formData.contact_phone }}
      </el-form-item>
      <el-form-item label="地址：">
        {{ formData.address }}
      </el-form-item>
      <el-divider />
      <!-- 经营资质 -->
      <div>
        <h3>经营资质</h3>
        <div class="flex flex-col">
          <el-form-item label="类别："> {{ formData.supply_category }} </el-form-item>
          <div
            class="flex flex-items-center"
            v-if="formData && formData.certification_info && formData.certification_info.length"
          >
            <div
              v-for="(item, index) in formData.certification_info"
              :key="index"
              class="ml-20px mr-20px flex flex-col flex-justify-between flex-items-center h-150px"
            >
              <el-image
                v-if="item.imgUrl.length"
                :preview-src-list="[item.imgUrl[0]]"
                class="w-150 h-100"
                :src="item.imgUrl[0]"
                :fit="'fill'"
              />
              <div>{{ aptitudeName(item.aptitude) }}</div>
              <div>
                {{
                  item.expiry_date && item.expiry_date.length > 0
                    ? item.expiry_date[0] + " 至 " + item.expiry_date[1]
                    : ""
                }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-divider />
      <!-- 合同信息 -->
      <div v-for="(vnItem, vnIndex) in formData.vnedor_contract" :key="vnIndex">
        <h3>合同信息</h3>
        <div class="flex flex-col">
          <el-form-item label="合同期限："> {{ vnItem.contract_period }}月 </el-form-item>
          <el-form-item label="合同有效期：" v-if="vnItem && vnItem.contract_start">
            自 {{ vnItem.contract_start }}起 至 {{ vnItem.contract_end }} 止
          </el-form-item>
          <el-form-item label="合同信息：" v-if="vnItem.contract_json">
            <div class="flex flex-items-center">
              <div
                v-for="(item, index) in vnItem.contract_json"
                :key="index"
                class="mr-20px flex flex-col flex-justify-between flex-items-center h-150px"
              >
                <el-image class="h-100 w-150" :preview-src-list="[item]" :src="item" :fit="'fill'" />
              </div>
            </div>
          </el-form-item>
        </div>
      </div>
      <el-divider v-if="formData.vnedor_contract && formData.vnedor_contract.length > 0" />
      <!-- 车辆信息 -->
      <div>
        <h3>车辆信息</h3>
        <div class="flex flex-col" v-for="(item, index) in formData.vehicle_info" :key="index">
          <el-form-item label="车辆号：" class="mr-10px">
            {{ item.plate_number }}
          </el-form-item>
          <el-form-item label="车辆类型：">
            {{ item.car_type_alias }}
          </el-form-item>
          <div class="flex flex-items-center">
            <div
              v-for="(carImgItem, carImgIndex) in item.car_img"
              :key="carImgIndex"
              class="ml-20px mr-20px flex flex-col flex-justify-between flex-items-center h-150px"
            >
              <el-image class="h-100 w-150" :preview-src-list="[carImgItem]" :src="carImgItem" :fit="'fill'" />
            </div>
          </div>
          <el-divider v-if="index !== formData.vehicle_info.length - 1" />
        </div>
      </div>
      <el-divider />
      <!-- 司机信息 -->
      <div>
        <h3>司机信息</h3>
        <div class="flex flex-col" v-for="(item, index) in formData.driver_info" :key="index">
          <el-form-item label="司机姓名：">
            {{ item.name }}
          </el-form-item>
          <el-form-item label="联系方式：">
            {{ item.number }}
          </el-form-item>
          <el-form-item label="证件信息：">
            <div>
              <div class="flex flex-items-center">
                <div
                  v-for="(drivingLicenceItem, drivingLicenceIndex) in item.driving_licence"
                  :key="drivingLicenceIndex"
                  class="mr-20px flex flex-col flex-justify-between flex-items-center h-150px"
                >
                  <el-image
                    class="h-100 w-150"
                    :preview-src-list="[drivingLicenceItem]"
                    :src="drivingLicenceItem"
                    :fit="'fill'"
                  />
                  <div>驾驶证件</div>
                </div>
              </div>
              <div class="flex flex-items-center">
                <div
                  v-for="(healthCertificateItem, healthCertificateIndex) in item.health_certificate"
                  :key="healthCertificateIndex"
                  class="mr-20px flex flex-col flex-justify-between flex-items-center h-150px"
                >
                  <el-image
                    class="h-100 w-150"
                    :preview-src-list="[healthCertificateItem]"
                    :src="healthCertificateItem"
                    :fit="'fill'"
                  />
                  <div>健康证件</div>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-divider />
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from "lodash"
import { onMounted, ref, watch } from "vue"
const props = defineProps({
  treeNodeData: {
    type: Object,
    default: () => {
      return {}
    }
  },
  currentTree: {
    type: Number,
    default: -1
  }
})
// 表单数据
const formData = ref<any>({
  name: "",
  phone: "",
  address: "",
  businessQualification: {
    label: "",
    type: "",
    qualificationList: [] as any[]
  },
  contractInfo: {
    label: "",
    timeLimit: 0,
    validity: [] as any[],
    infoList: [] as any[]
  },
  carInfo: {
    label: "",
    carNo: "",
    carType: "",
    imgList: [] as any[]
  },
  driverInfo: {
    label: "",
    name: "",
    phone: "",
    certificateInfo: [] as any[]
  }
})
onMounted(() => {
  formData.value = cloneDeep(props.treeNodeData)
  setContrachList()
})
const aptitudeName = (aptitude: String) => {
  let name = ""
  switch (aptitude) {
    case "1":
      name = "营业执照"
      break
    case "2":
      name = "食品经营许可证"
      break
    case "3":
      name = "食品生产许可证"
      break
  }
  return name
}
const setContrachList = () => {
  if (formData.value.vnedor_contract && Array.isArray(formData.value.vnedor_contract)) {
    formData.value.vnedor_contract.forEach((item: any) => {
      item.contract_json = item.contract_json ? JSON.parse(item.contract_json) : []
    })
  } else {
    formData.value.vnedor_contract = []
  }
}
watch(
  () => props.currentTree,
  (newValue: any) => {
    if (newValue) {
      formData.value = cloneDeep(props.treeNodeData)
      setContrachList()
    }
  }
)
</script>

<style lang="scss" scoped></style>
