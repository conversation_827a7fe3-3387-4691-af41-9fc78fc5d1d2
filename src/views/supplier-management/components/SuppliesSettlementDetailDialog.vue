<template>
  <div class="supplies-settlement-detail container-wrapper">
    <el-drawer
      v-model="dialogFormVisible"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div class="flex flex-item-center head-setting">
        <div>结算金额：¥{{ totalSettledFee }}</div>
        <div class="m-l-20">待结算金额：¥{{ totalSettlingFee }}</div>
      </div>
      <el-table
        :data="tableData"
        stripe
        style="width: 100%"
        border
        header-row-class-name="ps-table-header-row"
        v-loading="tableLoading"
      >
        <el-table-column prop="settle_time" label="结算时间" align="center" />
        <el-table-column prop="trade_no" label="结算单号" align="center" />
        <el-table-column prop="settle_fee" label="结算金额" align="center">
          <template #default="{ row }">
            <div>{{ row.settle_fee ? "¥" + divide(row.settle_fee) : "-" }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="settle_status_alias" label="是否结算" align="center" />
        <el-table-column prop="orderNo" label="关联取货单号" align="center">
          <template #default="{ row }">
            <el-button :type="'primary'" link @click="showOrderDetail(row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="ps-pagination">
        <el-pagination v-bind="pageConfig" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
      <el-drawer
        v-model="showDetailDialog"
        title="查看"
        :direction="'rtl'"
        class="ps-drawer"
        size="648px"
        :destroy-on-close="true"
        :close-on-click-modal="false"
      >
        <el-table :data="orderData" stripe border header-row-class-name="ps-table-header-row">
          <el-table-column prop="trade_no" label="收货单" align="center" />
          <el-table-column prop="create_time" label="创建时间" align="center" />
        </el-table>
      </el-drawer>
      <div class="dialog-footer m-t-20px">
        <!-- <el-button type="primary" class="ps-origin-btn-plain" @click="closeDialog" v-loading="confirmLoading">
          取消
        </el-button> -->
        <el-button type="primary" @click="closeDialog" v-loading="confirmLoading"> 关闭 </el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { cloneDeep } from "lodash"
import { ref, watch, reactive } from "vue"
import { divide } from "@/utils"
import { apiBackgroundFundSupervisionSupplierManageSupplierFinalStatementInfoListNewPost } from "@/api/supplier"
import to from "await-to-js"
import { ElMessage } from "element-plus"
// import { confirm } from "@/utils/message"
// import { cloneDeep } from "lodash"
const props = defineProps({
  title: {
    type: String,
    default: "结算明细"
  },
  width: {
    type: String,
    default: "648px"
  },
  isShow: {
    type: Boolean,
    default: false
  },
  type: {
    type: String, // detail 详情
    default: "detail"
  },
  orgId: {
    type: Array<any>,
    default: () => {
      return []
    }
  },
  supplierManageId: {
    type: Number,
    default: 0
  },
  startTime: {
    type: String,
    default: ""
  },
  endTime: {
    type: String,
    default: ""
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const dialogFormVisible = ref(false)
const confirmLoading = ref(false)
const detailData = ref()
const showDetailDialog = ref(false) // 是否显示详情
const orderData = ref<Array<any>>() // 订单数据
// 待结算汇总
const totalSettlingFee = ref()
// 已结算汇总
const totalSettledFee = ref()
// table数据
const tableData = ref<Array<any>>()
const tableLoading = ref(false)
// const tableSetting = ref()
// const cloneTableData = cloneDeep(tableData)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

// 弹窗关闭
const closeDialog = () => {
  tableData.value = []
  emit("cancelDialog")
}
// 设置数据
const setDialogData = (data: any, type: string) => {
  console.log("setDialogData", type, data)
  if (data) {
    detailData.value = cloneDeep(data || [])
  }
}

// 显示详情
const showOrderDetail = (row: any) => {
  if (row && typeof row === "object") {
    orderData.value = row.delivery_info_nos || []
  } else {
    orderData.value = []
  }
  showDetailDialog.value = true
}

// 页码改变
const handleCurrentChange = (page: number) => {
  pageConfig.currentPage = page
  getDataList()
}
// 数量改变
const handleSizeChange = (pageSize: number) => {
  pageConfig.currentPage = 1
  pageConfig.pageSize = pageSize
  getDataList()
}
// 获取结算数据
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionSupplierManageSupplierFinalStatementInfoListNewPost({
      start_time: props.startTime,
      end_time: props.endTime,
      org_ids: detailData.value.organization_id ? [detailData.value.organization_id] : undefined,
      supplier_manage_id: props.supplierManageId,
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    let results = data.results || []
    tableData.value = cloneDeep(results)
    let collect = data.collect || {}
    totalSettledFee.value = collect.total_settled_fee ? divide(collect.total_settled_fee) : 0
    totalSettlingFee.value = collect.total_unliquidated_fee ? divide(collect.total_unliquidated_fee) : 0
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
watch(
  () => ({
    isShow: props.isShow
  }),
  (newValue) => {
    console.log("watch dialog", newValue)
    dialogFormVisible.value = newValue.isShow
    if (newValue.isShow) {
      getDataList()
    }
  }
)
defineExpose({ setDialogData })
</script>
<style lang="scss" scoped>
.container-wrapper .table-content {
  padding: 0 !important;
  position: relative;
}

.supplies-settlement-detail {
  .head-setting {
    background: #f4f6fc;
    border-radius: 10px;
    width: fit-content;
    padding: 10px;
    margin-bottom: 20px;
  }
}
</style>
