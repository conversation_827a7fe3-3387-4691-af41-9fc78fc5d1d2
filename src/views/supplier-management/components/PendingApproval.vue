<template>
  <div class="container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper pt-20px" ref="tableWrapperRef">
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="loading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #apply_time="{ row }">
              {{ getNameByKey(row, "apply_time") }}
            </template>
            <template #supplier_name="{ row }">
              {{ getNameByKey(row, "supplier_name") }}
            </template>
            <template #contact_name="{ row }">
              {{ getNameBy<PERSON>ey(row, "contact_name") }}
            </template>
            <template #contact_phone="{ row }">
              {{ getName<PERSON>y<PERSON>ey(row, "contact_phone") }}
            </template>
            <template #address="{ row }">
              {{ getNameByKey(row, "address") }}
            </template>
            <template #operation="{ row }">
              <el-button plain link size="small" type="primary" @click="openHandle(row)"> 详情 </el-button>
              <el-button plain link size="small" type="primary" @click="rejectHandle(row)"> 拒绝 </el-button>
              <el-button plain link size="small" type="primary" @click="agreeHandle(row)"> 同意 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>

    <!-- 详情 -->
    <approval-detail ref="approvalDetailPendingRef" />

    <!--拒绝弹窗-->
    <el-dialog v-model="dialogRejectVisible" title="提示" width="500">
      <div class="text-left text-size-16px">确认拒绝该申请？</div>
      <el-form :model="rejectForm" :rules="rules" label-position="top" ref="formEl" class="m-t-10px">
        <el-form-item label="拒绝原因" prop="remark">
          <el-input
            v-model="rejectForm.remark"
            autocomplete="off"
            type="textarea"
            maxlength="100"
            show-word-limit
            autosize
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogRejectVisible = false">取消</el-button>
          <el-button type="primary" @click="handlerRejectConfirm" v-loading="loading" :disabled="loading">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import debounce from "lodash/debounce"
import { cloneDeep } from "lodash"
import { ref, reactive, onMounted } from "vue"
import { ElMessage, ElMessageBox } from "element-plus"
import { SEARCH_FORM_PENDINGAPPROVAL, TABLE_SETTING_PENDINGAPPROVAL } from "./constants"
import useTableHeightHook from "@/hooks/useTableHeight"
import {
  apiBackgroundFundSupervisionSupplierManageSupplierManageApplyData,
  apiBackgroundFundSupervisionSupplierManageSupplierManageApplyAgree,
  apiBackgroundFundSupervisionSupplierManageSupplierManageApplyRefuse
} from "@/api/supplier_manage/index"
import type { FormRules } from "element-plus"
import ApprovalDetail from "./ApprovalDetail.vue"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(20, 64, psTableRef, 50)
// table数据
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_PENDINGAPPROVAL))
const loading = ref(false)
const tableData = ref<any>([])
const tableSetting = cloneDeep(TABLE_SETTING_PENDINGAPPROVAL)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

// 搜索
const changeSearch = debounce(() => {
  pageConfig.currentPage = 1
  getDataList()
}, 300)
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 改变页面
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}
// 拒绝弹窗
const dialogRejectVisible = ref(false)
// 拒绝form
const rejectForm = reactive({
  remark: "",
  id: ""
})
// ref
const formEl = ref()
// 规则
const rules = reactive<FormRules<any>>({
  remark: [{ required: true, message: "请输入拒绝原因", trigger: "blur" }]
})

// search参数格式化
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    // 默认参数为空不传，false要传，空数组不传
    if (data[key].value !== "" && data[key].value !== null && data[key].value.length !== 0) {
      if (key !== "select_time") {
        params[key] = data[key].value
      } else if (data[key].value.length > 0) {
        params.apply_start_time = data[key].value[0]
        params.apply_end_time = data[key].value[1]
      }
    }
  }
  return params
}

// 获取列表
async function getDataList() {
  // tableData.value = cloneDeep(PENDING_APPROVAL_TABLE_DATA)
  loading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionSupplierManageSupplierManageApplyData({
      ...formatQueryParams(searchFormSetting.value),
      apply_result: "pending",
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  loading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  }
}

const approvalDetailPendingRef = ref()

const openHandle = (data: any) => {
  if (!data) {
    return
  }
  if (approvalDetailPendingRef.value) {
    approvalDetailPendingRef.value.setFormData(data)
  }
  approvalDetailPendingRef.value.visible = true
}

const agreeHandle = (row: any) => {
  ElMessageBox.confirm("确定同意该申请？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "info"
  })
    .then(async () => {
      if (loading.value) return
      loading.value = true
      const [err, res] = await to(
        apiBackgroundFundSupervisionSupplierManageSupplierManageApplyAgree({
          id: row.id
        })
      )
      loading.value = false
      // const [err, res] = [null, {code: 2, msg: "11111"}]
      // await sleep(3000)
      if (err) {
        return
      }
      if (res && res.code === 0) {
        ElMessage.success(res.msg)
        getDataList()
      } else {
        ElMessage.error(res.msg)
      }
    })
    .catch(() => {})
}
const rejectHandle = (row: any) => {
  rejectForm.id = row.id
  rejectForm.remark = ""
  if (formEl.value) {
    formEl.value.clearValidate()
  }
  dialogRejectVisible.value = true

  // ElMessageBox.prompt("拒绝原因", "提示", {
  //   confirmButtonText: "确定",
  //   cancelButtonText: "取消",
  //   dangerouslyUseHTMLString: true,
  //   message: `<div><div class="mb-5px">确定拒绝该申请？</div><div><span style="color: red;">*</span>拒绝原因</div></div>`,
  //   inputType: "textarea",
  //   inputPattern: /^.+$/,
  //   inputPlaceholder: "请输入拒绝原因",
  //   inputErrorMessage: "拒绝原因不能为空"
  // })
  //   .then(async ({ value }) => {
  //     if (loading.value) return
  //     loading.value = true
  //     const [err, res] = await to(
  //       apiBackgroundFundSupervisionSupplierManageSupplierManageApplyRefuse({
  //         id: row.id,
  //         reason: value
  //       })
  //     )
  //     loading.value = false
  //     // const [err, res] = [null, {code: 2, msg: "11111"}]
  //     // await sleep(3000)
  //     if (err) {
  //       return
  //     }
  //     if (res && res.code === 0) {
  //       ElMessage.success(res.msg)
  //       getDataList()
  //     }
  //   })
  //   .catch(() => {})
}

// 修改配送单状态
// const changeStatusHandle = (type: string, row: any) => {
//   confirmBefore({
//       content: type === 'delivering' ? '确定开始配送吗？' : "确定已送达吗？",
//       title: "提示"
//     },
//     async (action: any, instance: any, done: any) => {
//       if (action === "confirm") {
//         instance.confirmButtonLoading = true
//         const [err, res] = await to(apiVendorCentralVendorDeliveryInfoStatusChange({
//           id: row.id,
//           order_status: type
//         }))
//         // const [err, res] = [null, {code: 2, msg: "11111"}]
//         // await sleep(3000)
//         instance.confirmButtonLoading = false
//         if (err) {
//           return
//         }
//         if (res && res.code === 0) {
//           ElMessage.success("删除成功")
//           done()
//           getDataList()
//         }
//       } else {
//         if (!instance.confirmButtonLoading) {
//           done()
//         }
//       }
//     },
//     (action: any) => {
//     }
//   )
// }
// 确认拒绝
const handlerRejectConfirm = async () => {
  if (!formEl.value) return
  await formEl.value.validate((valid: any, fields: any) => {
    if (valid) {
      updateStatus()
    } else {
      console.log("error submit!", fields)
    }
  })
}
// 更新状态
const updateStatus = async () => {
  loading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionSupplierManageSupplierManageApplyRefuse({
      id: rejectForm.id,
      reason: rejectForm.remark
    })
  )
  loading.value = false
  dialogRejectVisible.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    ElMessage.success(res.msg || "拒绝成功")
    getDataList()
  } else {
    ElMessage.error(res.msg || "拒绝失败")
  }
}
// 获取类型名称
const getNameByKey = (data: any, key: string) => {
  const shortlistdApplicationData = data.shortlistd_application_data || {}
  const name = shortlistdApplicationData[key] || "--"
  return name
}

onMounted(() => {
  getDataList()
})
</script>

<style scoped></style>
