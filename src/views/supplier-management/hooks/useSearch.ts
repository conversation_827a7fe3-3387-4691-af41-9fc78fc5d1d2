import { cloneDeep } from "lodash"
import { ref, watch, reactive } from "vue"

const useSearch = (formSetting: any) => {
  const defaultData = ref<any[]>([])
  const tableData = ref<any[]>([])
  const settingData = ref<Map<string, any>>(new Map())
  // 监听formSetting的变化
  watch(
    formSetting,
    () => {
      // 获取formSetting里面的key以及对应的value并存一下方便后续对比筛选
      console.log("监听到变化1", formSetting)
      for (const key in formSetting) {
        settingData.value.set(key, formSetting[key].value)
      }
    },
    { deep: true, immediate: true }
  )
  // 点击筛选
  const searchHandle = () => {
    // 先找出需要筛选的key
    const keyList = reactive<string[]>([])
    settingData.value.forEach((value: any, key: string) => {
      if (value) {
        keyList.push(key)
      }
    })
    // 循环defaultData并且判断key在item中的数据是否匹配
    const newDataList = defaultData.value.filter((item: any) => {
      let flag = false
      keyList.forEach((keyItem) => {
        if (settingData.value.get(keyItem)) {
          flag = true
        } else {
          flag = false
        }
      })
      console.log("flag", flag)
      return flag
    })
    console.log("筛选出来的结果", newDataList)
    tableData.value = cloneDeep(newDataList)
  }

  return {
    defaultData,
    tableData,
    searchHandle
  }
}
export default useSearch
