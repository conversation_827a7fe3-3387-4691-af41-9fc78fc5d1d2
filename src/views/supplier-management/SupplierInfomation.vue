<template>
  <div class="flex pl-20px pr-20px">
    <!-- 左侧 -->
    <div
      class="flex-col bg-white border-rd-16px pt-20px pb-20px mr-20px overflow-hidden"
      :style="{ height: height + 'px' }"
      ref="containerRef"
    >
      <div class="pl-20px pr-20px pb-10px">
        <span>只看入围供应商</span>
        <el-switch
          class="m-l-10"
          v-model="onlyEnter"
          :active-value="1"
          :inactive-value="0"
          @change="getSupplierManageList"
        />
      </div>
      <el-input
        class="pl-20px pr-20px w-300px pb-20px"
        v-model="filterText"
        placeholder="请输入"
        @input="handleInput"
      />
      <div class="tree-container">
        <el-tree
          ref="treeRef"
          class="w-300px supplier-tree"
          :style="{ maxHeight: `${maxHeight}px` }"
          :data="treeData"
          :props="defaultProps"
          :filter-node-method="filterNode"
          @node-click="setData"
          v-loading="loading"
        >
          <template #default="{ node, data }">
            <div :class="[treeNodeId === data.id ? 'selected' : '', 'tree-node-style', 'line-1']">{{ data.name }}</div>
          </template>
        </el-tree>
      </div>
    </div>
    <!-- 右侧 -->
    <div class="right flex flex-col justify-between" ref="rightRef">
      <div v-if="isClick">
        <!--右侧顶部菜单-->
        <div class="title flex">
          <div
            v-for="(item, index) in menuList"
            :key="index"
            class="flex items-center"
            v-permission="[`${item.permission}`]"
          >
            <div
              :class="['menu-tag', currentMenu === item.value ? 'active' : 'in-active']"
              @click="handlerMenuChange(item.value)"
            >
              {{ item.name }}
            </div>
            <div class="line-gray" v-if="index > 0 && index < menuList.length - 1" />
          </div>
        </div>
        <!-- 内容 -->
        <!-- max-height="600px" -->
        <el-scrollbar :height="rightHeight - 60">
          <div v-loading="switchLoading">
            <base-info v-if="currentMenu === 'base_info'" :treeNodeData="treeNodeData" :currentTree="treeNodeId" />
            <supply-relationship
              v-if="currentMenu === 'supply_relationship'"
              :treeNodeData="treeNodeData"
              :currentTree="treeNodeId"
            />
            <supplier-evaluation
              v-if="currentMenu === 'supplier_evaluation'"
              :treeNodeData="treeNodeData"
              :currentTree="treeNodeId"
            />
            <special-inspection
              v-if="currentMenu === 'special_inspection' && false"
              :treeNodeData="treeNodeData"
              :currentTree="treeNodeId"
            />
            <supplies v-if="currentMenu === 'supplies'" :treeNodeData="treeNodeData" :currentTree="treeNodeId" />
          </div>
        </el-scrollbar>
      </div>
      <div v-else>
        <el-empty description="请先选择供应商" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import { Tree, SUPPLIER_INFORMATION_TREE_LIST, SUPERVISION_MENU_LIST } from "./constants"
import { ref, watch, reactive, onMounted } from "vue"
import { ElTree, ElMessage } from "element-plus"
import { cloneDeep, debounce } from "lodash"
import { BaseInfo, SupplyRelationship, SupplierEvaluation, SpecialInspection, Supplies } from "./components/index"
import { apiBackgroundFundSupervisionAppropriationSupplierManageListPost } from "@/api/supplier"
import useScreenHook from "@/hooks/useScreen"
import useTableHeightHook from "@/hooks/useTableHeight"
// table数据
// const tableData = ref([])
// const loading = ref(false)
// const searchFormSetting = ref(SEARCH_FORM_CERTIFICATE_WARNING)
const loading = ref(false)
// const tableSetting = cloneDeep(TABLE_SETTING_CERTIFICATE_WARNING)
const pageConfig = reactive({
  // total: 0,
  // currentPage: 1,
  // pageSize: 10
})
const isClick = ref(false)
// 搜索框
const filterText = ref("")
const containerRef = ref()
// 高度
const height = useScreenHook(containerRef, 20).maxHeight

// 节点
const treeRef = ref<InstanceType<typeof ElTree>>()
// SUPPLIER_INFORMATION_TREE_LIST
const treeData = ref<any>([])
const onlyEnter = ref<any>(0)
// let treeData: Tree[] = cloneDeep([])
const defaultProps: any = {
  children: "children",
  label: "name",
  class: "font-size-16px"
}
const filterNode = (value: string, data: Tree) => {
  if (!value) return true
  return data.label.includes(value)
}
// const formatQueryParams = (data: any) => {
//   const params: Record<string, any> = {}
//   for (const key in data) {
//     if (data[key].value !== "") {
//       if (key !== "selecttime") {
//         const value = data[key].value
//         if (value && value !== "全部") {
//           params[key] = data[key].value
//         }
//       } else if (data[key].value && data[key].value.length > 0) {
//         params.start_date = data[key].value[0]
//         params.end_date = data[key].value[1]
//       }
//     }
//   }
//   return params
// }
// 菜单列表
const menuList = cloneDeep(SUPERVISION_MENU_LIST)
// 当前选中菜单
const currentMenu = ref("")
import { useUserStoreHook } from "@/store/modules/user"
const { roles } = useUserStoreHook()
const setCurrentMenu = () => {
  if (roles.includes("background_fund_supervision.supplier_manage.supplier_manage_list")) {
    currentMenu.value = "base_info"
  } else if (roles.includes("background_fund_supervision.supplier_manage.supply_relationship_info")) {
    currentMenu.value = "supply_relationship"
  } else if (roles.includes("background_fund_supervision.supplier_manage.channel_democratic_feedback_list")) {
    currentMenu.value = "supplier_evaluation"
  } else if (roles.includes("background_fund_supervision.supplier_manage.supplier_manage_apply_materials")) {
    currentMenu.value = "supplies"
  }
}
const handlerMenuChange = (value: string) => {
  currentMenu.value = value
}

// 右侧高度
const rightRef = ref()
const rightHeight = useScreenHook(rightRef, 20).maxHeight
// 计算高度，减去顶部标题和搜索框的高度（约40px）
const { maxHeight } = useTableHeightHook(40)
// 获取当前节点数据
const treeNodeData = ref<any>({})
const treeNodeId = ref<any>(-1)
const switchLoading = ref(false)
const setData = (data: any) => {
  let cloneData = cloneDeep(data)
  switchLoading.value = true
  treeNodeId.value = data.id
  let vendorContractData = cloneData.vendor_contract.find((vendorItem: any) => vendorItem.supervision_channel_id)
  treeNodeData.value = { ...cloneDeep(cloneData), vendorContractData }
  // currentMenu.value = "base_info"
  setCurrentMenu()
  isClick.value = true
  setTimeout(() => {
    switchLoading.value = false
  }, 300)
}
onMounted(() => {
  getSupplierManageList()
})
// 获取列表
const getSupplierManageList = async () => {
  loading.value = true
  const params: Record<string, any> = {}
  if (filterText.value) {
    params.name = filterText.value
  }
  const [err, res] = await to(
    apiBackgroundFundSupervisionAppropriationSupplierManageListPost({
      // page: pageConfig.currentPage,
      // page_size: pageConfig.pageSize,
      ...params,
      only_enter: onlyEnter.value
    })
  )
  loading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    treeData.value = res.data.results
    // tableData.value = res.data.results
    // pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
const handleInput = debounce(() => {
  getSupplierManageList()
}, 500)
</script>

<style lang="scss" scoped>
.tree-node-style {
  display: block;
  width: 86%;
  padding: 14px 10px;
  border-radius: 4px;
}

.selected {
  color: #fff;
  background-color: var(--el-color-primary);
}

.right {
  width: 100%;
  flex: 1;
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;
  padding: 24px;
  margin-right: 24px;

  .title {
    height: 40px;
    background: #f4f6fc;
    padding: 4px;

    .menu-tag {
      padding: 0 12px;
      font-size: 16px;
      height: 32px;
      line-height: 32px;
      cursor: pointer;
    }

    .menu-tag:hover {
      background: #ffffff;
      color: var(--el-color-primary);
      border-radius: 4px;
    }

    .active {
      background: #ffffff;
      color: var(--el-color-primary);
      border-radius: 4px;
    }

    .in-avtive {
      background: transparent;
      color: #363636;
    }

    .line-gray {
      width: 1px;
      height: 16px;
      background: #e9ecf1;
    }
  }
}

:deep(.el-tree-node__content) {
  padding: 25px 0px !important;
}

.tree-container {
  overflow-y: auto;

  /* 可选：美化滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}

.supplier-tree {
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}
</style>
