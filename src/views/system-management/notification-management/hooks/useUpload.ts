import { apiBackgroundFileChannelUploadFile } from "@/api"
import to from "await-to-js"
import { ElMessage } from "element-plus"
import type { UploadProps, UploadRequestOptions } from "element-plus"

// 处理URL，移除tk校验参数
const processUrl = (url: string): string => {
  const isSplit = url.indexOf("?")
  return isSplit > -1 ? url.substring(0, isSplit) : url
}

// 上传前校验
const beforeUpload: UploadProps["beforeUpload"] = (file) => {
  const isLt20M = file.size / 1024 / 1024 < 20
  if (!isLt20M) {
    ElMessage.error("上传文件大小不能超过 20MB!")
    return false
  }
  return true
}

// 超出限制
const handleExceed: UploadProps["onExceed"] = (files) => {
  ElMessage.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length} 个文件`)
}

// 上传文件
const handleUpload = async (options: UploadRequestOptions) => {
  const name = options.file.name
  const [err, res]: any[] = await to(
    apiBackgroundFileChannelUploadFile({
      file: options.file,
      key: name,
      prefix: "file"
    })
  )
  if (err) {
    return { error: true }
  }

  if (res && res.code === 0) {
    const url = res.data.public_url
    return {
      error: false,
      data: {
        name,
        url: processUrl(url),
        status: "success",
        uid: options.file.uid
      }
    }
  } else {
    ElMessage.error(res.msg || "上传失败")
    return { error: true }
  }
}

// 删除图片
const handleRemove = (file: any, resource: any[]) => {
  if (file.uid) {
    return resource.filter((item: any) => item.uid !== file.uid)
  }
  return resource
}

// 预览图片
const handlePreview = (uploadFile: any) => {
  const url = uploadFile.url
  if (!url) {
    ElMessage.info("无法预览该文件")
    return null
  }

  const processedUrl = processUrl(url)
  const fileName = processedUrl.split("/").pop() || ""
  const extension = fileName.split(".").pop()?.toLowerCase()
  const supportedImageFormats = ["jpg", "png", "jpeg", "gif", "bmp", "webp"]

  if (!extension || !supportedImageFormats.includes(extension)) {
    ElMessage.info("文件不支持预览")
    return null
  }
  return processedUrl
}

export const useUpload = () => {
  return {
    beforeUpload,
    handleExceed,
    handleUpload,
    handleRemove,
    handlePreview
  }
}
