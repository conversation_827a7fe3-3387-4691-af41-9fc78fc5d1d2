<template>
  <div class="system-management container-wrapper" v-loading="mainLoading">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
        labelWidth="160px"
      />
    </div>

    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="table-button">
          <el-button type="primary" @click="onAdd">新建</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
          header-align="center"
        >
          <ps-column :table-headers="tableSetting">
            <template #receivers_name="{ row }">
              <el-button plain link size="small" type="primary" @click="checkReceiveObject(row)"> 查看 </el-button>
            </template>

            <template #read_count="{ row }">
              <text v-if="row.read_info.length > 0">
                <el-button type="primary" plain link @click="checkReadCount(row)">
                  {{ row.read_info.length }}
                </el-button>
              </text>
              <text v-else> 0 </text>
            </template>

            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="onCheck(row)" type="primary" :disabled="row.status === 0"
                >查看</el-button
              >
              <el-button plain link size="small" @click="onEditor(row)" type="primary" :disabled="row.status === 2"
                >编辑</el-button
              >
              <el-button plain link size="small" @click="onDelete(row)" type="danger" :disabled="row.status === 0"
                >删除</el-button
              >
              <el-button
                plain
                link
                size="small"
                @click="onPush(row)"
                type="success"
                :disabled="row.status !== 1 && row.status !== 3"
                >发布</el-button
              >
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>

    <!-- 新建公告/编辑公告 -->
    <form-drawer-add
      class="ps-drawer"
      ref="formDrawerRefAdd"
      v-model:visible="drawerShowAdd"
      :msg_no="msg_no"
      :isEditor="isEditor"
      @getDataList="getDataList"
    />

    <!-- 查看公告 -->
    <form-drawer-check ref="formDrawerRefCheck" v-model:visible="drawerShowCheck" :msg_no="msg_no" />

    <!-- 查看阅读人数弹窗 -->
    <el-drawer class="ps-drawer" v-model="drawerShowReadCount" title="查看人数" size="40%" :z-index="1005">
      <el-table :data="readCountData" style="width: 100%" border stripe>
        <el-table-column prop="account_name" label="账户名称" align="center" />
        <el-table-column prop="org_name" label="组织名称" align="center" />
        <el-table-column prop="view_time" label="查看时间" align="center" />
      </el-table>
      <div class="dialog-footer m-t-20px">
        <el-button type="primary" @click="drawerShowReadCount = false">关闭</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue"
import cloneDeep from "lodash/cloneDeep"
import { SEARCH_FORM_SETTING_SYSTEM_ANNOUNCEMENT, TABLE_SETTING_SYSTEM_ANNOUNCEMENT } from "./constants"
import {
  apiBackgroundSupervisionSupervisionMessagesNoticeList,
  apiBackgroundSupervisionSupervisionMessagesNoticeDelete,
  apiBackgroundSupervisionSupervisionMessagesBulkPush
} from "@/api/supervision_messages"
import to from "await-to-js"
import { ElMessage, ElMessageBox } from "element-plus"
import useTableHeightHook from "@/hooks/useTableHeight"
import FormDrawerAdd from "./components/formDrawerAdd.vue"
import FormDrawerCheck from "./components/formDrawerCheck.vue"
import { TABLEDATA } from "./data"

// 抽屉弹窗
const drawerShowAdd = ref(false)
const drawerShowCheck = ref(false)
const drawerShowReadCount = ref(false)

// table数据
const tableData = ref(TABLEDATA)
const loading = ref(false)
const mainLoading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_SYSTEM_ANNOUNCEMENT))
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_SYSTEM_ANNOUNCEMENT))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

// 表格
const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// 查看人数列表数据（模拟）
const readCountData = ref<any[]>([])

// 格式化参数
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    const keyValue = data[key].value
    if (keyValue === "") continue
    if (key === "selecttime" && keyValue && keyValue.length > 0) {
      params.start_time = keyValue[0] + " 00:00:00"
      params.end_time = keyValue[1] + " 23:59:59"
    } else if (key !== "selecttime") {
      if (key === "status" && keyValue === 99) {
        continue
      } else if (keyValue || keyValue == 0) {
        params[key] = keyValue
      }
    }
  }
  return params
}

// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundSupervisionSupervisionMessagesNoticeList({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}

// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 新建 查看、编辑、删除、发布
const formDrawerRefAdd: any = ref(null)
const formDrawerRefCheck: any = ref(null)
const msg_no = ref("")

// 新建
const onAdd = () => {
  msg_no.value = "" // 清空 msg_no 表示新建状态
  drawerShowAdd.value = true
}

// 查看
const onCheck = (row: any) => {
  msg_no.value = row.msg_no
  drawerShowCheck.value = true
}

// 编辑
const isEditor = ref(false)
const onEditor = (row: any) => {
  msg_no.value = row.msg_no // 设置 msg_no 表示编辑状态
  isEditor.value = true
  drawerShowAdd.value = true
}

// 删除
const onDelete = (row: any) => {
  ElMessageBox.confirm("确认删除?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "info"
  }).then(async () => {
    tableLoading.value = true
    const [err, res]: any[] = await to(
      apiBackgroundSupervisionSupervisionMessagesNoticeDelete({
        msg_no: row.msg_no
      })
    )
    tableLoading.value = false
    if (err) {
      ElMessage.error(err.message)
      return
    }
    if (res && res.code === 0) {
      ElMessage.success("删除成功")
      getDataList()
    } else {
      ElMessage.error(res.msg)
    }
  })
}

// 发布
const onPush = (row: any) => {
  console.log("onPush")
  ElMessageBox.confirm("确认发布?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "info"
  }).then(async () => {
    tableLoading.value = true
    const [err, res]: any[] = await to(
      apiBackgroundSupervisionSupervisionMessagesBulkPush({
        msg_nos: [Number(row.msg_no)]
      })
    )
    tableLoading.value = false
    if (err) {
      ElMessage.error(err.message)
      return
    }
    if (res && res.code === 0) {
      ElMessage.success("发布成功")
      getDataList()
    } else {
      ElMessage.error(res.msg)
    }
  })
}

// 一个弹窗显示item.receivers_name数组
const checkReceiveObject = function (item: any) {
  ElMessageBox.alert(`${item.receivers_name}`, "接收对象", {
    confirmButtonText: "关闭"
  })
}

// 查看人数
const checkReadCount = function (item: any) {
  msg_no.value = item.msg_no // 设置当前查看的公告 msg_no
  readCountData.value = item.read_info
  drawerShowReadCount.value = true // 打开抽屉
}

onMounted(() => {
  getDataList()
})
</script>

<style lang="scss" scoped>
.system-management {
  padding: 0 20px;
}
</style>
