import { getPreDate } from "@/utils/date"

export const TYPES_ANNOUNCEMENT = [
  { label: "全部", value: 99 },
  // { label: "已删除", value: 0 },
  { label: "待发布", value: 1 },
  { label: "已发布", value: 2 },
  { label: "草稿", value: 3 }
  // { label: "置顶", value: 5 }
]

// 系统公告
export const SEARCH_FORM_SETTING_SYSTEM_ANNOUNCEMENT = {
  selecttime: {
    label: "日期筛选",
    labelWidth: "80px",
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    value: [getPreDate(6, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  status: {
    type: "select",
    label: "公告状态",
    labelWidth: "80px",
    value: 99,
    dataList: [...TYPES_ANNOUNCEMENT],
    placeholder: "请选择",
    clearable: true
  },
  title: {
    type: "input",
    label: "公告标题",
    labelWidth: "80px",
    value: "",
    placeholder: "请输入",
    clearable: true,
    maxlength: 20
  },
  sender: {
    type: "input",
    label: "创建人",
    labelWidth: "80px",
    value: "",
    placeholder: "请输入",
    clearable: true,
    maxlength: 20
  }
}

// 系统公告
export const TABLE_SETTING_SYSTEM_ANNOUNCEMENT = [
  {
    label: "创建时间",
    prop: "create_time",
    align: "center",
    width: "120px"
  },
  {
    label: "公告标题",
    align: "center",
    prop: "title"
  },
  {
    label: "接收对象",
    align: "center",
    prop: "receivers_name",
    slot: "receivers_name"
  },
  {
    label: "状态",
    align: "center",
    prop: "status_alias"
  },
  {
    label: "查看人数",
    align: "center",
    prop: "read_count",
    slot: "read_count"
  },
  {
    label: "发布时间",
    align: "center",
    prop: "post_time"
  },
  {
    label: "修改时间",
    align: "center",
    prop: "update_time"
  },
  {
    label: "创建人",
    align: "center",
    prop: "sender_name"
  },
  {
    label: "操作",
    align: "center",
    fixed: "right",
    slot: "operationNew"
  }
]
