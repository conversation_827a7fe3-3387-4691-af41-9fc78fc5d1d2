export type TSystemFrom = {
  [key: string]: any // 索引签名，允许动态添加任意字段
  msg_no?: string // 消息编号
  status: number // 状态
  receivers_name?: array[string] // 接收对象合集
  title: string // 名称
  content: string // 富文本内容
  receiver_org: Array[number] // 组织列表id
  receiver_supplier: Array[number] // 供应商id
  post_time: string | undefined | date // 发布时间
  resource: Array[string] // 上传文件列表
  use_post_time: boolean // 是否使用发布时间
}

interface TSystemForm {
  id: string
}
