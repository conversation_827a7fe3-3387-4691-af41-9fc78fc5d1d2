<template>
  <div>
    <el-drawer
      class="ps-drawer"
      v-model="visibleCurrent"
      title="查看公告"
      size="50%"
      :z-index="1005"
      :before-close="handleClose"
    >
      <div>
        <h2>{{ formData.title }}</h2>
        <div class="html-content" v-html="formData.content" />
        <div class="img-box">
          <div v-for="(item, index) in imagesList" :key="index">
            <div class="img-content">
              <el-image :src="item.url" class="image" :preview-src-list="[item.url]" />
              <el-icon class="icon" size="18" @click.prevent="downloadFileWithFileSaver(item.url)">
                <Download />
              </el-icon>
            </div>
          </div>
        </div>
        <div class="img-box">
          <div v-for="(item, index) in filesList" :key="index" class="other-content">
            <div>{{ item.name }}</div>
            <el-icon class="icon" size="18" @click.prevent="downloadFileWithFileSaver(item.url)">
              <Download />
            </el-icon>
          </div>
        </div>
      </div>

      <div class="dialog-footer m-t-20px">
        <el-button type="primary" @click="handleClose">关闭</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
export default defineComponent({
  name: "FormDrawerCheck"
})
</script>

<script setup lang="ts">
import { defineComponent } from "vue"
import { TSystemFrom } from "../index"
import to from "await-to-js"
import { ElMessage } from "element-plus"
import { computed, onMounted, reactive, ref, watch } from "vue"
import { apiBackgroundSupervisionSupervisionMessagesDetails } from "@/api/supervision_messages"
import { saveAs } from "file-saver"

interface FileItem {
  name: string
  url: string
  status: string
  uid: number
}

// 图片文件类型
const IMAGE_EXTENSIONS = ["jpeg", "jpg", "png", "gif"] as const

// 处理URL，移除tk校验参数
const processUrl = (url: string): string => {
  const isSplit = url.indexOf("?")
  return isSplit > -1 ? url.substring(0, isSplit) : url
}

// 创建文件对象
const createFileItem = (url: string): FileItem => {
  const processedUrl = processUrl(url)
  const fileName = processedUrl.split("/").pop() || ""
  return {
    name: fileName,
    url: processedUrl,
    status: "success",
    uid: Date.now() + Math.random()
  }
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  msg_no: {
    type: String,
    default: ""
  }
})

let formData = reactive<TSystemFrom>({
  msg_no: "",
  status: 1,
  title: "",
  content: "",
  receiver_org: [],
  receiver_supplier: [],
  post_time: "",
  resource: [],
  use_post_time: false
})

const emit = defineEmits(["update:visible"])
const visibleCurrent = computed({
  get() {
    return props.visible
  },
  set(val: any) {
    emit("update:visible", val)
  }
})

// 区分不同文件类型
let imagesList = ref<FileItem[]>([])
let filesList = ref<FileItem[]>([])

watch(
  () => formData.resource,
  (newUrls: string[]) => {
    const { images, files } = separateFiles(newUrls || [])
    imagesList.value = images.map(createFileItem)
    filesList.value = files.map(createFileItem)
  }
)

// 监听抽屉显示状态
watch(
  () => props.visible,
  (newVal: Boolean) => {
    if (newVal) {
      // 查看公告
      getNoticeDetails()
    }
  }
)

// 区分图片和其他文件类型
function separateFiles(urls: string[]) {
  const images: string[] = []
  const files: string[] = []

  urls.forEach((url) => {
    const fileName = url.split("/").pop() || ""
    const extension = fileName.split(".").pop()?.toLowerCase()
    if (extension && IMAGE_EXTENSIONS.includes(extension as any)) {
      images.push(url)
    } else {
      files.push(url)
    }
  })

  return { images, files }
}

// 获取公告详情
const getNoticeDetails = async () => {
  const [err, res]: any[] = await to(apiBackgroundSupervisionSupervisionMessagesDetails({ msg_no: props.msg_no }))
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    // 使用 Object.assign 保持响应式
    Object.assign(formData, res.data)
  } else {
    ElMessage.error(res.msg)
  }
}

// 下载文件
const downloadFileWithFileSaver = async (url: string) => {
  try {
    const fileName = url.split("/").pop() || "download"
    const response = await fetch(url)
    const blob = await response.blob()
    saveAs(blob, fileName)
  } catch (error) {
    console.error("Error downloading file:", error)
    ElMessage.error("文件下载失败")
  }
}

onMounted(() => {})

// 关闭
const handleClose = () => {
  visibleCurrent.value = false
}

defineExpose({})
</script>

<style lang="scss" scoped>
/* 在这里添加你的样式 */
.html-content {
  width: 100%;
  min-height: 300px;
  word-wrap: break-word !important;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.img-box {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
  .img-content {
    width: 200px;
    border-radius: 10px;
    position: relative;
    padding: 20px;
    .image {
      width: 100%;
      height: 150px;
      border-radius: 4px;
    }
    .icon {
      position: absolute;
      z-index: 999;
      cursor: pointer;
      top: 20px;
      right: 20px;
      z-index: 999;
      background: #666;
      color: #fff;
    }
  }
  .other-content {
    min-width: 300px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 10px;
    padding: 10px;
    border-radius: 4px;
    align-items: center;
    background-color: #f5f5f5;
    .icon {
      cursor: pointer;
    }
  }
}

:deep(.el-drawer__header) {
  margin-bottom: 0 !important;
}
</style>
