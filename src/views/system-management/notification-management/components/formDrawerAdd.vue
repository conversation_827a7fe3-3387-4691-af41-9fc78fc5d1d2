<template>
  <div>
    <el-drawer
      class="ps-drawer"
      v-model="visibleCurrent"
      :title="isEditor ? '编辑公告' : '新建公告'"
      size="50%"
      :z-index="1005"
      :before-close="handleClose"
    >
      <el-form :model="formData" ref="formDataRef" label-width="100px" :rules="rules">
        <el-form-item prop="title" label="公告标题">
          <el-input v-model="formData.title" placeholder="请输入标题" style="width: 300px" maxlength="20" />
        </el-form-item>

        <el-form-item label="接收组织" class="flex flex-align-center">
          <supervision-orgs
            ref="supervisionOrgsRef"
            v-model="formData.receiver_org"
            :multiple="true"
            :collapse-tags="true"
            :clearable="true"
            :isShowChildOrs="true"
            :filterable="true"
            placeholder="请选择"
            width="300px"
            style="margin-right: 20px"
            @change="onChangeOrgs"
            @callBackFunc="checkOrgsSelectStatus"
          />
          <div>
            <el-checkbox v-model="isAllOrgId" label="全选" size="large" @click="handleAllSelect('receiver_org')" />
          </div>
        </el-form-item>

        <el-form-item label="接收供应商" class="flex flex-align-center">
          <el-select
            class="search-item-w"
            v-model="formData.receiver_supplier"
            placeholder="请选择"
            style="width: 300px; margin-right: 20px"
            multiple
            collapse-tags
            clearable
            filterable
            @change="onChangeSupplierIds"
          >
            <el-option v-for="item in supplierList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
          <div>
            <el-checkbox
              v-model="isAllSupplierId"
              label="全选"
              size="large"
              @click="handleAllSelect('receiver_supplier')"
            />
          </div>
        </el-form-item>

        <el-form-item label="公告正文" prop="content" required>
          <tinymce-editor ref="editorRef" v-model:modelValue="formData.content" />
        </el-form-item>

        <el-form-item label="公告附件" prop="resource ">
          <el-upload
            v-model:file-list="formData.resource"
            multiple
            :http-request="handleUploadFile"
            :before-upload="beforeUpload"
            :on-remove="onRemove"
            :on-exceed="handleExceed"
            :on-preview="onPreview"
            :limit="3"
            style="width: 100%"
          >
            <div style="display: flex; align-items: flex-end">
              <el-button type="primary">点击上传</el-button>
              <div style="line-height: 1; margin-left: 10px; color: #666">附件不能超过20M</div>
            </div>
          </el-upload>
        </el-form-item>

        <!-- <el-form-item label="发布时间" prop="post_time" class="flex flex-align-center">
          <el-radio-group v-model="formData.use_post_time" @change="onChangeTime(formData.use_post_time)">
            <el-radio :value="false" size="large">立即发布</el-radio>
            <el-radio :value="true" size="large">定时发布</el-radio>
          </el-radio-group>
          <div v-if="formData.use_post_time" style="margin-left: 20px">
            <el-date-picker
              v-model="formData.post_time"
              type="datetime"
              placeholder="请选择发布时间"
              :disabled-date="disabledDate"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </div>
        </el-form-item> -->
      </el-form>

      <div class="dialog-footer m-t-20px">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit(formDataRef, formData.use_post_time ? 1 : 2)">保存</el-button>
        <el-button type="primary" @click="handleSubmit(formDataRef, 3)">保存不发布</el-button>
      </div>

      <!-- 图片预览-->
      <image-preview v-model="imageVisible" :imgs="imageList" />
    </el-drawer>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue"
import { checkArrayEquality } from "@/utils/tools"
import { TSystemFrom } from "../index"
export default defineComponent({
  name: "FormDrawerAdd"
})
</script>

<script setup lang="ts">
import to from "await-to-js"
import { FormInstance, ElMessage, FormRules, UploadProps, UploadRequestOptions } from "element-plus"
import { computed, nextTick, onMounted, reactive, ref, watch } from "vue"
import TinymceEditor from "@/components/Tinymce/index.vue"
import SupervisionOrgs from "@/components/SupervisionOrgs/index.vue"
import { apiBackgroundFundSupervisionAppropriationSupplierManageListPost } from "@/api/supplier"
import {
  apiBackgroundSupervisionSupervisionMessagesNoticeAdd,
  apiBackgroundSupervisionSupervisionMessagesNoticeModify,
  apiBackgroundSupervisionSupervisionMessagesDetails
} from "@/api/supervision_messages"
import { useUpload } from "../hooks/useUpload"

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  msg_no: {
    type: String,
    default: ""
  },
  isEditor: {
    type: Boolean,
    default: false
  }
})

// 表单校验
const formDataRef = ref<FormInstance>()
const rules = reactive<FormRules>({
  title: [
    {
      message: "标题不能为空",
      trigger: "blur",
      required: true
    }
  ],
  content: [
    {
      required: true,
      message: "公告正文不能为空",
      trigger: ["blur", "change"],
      validator: (rule: any, value: any, callback: any) => {
        if (!formData.content) {
          callback(new Error("公告正文不能为空"))
        }
        callback()
      }
    }
  ]
  // post_time: [
  //   {
  //     message: "定时发布不能小于当前时间",
  //     trigger: "change",
  //     validator: (rule: any, value: any, callback: any) => {
  //       // 当 use_post_time 为 false 时，不执行此验证
  //       if (!formData.use_post_time) {
  //         callback()
  //         return
  //       }
  //       if (new Date(value).getTime() < new Date().getTime()) {
  //         callback(new Error("不能小于当前时间"))
  //       } else {
  //         callback()
  //       }
  //     }
  //   }
  // ]
})

// 重置发布时间验证状态
// const onChangeTime = function (value: boolean) {
//   if (!value && formDataRef.value) {
//     formDataRef.value.clearValidate("use_post_time")
//   }
// }

// 设置日期范围
// const disabledDate = (time: Date) => {
//   const now = new Date()
//   now.setHours(0, 0, 0, 0)
//   return time.getTime() < now.getTime()
// }

const emit = defineEmits(["update:visible", "getDataList"])
const visibleCurrent = computed({
  get() {
    return props.visible
  },
  set(val) {
    emit("update:visible", val)
  }
})

// 富文本编辑器
const editorRef: any = ref(null)

// 表单
let formData = reactive<TSystemFrom>({
  msg_no: "",
  status: 1,
  title: "",
  content: "",
  receiver_org: [],
  receiver_supplier: [],
  post_time: "",
  resource: [] as string[],
  use_post_time: false
})

// 初始化表单、富文本内容数据
const initFormData = () => {
  Object.assign(formData, {
    msg_no: "",
    title: "",
    content: "",
    receiver_org: [],
    receiver_supplier: [],
    post_time: "",
    resource: [],
    use_post_time: false
  })

  if (editorRef.value) {
    editorRef.value.handleSetContent(formData.content)
  }
}

onMounted(() => {
  // 获取接收供应商列表
  getSupervisionsList()
})

// 监听抽屉显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      if (props.msg_no) {
        // 编辑状态
        getNoticeDetails()
      } else {
        // 新建状态
        initFormData()
      }
    }
  }
)

// 判断组织、供应商 是否全选
const supplierList = ref<Array<any>>() // 供应商列表
const supervisionOrgsRef: any = ref(null) // 监管组织选择器
const isAllSupplierId = ref(false) // 是否全选供应商
const isAllOrgId = ref(false) // 是否全选监管组织

const handleAllSelect = (type: string) => {
  const isAllSelected = type === "receiver_org" ? isAllOrgId : isAllSupplierId
  const formDataKey = type === "receiver_org" ? "receiver_org" : "receiver_supplier"
  const list = type === "receiver_org" ? supervisionOrgsRef.value?.selectData : supplierList.value
  formData[formDataKey] = isAllSelected.value ? [] : list?.map((item: any) => item.id) || []
}

// 组织下拉列表回调
function checkOrgsSelectStatus(list: []) {
  const orgIds = formData.receiver_org
  if (list.length > 0) {
    isAllOrgId.value = checkArrayEquality(orgIds, list)
  }
}

const onChangeOrgs = function (orgIds: []) {
  const selectedOrgIds = supervisionOrgsRef.value?.selectData?.map((item: any) => item.id) || []
  if (selectedOrgIds.length > 0) {
    isAllOrgId.value = checkArrayEquality(orgIds, selectedOrgIds)
  }
}

const onChangeSupplierIds = function (suppliers: []) {
  const allSupplierIds = supplierList.value?.map((item) => item.id) || []
  if (allSupplierIds.length > 0) {
    isAllSupplierId.value = checkArrayEquality(suppliers, allSupplierIds)
  }
}

// 使用上传钩子
const { beforeUpload, handleUpload, handleExceed, handleRemove, handlePreview } = useUpload()

// 图片预览
const imageVisible = ref(false)
const imageList = ref<string[]>([])

// 处理URL，移除tk校验参数
const processUrl = (url: string): string => {
  const isSplit = url.indexOf("?")
  return isSplit > -1 ? url.substring(0, isSplit) : url
}

// 上传文件
const handleUploadFile = async (options: UploadRequestOptions) => {
  try {
    const result = await handleUpload(options)
    if (!result.error && result.data) {
      // 检查是否已存在相同uid的文件
      const existingIndex = formData.resource.findIndex((item: any) => item.uid === result.data.uid)
      if (existingIndex > -1) {
        // 如果存在，则更新该文件
        formData.resource.splice(existingIndex, 1, result.data)
      } else {
        // 如果不存在，则添加新文件
        formData.resource.push(result.data)
      }
    } else {
      // 上传失败时，移除对应的文件
      removeFileByUid(options.file.uid)
    }
  } catch (error) {
    // 发生错误时，移除对应的文件
    removeFileByUid(options.file.uid)
  }
}

// 根据uid移除文件
const removeFileByUid = (uid: string | number) => {
  const index = formData.resource.findIndex((item: any) => item.uid === uid)
  if (index > -1) {
    formData.resource.splice(index, 1)
  }
}

// 删除图片
const onRemove: UploadProps["onRemove"] = (file: any) => {
  formData.resource = handleRemove(file, formData.resource)
}

// 预览图片
const onPreview: UploadProps["onPreview"] = (uploadFile: any) => {
  const previewUrl = handlePreview(uploadFile)
  if (previewUrl) {
    imageList.value = [previewUrl]
    imageVisible.value = true
  }
}

// 获取公告详情
const getNoticeDetails = async () => {
  const [err, res]: any[] = await to(apiBackgroundSupervisionSupervisionMessagesDetails({ msg_no: props.msg_no }))
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    // 使用 Object.assign 保持响应式
    Object.assign(formData, res.data)
    // 为文件补充必要的属性
    if (formData.resource && formData.resource.length > 0) {
      formData.resource = formData.resource.map((url: string) => {
        const processedUrl = processUrl(url)
        const fileName = processedUrl.split("/").pop() || ""
        return {
          url: processedUrl,
          name: fileName,
          status: "success",
          uid: Date.now() + Math.random()
        }
      })
    }
    // 重置全选状态
    isAllSupplierId.value = false
    isAllOrgId.value = false
    // 检查供应商和组织选择状态
    nextTick(() => {
      onChangeSupplierIds(formData.receiver_supplier)
      onChangeOrgs(formData.receiver_org)

      // 发布时间单选框
      // formData.use_post_time = formData.post_time ? true : false
      formData.use_post_time = false

      // 确保编辑器实例已初始化
      if (editorRef.value) {
        // 设置富文本内容
        editorRef.value.handleSetContent(formData.content)
      }
    })
  } else {
    ElMessage.error(res.msg)
  }
}

// 获取供应商列表
const getSupervisionsList = async () => {
  const [err, res]: any[] = await to(apiBackgroundFundSupervisionAppropriationSupplierManageListPost({ only_enter: 0 }))
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    let results = data.results || []
    supplierList.value = results || []
  } else {
    ElMessage.error(res.msg)
  }
}

// 关闭
const handleClose = () => {
  visibleCurrent.value = false
  // 重置全选状态
  isAllSupplierId.value = false
  isAllOrgId.value = false
  // 重置表单数据
  initFormData()
}

// 提交表单
const saveLoading = ref(false)
const handleSubmit = async (formEl: FormInstance | undefined, statusType: number) => {
  if (formData.receiver_supplier.length === 0 && formData.receiver_org.length === 0) {
    ElMessage.error("请至少填写一处'接收供应商、接收组织'")
    return
  }

  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      saveLoading.value = true
      if (!formData.use_post_time) {
        formData.post_time = undefined
      }

      let requestApi = apiBackgroundSupervisionSupervisionMessagesNoticeAdd
      if (props.msg_no) {
        requestApi = apiBackgroundSupervisionSupervisionMessagesNoticeModify
        formData.msg_no = props.msg_no
      } else {
        formData.msg_no = undefined
      }

      // 发布状态
      formData.status = statusType

      // if (formData.use_post_time) {
      //   if (!formData.post_time) {
      //     ElMessage.error("定时发布时间不能为空")
      //     return
      //   } else if (new Date(formData.post_time).getTime() < new Date().getTime()) {
      //     ElMessage.error("定时发布不能小于当前时间")
      //     return
      //   }
      // }

      // 只保留URL数组
      const submitData = {
        ...formData,
        resource: formData.resource.map((item: any) => item.url)
      }

      const [err, res]: any[] = await to(requestApi(submitData))
      saveLoading.value = false
      if (err) {
        ElMessage.error(err.message)
        return
      }
      if (res && res.code === 0) {
        ElMessage.success("保存成功")
        emit("getDataList")
        handleClose()
      } else {
        ElMessage.error(res.msg)
        return
      }
    } else {
      console.log("error submit!", fields)
    }
  })
}
</script>

<style lang="scss" scoped>
/* 在这里添加你的样式 */
.el-icon.avatar-uploader-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 280px;
  font-size: 64px;
  color: #e9ecf1;
}

:deep(.el-drawer__header) {
  margin-bottom: 0 !important;
}
</style>
