<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import useTableHeightHook from "@/hooks/useTableHeight"
const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

const searchFormSetting = ref<any>({
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "日期筛选",
    labelWidth: "100px",
    value: [dayjs().subtract(1, "week").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  message_type: {
    type: "select",
    label: "类型",
    value: "全部",
    multiple: false,
    clearable: false,
    collapseTags: true,
    placeholder: "请选择",
    dataList: [
      {
        label: "全部",
        value: "全部"
      },
      {
        label: "普通公告",
        value: 0
      },
      {
        label: "问卷公告",
        value: 1
      }
    ]
  },
  read_flag: {
    type: "select",
    label: "状态",
    value: "全部",
    multiple: false,
    clearable: false,
    collapseTags: true,
    placeholder: "请选择",
    dataList: [
      {
        label: "全部",
        value: "全部"
      },
      {
        label: "未读",
        value: false
      },
      {
        label: "已读",
        value: true
      }
    ]
  }
})
const changeSearch = async () => {
  pageConfig.currentPage = 1
  getTableData()
}
const handlerReset = () => {
  pageConfig.currentPage = 1
  getTableData()
}

import {
  apiBackgroundSupervisionSupervisionMessagesGetQuestionnaireMsgListPost,
  apiBackgroundSupervisionSupervisionMessagesBulkQuestionnaireMsgReadPost
} from "@/api/system"
import dayjs from "dayjs"
import to from "await-to-js"
import { cloneDeep } from "lodash"
import { ElMessage } from "element-plus"
const tableData = ref<any>([])
const loading = ref(false)
const tableLoading = ref(false)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
const getTableData = async () => {
  tableLoading.value = true
  const [err, res]: any = await to(
    apiBackgroundSupervisionSupervisionMessagesGetQuestionnaireMsgListPost({
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize,
      start_date: searchFormSetting.value.selecttime.value[0] + " 00:00:00",
      end_date: searchFormSetting.value.selecttime.value[1] + " 23:59:59",
      message_type:
        searchFormSetting.value.message_type.value === "全部" ? undefined : searchFormSetting.value.message_type.value,
      read_flag:
        searchFormSetting.value.read_flag.value === "全部" ? undefined : searchFormSetting.value.read_flag.value
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res.code === 0) {
    pageConfig.total = res.data.count
    tableData.value = cloneDeep(res.data.results)
  } else {
    ElMessage.error(res.msg)
  }
}
onMounted(() => {
  getTableData()
})
const tableSetting = [
  { label: "公告标题", prop: "title" },
  { label: "通知类型", prop: "message_type_alias" },
  { label: "来源", prop: "sender_name" },
  { label: "状态", prop: "read_flag" },
  { label: "操作", prop: "operation", slot: "operation" }
]
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getTableData()
}

// 填写问卷
import { useRouter } from "vue-router"
const router = useRouter()
const gotoFillIn = async (row: any) => {
  const [err, res]: any[] = await to(
    apiBackgroundSupervisionSupervisionMessagesBulkQuestionnaireMsgReadPost({
      msg_nos: [row.msg_no]
    })
  )
  if (err) return
  if (res.code === 0) {
    if (row.message_type === 1) {
      const { href } = router.resolve({
        path: "/questionnaire-detail",
        query: {
          id: parseInt(row.questionnaire_id)
        }
      })
      window.open(href, "_blank")
    } else {
      ElMessage.error("该公告不是问卷")
    }
  }
}
</script>

<template>
  <div class="container-wrapper p-l-20px p-r-20px">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <!-- <div class="table-button">
          <el-button type="primary">导出</el-button>
          <el-button type="primary">打印</el-button>
        </div> -->
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operation="{ row }">
              <el-button link size="small" type="primary" @click="gotoFillIn(row)">去填写</el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
