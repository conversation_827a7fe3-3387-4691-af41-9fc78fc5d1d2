<template>
  <div class="role-management">
    <div class="left">
      <channel-tree @tree-click="handlerTreeChange" />
    </div>
    <div class="container-wrapper right">
      <div ref="searchRef">
        <search-form
          :form-setting="searchFormSetting"
          @change-search="changeSearch"
          @reset="handlerReset"
          v-loading="loading"
          search-mode="normal"
        />
      </div>
      <div class="table-wrapper" ref="tableWrapperRef">
        <div class="table-header">
          <div class="flex items-center" />
          <div class="table-button">
            <el-button type="primary" class="ps-origin-btn-plain" @click="handlerShowRecord()">历史记录</el-button>
            <el-button
              type="primary"
              @click="handlerShowAddDialog('add', null)"
              v-permission="['background_fund_supervision.channel_role.add']"
              >新增</el-button
            >
          </div>
        </div>
        <div class="table-content">
          <ps-table
            :tableData="tableData"
            ref="psTableRef"
            v-loading="tableLoading"
            :show-pagination="true"
            @pagination-change="handleCurrentChange"
            :pageConfig="pageConfig"
            :max-height="maxHeight"
          >
            <ps-column :table-headers="tableSetting">
              <template #operationNew="{ row }">
                <div v-if="row.name !== '超级管理员'" class="flex justify-center">
                  <el-button
                    plain
                    link
                    size="small"
                    @click="handlerShowAddDialog('edit', row)"
                    type="primary"
                    v-permission="['background_fund_supervision.channel_role.modify']"
                  >
                    编辑
                  </el-button>
                  <div v-if="row.account_num < 1">
                    <el-button
                      plain
                      link
                      size="small"
                      @click="handlerDelete(row)"
                      color="#ff5656"
                      type="primary"
                      v-permission="['background_fund_supervision.channel_role.delete']"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
              </template>
              <template #status="{ row }">
                <el-switch
                  v-model="row.status"
                  :disabled="
                    row.name === '超级管理员' ||
                    !rules.includes('background_fund_supervision.channel_role.status_change')
                  "
                  active-value="enable"
                  inactive-value="disable"
                  :before-change="() => handlerSwitchBeforeChange(row)"
                />
              </template>
            </ps-column>
          </ps-table>
        </div>
      </div>
    </div>

    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
    <!--弹窗-->
    <role-add-or-edit-dialog
      ref="addDialogRef"
      :is-show="isShowAddDialog"
      :type="dialogType"
      :title="dialogTitle"
      :channnel-id="chanelId"
      :permissionList="permissionList"
      :organizations="organizations"
      :width="'65%'"
      @cancel-dialog="handlerClose"
      @confirm-dialog="handlerConfirm"
    />
    <!--历史记录 -->
    <record-dialog
      ref="recordDialogRef"
      :is-show="isShowRecordDialog"
      :type="dialogType"
      :title="dialogTitle"
      @cancel-dialog="handlerClose"
      @confirm-dialog="handlerConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import { SEARCH_FORM_SETTING_ROLE_MANAGEMENT, TABLE_SETTING_ROLE_MANAGEMENT } from "./constants"
import {
  apiBackgroundFundSupervisionChannelRoleListPost,
  apiBackgroundFundSupervisionChannelRoleDeletePost,
  apiBackgroundFundSupervisionChannelRoleStatusChangePost
} from "@/api/user"
import { ElMessage } from "element-plus"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
// import { exportHandle } from "@/utils/exportExcel"
import { confirmBefore, confirm } from "@/utils/message"
import RecordDialog from "./compontents/RecordDialog.vue"
import ChannelTree from "@/components/ChannelTree/index.vue"
import RoleAddOrEditDialog from "./compontents/RoleAddOrEditDialog.vue"

import { useUserStore } from "@/store/modules/user"
const userStore = useUserStore()
const rules = userStore.roles

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])
// 渠道Id
const chanelId = ref()

// table数据
const tableData = ref([])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_ROLE_MANAGEMENT))
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_ROLE_MANAGEMENT)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 弹窗
const isShowAddDialog = ref(false)
const isShowRecordDialog = ref(false)
const permissionList = ref<string[]>([])
const organizations = ref<any[]>([]) // 组织
const dialogType = ref("")
const dialogTitle = ref("")
const addDialogRef = ref()
const recordDialogRef = ref()

// 导出
// const importType = "FoodSafetyTraceabilityExport"
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "feature") {
        const value = data[key].value
        if (value && value != "全部") {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.feature = data[key].value
      }
    }
  }
  return params
}
// 搜索
const changeSearch = () => {
  if (!chanelId.value) {
    return ElMessage.warning("请选择渠道")
  }
  pageConfig.currentPage = 1
  getDataList()
}

// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 导出
// const goToExport = () => {
//   console.log("goToExport")
//   const option = {
//     type: importType,
//     api: apiBackgroundFundSupervisionChannelRoleListPost,
//     params: {
//       ...formatQueryParams(searchFormSetting.value),
//       page: pageConfig.currentPage,
//       page_size: pageConfig.pageSize
//     }
//   }
//   exportHandle(option)
// }

// 反馈详情
const handlerShowDetail = (row: any) => {
  console.log(row)
  dialogTitle.value = "详情"
  dialogType.value = "detail"
  if (addDialogRef.value) {
    addDialogRef.value.setDialogData(row)
  }
  isShowRecordDialog.value = true
}

// 弹窗关闭
const handlerClose = () => {
  isShowRecordDialog.value = false
  isShowAddDialog.value = false
  organizations.value = []
}
// 弹窗确认
const handlerConfirm = (value: any) => {
  isShowRecordDialog.value = false
  isShowAddDialog.value = false
  organizations.value = []
  if (value) {
    getDataList()
  }
}
// 切换开关
const handlerSwitchBeforeChange = async (row: any): Promise<boolean> => {
  console.log("handlerSwitchBeforeChange", row)
  const message =
    !row.status || row.status !== "enable"
      ? "启用后，该角色所关联账号需到【账号管理】中重新启用"
      : "该角色所关联账号也会同时禁用，是否继续？"
  const messageTiltle = !row.status || row.status !== "enable" ? "启用" : "禁用"
  return new Promise((resolve) => {
    confirm(
      {
        content: message,
        title: messageTiltle
      },
      async (action: any) => {
        if (action === "confirm") {
          row.loading = true
          let [err, res]: any[] = await to(
            apiBackgroundFundSupervisionChannelRoleStatusChangePost({
              ids: [row.id],
              status: !row.status || row.status !== "enable" ? "enable" : "disable"
            })
          )
          row.loading = false
          if (err) {
            return resolve(false)
          }
          if (res && res.code === 0) {
            ElMessage.success("修改状态成功")
            getDataList()
            resolve(true)
          } else {
            ElMessage.error(res.msg || "修改状态失败")
            resolve(false)
          }
        } else {
          resolve(false)
        }
      }
    )
  })
}
// 显示历史记录
const handlerShowRecord = () => {
  dialogTitle.value = "历史记录"
  dialogType.value = "roleRecord"
  isShowRecordDialog.value = true
  console.log("handlerShowRecord")
}
// 新增修改角色
const handlerShowAddDialog = (type: string, row: any) => {
  dialogTitle.value = type === "add" ? "新建角色" : "编辑角色"
  dialogType.value = type
  if (addDialogRef.value) {
    addDialogRef.value.setDialogData(row)
  }
  if (type === "edit") {
    permissionList.value = cloneDeep(row.permission)
    organizations.value = cloneDeep(row.organizations)
  } else {
    permissionList.value = []
  }
  isShowAddDialog.value = true
}
// 删除
const handlerDelete = (row: any) => {
  if (!row.can_delete) {
    ElMessage.warning("该角色下有账号，不允许删除")
    return
  }
  console.log("handleDelete", row)
  confirmBefore(
    {
      content: "是否删除该角色？",
      title: "提示"
    },
    async (action: any, instance: any, done: any) => {
      if (action === "confirm") {
        instance.confirmButtonLoading = true
        const flag = await deleteRole(row.id)
        console.log("flag", flag)

        instance.confirmButtonLoading = false
      }
      done()
    },
    (action: any) => {
      if (action === "confirm") {
        getDataList()
      }
    }
  )
}
// 删除角色
const deleteRole = (id: number): Promise<boolean> => {
  return new Promise((resolve) => {
    apiBackgroundFundSupervisionChannelRoleDeletePost({
      id: id
    })
      .then((res: any) => {
        console.log("setPaySettings", res)
        if (res && res.code === 0) {
          ElMessage.success("删除成功")
          resolve(true)
        } else {
          ElMessage.error(res.msg || "删除失败")
          resolve(false)
        }
      })
      .catch((error: any) => {
        ElMessage.error(error.message || "删除失败")
        console.log("error", error)
        resolve(false)
      })
  })
}
// 树选择改变
const handlerTreeChange = (val: any) => {
  console.log("handlerTreeChange", val)
  chanelId.value = val.id || ""
  getDataList()
}

onMounted(() => {})
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionChannelRoleListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize,
      supervision_channel_ids: [chanelId.value]
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
</script>
<style lang="scss">
.role-management {
  padding: 0 20px 0 0;
  display: flex;
  .right {
    flex: 1;
  }

  .el-popper {
    max-width: 300px;
  }
}
</style>
