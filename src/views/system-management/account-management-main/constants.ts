// 账号管理筛选设置
export const SEARCH_FORM_SETTING_ACCOUNT_MANAGEMENT = {
  role_ids: {
    type: "select",
    label: "所属角色",
    value: [],
    multiple: true,
    clearable: true,
    placeholder: "请选择",
    listNameKey: "name",
    listValueKey: "id",
    dataList: [],
    collapseTags: true
  },
  name: {
    type: "input",
    label: "账号名称",
    value: "",
    placeholder: "请输入账号名称",
    clearable: true,
    maxlength: 20
  },
  username: {
    type: "input",
    label: "账号",
    value: "",
    placeholder: "请输入账号",
    clearable: true,
    maxlength: 20
  },
  status: {
    type: "select",
    label: "状态",
    value: "全部",
    multiple: false,
    clearable: false,
    placeholder: "请选择",
    dataList: [
      {
        value: "全部",
        label: "全部"
      },
      {
        value: "1",
        label: "启用"
      },
      {
        value: "0",
        label: "禁用"
      }
    ]
  }
}
// 账号管理表格设置
export const TABLE_SETTING_ACCOUNT_MANAGEMENT = [
  {
    label: "账号名称",
    prop: "name"
  },
  {
    label: "账号",
    prop: "username"
  },
  {
    label: "手机号",
    prop: "mobile"
  },
  {
    label: "角色",
    prop: "role_name"
  },
  {
    label: "状态",
    prop: "status",
    slot: "status"
  },
  {
    label: "创建时间",
    prop: "create_time"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operationNew",
    align: "center"
  }
]

// 角色管理筛选设置
export const SEARCH_FORM_SETTING_ROLE_MANAGEMENT = {
  name: {
    type: "input",
    label: "角色名称",
    value: "",
    placeholder: "请输入角色名称",
    clearable: true,
    maxlength: 20
  },
  status: {
    type: "select",
    label: "状态",
    value: "enable",
    multiple: false,
    clearable: false,
    placeholder: "请选择",
    dataList: [
      {
        value: "enable",
        label: "启用"
      },
      {
        value: "disable",
        label: "禁用"
      },
      {
        value: "全部",
        label: "全部"
      }
    ]
  }
}
// 角色管理表格设置
export const TABLE_SETTING_ROLE_MANAGEMENT = [
  {
    label: "角色名称",
    prop: "name",
    align: "center"
  },
  {
    label: "关联账号数",
    prop: "account_num",
    align: "center"
  },
  {
    label: "创建时间",
    prop: "create_time",
    align: "center"
  },
  {
    label: "状态",
    prop: "status",
    slot: "status",
    align: "center"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operationNew",
    align: "center"
  }
]
// 账号操作记录
export const TABLE_SETTING_ACCOUNT_RECORD = [
  {
    label: "操作时间",
    prop: "update_time",
    align: "center"
  },
  {
    label: "操作人",
    prop: "operator_name",
    "show-overflow-tooltip": true,
    align: "center"
  },
  {
    label: "被操作账号",
    prop: "manipulated_name",
    "show-overflow-tooltip": true,
    align: "center"
  },
  {
    label: "操作",
    prop: "handle_type",
    align: "center"
  },
  {
    label: "操作内容",
    prop: "detail",
    "show-overflow-tooltip": true,
    align: "center"
  }
]
// 角色操作记录
export const TABLE_SETTING_ROLE_RECORD = [
  {
    label: "操作时间",
    prop: "update_time",
    align: "center"
  },
  {
    label: "操作人",
    prop: "operator_name",
    "show-overflow-tooltip": true,
    align: "center"
  },
  {
    label: "被操作角色",
    prop: "manipulated_name",
    "show-overflow-tooltip": true,
    align: "center"
  },
  {
    label: "操作",
    prop: "handle_type",
    align: "center"
  },
  {
    label: "操作内容",
    prop: "detail",
    "show-overflow-tooltip": true,
    align: "center"
  }
]
