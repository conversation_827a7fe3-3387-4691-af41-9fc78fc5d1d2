<template>
  <div class="account-management">
    <div class="left">
      <channel-tree @tree-click="handlerTreeChange" />
    </div>
    <div class="container-wrapper right">
      <div ref="searchRef">
        <search-form
          :form-setting="searchFormSetting"
          @change-search="changeSearch"
          @reset="handlerReset"
          v-loading="loading"
          search-mode="normal"
        />
      </div>
      <div class="table-wrapper position-relative" ref="tableWrapperRef">
        <div class="table-header">
          <div class="flex items-center" />
          <div class="table-button">
            <el-button type="primary" class="ps-origin-btn-plain" @click="handlerShowAddDialog('accountRecord', null)"
              >历史记录</el-button
            >
            <el-button
              type="primary"
              @click="handlerMulEdit"
              v-permission="['background_fund_supervision.audit_account.batch_modify']"
              >批量编辑</el-button
            >
            <el-button
              type="primary"
              class="ps-origin-btn-light"
              @click="handlerShowImport"
              v-permission="['background_fund_supervision.audit_account.batch_add']"
              >导入账号</el-button
            >
            <el-button
              type="primary"
              @click="handlerShowAddDialog('add', null)"
              v-permission="['background_fund_supervision.audit_account.channel_add']"
              >新增</el-button
            >
          </div>
        </div>
        <div class="table-content">
          <ps-table
            :tableData="tableData"
            ref="psTableRef"
            v-loading="tableLoading"
            :show-pagination="true"
            @pagination-change="handleCurrentChange"
            :pageConfig="pageConfig"
            :max-height="maxHeight"
            @selection-change="handlerSelectChange"
          >
            <el-table-column type="selection" width="55" :selectable="selectable" />

            <ps-column :table-headers="tableSetting">
              <template #operationNew="{ row }">
                <div
                  v-if="
                    (row.source_type === 'supervision_channel' && row.role_name !== '超级管理员') ||
                    (isSuperAdmin && row.source_type === 'supervision_channel' && row.role_name === '超级管理员')
                  "
                >
                  <el-button
                    plain
                    link
                    size="small"
                    @click="handlerShowAddDialog('edit', row)"
                    type="primary"
                    v-permission="['background_fund_supervision.audit_account.channel_modify']"
                  >
                    编辑
                  </el-button>
                  <el-button
                    plain
                    link
                    size="small"
                    @click="handlerDelete(row)"
                    color="#ff5656"
                    type="primary"
                    v-permission="['background_fund_supervision.audit_account.channel_delete']"
                  >
                    删除
                  </el-button>
                </div>
              </template>
              <template #status="{ row }">
                <el-switch
                  v-model="row.status"
                  :disabled="
                    row.source_type === 'admin' ||
                    (!isSuperAdmin && row.role_name === '超级管理员') ||
                    !rules.includes('background_fund_supervision.audit_account.channel_modify')
                  "
                  :active-value="1"
                  :inactive-value="0"
                  :before-change="() => handlerSwitchBeforeChange(row)"
                />
              </template>
            </ps-column>
          </ps-table>
        </div>
        <div class="table-tip m-l-20px">初始化账号不可删除</div>
      </div>
    </div>

    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
    <!--弹窗-->
    <account-add-or-edit-dialog
      ref="addDialogRef"
      :is-show="isShowAddDialog"
      :type="dialogType"
      :title="dialogTitle"
      :channnel-id="chanelId"
      @cancel-dialog="handlerClose"
      @confirm-dialog="handlerConfirm"
    />
    <!--历史记录 -->
    <record-dialog
      :is-show="isShowRecordDialog"
      :type="dialogType"
      :title="dialogTitle"
      @cancel-dialog="handlerClose"
      @confirm-dialog="handlerConfirm"
    />
    <!-- 导入-->
    <import-excel-dialog
      :visible="dialogImportVisible"
      :import-type="importType"
      :template-url="templateUrl"
      :header-len="headerLen"
      :is-delete-top-tips="isDeleteTopTips"
      @close-dialog="closeImportDialog"
      :import-api="importApi"
      @confirm-dialog="confirmImportDialog"
    />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import { SEARCH_FORM_SETTING_ACCOUNT_MANAGEMENT, TABLE_SETTING_ACCOUNT_MANAGEMENT } from "./constants"
import {
  apiBackgroundFundSupervisionAuditAccountChannelListPost,
  apiBackgroundFundSupervisionAuditAccountChannelDeletePost,
  apiBackgroundFundSupervisionAuditAccountChannelModifyPost,
  apiBackgroundFundSupervisionChannelRoleListPost,
  apiBackgroundFundSupervisionAuditAccountBatchAddPost
} from "@/api/user"
import { ElMessage } from "element-plus"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { confirmBefore, confirm } from "@/utils/message"
import AccountAddOrEditDialog from "./compontents/AccountAddOrEditDialog .vue"
import RecordDialog from "./compontents/RecordDialog.vue"
import ChannelTree from "@/components/ChannelTree/index.vue"
import { useUserStore } from "@/store/modules/user"

const userStore = useUserStore()

const rules = userStore.roles
const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// 当前账号是否是超级管理员
const isSuperAdmin = ref<boolean>(userStore.getRoleName.includes("超级管理员"))
console.log("isSuperAdmin", isSuperAdmin.value, userStore.getRoleName)

// table数据
const tableData = ref([])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_ACCOUNT_MANAGEMENT))
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_ACCOUNT_MANAGEMENT)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 选择的ids
const selectIds = ref<Array<any>>([])
// 渠道Id
const chanelId = ref()
// 弹窗
const isShowAddDialog = ref(false)
const isShowRecordDialog = ref(false)
const dialogType = ref("")
const dialogTitle = ref("")
const addDialogRef = ref()
// 导入弹窗
const dialogImportVisible = ref(false)
// 导入模板链接
const templateUrl = location.origin + "/api/temporary/template_excel/channel/导入渠道账号.xlsx"
// 表头长度
const headerLen = 0
// 是否显示导入弹窗的提示
const isDeleteTopTips = false
// 导入API
const importApi = apiBackgroundFundSupervisionAuditAccountBatchAddPost

// 导出
const importType = "FundSupervisionAuditAccountImport"
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "role_ids") {
        const value = data[key].value
        if (value && value !== "全部") {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.role_ids = data[key].value
      }
    }
  }
  return params
}
// 搜索
const changeSearch = () => {
  if (!chanelId.value) {
    return ElMessage.warning("请选择渠道")
  }
  pageConfig.currentPage = 1
  getDataList()
}

// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 反馈详情
const handlerShowAddDialog = (type: string, row: any) => {
  console.log(row)
  dialogType.value = type
  switch (type) {
    case "add":
      dialogTitle.value = "新建账号"
      break
    case "edit":
      dialogTitle.value = "编辑账号"
      if (addDialogRef.value) {
        addDialogRef.value.setDialogData(row)
      }
      break
    case "multiEdit":
      dialogTitle.value = "批量编辑"
      if (addDialogRef.value) {
        addDialogRef.value.setMultiSelectList(selectIds.value)
      }
      break
    case "accountRecord":
      dialogTitle.value = "历史记录"
      isShowRecordDialog.value = true
      return
    default:
      break
  }
  isShowAddDialog.value = true
}

// 弹窗关闭
const handlerClose = () => {
  isShowAddDialog.value = false
  isShowRecordDialog.value = false
}
// 弹窗确认
const handlerConfirm = (val: any) => {
  isShowAddDialog.value = false
  isShowRecordDialog.value = false
  if (val) {
    getDataList()
  }
}
// 确认导入
const confirmImportDialog = () => {
  dialogImportVisible.value = false
  getDataList()
}
// 关闭导入
const closeImportDialog = () => {
  dialogImportVisible.value = false
}
onMounted(() => {})
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionAuditAccountChannelListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize,
      supervision_channel_id: chanelId.value
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 获取角色列表
const getRoleList = async () => {
  tableLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionChannelRoleListPost({
      page: pageConfig.currentPage,
      page_size: "9999",
      supervision_channel_ids: [chanelId.value]
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    try {
      searchFormSetting.value.role_ids.dataList = res.data.results || []
    } catch (error) {
      console.log("error", error)
    }
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
// 选择改变
const handlerSelectChange = (val: any) => {
  console.log("handlerSelectChange", val)
  selectIds.value = []
  if (val) {
    val.forEach((item: any) => {
      selectIds.value.push(item)
    })
  }
}
// 批量编辑
const handlerMulEdit = () => {
  console.log("handlerMulEdit")
  if (selectIds.value && selectIds.value.length > 0) {
    console.log("selectIds.value", selectIds.value)
    handlerShowAddDialog("multiEdit", null)
  } else {
    ElMessage.error("请选择要编辑的数据")
  }
}
// 删除
const handlerDelete = (row: any) => {
  console.log("handleDelete", row)
  confirmBefore(
    {
      content: "是否删除该账号？",
      title: "提示"
    },
    async (action: any, instance: any, done: any) => {
      if (action === "confirm") {
        instance.confirmButtonLoading = true
        await deleteAccount(row.id)
        instance.confirmButtonLoading = false
      }
      done()
    },
    (action: any) => {
      if (action === "confirm") {
        getDataList()
      }
    }
  )
}
// 切换开关
const handlerSwitchBeforeChange = async (row: any): Promise<boolean> => {
  console.log("handlerSwitchBeforeChange", row)
  const message = !row.status ? "是否启用该账号？" : "是否禁用该账号？"
  return new Promise((resolve) => {
    confirm(
      {
        content: message,
        title: "提示"
      },
      async (action: any) => {
        if (action === "confirm") {
          row.loading = true
          let [err, res]: any[] = await to(
            apiBackgroundFundSupervisionAuditAccountChannelModifyPost({
              id: row.id,
              username: row.username,
              status: !row.status ? 1 : 0
            })
          )
          row.loading = false
          if (err) {
            return resolve(false)
          }
          if (res && res.code === 0) {
            ElMessage.success("修改状态成功")
            getDataList()
            resolve(true)
          } else {
            ElMessage.error(res.msg || "修改状态失败")
            resolve(false)
          }
        } else {
          resolve(false)
        }
      }
    )
  })
}

// 配置第一项没法选中
const selectable = (row: any) =>
  (row.source_type === "supervision_channel" && isSuperAdmin.value) ||
  (!isSuperAdmin.value && row.role_name !== "超级管理员")

// 导入用户
const handlerShowImport = () => {
  dialogImportVisible.value = true
}

// 树选择改变
const handlerTreeChange = (val: any) => {
  console.log("handlerTreeChange", val)
  chanelId.value = val.id || ""
  getDataList()
  getRoleList()
}
// 删除账号
const deleteAccount = (id: number): Promise<boolean> => {
  return new Promise((resolve) => {
    apiBackgroundFundSupervisionAuditAccountChannelDeletePost({
      ids: [id]
    })
      .then((res: any) => {
        console.log("setPaySettings", res)
        if (res && res.code === 0) {
          ElMessage.success("删除成功")
          resolve(true)
        } else {
          ElMessage.error(res.msg || "删除失败")
          resolve(false)
        }
      })
      .catch((error: any) => {
        ElMessage.error(error.message || "删除失败")
        console.log("error", error)
        resolve(false)
      })
  })
}
</script>
<style lang="scss">
.account-management {
  padding: 0 20px 0 0;
  display: flex;
  .right {
    flex: 1;
  }
  .el-popper {
    max-width: 300px;
  }

  .table-tip {
    position: absolute;
    bottom: 40px;
    color: #ff8b1e;
  }
}
</style>
