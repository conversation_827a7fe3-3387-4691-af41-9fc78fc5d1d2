<script lang="ts" setup>
import { ref, watch, computed } from "vue"
import { ElMessage } from "element-plus"
import type { TransferKey, TransferDirection } from "element-plus"

// 一些改变状态用的方法
const switchIndeterminate = (status: boolean, data: any) => {
  changeSelfIndeterminate(status, data)
  if (data.children && data.children.length) {
    data.children.map((item: any) => {
      if (item.isSelect) {
        switchIndeterminate(status, item)
      }
      return item
    })
  }
}
const changeSelfIndeterminate = (status: boolean, data: any) => {
  // 点击前改变当前节点的状态
  if (status) {
    // 看底下children情况
    if (!data.children.some((item: any) => item.isSelect)) {
      data.isIndeterminate = false
    } else if (data.children.some((item: any) => !item.isSelect)) {
      data.isIndeterminate = true
    } else {
      data.isIndeterminate = false
    }
  } else {
    if (data.children.some((item: any) => item.isSelect)) {
      data.isIndeterminate = true
    } else if (!data.children.some((item: any) => item.isSelect)) {
      data.isIndeterminate = false
    } else {
      data.isIndeterminate = false
    }
  }
  if (data.children && data.children.length) {
    data.children.forEach((item: any) => {
      changeSelfIndeterminate(status, item)
    })
  }
}

const changeParentIndeterminate = (status: boolean, father: any, data: any) => {
  // 先判断item的index是不是data的parent
  if (father.index === data.parent) {
    if (status) {
      // 判断此父级的勾选状态
      if (!father.children.some((item: any) => item.isSelect)) {
        father.isIndeterminate = false
        father.isSelect = false
      } else if (father.children.some((item: any) => !item.isSelect)) {
        father.isIndeterminate = true
        father.isSelect = true
      } else {
        father.isIndeterminate = false
        father.isSelect = true
      }
    } else {
      // 判断此父级的勾选状态
      if (father.children.some((item: any) => item.isSelect)) {
        father.isIndeterminate = true
        father.isSelect = true
      } else if (!father.children.some((item: any) => item.isSelect)) {
        father.isIndeterminate = false
        father.isSelect = false
      } else {
        father.isIndeterminate = false
        father.isSelect = true
      }
    }
    // 更往上的状态也要变动
    if (father.level !== 0) {
      channelPermissionTree.value.forEach((item: any) => {
        changeParentIndeterminate(status, item, father)
      })
    }
  } else {
    if (father.children && father.children.length) {
      father.children.forEach((item: any) => {
        changeParentIndeterminate(status, item, data)
      })
    }
  }
}

const selectAllItem = (status: boolean, data: any, index: number) => {
  data.isSelect = status
  if (data.children && data.children.length) {
    data.children.forEach((item: any) => {
      selectAllItem(status, item, index)
    })
  }
}

const searchCanNoSelect = (data: any, status: boolean, index: number) => {
  data.isSelect = status
  clickHandle(status, data, index, true)
  if (data.children && data.children.length) {
    data.children.forEach((item: any) => {
      searchCanNoSelect(item, status, index)
    })
  }
}

const hasChildren = (arr: any) => {
  let flag = false
  arr.forEach((item: any) => {
    if (item.children && item.children.length) {
      flag = true
    }
  })
  return flag
}

// 统计定制数量
const computedSelectCount = computed(() => {
  let count = 0
  channelPermissionTree.value.forEach((item: any) => {
    count += item.tabSelectCount
  })
  return count
})
const computedTotalCount = computed(() => {
  let count = 0
  channelPermissionTree.value.forEach((item: any) => {
    count += item.tabTotalCount
  })
  return count
})

const setCount = (data: any, index: number) => {
  if (Object.keys(data).includes("isSelect")) {
    channelPermissionTree.value[index].tabTotalCount++
  }
  if (data.isSelect) {
    channelPermissionTree.value[index].tabSelectCount++
  }
  // 判断该层是否有children
  if (data.children && data.children.length) {
    data.children.forEach((item: any) => {
      setCount(item, index)
    })
  }
}

const resetTabSelectCount = (arr: any[], index: number) => {
  if (arr.length) {
    arr.forEach((item) => {
      if (item.isSelect) {
        channelPermissionTree.value[index].tabSelectCount++
      }
      if (item.children && item.children.length) {
        resetTabSelectCount(item.children, index)
      }
    })
  }
}

// 获取权限树
import {
  apiBackgroundFundSupervisionSupervisionPermissionGetChannelSupervisionPermissionPost,
  apiBackgroundFundSupervisionSupervisionChannelChannelRoleOrgsInofPost
} from "@/api/user"
import to from "await-to-js"
import { cloneDeep } from "lodash"

const props = defineProps({
  isShow: Boolean,
  permissionList: {
    type: Array,
    default: () => []
  },
  channnelId: {
    type: Number,
    default: -1
  },
  organizations: {
    type: Array,
    default: () => []
  }
})

const isLoading = ref(false)
const channelPermissionTree = ref<any>([]) // 当前渠道能选的权限树
const selectedKeys = ref<any[]>([]) // 选择的组织数据

const switchStatus = (data: any) => {
  // 插入必要key
  Object.assign(data, {
    isSelect: false,
    isIndeterminate: false
  })
  if (props.permissionList.length && props.permissionList.includes(data.key)) {
    data.isSelect = true
  } else {
    data.isSelect = false
  }
  // 判断该层是否有children
  if (data.children && data.children.length) {
    data.children.forEach((item: any) => {
      switchStatus(item)
    })
  }
}

const getChannelPermissionTree = async () => {
  isLoading.value = true
  const [err, res]: [any, any] = await to(
    apiBackgroundFundSupervisionSupervisionPermissionGetChannelSupervisionPermissionPost()
  )
  isLoading.value = false
  if (err) {
    ElMessage.error(err.msg)
  }
  if (res.code === 0) {
    channelPermissionTree.value = res.data.map((item: any) => {
      // 插入必要的key
      Object.assign(item, {
        tabSelectCount: 0,
        tabTotalCount: 0
      })
      // 遍历插入需要的key且改变状态
      switchStatus(item)
      // 改变勾选后反显的样式
      switchIndeterminate(true, item)
      return item
    })
    // 计数
    let arr = channelPermissionTree.value.map((item: any, index: number) => {
      setCount(item, index)
      return item
    })
    channelPermissionTree.value = cloneDeep(arr)
  } else {
    ElMessage.error(res.msg)
  }
}
watch(
  () => props.isShow,
  (newVal) => {
    if (newVal) {
      getChannelPermissionTree()
    }
  },
  { immediate: true }
)

// 选择时操作
const selectedPermissionList = ref<string[]>([])
const emit = defineEmits(["updatePermission"])

const getSelectedPermission = (arr: string[], data: any) => {
  data.forEach((item: any) => {
    if (item.isSelect) {
      arr.push(item.key)
    }
    if (item.children && item.children.length) {
      getSelectedPermission(arr, item.children)
    }
  })
}
const clickHandle = (status: boolean, data: any, index: number, selectAllOrNot: boolean) => {
  // 先改变isSelect状态
  if (status) {
    selectNone.value = false
  } else {
    selectAll.value = false
  }
  selectAllItem(status, data, index)
  // 为了实现全选当前页的效果，这里需要再遍历判断（我实在想不到什么更好的办法了）
  changeSelfIndeterminate(status, data)
  // 判断是否是点击全选或全不选触发的，是的话没必要走多一次循环
  if (!selectAllOrNot) {
    // 判断是否全选了，未全选要显示对应的样式
    channelPermissionTree.value.forEach((item: any) => {
      changeParentIndeterminate(status, item, data)
    })
  }
  // 重新计算
  channelPermissionTree.value[index].tabSelectCount = 0
  if (channelPermissionTree.value[index].isSelect) {
    channelPermissionTree.value[index].tabSelectCount++
  }
  if (channelPermissionTree.value[index].children && channelPermissionTree.value[index].children.length) {
    resetTabSelectCount(channelPermissionTree.value[index].children, index)
  }
  // 每次操作完都要循环把已选的permission传出去
  updatePermission()
}
// 更新权限
const updatePermission = () => {
  let arr: string[] = []
  channelPermissionTree.value.forEach((item: any) => {
    if (item.isSelect) {
      arr.push(item.key)
    }
    if (item.children && item.children.length) {
      getSelectedPermission(arr, item.children)
    }
  })
  selectedPermissionList.value = cloneDeep(arr)
}
watch(
  [selectedPermissionList, () => props.organizations],
  ([newVal, newOrgs]) => {
    if (newVal) {
      emit("updatePermission", selectedPermissionList.value)
    }
    console.log("newOrgs", newOrgs)
    if (newOrgs) {
      selectedKeys.value = cloneDeep(newOrgs)
    }
  },
  {
    deep: true,
    immediate: true
  }
)

// 全选与非全选
const selectAll = ref(false)
const selectNone = ref(false)
watch(
  () => selectAll.value,
  (newValue) => {
    if (newValue) {
      if (selectNone.value) {
        selectNone.value = false
      }
    }
  }
)
watch(
  () => selectNone.value,
  (newValue) => {
    if (newValue) {
      if (selectAll.value) {
        selectAll.value = false
      }
    }
  }
)

const isSelectAll = (btn: string, status: boolean) => {
  if ((btn === "selectAll" && status) || (btn === "selectNone" && !status)) {
    channelPermissionTree.value.forEach((item: any, index: number) => {
      if (!item.canNotSelect) {
        searchCanNoSelect(item, status, index)
      }
      item.tabSelectCount = 0
      if (status) {
        item.tabSelectCount = item.tabTotalCount
      } else {
        if (item.isSelect) {
          channelPermissionTree.value[index].tabSelectCount++
        }
        if (item.children && item.children.length) {
          resetTabSelectCount(item.children, index)
        }
      }
    })
  }
}

// Transfer 相关的数据和方法
const transferData = ref<any[]>([])

// 初始化 Transfer 数据
const initTransferData = (data: any[]) => {
  transferData.value = data.map((item) => ({
    key: item.id || item.key,
    label: item.name || "--",
    disabled: false
  }))
}

// 获取数据权限列表并初始化 Transfer
const getDataPermissionList = async () => {
  isLoading.value = true
  const [err, res]: [any, any] = await to(
    apiBackgroundFundSupervisionSupervisionChannelChannelRoleOrgsInofPost({
      supervision_channel_id: props.channnelId,
      view_sub: true
    })
  )
  isLoading.value = false
  if (err) {
    ElMessage.error(err.msg || "获取数据权限列表失败")
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    let results = data.results || []
    initTransferData(results || [])
  } else {
    ElMessage.error(res?.msg || "获取数据权限列表失败")
  }
}

// Transfer 过滤方法
const filterMethod = (query: string, item: any) => {
  return item.label.toLowerCase().includes(query.toLowerCase())
}
// Transfer 数据变化处理
const handleChange = (value: TransferKey[], direction: TransferDirection, movedKeys: TransferKey[]) => {
  console.log(value, direction, movedKeys)
  selectedKeys.value = value as string[]
}

// 获取选中的数据权限
const getSelectedDataPermission = () => {
  return selectedKeys.value
}
// 获取是否可以保存
const getIsCanSave = () => {
  console.log("selectedPermissionList", selectedPermissionList.value)
  updatePermission()
  let flag = selectedPermissionList.value && selectedPermissionList.value.length > 0
  if (!flag) {
    ElMessage.warning("请选择角色权限")
  }
  // 如果选中的数据权限为空，则不能保存
  if (!transferData.value || transferData.value.length === 0) {
    return flag
  } else {
    let orgFlag = selectedKeys.value && selectedKeys.value.length > 0
    if (!orgFlag) {
      ElMessage.warning("请选择数据权限")
    }
    return flag && orgFlag
  }
}
// 获取选中的权限
const getSelectedPermissionList = () => {
  let selectedList: any[] = selectedPermissionList.value
  return selectedList
}

// 监听显示状态
watch(
  () => props.isShow,
  (newVal) => {
    if (newVal) {
      getChannelPermissionTree()
      getDataPermissionList()
    }
  },
  { immediate: true }
)

// 暴露方法给父组件
defineExpose({
  getSelectedPermissionList,
  getSelectedDataPermission,
  getIsCanSave
})
</script>

<template>
  <div v-loading="isLoading">
    <el-tabs type="card" tab-position="left" class="version-configuration-content-box">
      <el-tab-pane :label="item.verbose_name" v-for="(item, index) in channelPermissionTree" :key="index">
        <div class="p-20px">
          <div class="mb-10px w-150px">
            <el-checkbox
              v-model="item.isSelect"
              :indeterminate="item.isIndeterminate"
              class="flex flex-items-center"
              @change="clickHandle(item.isSelect, item, index, true)"
            >
              <span class="font-size-18 font-medium">全选当前页</span>
            </el-checkbox>
          </div>
          <div v-for="(item1, index1) in item.children" :key="index1" class="mb-20px">
            <div class="w-150px">
              <el-checkbox
                v-model="item1.isSelect"
                :indeterminate="item1.isIndeterminate"
                class="flex flex-items-center mb-10px"
                @change="clickHandle(item1.isSelect, item1, index, false)"
              >
                <span class="font-size-18ps font-bold">{{ item1.verbose_name }}</span>
              </el-checkbox>
            </div>
            <div style="border-top: 1px solid #e5e7ea">
              <div
                class="box-item flex flex-justify-start"
                :style="index2 % 2 === 0 ? { backgroundColor: '#ffffff' } : { backgroundColor: '#f8f9fa' }"
                v-for="(item2, index2) in item1.children"
                :key="index2"
              >
                <div :class="[item2.children.length ? '' : 'box-item-left', 'p-20px']">
                  <el-checkbox
                    v-model="item2.isSelect"
                    :indeterminate="item2.isIndeterminate"
                    @change="clickHandle(item2.isSelect, item2, index, false)"
                  >
                    <span class="text-ellipsis w-100px">{{ item2.verbose_name }}</span>
                  </el-checkbox>
                </div>
                <!-- 只有四层的显示布局 -->
                <div
                  :class="[item2.children.length ? 'box-item-right1' : '', 'p-20px', 'w-full']"
                  v-if="item2.children.length && !hasChildren(item2.children)"
                >
                  <div v-for="(item3, index3) in item2.children" :key="index3">
                    <el-checkbox v-model="item3.isSelect" @change="clickHandle(item3.isSelect, item3, index, false)">
                      <span class="text-ellipsis w-150px">{{ item3.verbose_name }}</span>
                    </el-checkbox>
                  </div>
                </div>
                <!-- 有五层的显示布局 -->
                <div
                  :class="[item2.children.length ? 'box-item-right2' : '', 'w-full']"
                  v-if="item2.children.length && hasChildren(item2.children)"
                >
                  <div
                    v-for="(item3, index3) in item2.children"
                    :key="index3"
                    class="three-level flex flex-justify-start flex-items-center"
                    :style="index3 < item2.children.length - 1 ? { borderBottom: '1px solid #e5e7ea' } : {}"
                  >
                    <el-checkbox
                      v-model="item3.isSelect"
                      class="p-20px"
                      @change="clickHandle(item3.isSelect, item3, index, false)"
                    >
                      <span class="text-ellipsis w-150px">{{ item3.verbose_name }}</span>
                    </el-checkbox>
                    <div v-if="item3.children.length" class="three-level-right p-20px w-full">
                      <div v-for="(item4, index4) in item3.children" :key="index4">
                        <el-checkbox
                          v-model="item4.isSelect"
                          @change="clickHandle(item4.isSelect, item4, index, false)"
                        >
                          <span class="text-ellipsis w-150px">{{ item4.verbose_name }}</span>
                        </el-checkbox>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 数据权限部分使用 Transfer 重新实现 -->
    <div class="mt-20px">
      <div class="data-permission-section">
        <h2 class="data-permission-header">数据权限</h2>
        <div class="p-20px transfer-container">
          <el-transfer
            v-model="selectedKeys"
            :data="transferData"
            :titles="['待选', '已选']"
            :filter-method="filterMethod"
            :left-default-checked="[]"
            :right-default-checked="[]"
            :button-texts="['往左', '往右']"
            :props="{
              key: 'key',
              label: 'label'
            }"
            filterable
            :filter-placeholder="'输入关键词检索'"
            @change="handleChange"
          >
            <template #default="{ option }">
              <span class="transfer-item">{{ option.label }}</span>
            </template>
          </el-transfer>
        </div>
      </div>
    </div>

    <div class="version-configuration-content-footer">
      <slot name="footer" />
      <div class="checkbox-area ml-20px">
        <el-checkbox v-model="selectAll" @change="isSelectAll('selectAll', true)">
          <span class="font-size-16px">全选</span>
        </el-checkbox>
        <el-checkbox v-model="selectNone" @change="isSelectAll('selectNone', false)">
          <span class="font-size-16px">全不选</span>
        </el-checkbox>
      </div>
      <div class="ml-20px">
        <span>定制数量：{{ computedSelectCount }}/{{ computedTotalCount }}</span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.version-configuration {
  &-content {
    &-box {
      .box-item {
        background-color: #d7d7d7;
        border-bottom: 1px solid #e5e7ea;
        // border-radius: 4px;
        &-left {
          border-right: 1px solid #e5e7ea;
        }
        &-right1 {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 10px 20px;
          border-left: 1px solid #e5e7ea;
        }
        &-right2 {
          display: flex;
          flex-direction: column;
          .three-level {
            display: grid;
            grid-template-columns: 1fr 3fr;
            border-left: 1px solid #e5e7ea;
            &-right {
              margin-left: -5px;
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              gap: 10px 20px;
              border-left: 1px solid #e5e7ea;
            }
          }
        }
      }
    }
    &-box2 {
      background-color: #f8f9fa;
      border-radius: 4px;
    }
    &-footer {
      z-index: 10;
      position: sticky;
      bottom: -20px;
      padding: 20px 0px 20px;
      background-color: #fff;
      display: flex;
      align-items: center;
      .button-area {
        display: flex;
        align-items: center;
      }
      .checkbox-area {
        display: flex;
        align-items: center;
      }
    }
  }
  .bg-style {
    height: calc(86vh - 75px);
    background-color: #fff;
    border-radius: 8px;
  }
}

:deep(.el-tabs--card > .el-tabs__header) {
  border-bottom: none;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: none;
}

:deep(.el-tabs--left.el-tabs--card .el-tabs__item.is-left) {
  border: none;
}

:deep(.el-tabs--left.el-tabs--card .el-tabs__item.is-left.is-active) {
  background-color: #f8f9fa;
}

:deep(.el-tabs--left .el-tabs__header.is-left) {
  margin-right: 0px;
}

:deep(.el-tabs__content) {
  background-color: #f8f9fa;
  overflow: auto;
  height: 100%;
}

:deep(.el-checkbox) {
  display: flex;
  align-items: center;
}

:deep(.el-checkbox__label) {
  display: flex;
  align-items: center;
}

// 新增功能区样式
.new-feature-title {
  color: #f56c6c;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  padding: 10px 0;
  border-bottom: 1px solid #f56c6c;
  margin-bottom: 20px;
}

.data-permission-section {
  border-radius: 4px;
  margin-bottom: 20px;
}

.data-permission-header {
  font-size: 16px;
  font-weight: bold;
}

// Transfer 相关样式
.transfer-container {
  display: flex;
  justify-content: center;
  min-width: 700px; // 确保容器有足够的最小宽度
}

.data-permission-section {
  .el-transfer {
    display: flex;
    justify-content: space-between;
    width: 100%;
    gap: 20px; // 添加间距

    :deep(.el-transfer-panel) {
      width: calc(50% - 30px); // 调整宽度，预留中间按钮的空间
      min-width: 280px; // 设置最小宽度
      margin: 0; // 移除默认边距

      .el-transfer-panel__body {
        height: 400px;
      }

      .el-transfer-panel__list {
        padding: 0;
      }

      .el-checkbox__label {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 90%; // 使用百分比而不是固定宽度
        display: inline-block;
      }
    }

    // 调整中间按钮区域的样式
    :deep(.el-transfer__buttons) {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 0 10px;
      min-width: 60px; // 设置最小宽度
      .el-button {
        margin-left: 0px !important;
        margin-top: 10px !important;
      }
    }
  }
}

.transfer-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%; // 使用百分比
  display: inline-block;
}
</style>
