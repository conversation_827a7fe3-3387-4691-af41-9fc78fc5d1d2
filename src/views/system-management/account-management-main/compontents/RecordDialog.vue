<template>
  <div class="record-management container-wrapper">
    <el-drawer
      v-model="dialogFormVisible"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <!--历史记录-->
      <div class="table-content position-relative">
        <div class="m-t-20px m-b-10px flex items-center">
          <div>时间</div>
          <div class="m-l-10px">
            <el-date-picker
              v-model="searchDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDate"
              :clearable="false"
              @change="changeDate"
            />
          </div>
          <div class="m-l-20px">被操作{{ type === "accountRecord" ? "账号" : "角色" }}</div>
          <div class="m-l-10px">
            <el-input v-model="searchAccount" placeholder="请输入" clearable @input="handlerInputChange" />
          </div>
        </div>
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          maxHeight="600px"
        >
          <ps-column :table-headers="tableSetting" />
        </ps-table>
      </div>
      <div class="dialog-footer m-t-20px">
        <el-button type="primary" @click="closeDialog" v-loading="confirmLoading"> 关闭 </el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue"
import { cloneDeep, debounce } from "lodash"
import { TABLE_SETTING_ACCOUNT_RECORD, TABLE_SETTING_ROLE_RECORD } from "../constants"
import { getTestData } from "@/utils/index"
import { getPreDate } from "@/utils/date"
import { apiBackgroundFundSupervisionChannelOperationListPost } from "@/api/user"
import { to } from "await-to-js"
import { ElMessage } from "element-plus"

const props = defineProps({
  title: {
    type: String,
    default: "历史记录"
  },
  width: {
    type: String,
    default: "948px"
  },
  isShow: {
    type: Boolean,
    default: false
  },
  type: {
    type: String, // 账号记录  accountRecord:  角色记录 roleRecord
    default: "accountRecord"
  },
  orgId: {
    type: Number,
    default: -1
  },
  alreadyChooseIds: {
    type: Array,
    default: () => {
      return []
    }
  },
  // 选择节点数据
  organizationData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const dialogFormVisible = ref(false)
const confirmLoading = ref(false)
const searchDate = ref([getPreDate(3, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")])
const searchAccount = ref("")

// table数据
const tableData = ref([])
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_ACCOUNT_RECORD))
const cloneTableData = cloneDeep(tableData)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 初始化数据
const initData = () => {
  tableSetting.value = props.type === "accountRecord" ? TABLE_SETTING_ACCOUNT_RECORD : TABLE_SETTING_ROLE_RECORD
  getRecordList()
}
// 获取记录
const getRecordList = async () => {
  tableLoading.value = true
  let params: any = {
    start_time: searchDate.value ? searchDate.value[0] + " 00:00:00" : "",
    end_time: searchDate.value ? searchDate.value[1] + " 23:59:59" : "",
    operate_type: props.type === "accountRecord" ? "account" : "role",
    page: pageConfig.currentPage,
    page_size: pageConfig.pageSize
  }
  if (searchAccount.value) {
    params.manipulated_name = searchAccount.value
  }
  const [err, res]: any[] = await to(apiBackgroundFundSupervisionChannelOperationListPost(params))
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 只能选最近一个月的数据
const disabledDate = (time: Date) => {
  const currentDate = new Date()
  const beforeDate = new Date(getPreDate(30, "YYYY-MM-DD"))

  return time > currentDate || time < beforeDate
}

// 弹窗关闭
const closeDialog = () => {
  tableData.value = []
  pageConfig.currentPage = 1
  emit("cancelDialog")
}
// 设置数据
const setDialogData = (data: any) => {
  console.log("setDialogData", props)

  if (data && typeof data === "object") {
    cloneTableData.value = cloneDeep(data.tableData || [])
    if (data.tableData) {
      pageConfig.currentPage = 1
      tableSetting.value = cloneDeep(TABLE_SETTING_ACCOUNT_RECORD)
      getTestData(tableData, cloneTableData.value, tableLoading, pageConfig.currentPage)
      pageConfig.total = data.tableData.length || 0
    }
  }
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getRecordList()
}
// 修改日期
const changeDate = debounce(() => {
  getRecordList()
}, 500)
// 输入改变
const handlerInputChange = debounce(() => {
  getRecordList()
}, 500)

watch(
  () => ({
    isShow: props.isShow
  }),
  (newValue) => {
    console.log("watch dialog", newValue)
    dialogFormVisible.value = newValue.isShow
    if (newValue.isShow) {
      // initData()

      initData()
    }
  }
)
defineExpose({ setDialogData })
</script>
<style lang="scss" scoped>
.container-wrapper .table-content {
  padding: 0 !important;
  position: relative;
}
.btn-right {
  position: absolute;
  top: 0;
  right: 20px;
  z-index: 111;
}
.record-management {
  .tag-title {
    width: 120px;
    min-width: 120px;
    background: #f4f6fc;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-left: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .tag-content {
    min-width: 300px;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .border-bottom {
    border-bottom: 1px solid #e9ecf1;
  }
  .el-tag-my {
    color: var(--el-color-primary);
  }
}
</style>
