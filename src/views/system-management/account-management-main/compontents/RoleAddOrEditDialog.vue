<template>
  <div class="add-role container-wrapper">
    <el-drawer
      v-model="dialogFormVisible"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div>
        <el-form :model="ruleForm" label-width="200px" label-position="top" :rules="rules" ref="ruleFormRef">
          <el-form-item label="名称" prop="name">
            <div class="w-90%">
              <el-input v-model="ruleForm.name" placeholder="请输入名称" />
            </div>
          </el-form-item>
          <el-form-item label="角色权限" prop="role">
            <div class="w-90%">
              <role-permission-setting
                :isShow="dialogFormVisible"
                :permissionList="permissionList"
                :channnel-id="channnelId"
                :organizations="newOrgs"
                @updatePermission="getNewPermission"
                ref="rolePermissionSettingRef"
              >
                <template #footer>
                  <el-button
                    type="primary"
                    class="ps-origin-btn-plain"
                    @click="closeDialog"
                    v-loading="confirmLoading"
                    v-if="type !== 'record'"
                  >
                    取消
                  </el-button>
                  <el-button type="primary" @click="confirmDialog" v-loading="confirmLoading"> 保存 </el-button>
                </template>
              </role-permission-setting>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- <div class="dialog-footer m-t-20px">
        <el-button
          type="primary"
          class="ps-origin-btn-plain"
          @click="closeDialog"
          v-loading="confirmLoading"
          v-if="type !== 'record'"
        >
          取消
        </el-button>
        <el-button type="primary" @click="confirmDialog" v-loading="confirmLoading"> 保存 </el-button>
      </div> -->
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue"
// import { confirm } from "@/utils/message"
import type { TAccountForm } from "../index.d"
import { cloneDeep } from "lodash"
// import { TABLE_SETTING_ACCOUNT_RECORD } from "../constants"
import type { FormInstance, FormRules } from "element-plus"
import { validateNameChina } from "@/utils/validate-form"
import RolePermissionSetting from "./RolePermissionSetting.vue"
import {
  apiBackgroundFundSupervisionChannelRoleAddPost,
  apiBackgroundFundSupervisionChannelRoleModifyPost,
  apiBackgroundFundSupervisionChannelRoleRoleDetailPost
} from "@/api/user"
import { ElMessage } from "element-plus"
import to from "await-to-js"

const props = defineProps({
  title: {
    type: String,
    default: "新增角色"
  },
  width: {
    type: String,
    default: "948px"
  },
  isShow: {
    type: Boolean,
    default: false
  },
  type: {
    type: String, //  add:新增  edit:编辑
    default: "add"
  },
  channnelId: {
    // 渠道ID
    type: Number,
    default: -1
  },
  alreadyChooseIds: {
    type: Array,
    default: () => {
      return []
    }
  },
  // 选择节点数据
  organizationData: {
    type: Object,
    default: () => {
      return {}
    }
  },
  permissionList: {
    type: Array,
    default: () => {
      return [] as string[]
    }
  },
  organizations: {
    type: Array,
    default: () => {
      return []
    }
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const dialogFormVisible = ref(false)
const confirmLoading = ref(false)
// 表单
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<any>({
  name: ""
})
// 已选权限
const newOrgs = ref([])
// 规则
const rules = reactive<FormRules<TAccountForm>>({
  name: [
    { required: true, message: "请输入", trigger: "blur" },
    { min: 0, max: 20, message: "最多输入20个字符", trigger: "blur" },
    { required: false, validator: validateNameChina, trigger: "blur" }
  ]
})

// table数据
const tableData = ref([])
// const tableLoading = ref(false)
// const tableSetting = ref(cloneDeep(TABLE_SETTING_ACCOUNT_RECORD))
// const cloneTableData = cloneDeep(tableData)
// const pageConfig = reactive({
//   total: 0,
//   currentPage: 1,
//   pageSize: 10
// })
// 角色权限设置组件
const rolePermissionSettingRef = ref<any>()

const getNewPermission = (arr: string[]) => {
  console.log("getNewPermission", arr)
  ruleForm.mobile = cloneDeep(arr)
}
// 保存数据
const saveData = async () => {
  if (props.type === "add" && ruleForm.name === "超级管理员") {
    return ElMessage.error("系统默认有超级管理员的角色，请不要建该名字的角色")
  }
  let params: any = {
    supervision_channel: props.channnelId,
    name: ruleForm.name,
    permission: rolePermissionSettingRef.value?.getSelectedPermissionList(),
    organizations: rolePermissionSettingRef.value?.getSelectedDataPermission()
  }
  if (props.type === "edit") {
    params.id = ruleForm.id
  }
  confirmLoading.value = true
  const [err, res]: any[] =
    props.type === "add"
      ? await to(apiBackgroundFundSupervisionChannelRoleAddPost(params))
      : await to(apiBackgroundFundSupervisionChannelRoleModifyPost(params))
  confirmLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    ElMessage.success("保存成功")
    emit("confirmDialog", "addSuccess")
  } else {
    ElMessage.error(res.msg)
  }
}

// 弹窗确认
const confirmDialog = async () => {
  if (!ruleFormRef.value) return
  await ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      if (rolePermissionSettingRef.value) {
        if (rolePermissionSettingRef.value.getIsCanSave()) {
          saveData()
        }
      }
    } else {
      console.log("error submit!", fields)
    }
  })
}

// 弹窗关闭
const closeDialog = () => {
  ruleForm.name = ""
  tableData.value = []
  emit("cancelDialog")
}
// 设置数据
const setDialogData = (data: any) => {
  console.log("setDialogData", props)

  if (data && typeof data === "object") {
    ruleForm.name = data.name
    ruleForm.id = data.id
    // 清除校验
    setTimeout(() => {
      ruleFormRef.value?.clearValidate()
    }, 100)
  }
}
// 获取详情组织
const getDetailOrgs = async () => {
  if (props.type === "add") {
    return
  }
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionChannelRoleRoleDetailPost({
      id: ruleForm.id
    })
  )
  if (err) {
    return
  }
  if (res && res.code === 0) {
    const data: any = res.data || {}
    newOrgs.value = data.organizations || props.organizations
  }
}

watch(
  () => ({
    isShow: props.isShow
  }),
  (newValue) => {
    console.log("watch dialog", newValue, props)
    dialogFormVisible.value = newValue.isShow
    if (newValue.isShow) {
      getDetailOrgs()
    }
  }
)
defineExpose({ setDialogData })
</script>
<style lang="scss" scoped>
.container-wrapper .table-content {
  padding: 0 !important;
  position: relative;
}
.btn-right {
  position: absolute;
  top: 0;
  right: 20px;
  z-index: 111;
}
.add-role {
  .tag-title {
    width: 120px;
    min-width: 120px;
    background: #f4f6fc;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-left: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .tag-content {
    min-width: 300px;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .border-bottom {
    border-bottom: 1px solid #e9ecf1;
  }
  .el-tag-my {
    color: var(--el-color-primary);
  }
}
</style>
