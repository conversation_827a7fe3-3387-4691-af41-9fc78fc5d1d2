<template>
  <div class="add-account container-wrapper">
    <el-drawer
      v-model="dialogFormVisible"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div v-if="type !== 'record'">
        <el-form :model="ruleForm" label-width="200px" label-position="top" :rules="rules" ref="ruleFormRef">
          <el-form-item label="名称" prop="name" v-if="type == 'add' || type === 'edit'">
            <el-input v-model="ruleForm.name" placeholder="请输入名称" />
          </el-form-item>
          <el-form-item label="账号" prop="accountName" v-if="type == 'add' || type === 'edit'">
            <el-input
              v-model="ruleForm.accountName"
              placeholder="请输入账号"
              :disabled="type !== 'add'"
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item label="账号密码" prop="password" v-if="type == 'add' || type === 'edit'">
            <el-input
              v-model="ruleForm.password"
              placeholder="请输入密码"
              type="password"
              autocomplete="new-password"
            />
          </el-form-item>
          <el-form-item label="手机号" prop="mobile" v-if="type == 'add' || type === 'edit'">
            <el-input v-model="ruleForm.mobile" placeholder="请输入手机号" autocomplete="off" maxlength="11" />
          </el-form-item>
          <el-form-item label="登录校验（手机验证码）" prop="mobileChecked" v-if="type == 'add' || type === 'edit'">
            <el-radio-group v-model="ruleForm.mobileChecked">
              <el-radio value="1">启用</el-radio>
              <el-radio value="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="角色" prop="role" v-if="type == 'add' || type === 'edit'">
            <el-select v-model="ruleForm.role" placeholder="请选择角色">
              <el-option v-for="(item, index) in roleList" :key="index" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status" v-if="type == 'add' || type === 'edit'">
            <el-radio-group v-model="ruleForm.status">
              <el-radio value="1">启用</el-radio>
              <el-radio value="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- 批量编辑 不校验必填-->
          <div v-if="type === 'multiEdit'">
            <el-form-item label="角色" prop="">
              <el-select v-model="ruleForm.role" placeholder="请选择角色">
                <el-option v-for="(item, index) in roleList" :key="index" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="">
              <el-radio-group v-model="ruleForm.status">
                <el-radio value="1">启用</el-radio>
                <el-radio value="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="登录校验（手机验证码）" prop="">
              <el-radio-group v-model="ruleForm.mobileChecked">
                <el-radio value="1">启用</el-radio>
                <el-radio value="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <!--历史记录-->
      <div class="table-content position-relative" v-if="type === 'record'">
        <div class="m-t-20px m-b-10px flex items-center">
          <div>时间</div>
          <div class="m-l-10px">
            <el-date-picker
              v-model="searchDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDate"
            />
          </div>
          <div class="m-l-20px">被操作账号</div>
          <div class="m-l-10px">
            <el-input v-model="searchAccount" placeholder="请输入" clearable />
          </div>
        </div>
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          maxHeight="600px"
        >
          <ps-column :table-headers="tableSetting" />
        </ps-table>
      </div>
      <div class="dialog-footer m-t-20px">
        <el-button
          type="primary"
          class="ps-origin-btn-plain"
          @click="closeDialog"
          v-loading="confirmLoading"
          v-if="type !== 'record'"
        >
          取消
        </el-button>
        <el-button type="primary" @click="confirmDialog" v-loading="confirmLoading" v-if="type !== 'record'">
          保存
        </el-button>
        <el-button type="primary" @click="closeDialog" v-loading="confirmLoading" v-if="type === 'record'">
          关闭
        </el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue"
// import { confirm } from "@/utils/message"
import type { TAccountForm } from "../index.d"
import { cloneDeep } from "lodash"
import { TABLE_SETTING_ACCOUNT_RECORD } from "../constants"
import { getTestData } from "@/utils/index"
import type { FormInstance, FormRules } from "element-plus"
import { Md5 } from "ts-md5"
import { validateTelphoneNoRequire, validateNameChina, validatePass, validateAccount } from "@/utils/validate-form"
import { getPreDate } from "@/utils/date"
import {
  apiBackgroundFundSupervisionAuditAccountChannelAddPost,
  apiBackgroundFundSupervisionAuditAccountChannelModifyPost,
  apiBackgroundFundSupervisionChannelRoleListPost,
  apiBackgroundFundSupervisionAuditAccountBatchModifyPost
} from "@/api/user"
import to from "await-to-js"
import { ElMessage } from "element-plus"
import { useUserStore } from "@/store/modules/user"

const props = defineProps({
  title: {
    type: String,
    default: "新增账号"
  },
  width: {
    type: String,
    default: "948px"
  },
  isShow: {
    type: Boolean,
    default: false
  },
  type: {
    type: String, //  add:新增  edit:编辑 record:查看记录  multiEdit： 多选编辑
    default: "add"
  },
  channnelId: {
    type: Number,
    default: -1
  },
  alreadyChooseIds: {
    type: Array,
    default: () => {
      return []
    }
  },
  // 选择节点数据
  organizationData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const dialogFormVisible = ref(false)
const confirmLoading = ref(false)
const searchDate = ref([getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")])
const searchAccount = ref("")
// 表单
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<TAccountForm>({
  name: "",
  accountName: "",
  password: "",
  mobile: "",
  status: "",
  role: "",
  mobileChecked: ""
})
// 规则
const rules = reactive<FormRules<TAccountForm>>({
  name: [
    { required: true, message: "请输入", trigger: "blur" },
    { min: 0, max: 20, message: "最多输入20个字符", trigger: "blur" },
    { required: false, validator: validateNameChina, trigger: "blur" }
  ],
  accountName: [
    { required: true, message: "请输入", trigger: "blur" },
    { min: 0, max: 20, message: "最多输入20个字符", trigger: "blur" },
    { required: false, validator: validateAccount, trigger: "blur" }
  ],
  password: [
    { required: true, message: "请输入", trigger: "blur" },
    {
      required: false,
      validator: validatePass,
      trigger: "blur"
    }
  ],
  mobile: [
    { required: true, message: "请输入", trigger: "blur" },
    {
      required: true,
      validator: validateTelphoneNoRequire,
      trigger: "blur"
    }
  ],
  mobileChecked: [{ required: true, message: "请选择", trigger: ["blur", "change"] }],
  role: [{ required: true, message: "请选择", trigger: ["blur", "change"] }],
  status: [{ required: true, message: "请选择", trigger: ["blur", "change"] }]
})

// table数据
const tableData = ref([])
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_ACCOUNT_RECORD))
const cloneTableData = cloneDeep(tableData)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 角色列表
const roleList = ref<Array<any>>()
// 多选列表
const multiSelectList = ref<Array<any>>()

// 只能选最近一个月的数据
const disabledDate = (time: Date) => {
  const currentDate = new Date()
  const beforeDate = new Date(getPreDate(31, "YYYY-MM-DD"))

  return time > currentDate || time < beforeDate
}
// 初始化数据
const initData = () => {
  if (rules.password && Array.isArray(rules.password)) {
    Reflect.set(rules.password[0], "required", props.type === "add")
  }
  // 获取角色数据
  getRoleList()
}
// 保存数据
const saveData = async () => {
  let params: any = {
    supervision_channel_id: props.channnelId,
    name: ruleForm.name,
    username: ruleForm.accountName,
    mobile: ruleForm.mobile,
    role_id: ruleForm.role,
    status: Number(ruleForm.status),
    is_double_factor: ruleForm.mobileChecked === "1"
  }
  if (ruleForm.password) {
    const md5: any = new Md5()
    md5.appendAsciiStr(ruleForm.password)
    const newPass = md5.end()
    params.password = newPass
  }
  if (props.type === "edit") {
    params.id = ruleForm.id
  }
  confirmLoading.value = true
  const [err, res]: any[] =
    props.type === "add"
      ? await to(apiBackgroundFundSupervisionAuditAccountChannelAddPost(params))
      : await to(apiBackgroundFundSupervisionAuditAccountChannelModifyPost(params))
  confirmLoading.value = false
  if (err) {
    // ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    ElMessage.success(props.type === "add" ? "新增成功" : "修改成功")
    emit("confirmDialog", "新增成功")
  } else {
    ElMessage.error(res.msg)
  }
}
// 批量编辑保存
const saveMultiData = async () => {
  if (!ruleForm.role && !ruleForm.mobileChecked && !ruleForm.status) {
    return ElMessage.warning("请选择编辑项")
  }
  console.log("ruleForm", ruleForm, multiSelectList.value)

  let currentEditList: Array<any> = []
  if (multiSelectList.value) {
    multiSelectList.value.forEach((item) => {
      let multiTag: any = {}
      if (ruleForm.role && item.role_id !== ruleForm.role) {
        multiTag.role_id = ruleForm.role
      }
      if (ruleForm.mobileChecked) {
        let check = ruleForm.mobileChecked === "1"
        if (item.is_double_factor !== check) multiTag.is_double_factor = check
      }
      if (ruleForm.status && item.status !== Number(ruleForm.status)) {
        multiTag.status = Number(ruleForm.status)
      }
      if (Object.keys(multiTag).length > 0) {
        multiTag.id = item.id
        currentEditList.push(multiTag)
      }
    })
  }
  if (currentEditList && currentEditList.length > 0) {
    let params: any = {
      account_data: currentEditList
    }
    confirmLoading.value = true
    const [err, res]: any[] = await to(apiBackgroundFundSupervisionAuditAccountBatchModifyPost(params))
    confirmLoading.value = false
    if (err) {
      return
    }
    if (res && res.code === 0) {
      ElMessage.success("批量编辑成功")
      emit("confirmDialog", "批量编辑成功")
    } else {
      ElMessage.error(res.msg)
    }
  }

  console.log("currentEditList", currentEditList)
}
// 弹窗确认
const confirmDialog = async () => {
  if (!ruleFormRef.value) return
  await ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      if (props.type === "multiEdit") {
        saveMultiData()
      } else {
        saveData()
      }
    } else {
      console.log("error submit!", fields)
    }
  })
}

// 弹窗关闭
const closeDialog = () => {
  ruleForm.name = ""
  ruleForm.accountName = ""
  ruleForm.password = ""
  ruleForm.mobile = ""
  ruleForm.status = ""
  ruleForm.role = ""
  ruleForm.mobileChecked = ""
  tableData.value = []
  multiSelectList.value = []
  emit("cancelDialog")
}
// 设置数据
const setDialogData = (data: any) => {
  console.log("setDialogData", props)

  if (data && typeof data === "object") {
    ruleForm.id = data.id
    ruleForm.name = data.name
    ruleForm.accountName = data.username
    ruleForm.password = ""
    ruleForm.mobile = data.mobile
    ruleForm.status = data.status ? "1" : "0"
    ruleForm.role = data.role_id
    ruleForm.mobileChecked = data.is_double_factor ? "1" : "0"
    cloneTableData.value = cloneDeep(data.tableData || [])
    // 清除校验
    setTimeout(() => {
      ruleFormRef.value?.clearValidate()
    }, 100)
    if (rules.password && Array.isArray(rules.password)) {
      Reflect.set(rules.password[0], "required", props.type === "add")
    }
    if (data.tableData) {
      pageConfig.currentPage = 1
      tableSetting.value = cloneDeep(TABLE_SETTING_ACCOUNT_RECORD)
      getTestData(tableData, cloneTableData.value, tableLoading, pageConfig.currentPage)
      pageConfig.total = data.tableData.length || 0
    }
  }
}
// 设置多选列表
const setMultiSelectList = (val: Array<any>) => {
  console.log("setMultiSelect", val)
  multiSelectList.value = cloneDeep(val || [])
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getTestData(tableData, cloneTableData.value, tableLoading, pageConfig.currentPage)
}
// 获取角色列表
const getRoleList = async () => {
  tableLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionChannelRoleListPost({
      page: pageConfig.currentPage,
      page_size: "9999",
      supervision_channel_ids: [props.channnelId]
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    let results = res.data.results || []
    const userStore = useUserStore()
    const isSuperAdmin = userStore.getRoleName.includes("超级管理员")
    if (!isSuperAdmin && results) {
      results = results.filter((item: any) => item.name !== "超级管理员")
    }
    roleList.value = cloneDeep(results)
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
watch(
  () => ({
    isShow: props.isShow
  }),
  (newValue) => {
    console.log("watch dialog", newValue)
    dialogFormVisible.value = newValue.isShow
    if (newValue.isShow) {
      initData()
    }
  }
)
defineExpose({ setDialogData, setMultiSelectList })
</script>
<style lang="scss" scoped>
.container-wrapper .table-content {
  padding: 0 !important;
  position: relative;
}
.btn-right {
  position: absolute;
  top: 0;
  right: 20px;
  z-index: 111;
}
.add-account {
  .tag-title {
    width: 120px;
    min-width: 120px;
    background: #f4f6fc;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-left: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .tag-content {
    min-width: 300px;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .border-bottom {
    border-bottom: 1px solid #e9ecf1;
  }
  .el-tag-my {
    color: var(--el-color-primary);
  }
}
</style>
