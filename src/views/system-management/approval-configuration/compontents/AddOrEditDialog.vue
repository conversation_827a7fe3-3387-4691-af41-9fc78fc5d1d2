<template>
  <div class="add-approval container-wrapper">
    <el-drawer
      v-model="dialogFormVisible"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div>
        <el-form :model="ruleForm" label-width="110px" label-position="left" :rules="rules" ref="ruleFormRef">
          <el-form-item label="规则名称" prop="name">
            <el-input
              v-model="ruleForm.name"
              placeholder="请输入规则名称"
              class="tag-big"
              :disabled="type === 'detail'"
          /></el-form-item>
          <el-form-item label="审批类型" prop="approve_type">
            <el-select
              v-model="ruleForm.approve_type"
              placeholder="请选择"
              class="tag-big"
              :disabled="type === 'detail'"
              multiple
            >
              <el-option
                v-for="(item, index) in approvalTypeList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="审批方式" prop="approve_method">
            <el-radio-group v-model="ruleForm.approve_method" class="tag-big" :disabled="type === 'detail'">
              <el-radio v-for="(item, index) in approvalMethodList" :key="index" :label="item.value">{{
                item.label + "（" + item.tip + "）"
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="" prop="approve_json">
            <div v-for="(subItem, subIndex) in ruleForm.approve_json" :key="subIndex">
              <div :class="['flex', subIndex > 0 ? 'm-t-10px' : '']">
                <el-form-item
                  :prop="'approve_json[' + subIndex + '].name'"
                  :label="'审批节点' + (subIndex + 1)"
                  :rules="rules.name"
                >
                  <el-input
                    v-model="subItem.name"
                    placeholder="请输入节点名称"
                    class="tag-small"
                    :disabled="type === 'detail'"
                  />
                </el-form-item>
                <el-form-item
                  :prop="'approve_json[' + subIndex + '].account_ids'"
                  label=""
                  :rules="rules.account_ids"
                  class="m-l-10px"
                >
                  <el-select
                    v-model="subItem.account_ids"
                    placeholder="请选择审批账号"
                    clearable
                    filterable
                    multiple
                    collapse-tags
                    :max-collapse-tags="3"
                    class="tag-default"
                    @visible-change="(visible: boolean) => selectMultiAccountChange(visible, subItem.account_ids)"
                    :disabled="type === 'detail'"
                  >
                    <el-option
                      v-for="(item, index) in accountList"
                      :key="index"
                      :label="item.name"
                      :value="item.id"
                      :disabled="item.disabled"
                    />
                  </el-select>
                </el-form-item>
                <el-icon
                  v-if="type !== 'detail'"
                  class="w-32px h-32px m-l-10px cursor-pointer el-tag-my"
                  @click="addApprovalAccount(subIndex)"
                  size="32"
                  ><CirclePlus
                /></el-icon>
                <el-icon
                  class="w-32px h-32px m-l-10px cursor-pointer el-tag-my"
                  @click="delApprovalAccount(subIndex)"
                  v-if="subIndex > 0 && type !== 'detail'"
                  size="32"
                  ><Remove
                /></el-icon>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="审批通过备注" prop="remark_required">
            <el-radio-group v-model="ruleForm.remark_required" :disabled="type === 'detail'">
              <el-radio :value="true">启用</el-radio>
              <el-radio :value="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <div class="dialog-footer m-t-20px">
        <el-button
          type="primary"
          class="ps-origin-btn-plain"
          @click="closeDialog"
          v-loading="confirmLoading"
          v-if="type !== 'detail'"
        >
          取消
        </el-button>
        <el-button type="primary" @click="confirmDialog" v-loading="confirmLoading" v-if="type !== 'detail'">
          保存
        </el-button>
        <el-button type="primary" @click="closeDialog" v-loading="confirmLoading" v-if="type === 'detail'">
          关闭
        </el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue"
// import { confirm } from "@/utils/message"
import type { TApprovalFrom } from "../index.d"
import { cloneDeep } from "lodash"
import type { FormInstance, FormRules } from "element-plus"
import { validateNameChina } from "@/utils/validate-form"
import { apiBackgroundFundSupervisionAuditAccountChannelListPost } from "@/api/user"
import {
  apiBackgroundFundSupervisionChannelApproveRuleModifyPost,
  apiBackgroundFundSupervisionChannelApproveRuleAddPost
} from "@/api/approval"
import to from "await-to-js"
import { ElMessage } from "element-plus"
import { APPROVAL_TYPE, APPROVAL_METHOD } from "../constants"
import { CirclePlus, Remove } from "@element-plus/icons-vue"
import { getLocalStorage } from "@/utils/storage"

const props = defineProps({
  title: {
    type: String,
    default: "新增配置"
  },
  width: {
    type: String,
    default: "948px"
  },
  isShow: {
    type: Boolean,
    default: false
  },
  type: {
    type: String, //  add:新增  edit:编辑 detail:查看详情
    default: "add"
  },
  channnelId: {
    type: Number,
    default: -1
  },
  alreadyChooseIds: {
    type: Array,
    default: () => {
      return []
    }
  },
  // 选择节点数据
  organizationData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const dialogFormVisible = ref(false)
const confirmLoading = ref(false)
// 表单
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<TApprovalFrom>({
  name: "", // 审批规则名称
  supervision_channel: "", // 所属通道
  approve_type: [], // 审批类型
  approve_method: "", // 审批方式
  approve_json: [], // 审批规则
  extra: "", // 额外参数
  remark_required: false, // 是否需要填写审批说明
  status: "", // 状态
  id: "" // id
})
// 规则
const rules = reactive<FormRules<TApprovalFrom>>({
  name: [
    { required: true, message: "请输入", trigger: "blur" },
    { min: 0, max: 20, message: "最多输入20个字符", trigger: "blur" },
    { required: false, validator: validateNameChina, trigger: "blur" }
  ],
  approve_type: [{ required: true, message: "请选择审批类型", trigger: ["blur", "change"] }],
  approve_method: [{ required: true, message: "请选择审批方式", trigger: ["blur", "change"] }],
  remark_required: [{ required: true, message: "请选择是否需要填写审批说明", trigger: ["blur", "change"] }],
  account_ids: [{ required: true, message: "请选择账号", trigger: ["blur", "change"] }]
})
// 是否编辑
const isEdit = ref(false)

// 角色类型
const approvalTypeList = ref<Array<any>>(cloneDeep(APPROVAL_TYPE))
// 审批方式
const approvalMethodList = ref<Array<any>>(cloneDeep(APPROVAL_METHOD))
// 用户列表
const accountList = ref<Array<any>>()
// 初始化数据
const initData = () => {
  if (props.type === "add") {
    ruleForm.approve_json = [{ name: "", account_ids: [] }]
  }
  isEdit.value = props.type === "edit" || props.type === "add"
  let list = getLocalStorage("approval_process_list") || []
  if (list && list.length > 0) {
    let methodStr = ""
    list.forEach((item: any) => {
      methodStr += item.approve_type + ","
    })
    console.log("methodStr", methodStr, approvalTypeList.value)
    let approvalList = cloneDeep(APPROVAL_TYPE)
    if (props.type === "add") {
      approvalTypeList.value = approvalList.filter((item: any) => {
        return methodStr.indexOf(item.value) == -1
      })
    } else {
      approvalTypeList.value = cloneDeep(approvalList)
    }
  } else {
    approvalTypeList.value = cloneDeep(APPROVAL_TYPE)
  }
  console.log("approvalTypeList.value", approvalTypeList.value)

  // 获取账号数据
  getAccountList()
}
// 保存数据
const saveData = async () => {
  let params: any = {
    supervision_channel_id: props.channnelId,
    name: ruleForm.name,
    approve_type: ruleForm.approve_type?.join(","),
    approve_method: ruleForm.approve_method,
    remark_required: ruleForm.remark_required
  }
  if (ruleForm.approve_json) {
    let approveJson = ruleForm.approve_json.map((item: any) => {
      item.approve_method = ruleForm.approve_method
      return item
    })
    params.approve_json = approveJson
  }
  if (props.type === "edit") {
    params.id = ruleForm.id
  }
  confirmLoading.value = true
  const [err, res]: any[] =
    props.type === "add"
      ? await to(apiBackgroundFundSupervisionChannelApproveRuleAddPost(params))
      : await to(apiBackgroundFundSupervisionChannelApproveRuleModifyPost(params))
  confirmLoading.value = false
  if (err) {
    // ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    ElMessage.success(props.type === "add" ? "新增成功" : "修改成功")
    emit("confirmDialog", "新增成功")
  } else {
    ElMessage.error(res.msg)
  }
}

// 弹窗确认
const confirmDialog = async () => {
  if (!ruleFormRef.value) return
  console.log("ruleFormRef", ruleForm)
  await ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      saveData()
    } else {
      console.log("error submit!", fields)
    }
  })
}

// 弹窗关闭
const closeDialog = () => {
  ruleForm.name = ""
  ruleForm.approve_type = []
  ruleForm.approve_method = ""
  ruleForm.approve_json = []
  ruleForm.remark_required = false
  ruleForm.id = ""
  emit("cancelDialog")
}
// 设置数据
const setDialogData = (data: any) => {
  console.log("setDialogData", props)

  if (data && typeof data === "object") {
    ruleForm.id = data.id
    ruleForm.name = data.name
    if (data.approve_type) {
      ruleForm.approve_type = data.approve_type.split(",")
    }
    ruleForm.approve_method = data.approve_method
    ruleForm.approve_json = data.approve_json
    if (!ruleForm.approve_json || ruleForm.approve_json.length === 0) {
      ruleForm.approve_json = [{ name: "", account_ids: [] }]
    }
    ruleForm.status = data.status
    ruleForm.remark_required = data.remark_required
    // 清除校验
    setTimeout(() => {
      ruleFormRef.value?.clearValidate()
    }, 100)
  }
}

// 获取用户列表
const getAccountList = async () => {
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionAuditAccountChannelListPost({
      page: 1,
      page_size: "9999",
      supervision_channel_id: props.channnelId,
      is_get_sub_account: false,
      status: "1"
    })
  )
  if (err) {
    return
  }
  if (res && res.code === 0) {
    let results = res.data.results || []
    accountList.value = cloneDeep(results)
  } else {
    ElMessage.error(res.msg)
  }
}

// 添加审批
const addApprovalAccount = (index: number) => {
  console.log("addApprovalAccount", index)
  ruleForm.approve_json.push({ name: "" })
}
// 删除审批
const delApprovalAccount = (index: number) => {
  ruleForm.approve_json.splice(index, 1)
}
// 审批人选择改变
const selectMultiAccountChange = (isShow: boolean, val: Array<any>) => {
  console.log("selectMultiAccountChange", isShow, val)
  if (isShow) {
    let selectIds: any[] = []
    ruleForm.approve_json &&
      ruleForm.approve_json.forEach((item) => {
        selectIds = selectIds.concat(item.account_ids)
      })
    if (val) {
      selectIds = selectIds.filter((item) => {
        return !val.includes(item)
      })
    }
    accountList.value = accountList.value?.map((item: any) => {
      item.disabled = selectIds.includes(item.id)
      return item
    })
  }
}
watch(
  () => ({
    isShow: props.isShow
  }),
  (newValue) => {
    console.log("watch dialog", newValue)
    dialogFormVisible.value = newValue.isShow
    if (newValue.isShow) {
      initData()
    }
  }
)
defineExpose({ setDialogData })
</script>
<style lang="scss" scoped>
.container-wrapper .table-content {
  padding: 0 !important;
  position: relative;
}

.btn-right {
  position: absolute;
  top: 0;
  right: 20px;
  z-index: 111;
}

.add-approval {
  .tag-title {
    width: 120px;
    min-width: 120px;
    background: #f4f6fc;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-left: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .tag-big {
    width: 620px !important;
  }
  .tag-small {
    width: 200px !important;
  }
  .tag-default {
    width: 300px !important;
  }

  .tag-content {
    min-width: 300px;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }

  .border-bottom {
    border-bottom: 1px solid #e9ecf1;
  }

  .el-tag-my {
    color: var(--el-color-primary);
  }
}
</style>
