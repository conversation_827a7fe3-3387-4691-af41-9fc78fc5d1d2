// 审批类型
export const APPROVAL_TYPE = [
  {
    value: "supplier",
    label: "供应商审批"
  },
  {
    value: "finance",
    label: "财务审批"
  }
]

// 审批方式
export const APPROVAL_METHOD = [
  {
    value: "one_by_one_approve",
    label: "依次审批",
    tip: "流程内审批账号依次审批"
  },
  {
    value: "and_approve",
    label: "会签",
    tip: "需所有审批账号同意"
  },
  {
    value: "or_approve",
    label: "或签",
    tip: "一名审批账号同意或拒绝即可"
  }
]

// 审批配置管理筛选设置
export const SEARCH_FORM_SETTING_APPROVAL_PROCESS = {
  approve_type: {
    type: "select",
    label: "审批类型",
    value: [],
    multiple: true,
    clearable: true,
    placeholder: "请选择",
    collapseTags: true,
    dataList: APPROVAL_TYPE
  },
  status: {
    type: "select",
    label: "状态",
    value: "全部",
    multiple: false,
    clearable: false,
    placeholder: "请选择",
    dataList: [
      {
        value: "全部",
        label: "全部"
      },
      {
        value: "enable",
        label: "启用"
      },
      {
        value: "disable",
        label: "禁用"
      }
    ]
  }
}
// 审批配置表格设置
export const TABLE_SETTING_APPROVAL_PROCESS = [
  {
    label: "规则名称",
    prop: "name",
    align: "center"
  },
  {
    label: "审批类型",
    prop: "approve_type",
    slot: "approveType",
    align: "center"
  },
  {
    label: "审批方式",
    prop: "approve_method_alias",
    align: "center"
  },
  {
    label: "备注是否必填",
    prop: "remark_required",
    slot: "remarkRequired",
    align: "center"
  },
  {
    label: "状态",
    prop: "status",
    slot: "status",
    align: "center"
  },
  {
    label: "创建时间",
    prop: "create_time",
    align: "center"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operationNew",
    align: "center"
  }
]
