<template>
  <div class="role-management">
    <div class="left">
      <channel-tree @tree-click="handlerTreeChange" />
    </div>
    <div class="container-wrapper right">
      <div ref="searchRef">
        <search-form
          :form-setting="searchFormSetting"
          @change-search="changeSearch"
          @reset="handlerReset"
          v-loading="loading"
          search-mode="normal"
        />
      </div>
      <div class="table-wrapper" ref="tableWrapperRef">
        <div class="table-header">
          <div class="flex items-center" />
          <div class="table-button">
            <el-button
              type="primary"
              @click="handlerShowAddDialog('add', null)"
              v-permission="['background_fund_supervision.channel_approve_rule.add']"
              >新增</el-button
            >
          </div>
        </div>
        <div class="table-content">
          <ps-table
            :tableData="tableData"
            ref="psTableRef"
            v-loading="tableLoading"
            :show-pagination="true"
            @pagination-change="handleCurrentChange"
            :pageConfig="pageConfig"
            :max-height="maxHeight"
          >
            <ps-column :table-headers="tableSetting">
              <template #remarkRequired="{ row }">
                {{ row.remark_required ? "是" : "否" }}
              </template>
              <template #approveType="{ row }">
                {{ getApproveType(row.approve_type) }}
              </template>
              <template #operationNew="{ row }">
                <div v-if="row.status == 'enable'">
                  <el-button plain link size="small" @click="handlerShowAddDialog('detail', row)" type="primary">
                    详情
                  </el-button>
                </div>
                <div v-if="row.status !== 'enable'">
                  <el-button
                    plain
                    link
                    size="small"
                    @click="handlerShowAddDialog('edit', row)"
                    type="primary"
                    v-permission="['background_fund_supervision.channel_approve_rule.modify']"
                  >
                    编辑
                  </el-button>
                  <el-button
                    plain
                    link
                    size="small"
                    @click="handlerDelete(row)"
                    color="#ff5656"
                    type="primary"
                    v-permission="['background_fund_supervision.channel_approve_rule.delete']"
                  >
                    删除
                  </el-button>
                </div>
              </template>
              <template #status="{ row }">
                <el-switch
                  :disabled="!rules.includes('background_fund_supervision.channel_approve_rule.modify')"
                  v-model="row.status"
                  active-value="enable"
                  inactive-value="disable"
                  :before-change="() => handlerSwitchBeforeChange(row)"
                />
              </template>
            </ps-column>
          </ps-table>
        </div>
      </div>
    </div>

    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
    <!--弹窗-->
    <add-or-edit-dialog
      ref="addDialogRef"
      :is-show="isShowAddDialog"
      :type="dialogType"
      :title="dialogTitle"
      :channnel-id="chanelId"
      @cancel-dialog="handlerClose"
      @confirm-dialog="handlerConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import { SEARCH_FORM_SETTING_APPROVAL_PROCESS, TABLE_SETTING_APPROVAL_PROCESS, APPROVAL_TYPE } from "./constants"
import {
  apiBackgroundFundSupervisionChannelApproveRuleListPost,
  apiBackgroundFundSupervisionChannelApproveRuleModifyPost,
  apiBackgroundFundSupervisionChannelApproveRuleDeletePost
} from "@/api/approval"
import { ElMessage } from "element-plus"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { confirmBefore, confirm } from "@/utils/message"
import ChannelTree from "@/components/ChannelTree/index.vue"
import AddOrEditDialog from "./compontents/AddOrEditDialog.vue"
import { setLocalStorage } from "@/utils/storage"

import { useUserStore } from "@/store/modules/user"
const userStore = useUserStore()
const rules = userStore.roles

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])
// 渠道Id
const chanelId = ref()

// table数据
const tableData = ref([])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_APPROVAL_PROCESS))
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_APPROVAL_PROCESS)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 弹窗
const isShowAddDialog = ref(false)
const isShowRecordDialog = ref(false)
const dialogType = ref("")
const dialogTitle = ref("")
const addDialogRef = ref()

// 导出
// const importType = "FoodSafetyTraceabilityExport"
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "approve_type") {
        const value = data[key].value
        if (value && value != "全部") {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.approve_type = data[key].value.join(",")
      }
    }
  }
  return params
}
// 搜索
const changeSearch = () => {
  if (!chanelId.value) {
    return ElMessage.warning("请选择渠道")
  }
  pageConfig.currentPage = 1
  getDataList()
}

// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 导出
// const goToExport = () => {
//   console.log("goToExport")
//   const option = {
//     type: importType,
//     api: apiBackgroundFundSupervisionChannelRoleListPost,
//     params: {
//       ...formatQueryParams(searchFormSetting.value),
//       page: pageConfig.currentPage,
//       page_size: pageConfig.pageSize
//     }
//   }
//   exportHandle(option)
// }

// 弹窗关闭
const handlerClose = () => {
  isShowRecordDialog.value = false
  isShowAddDialog.value = false
}
// 弹窗确认
const handlerConfirm = (value: any) => {
  isShowRecordDialog.value = false
  isShowAddDialog.value = false
  if (value) {
    getDataList()
  }
}
// 切换开关
const handlerSwitchBeforeChange = async (row: any): Promise<boolean> => {
  console.log("handlerSwitchBeforeChange", row)
  const message = !row.status || row.status !== "enable" ? "是否启用该规则" : "是否禁用该规则"
  const messageTiltle = !row.status || row.status !== "enable" ? "启用" : "禁用"
  return new Promise((resolve) => {
    confirm(
      {
        content: message,
        title: messageTiltle
      },
      async (action: any) => {
        if (action === "confirm") {
          row.loading = true
          let [err, res]: any[] = await to(
            apiBackgroundFundSupervisionChannelApproveRuleModifyPost({
              id: row.id,
              approve_method: row.approve_method,
              status: !row.status || row.status !== "enable" ? "enable" : "disable"
            })
          )
          row.loading = false
          if (err) {
            return resolve(false)
          }
          if (res && res.code === 0) {
            ElMessage.success("修改状态成功")
            getDataList()
            resolve(true)
          } else {
            ElMessage.error(res.msg || "修改状态失败")
            resolve(false)
          }
        } else {
          resolve(false)
        }
      }
    )
  })
}

// 新增修改角色
const handlerShowAddDialog = (type: string, row: any) => {
  setLocalStorage("approval_process_list", tableData.value)
  dialogTitle.value = type === "add" ? "新建配置" : type === "edit" ? "编辑配置" : "查看详情"
  dialogType.value = type
  if (addDialogRef.value) {
    addDialogRef.value.setDialogData(row)
  }
  isShowAddDialog.value = true
}
// 删除点击
const handlerDelete = (row: any) => {
  if (row.status === "enable") {
    ElMessage.warning("规则生效中，不能删除")
    return
  }
  console.log("handleDelete", row)
  confirmBefore(
    {
      content: "是否删除该规则？",
      title: "提示"
    },
    async (action: any, instance: any, done: any) => {
      if (action === "confirm") {
        instance.confirmButtonLoading = true
        const flag = await deleteRule(row.id)
        console.log("flag", flag)

        instance.confirmButtonLoading = false
      }
      done()
    },
    (action: any) => {
      if (action === "confirm") {
        getDataList()
      }
    }
  )
}
// 删除规则
const deleteRule = (id: number): Promise<boolean> => {
  return new Promise((resolve) => {
    apiBackgroundFundSupervisionChannelApproveRuleDeletePost({
      ids: [id]
    })
      .then((res: any) => {
        console.log("setPaySettings", res)
        if (res && res.code === 0) {
          ElMessage.success("删除成功")
          resolve(true)
        } else {
          ElMessage.error(res.msg || "删除失败")
          resolve(false)
        }
      })
      .catch((error: any) => {
        ElMessage.error(error.message || "删除失败")
        console.log("error", error)
        resolve(false)
      })
  })
}
// 树选择改变
const handlerTreeChange = (val: any) => {
  console.log("handlerTreeChange", val)
  chanelId.value = val.id || ""
  getDataList()
}
// 获取审批类型
const getApproveType = (val: any) => {
  const list = cloneDeep(APPROVAL_TYPE)
  let findItem = list.find((item: any) => item.value === val)
  if (findItem) {
    return findItem.label
  }
  if (val.indexOf("supplier") != -1 && val.indexOf("finance") != -1) {
    return "供应商审批，财务审批"
  }
  return ""
}

onMounted(() => {})
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionChannelApproveRuleListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize,
      supervision_channel_ids: [chanelId.value]
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
</script>
<style lang="scss">
.role-management {
  padding: 0 20px;
  display: flex;
  .right {
    flex: 1;
  }

  .el-popper {
    max-width: 300px;
  }
}
</style>
