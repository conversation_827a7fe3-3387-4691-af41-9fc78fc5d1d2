import {
  CLEAN_TYPE_LIST,
  ATTITUDE_TYPE_LIST,
  WASTE_TYPE_LIST,
  OPERATE_TYPE_LIST,
  DISINFECTION_TYPE_LIST,
  OPERATION_TYPE_LIST,
  TYPES_MEAL
} from "./constants.js"
import { cloneDeep } from "lodash"
// 获取名字
export const getNameByType = (type: string, value: any) => {
  let dicList: any[] = []
  console.log("getNameByType", type, value)
  switch (type) {
    case "room_clean_type":
      dicList = cloneDeep(CLEAN_TYPE_LIST)
      break
    case "room_attitude_type":
      dicList = cloneDeep(ATTITUDE_TYPE_LIST)
      break
    case "area_clean_type":
      dicList = cloneDeep(CLEAN_TYPE_LIST)
      break
    case "area_waste_type":
      dicList = cloneDeep(WASTE_TYPE_LIST)
      break
    case "oa_clean_type":
      dicList = cloneDeep(CLEAN_TYPE_LIST)
      break
    case "oa_operate_type":
      dicList = cloneDeep(OPERATE_TYPE_LIST)
      break
    case "tda_clean_type":
      dicList = cloneDeep(CLEAN_TYPE_LIST)
      break
    case "tda_disinfection_type":
      dicList = cloneDeep(DISINFECTION_TYPE_LIST)
      break
    case "operation_type":
      dicList = cloneDeep(OPERATION_TYPE_LIST)
      break

    default:
      break
  }
  let result = dicList.find((item: any) => item.value === value)
  console.log("getDicLabelByValue", dicList, value, result)
  if (result) {
    return Reflect.has(result, "label") ? result.label : ""
  }
  return ""
}

// 获取餐段名称
export const getMealTypeName = (type: string) => {
  let dicList = cloneDeep(TYPES_MEAL)
  let result = dicList.find((item: any) => item.value === type)
  if (result) {
    return Reflect.has(result, "label") ? result.label : ""
  }
  return ""
}

// 根据列表获取人员名称
export const getPersonNameByList = (list: any[]) => {
  if (!list || list.length === 0) {
    return ""
  }
  let result = list.map((item: any) => {
    let name = item.name
    let identityTypeAlias = item.identity_type_alias
    return name + (identityTypeAlias ? `(${identityTypeAlias})` : "")
  })
  let strList = result.join("、")
  return strList
}
