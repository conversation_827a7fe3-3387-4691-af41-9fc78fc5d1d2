<template>
  <div class="meal-management container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="flex items-center" />
        <div class="table-button">
          <el-button
            type="primary"
            @click="goToExport"
            v-permisson="[
              'background_fund_supervision.channel_canteen_management.channel_meal_accompanying_list_export'
            ]"
            >导出</el-button
          >
          <el-button type="primary" class="ps-origin-btn-light" @click="gotoPrint">打印</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
          :custom-span-method="customSpanMethod"
        >
          <ps-column :table-headers="tableSetting">
            <template #meal_type="{ row }">
              {{ getMealTypeName(row.meal_type) }}
            </template>
            <template #person="{ row }">
              {{ getPersonList(row.person_record_list) }}
            </template>
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowDetail(row)" type="primary"> 详情 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
    <!--弹窗-->
    <meal-management-detail-dialog
      ref="mealDetailDialogRef"
      :is-show="isShowMealDetailDialog"
      :type="dialogType"
      :title="dialogTitle"
      @cancel-dialog="handlerClose"
      @confirm-dialog="handlerConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import { SEARCH_FORM_SETTING_MEAL_MANAGEMENT, TABLE_SETTING_MEAL_MANAGEMENT_ADMIN } from "./constants"
import {
  apiBackgroundFundSupervisionChannelCanteenManagementChannelMealAccompanyingListPost,
  apiBackgroundFundSupervisionChannelCanteenManagementChannelMealAccompanyingListExportPost
} from "@/api/canteen"
import { ElMessage, TableColumnCtx } from "element-plus"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { exportHandle } from "@/utils/exportExcel"
import MealManagementDetailDialog from "./compontents/MealManagementDetailDialog.vue"
import { setLocalStorage } from "@/utils/storage"
import { useRouter } from "vue-router"
import { getNameByType, getMealTypeName, getPersonNameByList } from "./utils"
import { divide } from "@/utils"
// 路由
const router = useRouter()
const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// table数据
const tableData = ref([])
const oldTableData = ref([])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_MEAL_MANAGEMENT))
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_MEAL_MANAGEMENT_ADMIN))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 弹窗
const isShowMealDetailDialog = ref(false)
const dialogType = ref("")
const dialogTitle = ref("")
const mealDetailDialogRef = ref()

// 导出
const printType = "MealManagementExport"
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value && data[key].value !== "" && data[key].value.length > 0) {
      if (key !== "select_time") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}
// 搜索
const changeSearch = (model: any, type: any, itemType: string) => {
  console.log("changeSearch", model, type, itemType)
  if (itemType === "supervision_organization_ids") {
    searchFormSetting.value.organization_ids.value = []
    searchFormSetting.value.organization_ids.orgsId = type
  }
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 导出
const goToExport = () => {
  console.log("goToExport")
  const option = {
    type: printType,
    api: apiBackgroundFundSupervisionChannelCanteenManagementChannelMealAccompanyingListExportPost,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    }
  }
  exportHandle(option)
}

// 查看照片
// const handlerShowPhoto = (row: any) => {
//   console.log(row)
//   const img = row ? row.imgs : ""
//   if (!img) {
//     return ElMessage.error("防制图片不存在")
//   }
//   imageList.value = [img]
//   imageVisible.value = true
// }

// 反馈详情
const handlerShowDetail = (row: any) => {
  console.log(row)
  let id = row.id
  let findItem = oldTableData.value.find((item: any) => item.id === id)
  dialogTitle.value = "详情"
  dialogType.value = "detail"
  if (mealDetailDialogRef.value) {
    mealDetailDialogRef.value.setDialogData(findItem)
  }
  isShowMealDetailDialog.value = true
}

// 弹窗关闭
const handlerClose = () => {
  isShowMealDetailDialog.value = false
}
// 弹窗确认
const handlerConfirm = () => {
  isShowMealDetailDialog.value = false
}
onMounted(() => {
  getDataList()
})
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res]: any = await to(
    apiBackgroundFundSupervisionChannelCanteenManagementChannelMealAccompanyingListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    let results = data.results || []
    // 处理陪餐管理数据
    if (results && results.length > 0) {
      oldTableData.value = cloneDeep(results)
      results = getNewListByGoodList(results)
    }
    console.log("getDataList", results)
    tableData.value = cloneDeep(results)
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
// 获取新列表，根据菜品列表
const getNewListByGoodList = (list: any[]) => {
  if (!list || list.length === 0) {
    return []
  }
  // 用一个新的列表存储拿出来的菜品数据
  let newList: any[] = []
  list.forEach((item: any) => {
    let foodRecordList = item.food_record_list || []
    let setMealRecordList = item.set_meal_record_list || []
    item.room_clean_type =
      getCurrentNameByType("room_clean_type", item.room_clean_type) +
      "、" +
      getCurrentNameByType("room_attitude_type", item.room_attitude_type)
    item.area_clean_type =
      getCurrentNameByType("area_clean_type", item.area_clean_type) +
      "、" +
      getCurrentNameByType("area_waste_type", item.area_waste_type)
    item.oa_clean_type =
      getCurrentNameByType("oa_clean_type", item.oa_clean_type) +
      "、" +
      getCurrentNameByType("oa_operate_type", item.oa_operate_type)
    item.tda_clean_type =
      getCurrentNameByType("tda_clean_type", item.tda_clean_type) +
      "、" +
      getCurrentNameByType("tda_disinfection_type", item.tda_disinfection_type)
    item.operation_type = getCurrentNameByType("operation_type", item.operation_type)
    foodRecordList = foodRecordList.concat(setMealRecordList)
    if (foodRecordList && foodRecordList.length > 0) {
      foodRecordList.forEach((subItem: any, index: number) => {
        newList.push({
          ...item,
          food_name: getFoodName(subItem),
          gg_excellent_type_alias: subItem.gg_excellent_type_alias,
          zl_excellent_type_alias: subItem.zl_excellent_type_alias,
          quantity_type_alias: subItem.quantity_type_alias,
          price_type_alias: subItem.price_type_alias,
          food_remark: subItem.remark,
          isMerge: foodRecordList && foodRecordList.length > 1,
          spanNumber: foodRecordList && foodRecordList.length > 1 ? (index === 0 ? foodRecordList.length : 0) : 1
        })
      })
    } else {
      newList.push({
        ...item,
        foodName: "",
        gg_excellent_type_alias: "",
        zl_excellent_type_alias: "",
        quantity_type_alias: "",
        price_type_alias: "",
        food_remark: "",
        isMerge: false,
        spanNumber: 1
      })
    }
  })
  return newList
}
// 获取人员列表
const getPersonList = (list: any[]) => {
  return getPersonNameByList(list)
}
// 获取名称
const getCurrentNameByType = (type: string, value: any) => {
  console.log("getCurrentNameByType", type, value)
  return getNameByType(type, value)
}

// 获取详情菜品名称
const getFoodName = (row: any) => {
  let name = row.name
  let price = row.price ? divide(row.price) : "0.00"
  return `${name}  ¥${price}/份`
}

// 打印
const gotoPrint = () => {
  let tabbleSetting = cloneDeep(tableSetting.value)
  tabbleSetting = tabbleSetting.filter((item: any) => item.prop !== "operation")
  setLocalStorage("print_setting", tabbleSetting)
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: printType,
      print_title: "陪餐管理",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiBackgroundFundSupervisionChannelCanteenManagementChannelMealAccompanyingListPost",
      params: JSON.stringify({
        ...formatQueryParams(searchFormSetting.value),
        page: 1,
        page_size: 9999
      })
    }
  })
  console.log("gotoPrint", href)
  window.open(href, "_blank")
}
export interface SpanMethodProps {
  row: any
  column: TableColumnCtx<any>
  rowIndex: number
  columnIndex: number
}
// 第一到第九列值相同的进行合并单元格
const customSpanMethod = ({ row, column, rowIndex, columnIndex }: SpanMethodProps) => {
  console.log("customSpanMethod")
  if ((columnIndex < 10 || columnIndex === 16) && row.isMerge) {
    return { rowspan: row.spanNumber, colspan: 1 }
  }
  // 其他列不合并
  return { rowspan: 1, colspan: 1 }
}
</script>
<style lang="scss">
.meal-management {
  padding: 0 20px;

  .el-popper {
    max-width: 300px;
  }
}
</style>
