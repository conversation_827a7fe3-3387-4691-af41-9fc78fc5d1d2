export type TFeedbackForm = {
  school_name: string
  feedback_person: string
  person_no: string
  mobile: string
  create_time: string
  type: string
  content: string
  imgs: string
  reply: string
}

export type TInquiryDetailForm = {
  id?: string | number
  inquiry_name: string
  valid_time: string
  inquiry_num: string
  type: string
  inquiry_warehouse: string
  inquiry_person: string
  supplierList: Array<any>
}

export type TPersonMealForm = {
  person: string
  pantry: string
  dining_area: string
  other_area: string
  dishware_cleaning: string
  bright_kitchen: string
  personImgList: Array<any>
  signImgList: Array<any>
  remark: string
}
