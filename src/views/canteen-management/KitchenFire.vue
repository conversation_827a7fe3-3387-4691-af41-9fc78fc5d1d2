<template>
  <div class="kitchen-fire container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div ref="tableWrapperRef" :style="{ height: maxheight }" class="content m-t-20px">
      <div class="left">
        <kitchen-tree @treeClick="handlerThreeChange" />
      </div>
      <div class="right">
        <div class="right-left">
          <div>视频监控</div>
          <div class="flex flex-wrap m-t-10px">
            <div
              v-for="(item, index) in imgList"
              :key="index"
              :class="[
                imgList.length > 1 ? 'img-tag' : 'img-tag-big',
                'img-layout',
                index != 0 && index != 2 ? 'm-l-16px' : '',
                index > 1 ? 'm-t-16px' : ''
              ]"
            >
              <img :src="item.img" alt="" class="cursor-pointer" @click="handlerShowPhoto(item.img)" />
              <div class="img-down flex justify-between items-center">
                <div class="m-l-10px">{{ item.name }}</div>
                <div class="m-r-10px">
                  <img :src="icSearchWhite" class="search-img cursor-pointer" @click="handlerShowPhoto(item.img)" />
                </div>
              </div>
            </div>
          </div>
          <div class="flex m-t-5px">
            <div>今日识别</div>
            <div class="m-l-20px">未带口罩：<span class="ps-red-txt">10</span></div>
            <div class="m-l-20px">未戴帽子：<span class="ps-red-txt">5</span></div>
            <div class="m-l-20px">未穿厨师服：<span class="ps-red-txt">1</span></div>
            <div class="m-l-20px">打手机：<span class="ps-red-txt">10</span></div>
            <div class="m-l-20px">动火离人：<span class="ps-red-txt">2</span></div>
            <div class="m-l-20px">陌生人：<span class="ps-red-txt">2</span></div>
          </div>
        </div>
        <div class="right-right">
          <div class="flex items-center justify-between">
            <div>风险预警</div>
            <div class="">
              <el-select
                v-model="riskLevel"
                placeholder="请选择"
                class="w-120px"
                style="width: 150px"
                @change="changeRiskLevel"
              >
                <el-option v-for="item in riskLevelList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
          </div>
          <div class="m-b-20px">
            <vue3-seamless-scroll :list="riskList" :step="0.5" class="scrollFlag">
              <div v-for="(item, index) in riskList" :key="index" :class="['flex', 'm-t-20px']">
                <img :src="item.img" class="w-100px h-90px cursor-pointer" @click="handlerShowPhoto(item.img)" />
                <div class="m-l-10px">
                  <div>
                    算法类型：<span>{{ item.type }}</span>
                  </div>
                  <div class="m-t-20px">
                    区域：<span>{{ item.area }}</span>
                  </div>
                  <div class="m-t-20px">
                    检查时间：<span>{{ item.time }}</span>
                  </div>
                </div>
              </div>
            </vue3-seamless-scroll>
          </div>
        </div>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
  </div>
</template>
<script lang="ts" setup>
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted, watch, computed } from "vue"
import SearchForm from "@/components/SearchForm/index.vue"
import { SEARCH_FORM_SETTING_KITCHEN_FIRE } from "./constants"
import KitchenTree from "./compontents/KitchenTree.vue"
import useScreenHook from "@/hooks/useScreen"
import bgMclz1 from "@/assets/test/bg_mclz_1.png"
import bgMclz2 from "@/assets/test/bg_mclz_2.png"
import bgMclz3 from "@/assets/test/bg_mclz_3.png"
import bgMclz4 from "@/assets/test/bg_mclz_4.png"
import { kitchenData } from "./data"
import bgMclzShaona from "@/assets/test/bg_mclz_shaona.png"
import bgMclzDianxinfang from "@/assets/test/bg_mclz_dianxinfang.png"
import icSearchWhite from "@/assets/layouts/ic_search_white.png"
import { ElMessage } from "element-plus"

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

const tableWrapperRef = ref()
const maxheight = useScreenHook(tableWrapperRef, 120).maxHeight
const defaultList = [
  {
    name: "炒锅1",
    img: bgMclz1
  },
  {
    name: "炒锅2",
    img: bgMclz2
  },
  {
    name: "上什",
    img: bgMclz3
  },
  {
    name: "砧板",
    img: bgMclz4
  }
]
const imgList = ref(cloneDeep(defaultList))
// 搜索
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_KITCHEN_FIRE))
// 风险预警
const riskLevel = ref("全部")
const riskLevelList = ref([
  {
    label: "全部",
    value: "全部"
  },
  {
    label: "未带口罩",
    value: "未带口罩"
  },
  {
    label: "未带厨师帽",
    value: "未带厨师帽"
  },
  {
    label: "未穿厨师服",
    value: "未穿厨师服"
  },
  {
    label: "打手机",
    value: "打手机"
  },
  {
    label: "动火离人",
    value: "动火离人"
  },
  {
    label: "陌生人",
    value: "陌生人"
  }
])
const schoolHeight = computed(() => {
  return maxheight.value - 70 + "px"
})
const imgHeight = computed(() => {
  return (maxheight.value - 80) / 2 + "px"
})
const imgHeigh2 = computed(() => {
  return maxheight.value - 70 + "px"
})
//
const riskList = ref<Array<any>>(cloneDeep(kitchenData))
// 搜索
const changeSearch = () => {}
// 重置
const handlerReset = () => {}
// 树点击
const handlerThreeChange = (data: any) => {
  console.log("handlerThreeChange", data)
  const name = data.name || ""
  switch (name) {
    case "东区食堂":
      imgList.value = cloneDeep(defaultList)
      break
    case "热菜区":
      imgList.value = cloneDeep(defaultList)
      break

    case "烧腊区":
      imgList.value = [{ name: name, img: bgMclzShaona }]
      break
    case "点心房":
      imgList.value = [{ name: name, img: bgMclzDianxinfang }]
      break
    case "炒锅1":
      imgList.value = [{ name: name, img: bgMclz1 }]
      break
    case "炒锅2":
      imgList.value = [{ name: name, img: bgMclz2 }]
      break
    case "上什":
      imgList.value = [{ name: name, img: bgMclz3 }]
      break
    case "砧板":
      imgList.value = [{ name: name, img: bgMclz4 }]
      break
    default:
      break
  }
}

// 切换风险类型
const changeRiskLevel = (data: any) => {
  console.log("changeRiskLevel", data)
  const list: Array<any> = cloneDeep(kitchenData)
  if (data && data !== "全部") {
    riskList.value = list.filter((item: any) => {
      return item.type == data
    })
  } else {
    {
      riskList.value = list
    }
  }

  console.log("riskList.value", riskList.value)
}
const handlerShowPhoto = (data: any) => {
  console.log("handlerShowPhoto", data)
  const img = data || ""
  if (!img) {
    return ElMessage.error("图片不存在")
  }
  imageList.value = [img]
  imageVisible.value = true
}
</script>
<style lang="scss" scoped>
.kitchen-fire {
  padding: 0 20px;
  .content {
    overflow: auto;
    display: flex;
    .right {
      min-width: 1300px;
      margin-left: 20px;
      background-color: #fff;
      border-radius: 16px;
      padding: 10px;
      flex: 1;
      display: flex;

      .right-left {
        width: 1000px;
        min-width: 1000px;
        .img-tag {
          width: 485px;
          // height: 280px;
          height: v-bind(imgHeight);

          border-radius: 10px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .img-tag-big {
          width: 980px;
          height: v-bind(imgHeigh2);
          border-radius: 10px;
          img {
            width: 100%;
            height: 100%;
            border-radius: 10px;
          }
        }
        .img-layout {
          position: relative;
          .img-down {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 40px;
            background-color: gray;
            opacity: 0.7;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
            color: #fff;
            font-size: 18px;
          }
          .search-img {
            width: 18px !important;
            height: 18px !important;
          }
        }
      }
      .right-right {
        width: 350px;
        min-width: 350px;
      }
    }
  }
  .scrollFlag {
    overflow: hidden;
    // height: 65vh;
    height: v-bind(schoolHeight);
  }
}
</style>
