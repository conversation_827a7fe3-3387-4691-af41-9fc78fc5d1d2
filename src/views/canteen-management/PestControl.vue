<template>
  <div class="pest-control container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="flex items-center" />
        <div class="table-button">
          <el-button
            type="primary"
            @click="goToExport"
            v-permission="['background_fund_supervision.channel_canteen_management.pest_control_record_export']"
            >导出</el-button
          >
          <el-button type="primary" class="ps-origin-btn-light" @click="gotoPrint">打印</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
          class="no-scale"
        >
          <ps-column :table-headers="tableSetting">
            <template #preventArea="{ row }">
              <div class="line-1">{{ row.prevent_area ? divide(row.prevent_area) + "㎡" : "--" }}</div>
            </template>
            <template #preventDuration="{ row }">
              <div class="line-1">{{ row.prevent_duration ? divide(row.prevent_duration) + "h" : "--" }}</div>
            </template>
            <template #imgs="{ row }">
              <el-button plain link size="small" @click="handlerShowPhoto(row)" type="primary"> 图片 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview-dialog v-model="imageVisible" :imgs="imageList" :currentIndex="0" :title="dialogTitle" />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted, watch } from "vue"
import { SEARCH_FORM_SETTING_PEST_CONTROL, TABLE_SETTING_PEST_CONTROL } from "./constants"
import {
  apiBackgroundFundSupervisionChannelCanteenManagementPestControlRecordPost,
  apiBackgroundFundSupervisionChannelCanteenManagementPestControlRecordExportPost
} from "@/api/canteen"
import { apiBackgroundFundSupervisionSupervisionChannelGetAllOrgPost } from "@/api/user"
import { ElMessage } from "element-plus"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { exportHandle } from "@/utils/exportExcel"
import { setLocalStorage } from "@/utils/storage"
import { useRouter } from "vue-router"
import { divide } from "@/utils"
// 路由
const router = useRouter()
// 表格
const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])
const dialogTitle = ref("图片预览")

// 组织列表
const childrensOrgs = ref<Array<any>>()

// table数据
const tableData = ref([])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_PEST_CONTROL))
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_PEST_CONTROL))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 导出
const printType = "PestControlExport"
// 格式化从参数
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value && data[key].value !== "" && data[key].value !== "全部" && data[key].value.length > 0) {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  // 这里要做一个处理： 监管组织选择，但是所属食堂不选的时候， 要传所选监管组织的父级与子级，如果所属食堂选了，那么只传所属食堂
  if (params.childOrgs && params.childOrgs.length > 0) {
    params.org_ids = params.childOrgs
  } else {
    let orglist = childrensOrgs.value?.map((item) => item.id)
    if (orglist && orglist.length > 0) {
      params.org_ids = orglist
    }
  }
  // 删除
  delete params.childOrgs
  delete params.parentOrgs
  return params
}
// 搜索
const changeSearch = (model: any, type: string, itemType: string) => {
  console.log("changeSearch", model, type, itemType)
  if (itemType !== "parentOrgs") {
    getDataList()
  }
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 导出
const goToExport = () => {
  console.log("goToExport")
  const option = {
    type: printType,
    api: apiBackgroundFundSupervisionChannelCanteenManagementPestControlRecordExportPost,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    }
  }
  exportHandle(option)
}

// 查看照片
const handlerShowPhoto = (row: any) => {
  console.log(row)
  const img = row ? row.images : ""
  dialogTitle.value =
    row.prevent_region +
    (row.measure_alias ? "-" + row.measure_alias : "") +
    (row.category_alias ? "-" + row.category_alias : "")
  if (!img) {
    return ElMessage.error("防制图片不存在")
  }
  imageList.value = cloneDeep(img)
  imageVisible.value = true
}
onMounted(() => {
  getDataList()
  getOrgsList(false)
})

// 获取监管列表
// const getSupervisions = async () => {
//   tableLoading.value = true
//   const [err, res]: any[] = await to(
//     apiBackgroundFundSupervisionSupervisionChannelListPost({
//       page: 1,
//       page_size: 9999,
//       is_list: true
//     })
//   )
//   tableLoading.value = false
//   if (err) {
//     ElMessage.error(err.message)
//     return
//   }
//   if (res && res.code === 0) {
//     let data = res.data || {}
//     let results = data.results || []
//     searchFormSetting.value.org_id.dataList = cloneDeep(results)
//     console.log("getSupervisions", data, results)
//   } else {
//     ElMessage.error(res.msg)
//   }
// }

// 获取所属食堂列表
const getOrgsList = async (isSearchParent: boolean) => {
  tableLoading.value = true
  let params = {
    page: 1,
    page_size: 9999
  }
  if (searchFormSetting.value.parentOrgs.value && searchFormSetting.value.parentOrgs.value.length > 0) {
    Reflect.set(params, "bind_org_ids", searchFormSetting.value.parentOrgs.value)
  }
  const [err, res]: any[] = await to(apiBackgroundFundSupervisionSupervisionChannelGetAllOrgPost(params))

  tableLoading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    let results = data.results || []
    searchFormSetting.value.childOrgs.dataList = cloneDeep(results)
    childrensOrgs.value = cloneDeep(results)
    searchFormSetting.value.childOrgs.value = []
    if (isSearchParent) {
      getDataList()
    }
    console.log("getSupervisions", data, results)
  } else {
    ElMessage.error(res.msg)
  }
}

// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionChannelCanteenManagementPestControlRecordPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    let results = data.results || []
    tableData.value = cloneDeep(results)
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 打印
const gotoPrint = () => {
  let tabbleSetting = cloneDeep(tableSetting.value)
  tabbleSetting = tabbleSetting.filter((item: any) => item.prop !== "imgs")
  setLocalStorage("print_setting", tabbleSetting)
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: printType,
      print_title: "有害生物防制",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiBackgroundFundSupervisionChannelCanteenManagementPestControlRecordPost",
      params: JSON.stringify({
        ...formatQueryParams(searchFormSetting.value),
        page: 1,
        page_size: 9999
      })
    }
  })
  window.open(href, "_blank")
}

watch(
  () => searchFormSetting.value.parentOrgs.value,
  (newValue) => {
    searchFormSetting.value.childOrgs.value = []
    console.log("newValue", newValue)
    getOrgsList(true)
  }
)
</script>
<style lang="scss">
.pest-control {
  padding: 0 20px;

  .el-popper {
    max-width: 300px;
  }

  .no-scale-preventRegion-tag {
    font-size: 14px;
  }
}
</style>
