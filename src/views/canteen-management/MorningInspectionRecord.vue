<template>
  <div class="scence-manage container-wrapper">
    <div ref="searchRef">
      <div class="tab-box m-b-10px">
        <el-radio-group v-model="tabType" @change="changeTabHandle" class="ps-radio-btn">
          <el-radio-button
            v-for="tab in tabTypeList"
            :key="tab.value"
            :label="tab.value"
            v-permission="[`${tab.permissions}`]"
            >{{ tab.label }}</el-radio-button
          >
        </el-radio-group>
      </div>
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="table-button">
          <!-- <el-button type="primary" @click="handlerShowSetting">报表设置</el-button> -->
          <el-button
            v-if="tabType === 'detail'"
            type="primary"
            @click="goToExport"
            v-permission="['background_fund_supervision.channel_canteen_management.morning_check_detail_export']"
            >导出</el-button
          >
          <el-button
            v-if="tabType === 'summary'"
            type="primary"
            @click="goToExport"
            v-permission="['background_fund_supervision.channel_canteen_management.morning_check_collect_export']"
            >导出</el-button
          >
          <el-button type="primary" @click="gotoPrint">打印</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #checkResult="{ row }">
              <div :class="row.check_result_alias === '合格' ? '' : 'ps-red-txt'">{{ row.check_result_alias }}</div>
            </template>
            <template #images="{ row }">
              <el-button plain link size="small" @click="handlerShowPhoto(row)" type="primary"> 查看 </el-button>
            </template>
            <template #hand="{ row }">
              <div :class="row.hand_result && row.hand_result !== '正常' ? 'ps-red-txt' : ''">
                {{ row.hand_result }}
              </div>
            </template>
            <template #checkStatus="{ row }">
              <div :class="row.check_status ? '' : 'ps-red-txt'">{{ row.check_status ? "已晨检" : "未晨检" }}</div>
            </template>
            <template #temperature="{ row }"> {{ row.temperature ? row.temperature + "°C" : "" }} </template>
            <template #extraField="{ row, col }">
              <div v-if="row.extra && row.extra[col.key] !== undefined">
                <i v-if="row.extra[col.key]" class="el-icon-check" style="color: #fb9c52; font-size: 16px" />
                <span v-else style="color: #f56c6c">异常</span>
              </div>
              <div v-else>-</div>
            </template>
          </ps-column>
        </ps-table>
        <!--统计-->
        <div v-if="tabType !== 'detail' && getIsDateRange" class="collect">
          <div>
            <span>本日汇总：</span>
            <span>应晨检{{ collect.total_count }}人</span>
            <span class="m-l-20px">实际晨检{{ collect.check_count }}人</span>
            <span class="m-l-20px">未晨检{{ collect.not_check }}人</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
    <!--弹窗图片-->
    <pic-details-dialog v-model:isShow="showPicDialog" :images="imageList" :name="picName" />
    <!--设置弹窗-->
    <!-- <print-setting
      v-model="dialogPrintVisible"
      :table-setting="tableSetting"
      :default-checked-setting="currentTableSetting"
      @close-dialog="closeSettingDialog"
      @confirm-dialog="confirmSettingDialog"
    /> -->
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted, computed, nextTick } from "vue"
import {
  SEARCH_SETTING_MORNING_INSPECTION_DETAILS,
  SEARCH_SETTING_MORNING_INSPECTION_SUMMARY,
  TABLE_HEAD_DATA_MORNING_INSPECTION_DETAILS,
  TABLE_HEAD_DATA_MORNING_INSPECTION_SUMMARY
} from "./constants"
import {
  apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckCollectPost,
  apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckCollectExportPost,
  apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckDetailPost,
  apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckDetailExportPost
} from "@/api/supervision"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { exportHandle } from "@/utils/exportExcel"
import { useRouter } from "vue-router"
import { ElMessage } from "element-plus"
import PicDetailsDialog from "./compontents/PicDetailsDialog.vue"
import { setLocalStorage } from "@/utils/storage"
// import PrintSetting from "@/components/PrintSetting/index.vue"
// import useTabelSettingHook from "@/hooks/useTableSetting.hook"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)
const router = useRouter()

// tab切换
const tabType = ref("")
// tab切换列表
const tabTypeList = ref([
  {
    label: "晨检明细",
    value: "detail",
    permissions: "background_fund_supervision.channel_canteen_management.morning_check_detail"
  },
  {
    label: "晨检汇总",
    value: "summary",
    permissions: "background_fund_supervision.channel_canteen_management.morning_check_collect"
  }
])

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any>({})
const showPicDialog = ref(false) // 是否显示图片弹窗
const picName = ref("") // 名字

// table数据
const tableData = ref<Array<any>>([])
const loading = ref(false)
const searchFormSetting = ref<any>(cloneDeep(SEARCH_SETTING_MORNING_INSPECTION_DETAILS))
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_HEAD_DATA_MORNING_INSPECTION_DETAILS))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 导出
const printType = ref("SupervisionCanteenSafetyCheckDetail")
// 报表设置弹窗
// const dialogPrintVisible = ref(false)
// const { currentTableSetting, initPrintSetting, setPrintSettingInfo } = useTabelSettingHook(
//   tableSetting.value,
//   printType.value
// )
// 汇总
const collect = reactive({
  total_count: 0,
  check_count: 0,
  not_check: 0
})

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "" && data[key].value !== null) {
      if (key !== "select_time") {
        if (data[key].value !== "全部") {
          params[key] = data[key].value
        }
      } else if (data[key].value.length > 0) {
        if (tabType.value === "detail") {
          params.start_time = data[key].value[0] + " 00:00:00"
          params.end_time = data[key].value[1] + " 23:59:59"
        } else {
          params.start_date = data[key].value[0]
          params.end_date = data[key].value[1]
        }
      }
    }
  }
  return params
}
// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}

// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 导出
const goToExport = () => {
  console.log("goToExport")

  const option = {
    type: printType.value,
    api:
      tabType.value === "detail"
        ? apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckDetailExportPost
        : apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckCollectExportPost,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    }
  }
  exportHandle(option)
}

// 查看照片
const handlerShowPhoto = (row: any) => {
  console.log(row)
  let imgData = {
    person_face_image: row.images?.scene_img,
    picture_img: row.images?.picture_img,
    picture_back_img: row.images?.picture_back_img
  }
  imageList.value = imgData
  picName.value = row.name
  showPicDialog.value = true
}

// 切换tab
const changeTabHandle = (e: any) => {
  console.log("changeTabHandle", e)
  setTabDataHandle()
}
// 设置tab数据
const setTabDataHandle = () => {
  if (tabType.value === "detail") {
    searchFormSetting.value = cloneDeep(SEARCH_SETTING_MORNING_INSPECTION_DETAILS)
    tableSetting.value = cloneDeep(TABLE_HEAD_DATA_MORNING_INSPECTION_DETAILS)
    printType.value = "SupervisionCanteenSafetyCheckDetail"
  } else {
    searchFormSetting.value = cloneDeep(SEARCH_SETTING_MORNING_INSPECTION_SUMMARY)
    tableSetting.value = cloneDeep(TABLE_HEAD_DATA_MORNING_INSPECTION_SUMMARY)
    printType.value = "SupervisionCanteenSafetyCheckCollect"
  }
  // initPrintSetting()
  tableData.value = []
  getDataList()
}
// 获取数据
const getDataList = async () => {
  tableLoading.value = true
  let params = {
    ...formatQueryParams(searchFormSetting.value),
    page: pageConfig.currentPage,
    page_size: pageConfig.pageSize
  }
  const [err, res] =
    tabType.value === "detail"
      ? await to(apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckDetailPost(params))
      : await to(apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckCollectPost(params))
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    pageConfig.total = data.count
    tableData.value = cloneDeep(data.results)
    // 统计
    // this.setCollectData(res)
    let collectData: any = data.collect || {}
    collect.total_count = collectData.total_count || 0
    collect.check_count = collectData.check_count || 0
    collect.not_check = collectData.not_check || 0
  } else {
    ElMessage.error(res.msg)
  }
}

// 判断是不是日期是同一天，同一天才展示，不是同一天不展示
const getIsDateRange = computed(() => {
  try {
    let startDate = searchFormSetting.value.select_time.value[0]
    let endDate = searchFormSetting.value.select_time.value[1]
    console.log("getIsDateRange", startDate, endDate)

    return startDate === endDate
  } catch (error) {
    return false
  }
})
// 打印
const gotoPrint = () => {
  setLocalStorage("print_data", tableData.value)
  let tabbleSetting = cloneDeep(tableSetting.value)
  setLocalStorage(
    "print_setting",
    tabType.value === "detail" ? tabbleSetting.filter((item: any) => item.prop !== "images") : tabbleSetting
  )
  const isShowCollect = tabType.value !== "detail" && getIsDateRange.value ? 1 : 0
  console.log("isShowCollect", isShowCollect)

  if (isShowCollect) {
    let collectList = [
      {
        label: "应晨检：",
        value: collect.total_count,
        unit: "人"
      },
      {
        label: "实际晨检：",
        value: collect.check_count,
        unit: "人"
      },
      {
        label: "未晨检：",
        value: collect.not_check,
        unit: "人"
      }
    ]
    setLocalStorage("collect" + printType.value, collectList)
  }
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: printType.value,
      print_title: tabType.value === "detail" ? "晨检明细" : "晨检汇总",
      result_key: "result", // 返回的数据处理的data keys
      api:
        tabType.value === "detail"
          ? "apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckDetailPost"
          : "apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckCollectPost", // 请求的api
      params: JSON.stringify({
        ...formatQueryParams(searchFormSetting.value),
        page: 1,
        page_size: pageConfig.total || 10
      }),
      isShowCollect: isShowCollect // 是否显示汇总
    }
  })
  window.open(href, "_blank")
}

// 显示设置
// const handlerShowSetting = () => {
//   dialogPrintVisible.value = true
// }
// 关闭弹窗
// const closeSettingDialog = () => {
//   dialogPrintVisible.value = false
// }
// 确认设置弹窗
// const confirmSettingDialog = (value: Array<any>) => {
//   console.log("value", value)
//   currentTableSetting.value = cloneDeep(value)
//   setPrintSettingInfo(value)
// }

// 初始化判断当前应该显示哪个页面
import { useUserStoreHook } from "@/store/modules/user"
const { roles } = useUserStoreHook()
const setTabType = () => {
  let flag: any[] = []
  tabTypeList.value.forEach((item: any) => {
    if (roles.includes(item.permissions)) {
      flag.push(item)
    }
  })
  if (flag.length && flag.length === 1) {
    tabType.value = flag[0].value
  } else {
    tabType.value = "detail"
  }
}

onMounted(() => {
  setTabType()
  nextTick(() => {
    setTabDataHandle()
    getDataList()
  })
})
</script>
<style lang="scss">
.scence-manage {
  padding: 0 20px;

  .el-popper {
    max-width: 300px;
  }
}

.container-wrapper {
  .table-wrapper {
    .table-content {
      position: relative;

      .collect {
        position: absolute;
        left: 20px;
        bottom: 40px;
      }
    }
  }
}
</style>
