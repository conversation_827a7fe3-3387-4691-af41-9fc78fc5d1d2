import { getPreDate } from "@/utils/date"

// 餐段
export const TYPES_MEAL = [
  { label: "早餐", value: "breakfast" },
  { label: "午餐", value: "lunch" },
  { label: "下午茶", value: "afternoon" },
  { label: "晚餐", value: "dinner" },
  { label: "夜宵", value: "supper" },
  { label: "凌晨餐", value: "morning" }
]

// 有害生物防制措施
export const TYPE_PEST_CONTROL = [
  {
    label: "投饵",
    value: "feeding"
  },
  {
    label: "电蚊器",
    value: "electric_trap"
  },
  {
    label: "喷粉",
    value: "dusting"
  },
  {
    label: "烟雾",
    value: "smoke"
  },
  {
    label: "超低容量喷雾",
    value: "low_spray"
  },
  {
    label: "滞留性喷洒",
    value: "retention_spray"
  }
]

// 有害生物类别
export const TYPE_PEST_CATEGORIES = [
  {
    label: "鼠",
    value: "mouse"
  },
  {
    label: "蟑螂",
    value: "cockroach"
  },
  {
    label: "蚊",
    value: "mosquito"
  },
  {
    label: "苍蝇",
    value: "fly"
  },
  {
    label: "蚂蚁",
    value: "ant"
  },
  {
    label: "飞虫",
    value: "flying_insect"
  },
  {
    label: "其他",
    value: "other"
  }
]

// 留样记录
export const SEARCH_FORM_SETTING_SAMPLE_RECORD = {
  date_type: {
    type: "select",
    label: "",
    value: "reserved_time",
    dataList: [
      {
        label: "留样时间",
        value: "reserved_time"
      },
      {
        label: "入柜时间",
        value: "entry_date"
      },
      {
        label: "离柜时间",
        value: "exit_time"
      },
      {
        label: "销样时间",
        value: "xiaoyang_time"
      }
    ],
    placeholder: "请选择",
    clearable: false
  },
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "",
    value: [getPreDate(3, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  org_id: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: "",
    placeholder: "请选择",
    multiple: false,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  },
  organization_ids: {
    type: "canteenOrgs",
    label: "所属组织",
    value: [],
    placeholder: "请选择",
    orgsId: [],
    labelWidth: "140px",
    maxWidth: "200px",
    multiple: true,
    clearable: true,
    collapseTags: true
  },
  meal_type: {
    type: "select",
    label: "留样餐段",
    value: [],
    dataList: [...TYPES_MEAL],
    placeholder: "请选择",
    clearable: true,
    multiple: true,
    collapseTags: true
  },
  menu_type: {
    type: "select",
    label: "留样菜谱",
    labelWidth: "70px",
    value: "",
    dataList: [],
    placeholder: "请选择",
    clearable: true
  },
  food_name: {
    type: "input",
    label: "菜品",
    labelWidth: "80px",
    value: "",
    placeholder: "请输入",
    clearable: true,
    maxlength: 20
  },
  reserved_user: {
    type: "input",
    label: "留样员",
    value: "",
    placeholder: "请输入",
    clearable: true,
    maxlength: 20
  },
  sample_exit_user: {
    type: "input",
    label: "取样员",
    value: "",
    placeholder: "请输入",
    clearable: true,
    maxlength: 20
  },
  sample_entry_user: {
    type: "input",
    label: "入柜员",
    labelWidth: "70px",
    value: "",
    placeholder: "请输入",
    clearable: true,
    maxlength: 20
  },
  reserved_status: {
    type: "select",
    label: "留样状态",
    labelWidth: "80px",
    value: "all",
    dataList: [
      {
        label: "全部",
        value: "all"
      },
      {
        label: "已留样",
        value: "reserved"
      },
      {
        label: "未留样",
        value: "not_reserved"
      }
    ],
    placeholder: "请选择",
    clearable: false
  },
  entry_cupboard: {
    type: "select",
    label: "入柜状态",
    value: "all",
    dataList: [
      {
        label: "全部",
        value: "all"
      },
      {
        label: "是",
        value: true
      },
      {
        label: "否",
        value: false
      }
    ],
    placeholder: "请选择",
    clearable: false
  },
  entry_device_ids: {
    type: "select",
    label: "入柜设备",
    value: [],
    dataList: [],
    placeholder: "请选择",
    clearable: true,
    multiple: true,
    collapseTags: true
  },
  category_ids: {
    type: "cascader",
    label: "菜品分类",
    value: [],
    placeholder: "请选择分类",
    clearable: true,
    filterable: true,
    collapseTags: true,
    showAllLevels: false,
    labelWidth: "70px",
    dataList: [],
    props: {
      value: "id",
      label: "name",
      multiple: true,
      children: "children",
      emitPath: false
    }
  }
}

// 留样记录
export const TABLE_SETTING_SAMPLE_RECORD = [
  {
    label: "留样时间",
    prop: "reserved_time",
    width: "170px",
    align: "center"
  },
  {
    label: "监管组织",
    prop: "org_name",
    width: "170px",
    align: "center"
  },
  {
    label: "所属组织",
    prop: "organization_name",
    align: "center",
    width: "170px",
    "show-overflow-tooltip": true
  },
  {
    label: "所属菜谱",
    prop: "menu_name",
    width: "170px",
    align: "center"
  },
  {
    label: "餐段",
    prop: "meal_type_alias",
    align: "center"
  },
  {
    label: "菜品",
    prop: "food_name",
    width: "150px",
    align: "center",
    "show-overflow-tooltip": true
  },
  {
    label: "菜品分类",
    prop: "food_name",
    width: "150px",
    align: "center",
    "show-overflow-tooltip": true
  },
  {
    label: "留样状态",
    prop: "reserved_status_alias",
    width: "170px",
    align: "center"
  },
  {
    label: "留样数量",
    prop: "food_count",
    width: "170px",
    align: "center"
  },
  {
    label: "留样重量",
    prop: "food_weight",
    width: "170px",
    align: "center",
    unit: "g"
  },
  {
    label: "是否入柜",
    prop: "entry_cupboard",
    slot: "entryCupboard",
    align: "center",
    width: "170px"
  },
  {
    label: "入柜时间",
    prop: "entry_cupboard_time",
    align: "center",
    width: "170px"
  },
  {
    label: "存放时长",
    prop: "store_time",
    align: "center",
    width: "170px"
  },
  {
    label: "离柜时间",
    prop: "exit_cupboard_time",
    align: "center",
    width: "170px"
  },
  {
    label: "销样时间",
    prop: "",
    align: "center",
    width: "170px"
  },
  {
    label: "入柜设备",
    prop: "entry_device",
    align: "center",
    width: "170px"
  },
  {
    label: "当前柜内温度",
    prop: "temperature",
    slot: "temperature",
    align: "center",
    width: "170px"
  },
  {
    label: "留样员",
    prop: "reserved_user_name",
    width: "200px",
    align: "center",
    slot: "reservedUserName",
    "show-overflow-tooltip": true
  },
  {
    label: "入柜员",
    prop: "sample_entry_user",
    slot: "sampleEntryUser",
    width: "200px",
    align: "center",
    "show-overflow-tooltip": true
  },
  {
    label: "取样员",
    prop: "sample_exit_user",
    slot: "sampleExitUser",
    width: "200px",
    align: "center",
    "show-overflow-tooltip": true
  },
  // {
  //   label: "未留样原因",
  //   prop: "not_reserved_reason",
  //   slot: "notReservedReason",
  //   width: "150px",
  //   "show-overflow-tooltip": true
  // },
  {
    label: "未入柜原因",
    prop: "not_entry_reason",
    slot: "notEntryReason",
    width: "150px",
    align: "center",
    "show-overflow-tooltip": true
  },
  {
    label: "留样照片",
    prop: "img",
    width: "170px",
    align: "center",
    fixed: "right",
    slot: "operationNew"
  }
]

// 晨检明细 筛选设置
export const SEARCH_SETTING_MORNING_INSPECTION_DETAILS = {
  select_time: {
    type: "daterange",
    label: "晨检时间",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>,
    clearable: false
  },
  org_id: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: "",
    placeholder: "请选择",
    multiple: false,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  },
  organization_ids: {
    type: "canteenOrgs",
    label: "所属组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  },
  name: {
    type: "input",
    value: "",
    label: "晨检人",
    dataList: [],
    placeholder: "请输入",
    maxlength: 20,
    clearable: true
  },
  check_result: {
    type: "select",
    value: "全部",
    label: "晨检结果",
    dataList: [
      {
        label: "全部",
        value: "全部"
      },
      {
        label: "成功",
        value: "0"
      },
      {
        label: "失败",
        value: "1"
      }
    ],
    clearable: false
  }
}

// 晨检汇总 筛选设置
export const SEARCH_SETTING_MORNING_INSPECTION_SUMMARY = {
  select_time: {
    type: "daterange",
    label: "晨检时间",
    value: [getPreDate(0, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>,
    clearable: false
  },
  org_id: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: "",
    placeholder: "请选择",
    multiple: false,
    clearable: true,
    collapseTags: true
  },
  organization_ids: {
    type: "canteenOrgs",
    label: "所属组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  },
  name: {
    type: "input",
    value: "",
    label: "晨检人",
    dataList: [],
    placeholder: "请输入",
    maxlength: 20,
    clearable: true
  },
  is_check: {
    type: "select",
    value: "全部",
    label: "晨检状态",
    dataList: [
      {
        label: "全部",
        value: "全部"
      },
      {
        label: "已晨检",
        value: true
      },
      {
        label: "未晨检",
        value: false
      }
    ],
    clearable: false
  }
}

// 晨检明细 表格设置
export const TABLE_HEAD_DATA_MORNING_INSPECTION_DETAILS = [
  { label: "晨检时间", prop: "check_time", align: "center" },
  { label: "监管组织", prop: "org_name", align: "center" },
  { label: "所属组织", prop: "organization_name", align: "center" },
  { label: "晨检人姓名", prop: "name", align: "center" },
  { label: "晨检结果", prop: "check_result_alias", slot: "checkResult", align: "center" },
  { label: "不合格原因", prop: "remark", align: "center" },
  { label: "健康证是否有效", prop: "health_certificate_status_alias", align: "center" },
  { label: "体温", prop: "temperature", slot: "temperature", align: "center" },
  { label: "手部识别结果", prop: "hand_result", align: "center" },
  { label: "是否有腹泻和咽喉炎症", prop: "risk_type_one_alias", align: "center", width: "200px" },
  { label: "健康调查", prop: "22", align: "center" },
  {
    prop: "gerenweisheng",
    label: "个人卫生",
    children: [
      { prop: "发帽", label: "发帽", slot: "extraField", align: "center" },
      { prop: "工服", label: "工服", slot: "extraField", align: "center" },
      { prop: "指甲", label: "指甲", slot: "extraField", align: "center" },
      { prop: "恶心", label: "恶心", slot: "extraField", align: "center" },
      { prop: "饰物", label: "饰物", slot: "extraField", align: "center" }
    ]
  },
  {
    prop: "xiaohuidao",
    label: "消化道",
    children: [
      { prop: "呕吐", label: "呕吐", slot: "extraField", align: "center" },
      { prop: "腹痛", label: "腹痛", slot: "extraField", align: "center" },
      { prop: "腹泻", label: "腹泻", slot: "extraField", align: "center" }
    ]
  },
  {
    prop: "pifu",
    label: "皮肤",
    children: [
      { prop: "外伤", label: "外伤", slot: "extraField", align: "center" },
      { prop: "烫伤", label: "烫伤", slot: "extraField", align: "center" },
      { prop: "疖肿", label: "疖肿", slot: "extraField", align: "center" },
      { prop: "湿疹", label: "湿疹", slot: "extraField", align: "center" }
    ]
  },
  {
    prop: "huxidao",
    label: "呼吸道",
    children: [
      { prop: "黄疸", label: "黄疸", slot: "extraField", align: "center" },
      { prop: "咽痛", label: "咽痛", slot: "extraField", align: "center" },
      { prop: "咳嗽", label: "咳嗽", slot: "extraField", align: "center" },
      { prop: "流涕", label: "流涕", slot: "extraField", align: "center" }
    ]
  },
  { label: "图片", prop: "images", slot: "images", align: "center" }
]

// 晨检汇总 表格设置
export const TABLE_HEAD_DATA_MORNING_INSPECTION_SUMMARY = [
  { label: "晨检时间", prop: "check_time", align: "center" },
  { label: "监管组织", prop: "org_name", align: "center" },
  { label: "所属组织", prop: "organization_name", align: "center" },
  { label: "晨检人姓名", prop: "name", align: "center" },
  { label: "晨检状态", prop: "check_status", slot: "checkStatus", align: "center" },
  { label: "晨检次数", prop: "count", align: "center" },
  { label: "合格数", prop: "qualified_count", align: "center" },
  { label: "不合格数", prop: "unqualified_count", align: "center" }
]

// 有害生物防制筛选设置
export const SEARCH_FORM_SETTING_PEST_CONTROL = {
  date_type: {
    type: "select",
    label: "",
    value: "create_date",
    dataList: [
      {
        label: "创建时间",
        value: "create_date"
      },
      {
        label: "防制时间",
        value: "prevent_date"
      }
    ] as Array<any>,
    placeholder: "请选择",
    multiple: false,
    clearable: false,
    maxWidth: "220px"
  },
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>,
    maxWidth: "450px"
  },
  parentOrgs: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  },
  childOrgs: {
    type: "select",
    label: "所属组织",
    value: [],
    dataList: [],
    placeholder: "请选择",
    listNameKey: "name",
    listValueKey: "id",
    multiple: true,
    clearable: true,
    collapseTags: true
  },
  measures: {
    type: "select",
    label: "防制措施",
    value: [],
    dataList: TYPE_PEST_CONTROL,
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true
  },
  categorys: {
    type: "select",
    label: "有害生物类别",
    value: [],
    labelWidth: "140px",
    maxWidth: "170px",
    dataList: TYPE_PEST_CATEGORIES,
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true
  },
  name: {
    type: "input",
    label: "防制人员/监督人/责任人",
    labelWidth: "220px",
    maxWidth: "140px",
    value: "",
    placeholder: "请输入",
    clearable: true
  }
}

// 有害生物防制表格设置
export const TABLE_SETTING_PEST_CONTROL = [
  {
    label: "创建时间",
    prop: "create_time",
    width: "110px",
    align: "center"
  },
  {
    label: "防制时间",
    prop: "prevent_time",
    align: "center",
    width: "110px"
  },
  {
    label: "监管组织",
    prop: "channel_name",
    align: "center",
    // width: "90px",
    "show-overflow-tooltip": true
  },
  {
    label: "所属组织",
    prop: "organization_name",
    align: "center",
    // width: "100px",
    "show-overflow-tooltip": true
  },
  {
    label: "防制区域",
    prop: "prevent_region",
    align: "center",
    width: "180px",
    "show-overflow-tooltip": true
  },
  {
    label: "区域面积",
    prop: "prevent_area",
    align: "center",
    width: "120px",
    slot: "preventArea",
    "show-overflow-tooltip": true
  },
  {
    label: "防制措施",
    prop: "measure_alias",
    align: "center",
    // width: "100px",
    "show-overflow-tooltip": true
  },
  {
    label: "有害生物类别",
    prop: "category_alias",
    align: "center",
    // width: "80px",
    "show-overflow-tooltip": true
  },
  {
    label: "使用药物",
    prop: "using_drug",
    align: "center",
    "show-overflow-tooltip": true
  },
  {
    label: "药物用量",
    prop: "drug_dosage",
    align: "center",
    // width: "100px",
    "show-overflow-tooltip": true
  },
  {
    label: "防制时长/h",
    prop: "prevent_duration",
    align: "center",
    // width: "100px",
    slot: "preventDuration",
    "show-overflow-tooltip": true
  },
  {
    label: "防制人员",
    prop: "prevent_accounts",
    align: "center",
    // width: "80px",
    "show-overflow-tooltip": true
  },
  {
    label: "监督人",
    prop: "supervisor_accounts",
    align: "center",
    // width: "80px",
    "show-overflow-tooltip": true
  },
  {
    label: "责任人",
    prop: "responsible_account",
    align: "center",
    // width: "80px",
    "show-overflow-tooltip": true
  },
  {
    label: "备注",
    prop: "remark",
    align: "center",
    width: "180px",
    "show-overflow-tooltip": true
  },
  {
    label: "操作",
    prop: "imgs",
    align: "center",
    width: "70px",
    slot: "imgs"
  }
]

// 民主监督（食堂）筛选设置
export const SEARCH_FORM_SETTING_DEMOCRATIC_SUPERVISION = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "操作时间",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  source_organization_ids: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  },
  organization_ids: {
    type: "canteenOrgs",
    label: "所属组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    orgsId: []
  },
  reply_status: {
    type: "select",
    label: "回复状态",
    value: "全部",
    dataList: [
      {
        label: "全部",
        value: "全部"
      },
      {
        label: "已回复",
        value: "replied"
      },
      {
        label: "未回复",
        value: "no_reply"
      }
    ],
    placeholder: "请选择",
    clearable: false
  }
}

// 民主监督（食堂）表格设置
export const TABLE_SETTING_DEMOCRATIC_SUPERVISION = [
  {
    label: "反馈时间",
    prop: "create_time",
    align: "center"
  },
  {
    label: "反馈单号",
    prop: "trade_no",
    align: "center"
  },
  {
    label: "监管组织",
    prop: "source_organization_name",
    align: "center"
  },
  {
    label: "所属组织",
    prop: "organization_name",
    align: "center"
  },
  {
    label: "服务态度/分",
    prop: "service_score",
    align: "center",
    width: "90px"
  },
  {
    label: "卫生环境/分",
    prop: "hygiene_score",
    align: "center",
    width: "90px"
  },
  {
    label: "菜品味道/分",
    prop: "food_score",
    align: "center",
    width: "90px"
  },
  {
    label: "反馈内容",
    prop: "remark",
    align: "center",
    width: "180px",
    "show-overflow-tooltip": true
  },
  {
    label: "图片",
    prop: "imgs",
    align: "center",
    width: "70px",
    slot: "imgs"
  },
  {
    label: "反馈人",
    prop: "name",
    align: "center",
    "show-overflow-tooltip": true
  },
  {
    label: "回复",
    prop: "reply_remark",
    width: "180px",
    align: "center",
    "show-overflow-tooltip": true
  },
  {
    label: "回复时间",
    align: "center",
    prop: "reply_time"
  },
  {
    label: "操作员",
    prop: "operator_name",
    align: "center",
    slot: "operatorName",
    "show-overflow-tooltip": true
  }
]

// 民主监督(供应商)筛选设置
export const SEARCH_FORM_SETTING_DEMOCRATIC_SUPERVISION_SUPPLIER = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "操作时间",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  source_organization_ids: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  },
  supplier_manage_ids: {
    type: "select",
    label: "所属供应商",
    value: [],
    placeholder: "请选择",
    dataList: [],
    multiple: true,
    clearable: true,
    listNameKey: "name",
    listValueKey: "id",
    collapseTags: true
  },
  reply_status: {
    type: "select",
    label: "回复状态",
    value: "全部",
    dataList: [
      {
        label: "全部",
        value: "全部"
      },
      {
        label: "已回复",
        value: "replied"
      },
      {
        label: "未回复",
        value: "no_reply"
      }
    ],
    placeholder: "请选择",
    clearable: false
  }
}

// 民主监督(供应商)表格设置
export const TABLE_SETTING_DEMOCRATIC_SUPERVISION_SUPPLIER = [
  {
    label: "反馈时间",
    prop: "create_time",
    align: "center"
  },
  {
    label: "反馈单号",
    prop: "trade_no",
    align: "center"
  },
  {
    label: "监管组织",
    prop: "source_organization_name",
    align: "center"
  },
  {
    label: "所属供应商",
    prop: "supplier_manage_name",
    align: "center"
  },
  {
    label: "服务质量/分",
    prop: "service_score",
    width: "90px",
    align: "center"
  },
  {
    label: "产品质量/分",
    prop: "hygiene_score",
    width: "90px",
    align: "center"
  },
  {
    label: "物资价格/分",
    prop: "food_score",
    width: "90px",
    align: "center"
  },
  {
    label: "反馈内容",
    prop: "remark",
    width: "180px",
    align: "center",
    "show-overflow-tooltip": true
  },
  {
    label: "图片",
    prop: "imgs",
    align: "center",
    width: "70px",
    slot: "imgs"
  },
  {
    label: "反馈人",
    prop: "name",
    align: "center",
    "show-overflow-tooltip": true
  },
  {
    label: "回复",
    prop: "reply_remark",
    width: "180px",
    align: "center",
    "show-overflow-tooltip": true
  },
  {
    label: "回复时间",
    prop: "reply_time",
    align: "center"
  },
  {
    label: "操作员",
    prop: "operator_name",
    slot: "operatorName",
    align: "center",
    "show-overflow-tooltip": true
  }
]

// 市场询价筛选设置
export const SEARCH_FORM_SETTING_MARKET_INQUIRY = {
  selecttime: {
    type: "daterange",
    clearable: false,
    label: "询价时间",
    value: [getPreDate(30, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  // type: {
  //   type: "select",
  //   label: "询价类别",
  //   value: "",
  //   dataList: [
  //     {
  //       label: "果蔬类",
  //       value: "果蔬类"
  //     },
  //     {
  //       label: "肉类",
  //       value: "肉类"
  //     }
  //   ]
  // },
  name: {
    type: "input",
    label: "询价名称",
    value: "",
    placeholder: "请输入",
    clearable: true,
    maxlength: 20
  }
}

// 民市场询价表格设置
export const TABLE_SETTING_MARKET_INQUIRY = [
  {
    label: "创建时间",
    prop: "create_time",
    align: "center"
  },
  {
    label: "询价名称",
    prop: "name",
    align: "center"
  },
  {
    label: "询价商家",
    prop: "inquiry_warehouse",
    slot: "inquiryWarehouse",
    align: "center"
  },
  {
    label: "询价物资",
    prop: "materials_name",
    slot: "materialsName",
    align: "center"
  },
  {
    label: "操作人",
    prop: "operator_name",
    align: "center"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operationNew",
    align: "center"
  }
]

//  比价记录筛选设置
export const SEARCH_FORM_SETTING_COMPARE_RECORD = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "比价时间",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  compare_warehouse: {
    type: "select",
    label: "供应商",
    value: "",
    dataList: [
      {
        label: "宏利鲜果批发（雅宝店)",
        value: "宏利鲜果批发（雅宝店)"
      }
    ],
    clearable: true
  },
  inquiry_name: {
    type: "input",
    label: "询价名称",
    value: "",
    placeholder: "请输入询价名称",
    clearable: true,
    maxlength: 20
  }
}

//  比价记录表格设置
export const TABLE_SETTING_COMPARE_RECORD = [
  {
    label: "比价时间",
    prop: "compare_time"
  },
  {
    label: "询价名称",
    prop: "inquiry_name"
  },
  {
    label: "询价物资数量",
    prop: "inquiry_num"
  },
  {
    label: "询价类别",
    prop: "type"
  },
  {
    label: "比价供应商",
    prop: "compare_warehouse"
  },
  {
    label: "操作人",
    prop: "operator"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operationNew"
  }
]

//  询价详情表格设置
export const TABLE_SETTING_INQUIRY_DETAIL = [
  {
    label: "询价供应商",
    prop: "inquiry_warehouse"
  },
  {
    label: "物资名称",
    prop: "materials_name"
  },
  {
    label: "规格",
    prop: "specifications"
  },
  {
    label: "价格",
    prop: "price"
  },
  {
    label: "质量要求",
    prop: "quality_requirement"
  }
]

//  询价物资详情表格设置
export const TABLE_SETTING_MATERIALS_DETAIL = [
  {
    label: "询价商家",
    prop: "merchant_name",
    showOverflowTooltip: true,
    align: "center"
  },
  {
    label: "物资名称",
    prop: "material_name",
    showOverflowTooltip: true,
    align: "center"
  },
  {
    label: "规格",
    prop: "inquiry_unit",
    showOverflowTooltip: true,
    align: "center"
  },
  {
    label: "单价",
    prop: "inquiry_price",
    showOverflowTooltip: true,
    align: "center"
  }
]
//  比价详情表格设置
export const TABLE_SETTING_COMPARE_DETAIL = [
  {
    label: "物资名称",
    prop: "materials_name"
  },
  {
    label: "规格",
    prop: "specifications"
  },
  {
    label: "平均价",
    prop: "average_price"
  },
  {
    label: "比价供应商",
    prop: "compare_warehouse"
  },
  {
    label: "对比价格",
    prop: "compare_price"
  },
  {
    label: "比价结果",
    prop: "compare_result"
  }
]

//  记录详情表格设置
export const TABLE_SETTING_INQUIRY_RECORD_DETAIL = [
  {
    label: "操作时间",
    prop: "create_time",
    align: "center"
  },
  {
    label: "操作人",
    prop: "operator_name",
    align: "center"
  },
  {
    label: "操作",
    prop: "handle_type",
    align: "center"
  },
  {
    label: "详情",
    prop: "detail",
    align: "center",
    showOverflowTooltip: true
  }
]
//  资产统计筛选设置
export const SEARCH_FORM_SETTING_ASSET_STATISTICS = {
  selecttime: {
    type: "monthrange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "日期筛选",
    labelWidth: "100px",
    value: [getPreDate(0, "YYYY-MM"), getPreDate(0, "YYYY-MM")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  supervision_organization_ids: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: [] as Array<any>,
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  },
  organization_ids: {
    type: "canteenOrgs",
    label: "所属组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    orgsId: []
  }
}
// 资金统计明细
export const TABLE_SETTING_ASSET_DETALIS_RECORD = [
  {
    label: "资产编号",
    prop: "number_no",
    align: "center"
  },
  {
    label: "资产类别",
    prop: "asset_category_alias",
    align: "center"
  },
  {
    label: "资产类型",
    prop: "asset_type_alias",
    align: "center"
  },
  {
    label: "资产分类",
    prop: "asset_classify_name",
    align: "center"
  },
  {
    label: "资产名称",
    prop: "name",
    align: "center"
  },

  {
    label: "原值",
    prop: "original_fee",
    type: "price",
    align: "center"
  },
  {
    label: "累计折旧",
    prop: "depreciation_fee",
    type: "price",
    align: "center"
  },
  {
    label: "净值",
    prop: "net_worth_fee",
    type: "price",
    align: "center"
  },
  {
    label: "数量",
    prop: "quantity",
    align: "center"
  },
  {
    label: "购置日期",
    prop: "purchase_date",
    align: "center"
  },
  {
    label: "变更日期",
    prop: "change_date",
    align: "center"
  },
  {
    label: "单位",
    prop: "unit",
    align: "center"
  },
  {
    label: "存放地址",
    prop: "place",
    align: "center",
    showOverflowTooltip: true
  },
  {
    label: "在用单位",
    prop: "use_unit",
    align: "center",
    showOverflowTooltip: true
  }
]

export const TABLE_SETTING_ASSET_STATISTICS = [
  {
    label: "月份",
    prop: "statistics_date",
    align: "center"
  },
  {
    label: "监管组织",
    prop: "supervision_organization_name",
    align: "center"
  },
  {
    label: "所属组织",
    prop: "organization_name",
    align: "center"
  },
  {
    label: "资产总额",
    prop: "total_fee",
    align: "center",
    type: "price"
  },
  {
    label: "固定资产",
    prop: "gd",
    align: "center",
    children: [
      {
        label: "设备",
        prop: "device_fee",
        align: "center",
        type: "price"
      },
      {
        label: "长期投资",
        prop: "long_term_fee",
        align: "center",
        type: "price"
      }
    ]
  },
  {
    label: "流动资产",
    prop: "ld",
    align: "center",
    children: [
      {
        label: "货币资金",
        prop: "monetary_funds_fee",
        align: "center",
        type: "price"
      },
      {
        label: "预付账款",
        prop: "prepayment_fee",
        align: "center",
        type: "price"
      },
      {
        label: "应收账款",
        prop: "accounts_fee",
        align: "center",
        type: "price"
      },
      {
        label: "存货",
        prop: "stock_fee",
        align: "center",
        type: "price"
      },
      {
        label: "短期投资",
        prop: "short_term_fee",
        align: "center",
        type: "price"
      }
    ]
  },
  {
    label: "操作",
    prop: "operation",
    align: "center",
    slot: "operationNew"
  }
]

//  负债统计筛选设置
export const SEARCH_FORM_SETTING_DEBT_STATISTICS = {
  selecttime: {
    type: "monthrange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "归属时间",
    labelWidth: "100px",
    value: [getPreDate(0, "YYYY-MM"), getPreDate(0, "YYYY-MM")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  bing_org_id_list: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: [] as Array<any>,
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  },
  org_id_list: {
    type: "canteenOrgs",
    label: "所属组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    orgsId: []
  }
}
export const TABLE_SETTING_DEBT_STATISTICS = [
  {
    label: "月份",
    prop: "date_str",
    align: "center"
  },
  {
    label: "监管组织",
    prop: "channel_org_name",
    align: "center"
  },
  {
    label: "所属组织",
    prop: "org_name",
    align: "center"
  },
  {
    label: "负债总数",
    prop: "all_liability_price",
    align: "center",
    type: "price"
  },
  {
    label: "流动负债",
    prop: "ld",
    align: "center",
    children: [
      {
        label: "短期借款",
        prop: "short_borrowing_price",
        align: "center",
        type: "price"
      },
      {
        label: "应付账款",
        prop: "payable_price",
        align: "center",
        type: "price"
      },
      {
        label: "预收账款",
        prop: "pre_harvest_payable_price",
        align: "center",
        type: "price"
      },
      {
        label: "其他",
        prop: "other_liability_price",
        align: "center",
        type: "price"
      }
    ]
  },
  {
    label: "非流动负债",
    prop: "fld",
    align: "center",
    children: [
      {
        label: "长期借款",
        prop: "long_payable_price",
        align: "center",
        type: "price"
      },
      {
        label: "长期应付款",
        prop: "long_borrowing_price",
        align: "center",
        type: "price"
      },
      {
        label: "其他",
        prop: "non_other_liability_price",
        align: "center",
        type: "price"
      }
    ]
  },
  {
    label: "操作",
    prop: "operation",
    align: "center",
    slot: "operationNew"
  }
]
// 负债统计明细
export const TABLE_SETTING_DEBT_DETALIS_RECORD = [
  {
    label: "归属时间",
    prop: "belong_date",
    align: "center"
  },
  {
    label: "更新日期",
    prop: "update_time",
    align: "center"
  },
  {
    label: "负债名称",
    prop: "name",
    align: "center"
  },
  {
    label: "负债类别",
    prop: "liability_category_alias",
    align: "center"
  },
  {
    label: "负债类型",
    prop: "liability_type_alias",
    align: "center"
  },
  {
    label: "债权人",
    prop: "person",
    align: "center"
  },
  {
    label: "负债金额",
    prop: "price",
    align: "center",
    type: "price"
  },
  {
    label: "已还金额",
    prop: "repayment_price",
    align: "center",
    type: "price"
  },
  {
    label: "状态",
    align: "center",
    prop: "liability_state_alias"
  },
  {
    label: "附件",
    prop: "images",
    align: "center",
    slot: "certificate"
  },

  {
    label: "备注",
    align: "center",
    prop: "remark",
    showOverflowTooltip: true
  }
]
// 身份类型
export const IDENTITY_TYPE_LIST = [
  { value: "principal", label: "校长" },
  { value: "vice_principal", label: "副校长" },
  { value: "director", label: "主任" },
  { value: "head_teacher", label: "班主任" },
  { value: "teacher", label: "老师" },
  { value: "parent", label: "家长代表" }
]

// 备餐间(干净类型)
export const CLEAN_TYPE_LIST = [
  { value: "very_clean", label: "非常干净" },
  { value: "generally_clean", label: "一般干净" },
  { value: "not_clean", label: "不干净" },
  { value: "very_unclean", label: "非常不干净" }
]

// 备餐间(服务态度)
export const ATTITUDE_TYPE_LIST = [
  { value: "very_good_attitude", label: "态度很好" },
  { value: "average_attitude", label: "态度一般" },
  { value: "bad_attitude", label: "态度不好" },
  { value: "very_bad_attitude", label: "态度非常差" }
]
// 饮食浪费情况
export const WASTE_TYPE_LIST = [
  { value: "no_waste", label: "不存在浪费" },
  { value: "little_waste", label: "些许浪费" },
  { value: "very_wasteful", label: "非常浪费" }
]

// 操作规范类型
export const OPERATE_TYPE_LIST = [
  { value: "specifications", label: "操作规范" },
  { value: "little", label: "操作存在些许不规范" },
  { value: "improper", label: "操作不规范" },
  { value: "very_irregular", label: "操作非常不规范" }
]
// 消杀类型
export const DISINFECTION_TYPE_LIST = [
  { value: "timely", label: "及时消杀" },
  { value: "little", label: "存在一定时间延迟消杀" },
  { value: "very_untimely", label: "非常不及时消杀" },
  { value: "no_at_all", label: "完全不消杀" }
]

// 运作类型
export const OPERATION_TYPE_LIST = [
  { value: "normal", label: "运作正常" },
  { value: "little", label: "运作存在轻微卡顿" },
  { value: "not", label: "运作不正常" }
]

// 质量类型
export const QUALITY_TYPE_LIST = [
  { value: 3, name: "优秀", label: "excellent" },
  { value: 2, name: "良好", label: "good" },
  { value: 1, name: "差", label: "difference" }
]
// 分量类型
export const AMOUNT_TYPE_LIST = [
  { value: 3, name: "较多", label: "more" },
  { value: 2, name: "适中", label: "moderate" },
  { value: 1, name: "较少", label: "less" }
]
// 价格类型
export const PRICE_TYPE_LIST = [
  { value: 3, name: "实惠", label: "affordable" },
  { value: 2, name: "适中", label: "moderate" },
  { value: 1, name: "偏高", label: "high" }
]

//  陪餐管理筛选设置
export const SEARCH_FORM_SETTING_MEAL_MANAGEMENT = {
  date_type: {
    type: "select",
    label: "",
    value: "create_date",
    dataList: [
      {
        label: "创建时间",
        value: "create_date"
      },
      {
        label: "陪餐时间",
        value: "meal_date"
      }
    ],
    placeholder: "",
    maxWidth: "220px"
  },
  select_time: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "",
    value: [getPreDate(3, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>,
    maxWidth: "460px"
  },
  supervision_organization_ids: {
    type: "supervisionOrgs",
    label: "监管组织",
    labelWidth: "140px",
    maxWidth: "200px",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  },
  organization_ids: {
    type: "canteenOrgs",
    label: "所属组织",
    value: [],
    placeholder: "请选择",
    orgsId: [],
    labelWidth: "140px",
    maxWidth: "200px",
    multiple: true,
    clearable: true,
    collapseTags: true
  },
  meal_type: {
    type: "select",
    label: "餐段",
    value: [],
    dataList: TYPES_MEAL,
    placeholder: "请选择",
    multiple: true,
    labelWidth: "140px",
    maxWidth: "200px",
    clearable: true,
    collapseTags: true
  },
  identity_type: {
    type: "select",
    label: "陪餐人身份",
    value: [],
    dataList: IDENTITY_TYPE_LIST,
    labelWidth: "140px",
    maxWidth: "200px",
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true
  },
  person_name: {
    type: "input",
    label: "陪餐人",
    labelWidth: "140px",
    maxWidth: "200px",
    value: "",
    placeholder: "请输入",
    clearable: true,
    maxlength: 20
  },
  food_name: {
    type: "input",
    label: "菜品",
    labelWidth: "140px",
    maxWidth: "200px",
    value: "",
    placeholder: "请输入",
    clearable: true,
    maxlength: 20
  }
}

// 陪餐管理表格设置
export const TABLE_SETTING_MEAL_MANAGEMENT_ADMIN = [
  {
    label: "创建时间",
    prop: "create_time",
    width: "200px",
    align: "center"
  },
  {
    label: "陪餐时间",
    prop: "meal_time",
    width: "150px",
    align: "center"
  },
  {
    label: "组织",
    prop: "organization_name",
    align: "center"
  },
  {
    label: "餐段",
    prop: "meal_type",
    slot: "meal_type",
    align: "center"
  },
  {
    label: "陪餐人",
    prop: "person",
    slot: "person",
    align: "center",
    width: "150px"
  },
  {
    label: "环境卫生、工作情况评价",
    prop: "environmental_assessment",
    align: "center",
    children: [
      {
        label: "备餐间（含服务态度）",
        prop: "room_clean_type",
        width: "180px",
        align: "center"
      },
      {
        label: "就餐区（含餐饮浪费情况）",
        prop: "area_clean_type",
        width: "250px",
        align: "center"
      },
      {
        label: "其他加工操作区",
        prop: "oa_clean_type",
        width: "150px",
        align: "center"
      },
      {
        label: "餐具消洗",
        prop: "tda_clean_type",
        width: "150px",
        align: "center"
      },
      {
        label: "明厨亮灶运作",
        prop: "operation_type",
        width: "150px",
        align: "center"
      }
    ]
  },
  {
    label: "菜品名称",
    prop: "food_name",
    width: "200px",
    align: "center"
  },
  {
    label: "菜品直观评价",
    prop: "food_assessment",
    align: "center",
    children: [
      {
        label: "感观",
        prop: "gg_excellent_type_alias",
        align: "center"
      },
      {
        label: "质量",
        prop: "zl_excellent_type_alias",
        align: "center"
      },
      {
        label: "份量",
        prop: "quantity_type_alias",
        align: "center"
      },
      {
        label: "价格",
        prop: "price_type_alias",
        align: "center"
      }
    ]
  },
  {
    label: "学生意见和建议",
    prop: "food_remark",
    width: "150px",
    align: "center"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operationNew",
    align: "center",
    width: "100px",
    fixed: "right"
  }
]

// 陪餐管理详情表格设置
export const TABLE_SETTING_MEAL_MANAGEMENT_DETAIL = [
  {
    label: "菜品名称",
    prop: "name",
    slot: "name",
    width: "200px",
    align: "center"
  },
  {
    label: "感观",
    prop: "gg_excellent_type_alias",
    align: "center"
  },
  {
    label: "质量",
    prop: "zl_excellent_type_alias",
    align: "center"
  },
  {
    label: "份量",
    prop: "quantity_type_alias",
    align: "center"
  },
  {
    label: "价格",
    prop: "price_type_alias",
    align: "center"
  },
  {
    label: "学生意见和建议",
    prop: "remark",
    width: "150px",
    align: "center"
  },
  {
    label: "图片",
    prop: "operation",
    slot: "operationNew",
    width: "150px",
    align: "center"
  }
]

//  名厨亮灶筛选设置
export const SEARCH_FORM_SETTING_KITCHEN_FIRE = {
  school_name: {
    type: "select",
    label: "组织名称",
    value: "全部",
    dataList: [
      {
        label: "全部",
        value: "全部"
      },
      {
        label: "华溪实验中学",
        value: "1"
      },
      {
        label: "田家炳实验小学",
        value: "2"
      },
      {
        label: "启元中学",
        value: "3"
      }
    ] as Array<any>,
    placeholder: "请选择",
    multiple: false,
    clearable: false,
    collapseTags: true
  }
}

export const SEARCH_FORM_WEEKLY_RECIPES = {
  select_time: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "日期筛选",
    labelWidth: "80px",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  name: {
    type: "select",
    label: "组织名称",
    value: "" as string,
    dataList: [
      {
        label: "全部",
        value: ""
      },
      {
        label: "华溪实验中学",
        value: "1"
      },
      {
        label: "田家炳实验小学",
        value: "2"
      },
      {
        label: "启元中学",
        value: "3"
      }
    ] as Array<any>,
    placeholder: "请选择",
    multiple: true,
    clearable: false,
    collapseTags: true
  },
  name1: {
    type: "select",
    label: "食谱",
    value: "" as string,
    dataList: [
      {
        label: "全部",
        value: ""
      },
      {
        label: "星期一食谱",
        value: "1"
      },
      {
        label: "星期二食谱",
        value: "2"
      },
      {
        label: "星期三食谱",
        value: "3"
      },
      {
        label: "星期四食谱",
        value: "4"
      },
      {
        label: "星期五食谱",
        value: "5"
      },
      {
        label: "星期六食谱",
        value: "6"
      },
      {
        label: "星期日食谱",
        value: "7"
      }
    ] as Array<any>,
    placeholder: "请选择",
    multiple: true,
    clearable: false,
    collapseTags: true
  }
}
export const TABLE_SETTING_WEEKLY_RECIPES = [
  {
    label: "食谱",
    prop: "recipe"
  },
  {
    label: "餐次",
    prop: "mealNumber"
  },
  {
    label: "食物名称",
    prop: "foodName"
  },
  {
    label: "配料（g）",
    prop: "dosing"
  },
  {
    label: "6-8岁",
    prop: "six"
  },
  {
    label: "9-11岁",
    prop: "nine"
  },
  {
    label: "12-15岁",
    prop: "twelve"
  },
  {
    label: "16-18岁",
    prop: "sixteen"
  }
]
export const WEEKLY_RECIPES_TABLE_DATA = [
  {
    recipe: "",
    mealNumber: "",
    foodName: "",
    dosing: "",
    six: "",
    nine: "",
    twelve: "",
    sixteen: ""
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "早餐",
    foodName: "南瓜饼",
    dosing: "南瓜",
    six: "20",
    nine: "20",
    twelve: "25",
    sixteen: "30"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "早餐",
    foodName: "南瓜饼",
    dosing: "糯米粉",
    six: "25",
    nine: "25",
    twelve: "30",
    sixteen: "35"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "早餐",
    foodName: "肉包",
    dosing: "面粉",
    six: "25",
    nine: "35",
    twelve: "35",
    sixteen: "35"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "早餐",
    foodName: "肉包",
    dosing: "猪肉(瘦)",
    six: "5",
    nine: "10",
    twelve: "15",
    sixteen: "15"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "早餐",
    foodName: "燕麦粥",
    dosing: "大米",
    six: "20",
    nine: "25",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "早餐",
    foodName: "燕麦粥",
    dosing: "燕麦米",
    six: "5",
    nine: "10",
    twelve: "15",
    sixteen: "15"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "早餐",
    foodName: "牛奶",
    dosing: "牛奶",
    six: "250",
    nine: "300",
    twelve: "300",
    sixteen: "300"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "早餐",
    foodName: "炒青菜",
    dosing: "青菜",
    six: "90",
    nine: "100",
    twelve: "100",
    sixteen: "100"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "早餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "5",
    nine: "5",
    twelve: "6",
    sixteen: "6"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "午餐",
    foodName: "米饭",
    dosing: "大米",
    six: "80",
    nine: "100",
    twelve: "100",
    sixteen: "120"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "午餐",
    foodName: "米饭",
    dosing: "荞麦米",
    six: "20",
    nine: "25",
    twelve: "25",
    sixteen: "30"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "午餐",
    foodName: "番茄炒蛋",
    dosing: "番茄",
    six: "30",
    nine: "30",
    twelve: "40",
    sixteen: "45"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "午餐",
    foodName: "番茄炒蛋",
    dosing: "鸡蛋",
    six: "30",
    nine: "30",
    twelve: "40",
    sixteen: "45"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "午餐",
    foodName: "双菇豆腐",
    dosing: "茶树菇",
    six: "10",
    nine: "30",
    twelve: "40",
    sixteen: "60"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "午餐",
    foodName: "双菇豆腐",
    dosing: "金针菇",
    six: "10",
    nine: "30",
    twelve: "40",
    sixteen: "60"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "午餐",
    foodName: "双菇豆腐",
    dosing: "豆腐",
    six: "20",
    nine: "60",
    twelve: "80",
    sixteen: "120"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "午餐",
    foodName: "五香鸡块",
    dosing: "鸡",
    six: "10",
    nine: "10",
    twelve: "15",
    sixteen: "15"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "午餐",
    foodName: "山药排骨汤",
    dosing: "山药",
    six: "25",
    nine: "25",
    twelve: "25",
    sixteen: "30"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "午餐",
    foodName: "山药排骨汤",
    dosing: "猪排骨",
    six: "10",
    nine: "10",
    twelve: "10",
    sixteen: "15"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "午餐",
    foodName: "苹果",
    dosing: "苹果",
    six: "100",
    nine: "100",
    twelve: "150",
    sixteen: "150"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "午餐",
    foodName: "牛奶",
    dosing: "牛奶",
    six: "100",
    nine: "100",
    twelve: "200",
    sixteen: "100"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "午餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "10",
    nine: "10",
    twelve: "12",
    sixteen: "12"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "晚餐",
    foodName: "米饭",
    dosing: "大米",
    six: "80",
    nine: "110",
    twelve: "110",
    sixteen: "120"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "晚餐",
    foodName: "清蒸鱼",
    dosing: "鱼",
    six: "20",
    nine: "40",
    twelve: "40",
    sixteen: "45"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "晚餐",
    foodName: "胡萝卜牛腩",
    dosing: "胡萝卜",
    six: "30",
    nine: "30",
    twelve: "40",
    sixteen: "50"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "晚餐",
    foodName: "胡萝卜牛腩",
    dosing: "牛腩",
    six: "15",
    nine: "15",
    twelve: "20",
    sixteen: "25"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "晚餐",
    foodName: "西芹炒白干",
    dosing: "西芹",
    six: "80",
    nine: "80",
    twelve: "100",
    sixteen: "130"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "晚餐",
    foodName: "西芹炒白干",
    dosing: "豆腐干",
    six: "10",
    nine: "10",
    twelve: "15",
    sixteen: "20"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "晚餐",
    foodName: "紫菜蛋汤",
    dosing: "鸡蛋",
    six: "20",
    nine: "20",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "晚餐",
    foodName: "紫菜蛋汤",
    dosing: "紫菜",
    six: "5",
    nine: "5",
    twelve: "8",
    sixteen: "8"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "晚餐",
    foodName: "紫菜蛋汤",
    dosing: "虾皮",
    six: "10",
    nine: "10",
    twelve: "15",
    sixteen: "15"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "晚餐",
    foodName: "梨",
    dosing: "梨",
    six: "100",
    nine: "100",
    twelve: "100",
    sixteen: "150"
  },
  {
    recipe: "星期一菜谱",
    mealNumber: "晚餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "10",
    nine: "10",
    twelve: "12",
    sixteen: "12"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "早餐",
    foodName: "青菜鸡蛋杂粮面",
    dosing: "青菜",
    six: "60",
    nine: "60",
    twelve: "100",
    sixteen: "100"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "早餐",
    foodName: "青菜鸡蛋杂粮面",
    dosing: "鸡蛋",
    six: "30",
    nine: "30",
    twelve: "50",
    sixteen: "50"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "早餐",
    foodName: "青菜鸡蛋杂粮面",
    dosing: "荞麦面",
    six: "70",
    nine: "80",
    twelve: "100",
    sixteen: "100"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "早餐",
    foodName: "清炒菠菜",
    dosing: "菠菜",
    six: "30",
    nine: "30",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "早餐",
    foodName: "牛奶",
    dosing: "牛奶",
    six: "300",
    nine: "300",
    twelve: "300",
    sixteen: "300"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "早餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "5",
    nine: "5",
    twelve: "6",
    sixteen: "6"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "午餐",
    foodName: "米饭",
    dosing: "大米",
    six: "100",
    nine: "130",
    twelve: "140",
    sixteen: "150"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "午餐",
    foodName: "青椒烩鱼丸",
    dosing: "鱼丸",
    six: "40",
    nine: "50",
    twelve: "60",
    sixteen: "70"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "午餐",
    foodName: "青椒烩鱼丸",
    dosing: "青椒",
    six: "35",
    nine: "40",
    twelve: "45",
    sixteen: "55"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "午餐",
    foodName: "鸡蛋豆腐羹",
    dosing: "豆腐",
    six: "40",
    nine: "40",
    twelve: "50",
    sixteen: "50"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "午餐",
    foodName: "鸡蛋豆腐羹",
    dosing: "鸡蛋",
    six: "20",
    nine: "20",
    twelve: "25",
    sixteen: "25"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "午餐",
    foodName: "白灼菜心",
    dosing: "菜心",
    six: "60",
    nine: "65",
    twelve: "65",
    sixteen: "75"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "午餐",
    foodName: "冬瓜排骨汤",
    dosing: "冬瓜",
    six: "30",
    nine: "30",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "午餐",
    foodName: "冬瓜排骨汤",
    dosing: "猪小排",
    six: "10",
    nine: "10",
    twelve: "10",
    sixteen: "10"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "午餐",
    foodName: "香蕉",
    dosing: "香蕉",
    six: "100",
    nine: "100",
    twelve: "150",
    sixteen: "150"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "午餐",
    foodName: "牛奶",
    dosing: "牛奶",
    six: "100",
    nine: "100",
    twelve: "200",
    sixteen: "100"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "午餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "10",
    nine: "10",
    twelve: "12",
    sixteen: "12"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "晚餐",
    foodName: "米饭",
    dosing: "大米",
    six: "70",
    nine: "85",
    twelve: "95",
    sixteen: "105"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "晚餐",
    foodName: "米粉蒸肉",
    dosing: "糯米粉",
    six: "10",
    nine: "15",
    twelve: "15",
    sixteen: "20"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "晚餐",
    foodName: "米粉蒸肉",
    dosing: "五花肉",
    six: "20",
    nine: "30",
    twelve: "30",
    sixteen: "40"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "晚餐",
    foodName: "彩椒炒鸭脯",
    dosing: "甜椒",
    six: "30",
    nine: "30",
    twelve: "60",
    sixteen: "60"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "晚餐",
    foodName: "彩椒炒鸭脯",
    dosing: "鸭肉",
    six: "10",
    nine: "10",
    twelve: "20",
    sixteen: "20"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "晚餐",
    foodName: "蒜泥生菜",
    dosing: "生菜",
    six: "100",
    nine: "120",
    twelve: "120",
    sixteen: "120"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "晚餐",
    foodName: "西红柿平菇木耳汤",
    dosing: "西红柿",
    six: "10",
    nine: "10",
    twelve: "10",
    sixteen: "10"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "晚餐",
    foodName: "西红柿平菇木耳汤",
    dosing: "平菇",
    six: "10",
    nine: "10",
    twelve: "10",
    sixteen: "10"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "晚餐",
    foodName: "西红柿平菇木耳汤",
    dosing: "木耳",
    six: "5",
    nine: "5",
    twelve: "5",
    sixteen: "5"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "晚餐",
    foodName: "苹果",
    dosing: "苹果",
    six: "100",
    nine: "100",
    twelve: "100",
    sixteen: "150"
  },
  {
    recipe: "星期二菜谱",
    mealNumber: "晚餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "10",
    nine: "10",
    twelve: "12",
    sixteen: "12"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "早餐",
    foodName: "烧麦",
    dosing: "香菇(鲜)",
    six: "25",
    nine: "25",
    twelve: "25",
    sixteen: "30"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "早餐",
    foodName: "烧麦",
    dosing: "糯米",
    six: "15",
    nine: "15",
    twelve: "15",
    sixteen: "20"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "早餐",
    foodName: "烧麦",
    dosing: "面粉",
    six: "30",
    nine: "30",
    twelve: "30",
    sixteen: "40"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "早餐",
    foodName: "小米红豆粥",
    dosing: "红豆",
    six: "10",
    nine: "10",
    twelve: "15",
    sixteen: "15"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "早餐",
    foodName: "小米红豆粥",
    dosing: "小米",
    six: "20",
    nine: "20",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "早餐",
    foodName: "茶叶蛋",
    dosing: "鸡蛋",
    six: "25",
    nine: "25",
    twelve: "50",
    sixteen: "50"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "早餐",
    foodName: "清炒油麦菜",
    dosing: "油麦菜",
    six: "60",
    nine: "70",
    twelve: "80",
    sixteen: "90"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "早餐",
    foodName: "牛奶",
    dosing: "牛奶",
    six: "250",
    nine: "300",
    twelve: "300",
    sixteen: "300"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "早餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "5",
    nine: "5",
    twelve: "6",
    sixteen: "6"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "午餐",
    foodName: "米饭",
    dosing: "大米",
    six: "75",
    nine: "105",
    twelve: "105",
    sixteen: "120"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "午餐",
    foodName: "米饭",
    dosing: "糙米",
    six: "25",
    nine: "35",
    twelve: "35",
    sixteen: "40"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "午餐",
    foodName: "葱爆虾",
    dosing: "虾",
    six: "35",
    nine: "45",
    twelve: "45",
    sixteen: "50"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "午餐",
    foodName: "蘑菇炒青菜",
    dosing: "蘑菇(鲜)",
    six: "20",
    nine: "25",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "午餐",
    foodName: "蘑菇炒青菜",
    dosing: "青菜",
    six: "70",
    nine: "85",
    twelve: "105",
    sixteen: "105"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "午餐",
    foodName: "洋葱炒猪肝",
    dosing: "洋葱",
    six: "10",
    nine: "10",
    twelve: "10",
    sixteen: "20"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "午餐",
    foodName: "洋葱炒猪肝",
    dosing: "猪肝",
    six: "10",
    nine: "10",
    twelve: "10",
    sixteen: "20"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "午餐",
    foodName: "菠菜豆腐汤",
    dosing: "菠菜",
    six: "10",
    nine: "30",
    twelve: "30",
    sixteen: "40"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "午餐",
    foodName: "菠菜豆腐汤",
    dosing: "豆腐",
    six: "10",
    nine: "30",
    twelve: "30",
    sixteen: "40"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "午餐",
    foodName: "柑橘",
    dosing: "柑橘",
    six: "100",
    nine: "100",
    twelve: "150",
    sixteen: "150"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "午餐",
    foodName: "牛奶",
    dosing: "牛奶",
    six: "100",
    nine: "100",
    twelve: "200",
    sixteen: "200"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "午餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "10",
    nine: "10",
    twelve: "12",
    sixteen: "12"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "晚餐",
    foodName: "米饭",
    dosing: "大米",
    six: "60",
    nine: "90",
    twelve: "90",
    sixteen: "90"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "晚餐",
    foodName: "米饭",
    dosing: "糙米",
    six: "20",
    nine: "30",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "晚餐",
    foodName: "香酥鸡翅",
    dosing: "鸡翅",
    six: "20",
    nine: "30",
    twelve: "40",
    sixteen: "40"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "晚餐",
    foodName: "香酥鸡翅",
    dosing: "淀粉",
    six: "10",
    nine: "15",
    twelve: "20",
    sixteen: "20"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "晚餐",
    foodName: "芹菜炒肉丝",
    dosing: "芹菜",
    six: "50",
    nine: "50",
    twelve: "50",
    sixteen: "50"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "晚餐",
    foodName: "芹菜炒肉丝",
    dosing: "猪肉(肥瘦)",
    six: "10",
    nine: "10",
    twelve: "10",
    sixteen: "10"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "晚餐",
    foodName: "开洋冬瓜",
    dosing: "虾米",
    six: "5",
    nine: "5",
    twelve: "10",
    sixteen: "10"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "晚餐",
    foodName: "开洋冬瓜",
    dosing: "冬瓜",
    six: "20",
    nine: "20",
    twelve: "40",
    sixteen: "40"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "晚餐",
    foodName: "丝瓜蛋花汤",
    dosing: "丝瓜",
    six: "30",
    nine: "30",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "晚餐",
    foodName: "丝瓜蛋花汤",
    dosing: "鸡蛋",
    six: "25",
    nine: "25",
    twelve: "25",
    sixteen: "25"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "晚餐",
    foodName: "葡萄",
    dosing: "葡萄",
    six: "100",
    nine: "100",
    twelve: "100",
    sixteen: "150"
  },
  {
    recipe: "星期三菜谱",
    mealNumber: "晚餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "10",
    nine: "10",
    twelve: "12",
    sixteen: "12"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "早餐",
    foodName: "菜肉包",
    dosing: "面粉",
    six: "30",
    nine: "30",
    twelve: "60",
    sixteen: "60"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "早餐",
    foodName: "菜肉包",
    dosing: "白菜",
    six: "40",
    nine: "40",
    twelve: "80",
    sixteen: "80"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "早餐",
    foodName: "菜肉包",
    dosing: "五花肉",
    six: "5",
    nine: "5",
    twelve: "10",
    sixteen: "10"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "早餐",
    foodName: "杂粮粥",
    dosing: "大米",
    six: "20",
    nine: "30",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "早餐",
    foodName: "杂粮粥",
    dosing: "小米",
    six: "10",
    nine: "15",
    twelve: "15",
    sixteen: "15"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "早餐",
    foodName: "黄瓜炒鸡蛋",
    dosing: "黄瓜",
    six: "40",
    nine: "40",
    twelve: "55",
    sixteen: "55"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "早餐",
    foodName: "黄瓜炒鸡蛋",
    dosing: "鸡蛋",
    six: "30",
    nine: "30",
    twelve: "40",
    sixteen: "40"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "早餐",
    foodName: "牛奶",
    dosing: "牛奶",
    six: "300",
    nine: "300",
    twelve: "300",
    sixteen: "300"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "早餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "5",
    nine: "5",
    twelve: "6",
    sixteen: "6"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "午餐",
    foodName: "米饭",
    dosing: "大米",
    six: "80",
    nine: "95",
    twelve: "95",
    sixteen: "120"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "午餐",
    foodName: "米饭",
    dosing: "玉米糁",
    six: "20",
    nine: "25",
    twelve: "25",
    sixteen: "30"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "午餐",
    foodName: "番茄鱼",
    dosing: "鱼",
    six: "30",
    nine: "40",
    twelve: "50",
    sixteen: "50"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "午餐",
    foodName: "番茄鱼",
    dosing: "番茄",
    six: "30",
    nine: "40",
    twelve: "50",
    sixteen: "50"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "午餐",
    foodName: "青椒香干肉丝",
    dosing: "青椒",
    six: "35",
    nine: "35",
    twelve: "35",
    sixteen: "70"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "午餐",
    foodName: "青椒香干肉丝",
    dosing: "香干",
    six: "20",
    nine: "20",
    twelve: "20",
    sixteen: "40"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "午餐",
    foodName: "青椒香干肉丝",
    dosing: "猪肉(瘦)",
    six: "5",
    nine: "5",
    twelve: "5",
    sixteen: "10"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "午餐",
    foodName: "白菜炒木耳",
    dosing: "白菜",
    six: "80",
    nine: "80",
    twelve: "80",
    sixteen: "80"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "午餐",
    foodName: "白菜炒木耳",
    dosing: "木耳",
    six: "10",
    nine: "10",
    twelve: "10",
    sixteen: "10"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "午餐",
    foodName: "平菇蛋花汤",
    dosing: "平菇",
    six: "10",
    nine: "10",
    twelve: "13",
    sixteen: "13"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "午餐",
    foodName: "平菇蛋花汤",
    dosing: "鸡蛋",
    six: "20",
    nine: "20",
    twelve: "25",
    sixteen: "25"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "午餐",
    foodName: "苹果",
    dosing: "苹果",
    six: "100",
    nine: "100",
    twelve: "150",
    sixteen: "150"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "午餐",
    foodName: "牛奶",
    dosing: "牛奶",
    six: "100",
    nine: "150",
    twelve: "200",
    sixteen: "150"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "午餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "10",
    nine: "10",
    twelve: "12",
    sixteen: "12"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "晚餐",
    foodName: "米饭",
    dosing: "大米",
    six: "75",
    nine: "105",
    twelve: "100",
    sixteen: "115"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "晚餐",
    foodName: "米饭",
    dosing: "糙米",
    six: "15",
    nine: "25",
    twelve: "25",
    sixteen: "30"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "晚餐",
    foodName: "卤鸡腿",
    dosing: "鸡腿",
    six: "20",
    nine: "30",
    twelve: "30",
    sixteen: "40"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "晚餐",
    foodName: "西兰花炒胡萝卜",
    dosing: "西兰花",
    six: "30",
    nine: "30",
    twelve: "30",
    sixteen: "45"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "晚餐",
    foodName: "西兰花炒胡萝卜",
    dosing: "胡萝卜",
    six: "10",
    nine: "10",
    twelve: "10",
    sixteen: "15"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "晚餐",
    foodName: "蒜苗炒肉丝",
    dosing: "蒜苗",
    six: "30",
    nine: "30",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "晚餐",
    foodName: "蒜苗炒肉丝",
    dosing: "猪肉",
    six: "10",
    nine: "10",
    twelve: "10",
    sixteen: "10"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "晚餐",
    foodName: "萝卜丝虾皮汤",
    dosing: "白萝卜",
    six: "20",
    nine: "40",
    twelve: "40",
    sixteen: "40"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "晚餐",
    foodName: "萝卜丝虾皮汤",
    dosing: "虾皮",
    six: "5",
    nine: "10",
    twelve: "10",
    sixteen: "10"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "晚餐",
    foodName: "圣女果",
    dosing: "圣女果",
    six: "100",
    nine: "100",
    twelve: "100",
    sixteen: "150"
  },
  {
    recipe: "星期四菜谱",
    mealNumber: "晚餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "10",
    nine: "10",
    twelve: "12",
    sixteen: "12"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "早餐",
    foodName: "花卷",
    dosing: "葱",
    six: "5",
    nine: "5",
    twelve: "5",
    sixteen: "5"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "早餐",
    foodName: "花卷",
    dosing: "面粉",
    six: "35",
    nine: "45",
    twelve: "55",
    sixteen: "55"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "早餐",
    foodName: "烤红薯",
    dosing: "红薯",
    six: "50",
    nine: "50",
    twelve: "65",
    sixteen: "65"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "早餐",
    foodName: "炒生菜",
    dosing: "生菜",
    six: "50",
    nine: "50",
    twelve: "75",
    sixteen: "80"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "早餐",
    foodName: "牛奶",
    dosing: "牛奶",
    six: "200",
    nine: "200",
    twelve: "250",
    sixteen: "250"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "早餐",
    foodName: "蒸鸡蛋",
    dosing: "鸡蛋",
    six: "25",
    nine: "25",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "早餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "5",
    nine: "5",
    twelve: "5",
    sixteen: "5"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "午餐",
    foodName: "米饭",
    dosing: "小米",
    six: "50",
    nine: "60",
    twelve: "70",
    sixteen: "75"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "午餐",
    foodName: "米饭",
    dosing: "大米",
    six: "50",
    nine: "60",
    twelve: "70",
    sixteen: "75"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "午餐",
    foodName: "糖醋仔排",
    dosing: "猪仔排",
    six: "30",
    nine: "40",
    twelve: "50",
    sixteen: "60"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "午餐",
    foodName: "西红柿烩豆腐",
    dosing: "西红柿",
    six: "50",
    nine: "50",
    twelve: "75",
    sixteen: "75"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "午餐",
    foodName: "西红柿烩豆腐",
    dosing: "豆腐",
    six: "25",
    nine: "25",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "午餐",
    foodName: "清炒空心菜",
    dosing: "空心菜",
    six: "50",
    nine: "50",
    twelve: "75",
    sixteen: "90"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "午餐",
    foodName: "菠菜鱼丸汤",
    dosing: "菠菜",
    six: "30",
    nine: "30",
    twelve: "50",
    sixteen: "50"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "午餐",
    foodName: "菠菜鱼丸汤",
    dosing: "鱼丸",
    six: "5",
    nine: "5",
    twelve: "5",
    sixteen: "5"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "午餐",
    foodName: "牛奶",
    dosing: "牛奶",
    six: "100",
    nine: "100",
    twelve: "150",
    sixteen: "150"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "午餐",
    foodName: "梨",
    dosing: "梨",
    six: "100",
    nine: "100",
    twelve: "150",
    sixteen: "150"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "午餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "10",
    nine: "10",
    twelve: "15",
    sixteen: "15"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "晚餐",
    foodName: "米饭",
    dosing: "大米",
    six: "100",
    nine: "120",
    twelve: "140",
    sixteen: "150"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "晚餐",
    foodName: "甜椒炒鱿鱼",
    dosing: "甜椒",
    six: "50",
    nine: "50",
    twelve: "60",
    sixteen: "70"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "晚餐",
    foodName: "甜椒炒鱿鱼",
    dosing: "鱿鱼",
    six: "30",
    nine: "40",
    twelve: "50",
    sixteen: "60"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "晚餐",
    foodName: "茭白青椒肉丝",
    dosing: "青椒",
    six: "10",
    nine: "10",
    twelve: "10",
    sixteen: "10"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "晚餐",
    foodName: "茭白青椒肉丝",
    dosing: "茭白",
    six: "50",
    nine: "50",
    twelve: "60",
    sixteen: "70"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "晚餐",
    foodName: "茭白青椒肉丝",
    dosing: "猪肉(瘦)",
    six: "10",
    nine: "15",
    twelve: "20",
    sixteen: "25"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "晚餐",
    foodName: "蒜泥茄条",
    dosing: "茄子",
    six: "50",
    nine: "50",
    twelve: "60",
    sixteen: "70"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "晚餐",
    foodName: "黄豆芽蛋花汤",
    dosing: "黄豆芽",
    six: "30",
    nine: "30",
    twelve: "35",
    sixteen: "35"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "晚餐",
    foodName: "黄豆芽蛋花汤",
    dosing: "鸡蛋",
    six: "25",
    nine: "25",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "晚餐",
    foodName: "橙子",
    dosing: "橙子",
    six: "100",
    nine: "100",
    twelve: "100",
    sixteen: "150"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "晚餐",
    foodName: "牛奶",
    dosing: "牛奶",
    six: "100",
    nine: "100",
    twelve: "100",
    sixteen: "150"
  },
  {
    recipe: "星期五菜谱",
    mealNumber: "晚餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "10",
    nine: "10",
    twelve: "10",
    sixteen: "10"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "早餐",
    foodName: "豆沙卷",
    dosing: "豆沙",
    six: "5",
    nine: "7",
    twelve: "7",
    sixteen: "7"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "早餐",
    foodName: "豆沙卷",
    dosing: "面粉",
    six: "30",
    nine: "40",
    twelve: "40",
    sixteen: "40"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "早餐",
    foodName: "韭菜鸡蛋杂粮饼",
    dosing: "韭菜",
    six: "25",
    nine: "25",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "早餐",
    foodName: "韭菜鸡蛋杂粮饼",
    dosing: "玉米面",
    six: "40",
    nine: "40",
    twelve: "50",
    sixteen: "50"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "早餐",
    foodName: "韭菜鸡蛋杂粮饼",
    dosing: "鸡蛋",
    six: "25",
    nine: "25",
    twelve: "35",
    sixteen: "35"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "早餐",
    foodName: "牛奶",
    dosing: "牛奶",
    six: "150",
    nine: "200",
    twelve: "250",
    sixteen: "250"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "早餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "5",
    nine: "5",
    twelve: "5",
    sixteen: "5"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "午餐",
    foodName: "米饭",
    dosing: "大米",
    six: "50",
    nine: "60",
    twelve: "70",
    sixteen: "70"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "午餐",
    foodName: "米饭",
    dosing: "小米",
    six: "50",
    nine: "60",
    twelve: "70",
    sixteen: "80"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "午餐",
    foodName: "白灼虾",
    dosing: "虾",
    six: "30",
    nine: "50",
    twelve: "60",
    sixteen: "60"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "午餐",
    foodName: "黄瓜胡萝卜炒肉丝",
    dosing: "黄瓜",
    six: "50",
    nine: "50",
    twelve: "70",
    sixteen: "70"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "午餐",
    foodName: "黄瓜胡萝卜炒肉丝",
    dosing: "胡萝卜",
    six: "30",
    nine: "30",
    twelve: "50",
    sixteen: "50"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "午餐",
    foodName: "黄瓜胡萝卜炒肉丝",
    dosing: "猪肉",
    six: "10",
    nine: "10",
    twelve: "10",
    sixteen: "10"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "午餐",
    foodName: "蘑菇炒西兰花",
    dosing: "蘑菇(鲜)",
    six: "30",
    nine: "30",
    twelve: "40",
    sixteen: "40"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "午餐",
    foodName: "蘑菇炒西兰花",
    dosing: "西兰花",
    six: "60",
    nine: "60",
    twelve: "70",
    sixteen: "0"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "午餐",
    foodName: "海带冬瓜汤",
    dosing: "海带(泡发)",
    six: "30",
    nine: "30",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "午餐",
    foodName: "海带冬瓜汤",
    dosing: "冬瓜",
    six: "50",
    nine: "50",
    twelve: "50",
    sixteen: "50"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "午餐",
    foodName: "牛奶",
    dosing: "牛奶",
    six: "100",
    nine: "100",
    twelve: "150",
    sixteen: "150"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "午餐",
    foodName: "葡萄",
    dosing: "葡萄",
    six: "100",
    nine: "100",
    twelve: "150",
    sixteen: "150"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "午餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "10",
    nine: "10",
    twelve: "15",
    sixteen: "15"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "晚餐",
    foodName: "米饭",
    dosing: "大米",
    six: "100",
    nine: "120",
    twelve: "140",
    sixteen: "150"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "晚餐",
    foodName: "鹤鹑蛋烧五花肉",
    dosing: "鹌鹑蛋",
    six: "25",
    nine: "25",
    twelve: "35",
    sixteen: "35"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "晚餐",
    foodName: "鹤鹑蛋烧五花肉",
    dosing: "五花肉",
    six: "15",
    nine: "15",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "晚餐",
    foodName: "西芹炒香干",
    dosing: "西芹",
    six: "60",
    nine: "60",
    twelve: "80",
    sixteen: "80"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "晚餐",
    foodName: "西芹炒香干",
    dosing: "香干",
    six: "15",
    nine: "15",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "晚餐",
    foodName: "蒜泥小白菜",
    dosing: "小白菜",
    six: "60",
    nine: "60",
    twelve: "70",
    sixteen: "70"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "晚餐",
    foodName: "青菜平菇汤",
    dosing: "青菜",
    six: "20",
    nine: "20",
    twelve: "20",
    sixteen: "20"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "晚餐",
    foodName: "青菜平菇汤",
    dosing: "平菇",
    six: "10",
    nine: "10",
    twelve: "10",
    sixteen: "10"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "晚餐",
    foodName: "牛奶",
    dosing: "牛奶",
    six: "100",
    nine: "100",
    twelve: "100",
    sixteen: "100"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "晚餐",
    foodName: "枣",
    dosing: "枣",
    six: "100",
    nine: "100",
    twelve: "100",
    sixteen: "150"
  },
  {
    recipe: "星期六菜谱",
    mealNumber: "晚餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "10",
    nine: "10",
    twelve: "10",
    sixteen: "10"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "早餐",
    foodName: "紫薯杂粮饼",
    dosing: "紫薯",
    six: "25",
    nine: "25",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "早餐",
    foodName: "紫薯杂粮饼",
    dosing: "玉米面",
    six: "30",
    nine: "30",
    twelve: "36",
    sixteen: "36"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "早餐",
    foodName: "芹菜炒肉丝",
    dosing: "芹菜",
    six: "30",
    nine: "60",
    twelve: "60",
    sixteen: "60"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "早餐",
    foodName: "芹菜炒肉丝",
    dosing: "肉丝",
    six: "5",
    nine: "10",
    twelve: "10",
    sixteen: "10"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "早餐",
    foodName: "粥",
    dosing: "大米",
    six: "30",
    nine: "50",
    twelve: "70",
    sixteen: "90"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "早餐",
    foodName: "牛奶",
    dosing: "牛奶",
    six: "300",
    nine: "300",
    twelve: "300",
    sixteen: "300"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "早餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "5",
    nine: "5",
    twelve: "5",
    sixteen: "5"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "午餐",
    foodName: "扬州炒饭",
    dosing: "大米",
    six: "80",
    nine: "100",
    twelve: "120",
    sixteen: "120"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "午餐",
    foodName: "扬州炒饭",
    dosing: "胡萝卜",
    six: "20",
    nine: "25",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "午餐",
    foodName: "扬州炒饭",
    dosing: "鸡蛋",
    six: "25",
    nine: "30",
    twelve: "38",
    sixteen: "38"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "午餐",
    foodName: "扬州炒饭",
    dosing: "青豆",
    six: "10",
    nine: "13",
    twelve: "15",
    sixteen: "15"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "午餐",
    foodName: "白灼虾",
    dosing: "虾",
    six: "40",
    nine: "45",
    twelve: "60",
    sixteen: "60"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "午餐",
    foodName: "上汤苋菜",
    dosing: "苋菜",
    six: "50",
    nine: "70",
    twelve: "80",
    sixteen: "80"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "午餐",
    foodName: "生瓜炒木耳",
    dosing: "生瓜",
    six: "30",
    nine: "30",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "午餐",
    foodName: "生瓜炒木耳",
    dosing: "木耳(干)",
    six: "10",
    nine: "10",
    twelve: "10",
    sixteen: "10"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "午餐",
    foodName: "青菜豆腐汤",
    dosing: "青菜",
    six: "50",
    nine: "50",
    twelve: "75",
    sixteen: "75"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "午餐",
    dosing: "豆腐",
    six: "20",
    nine: "20",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "午餐",
    foodName: "桃",
    dosing: "桃",
    six: "100",
    nine: "100",
    twelve: "100",
    sixteen: "150"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "午餐",
    foodName: "牛奶",
    dosing: "牛奶",
    six: "100",
    nine: "150",
    twelve: "150",
    sixteen: "200"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "午餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "10",
    nine: "10",
    twelve: "13",
    sixteen: "13"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "晚餐",
    foodName: "米饭",
    dosing: "大米",
    six: "80",
    nine: "110",
    twelve: "110",
    sixteen: "120"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "晚餐",
    foodName: "杏鲍菇炒牛柳",
    dosing: "杏鲍菇",
    six: "25",
    nine: "30",
    twelve: "40",
    sixteen: "50"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "晚餐",
    foodName: "杏鲍菇炒牛柳",
    dosing: "牛柳",
    six: "25",
    nine: "30",
    twelve: "40",
    sixteen: "50"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "晚餐",
    foodName: "韭黄炒鸡蛋",
    dosing: "韭黄",
    six: "50",
    nine: "50",
    twelve: "60",
    sixteen: "80"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "晚餐",
    foodName: "韭黄炒鸡蛋",
    dosing: "鸡蛋",
    six: "25",
    nine: "25",
    twelve: "30",
    sixteen: "40"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "晚餐",
    foodName: "豆豉油麦菜",
    dosing: "豆豉",
    six: "5",
    nine: "5",
    twelve: "5",
    sixteen: "5"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "晚餐",
    foodName: "豆豉油麦菜",
    dosing: "油麦菜",
    six: "50",
    nine: "50",
    twelve: "50",
    sixteen: "50"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "晚餐",
    foodName: "玉米排骨汤",
    dosing: "玉米",
    six: "30",
    nine: "30",
    twelve: "30",
    sixteen: "30"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "晚餐",
    foodName: "玉米排骨汤",
    dosing: "猪小排",
    six: "10",
    nine: "10",
    twelve: "10",
    sixteen: "10"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "晚餐",
    foodName: "梨",
    dosing: "梨",
    six: "100",
    nine: "100",
    twelve: "150",
    sixteen: "150"
  },
  {
    recipe: "星期日菜谱",
    mealNumber: "晚餐",
    foodName: "植物油",
    dosing: "植物油",
    six: "10",
    nine: "10",
    twelve: "12",
    sixteen: "12"
  }
]

export const DRIS_FORM_TABLE_DATA = [
  {
    age: "0岁~",
    man_pal1: "—",
    man_pal2: "90kcal/(kg·d)",
    man_pal3: "—",
    woman_pal1: "—",
    woman_pal2: "90kcal/(kg·d)",
    woman_pal3: "—",
    AMDR1: "—",
    AMDR2: "48(AI)",
    AMDR3: "—",
    AMDR4: "—",
    AMDR5: "—",
    water_man: "—",
    water_woman: "—"
  },
  {
    age: "0.5岁~",
    man_pal1: "—",
    man_pal2: "75kcal/(kg·d)",
    man_pal3: "—",
    woman_pal1: "—",
    woman_pal2: "75kcal/(kg·d)",
    woman_pal3: "—",
    AMDR1: "—",
    AMDR2: "40(AI)",
    AMDR3: "—",
    AMDR4: "—",
    AMDR5: "—",
    water_man: "—",
    water_woman: "—"
  },
  {
    age: "1岁~",
    man_pal1: "—",
    man_pal2: "900",
    man_pal3: "—",
    woman_pal1: "—",
    woman_pal2: "800",
    woman_pal3: "—",
    AMDR1: "—",
    AMDR2: "35(AI)",
    AMDR3: "50~65",
    AMDR4: "5~10",
    AMDR5: "—",
    water_man: "—",
    water_woman: "—"
  },
  {
    age: "2岁~",
    man_pal1: "—",
    man_pal2: "1100",
    man_pal3: "—",
    woman_pal1: "—",
    woman_pal2: "1000",
    woman_pal3: "—",
    AMDR1: "—",
    AMDR2: "35(AI)",
    AMDR3: "50~65",
    AMDR4: "5~10",
    AMDR5: "—",
    water_man: "—",
    water_woman: "—"
  },
  {
    age: "3岁~",
    man_pal1: "—",
    man_pal2: "1250",
    man_pal3: "—",
    woman_pal1: "—",
    woman_pal2: "1150",
    woman_pal3: "—",
    AMDR1: "—",
    AMDR2: "35(AI)",
    AMDR3: "50~65",
    AMDR4: "5~10",
    AMDR5: "—",
    water_man: "—",
    water_woman: "—"
  },
  {
    age: "4岁~",
    man_pal1: "—",
    man_pal2: "1300",
    man_pal3: "—",
    woman_pal1: "—",
    woman_pal2: "1250",
    woman_pal3: "—",
    AMDR1: "8~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "10~15",
    AMDR5: "<10",
    water_man: "800",
    water_woman: "800"
  },
  {
    age: "5岁~",
    man_pal1: "—",
    man_pal2: "1400",
    man_pal3: "—",
    woman_pal1: "—",
    woman_pal2: "1300",
    woman_pal3: "—",
    AMDR1: "8~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "10~15",
    AMDR5: "<10",
    water_man: "800",
    water_woman: "800"
  },
  {
    age: "6岁~",
    man_pal1: "1400",
    man_pal2: "1600",
    man_pal3: "1800",
    woman_pal1: "1300",
    woman_pal2: "1450",
    woman_pal3: "1650",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "10~15",
    AMDR5: "<10",
    water_man: "800",
    water_woman: "800"
  },
  {
    age: "7岁~",
    man_pal1: "1500",
    man_pal2: "1700",
    man_pal3: "1900",
    woman_pal1: "1350",
    woman_pal2: "1550",
    woman_pal3: "1750",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "15~20",
    AMDR5: "<10",
    water_man: "1000",
    water_woman: "1000"
  },
  {
    age: "8岁~",
    man_pal1: "1600",
    man_pal2: "1850",
    man_pal3: "2100",
    woman_pal1: "1450",
    woman_pal2: "1700",
    woman_pal3: "1900",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "15~20",
    AMDR5: "<10",
    water_man: "1000",
    water_woman: "1000"
  },
  {
    age: "9岁~",
    man_pal1: "1700",
    man_pal2: "1950",
    man_pal3: "2200",
    woman_pal1: "1550",
    woman_pal2: "1800",
    woman_pal3: "2000",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "15~20",
    AMDR5: "<10",
    water_man: "1000",
    water_woman: "1000"
  },
  {
    age: "10岁~",
    man_pal1: "1800",
    man_pal2: "2050",
    man_pal3: "2300",
    woman_pal1: "1650",
    woman_pal2: "1900",
    woman_pal3: "2100",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "15~20",
    AMDR5: "<10",
    water_man: "1000",
    water_woman: "1000"
  },
  {
    age: "11岁~",
    man_pal1: "1900",
    man_pal2: "2200",
    man_pal3: "2450",
    woman_pal1: "1750",
    woman_pal2: "2000",
    woman_pal3: "2250",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "15~20",
    AMDR5: "<10",
    water_man: "1000",
    water_woman: "1000"
  },
  {
    age: "12岁~",
    man_pal1: "2300",
    man_pal2: "2600",
    man_pal3: "2900",
    woman_pal1: "1950",
    woman_pal2: "2200",
    woman_pal3: "2450",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "20~25",
    AMDR5: "<10",
    water_man: "1300",
    water_woman: "1100"
  },
  {
    age: "15岁~",
    man_pal1: "2600",
    man_pal2: "2950",
    man_pal3: "3300",
    woman_pal1: "2100",
    woman_pal2: "2350",
    woman_pal3: "2650",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "25~30",
    AMDR5: "<10",
    water_man: "1400",
    water_woman: "1200"
  },
  {
    age: "18岁~",
    man_pal1: "2150",
    man_pal2: "2550",
    man_pal3: "3000",
    woman_pal1: "1700",
    woman_pal2: "2100",
    woman_pal3: "2450",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "25~30",
    AMDR5: "<10",
    water_man: "1700",
    water_woman: "1500"
  },
  {
    age: "30岁~",
    man_pal1: "2050",
    man_pal2: "2500",
    man_pal3: "2950",
    woman_pal1: "1700",
    woman_pal2: "2050",
    woman_pal3: "2400",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "25~30",
    AMDR5: "<10",
    water_man: "1700",
    water_woman: "1500"
  },
  {
    age: "50岁~",
    man_pal1: "1950",
    man_pal2: "2400",
    man_pal3: "2800",
    woman_pal1: "1600",
    woman_pal2: "1950",
    woman_pal3: "2300",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "25~30",
    AMDR5: "<10",
    water_man: "1700",
    water_woman: "1500"
  },
  {
    age: "65岁~",
    man_pal1: "1900",
    man_pal2: "2300",
    man_pal3: "—",
    woman_pal1: "1550",
    woman_pal2: "1850",
    woman_pal3: "—",
    AMDR1: "15~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "25~30",
    AMDR5: "<10",
    water_man: "1700",
    water_woman: "1500"
  },
  {
    age: "75岁~",
    man_pal1: "1800",
    man_pal2: "2200",
    man_pal3: "—",
    woman_pal1: "1500",
    woman_pal2: "1750",
    woman_pal3: "—",
    AMDR1: "15~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "25~30",
    AMDR5: "<10",
    water_man: "1700",
    water_woman: "1500"
  },
  {
    age: "孕早期",
    man_pal1: "—",
    man_pal2: "—",
    man_pal3: "—",
    woman_pal1: "+0",
    woman_pal2: "+0",
    woman_pal3: "+0",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "+0",
    AMDR5: "<10",
    water_man: "—",
    water_woman: "+0"
  },
  {
    age: "孕中期",
    man_pal1: "—",
    man_pal2: "—",
    man_pal3: "—",
    woman_pal1: "+250",
    woman_pal2: "+250",
    woman_pal3: "+250",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "+4",
    AMDR5: "<10",
    water_man: "—",
    water_woman: "+200"
  },
  {
    age: "孕晚期",
    man_pal1: "—",
    man_pal2: "—",
    man_pal3: "—",
    woman_pal1: "+400",
    woman_pal2: "+400",
    woman_pal3: "+400",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "+4",
    AMDR5: "<10",
    water_man: "—",
    water_woman: "+200"
  },
  {
    age: "乳母",
    man_pal1: "—",
    man_pal2: "—",
    man_pal3: "—",
    woman_pal1: "+400",
    woman_pal2: "+400",
    woman_pal3: "+400",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "+4",
    AMDR5: "<10",
    water_man: "—",
    water_woman: "+600"
  }
]

export const WEEK_LIST = [
  { label: "星期一", value: 1 },
  { label: "星期二", value: 2 },
  { label: "星期三", value: 3 },
  { label: "星期四", value: 4 },
  { label: "星期五", value: 5 },
  { label: "星期六", value: 6 },
  { label: "星期日", value: 0 }
]

export const MEAL_TYPES = [
  { label: "早餐", value: "breakfast" },
  { label: "午餐", value: "lunch" },
  { label: "下午茶", value: "afternoon" },
  { label: "晚餐", value: "dinner" },
  { label: "夜宵", value: "supper" },
  { label: "凌晨餐", value: "morning" }
]
