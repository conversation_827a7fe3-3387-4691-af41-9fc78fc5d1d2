<template>
  <div class="pest-control container-wrapper">
    <div ref="searchRef">
      <div class="tab-box m-b-10px">
        <el-radio-group v-model="tabType" @change="changeTabHandle" class="ps-radio-btn">
          <el-radio-button v-for="tab in tabTypeList" :key="tab.value" :label="tab.value">{{
            tab.label
          }}</el-radio-button>
        </el-radio-group>
      </div>
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>

    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="flex items-center" />
        <div class="table-button">
          <el-button
            type="primary"
            @click="goToExport"
            v-permission="[
              tabType === 'canteen'
                ? 'background_fund_supervision.channel_canteen_management.channel_democratic_feedback_list_export'
                : 'no_permissions'
            ]"
            >导出</el-button
          >
          <el-button type="primary" class="ps-origin-btn-light" @click="gotoPrint">打印</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operatorName="{ row }">
              {{ getOperatorName(row) }}
            </template>
            <template #imgs="{ row }">
              <el-button plain link size="small" @click="handlerShowDetail(row)" type="primary"> 图片 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview-dialog v-model="imageVisible" :imgs="imageList" :currentIndex="0" :title="dialogTitle" />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import {
  SEARCH_FORM_SETTING_DEMOCRATIC_SUPERVISION,
  TABLE_SETTING_DEMOCRATIC_SUPERVISION,
  SEARCH_FORM_SETTING_DEMOCRATIC_SUPERVISION_SUPPLIER,
  TABLE_SETTING_DEMOCRATIC_SUPERVISION_SUPPLIER
} from "./constants"
import {
  apiBackgroundFundSupervisionChannelCanteenManagementChannelDemocraticFeedbackListPost,
  apiBackgroundFundSupervisionChannelCanteenManagementChannelDemocraticFeedbackListExportPost
} from "@/api/canteen"
import { apiBackgroundFundSupervisionSupplierManageBackgroundSupplierManageListPost } from "@/api/supplier/index"
import { ElMessage } from "element-plus"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { exportHandle } from "@/utils/exportExcel"
import { useRouter } from "vue-router"
import { setLocalStorage } from "@/utils/storage"

// 路由
const router = useRouter()

// tab切换
const tabType = ref("canteen")
// tab切换列表
const tabTypeList = ref([
  {
    label: "民主反馈-食堂",
    value: "canteen",
    permissions: "background_fund_supervision.channel_canteen_management.democratic_feedback_list"
  },
  {
    label: "民主反馈-供应商",
    value: "supplier",
    permissions: "no_permissions"
  }
])

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// table数据
const tableData = ref([])
const loading = ref(false)
const searchFormSetting = ref()
const tableLoading = ref(false)
const tableSetting = ref<any>(cloneDeep(TABLE_SETTING_DEMOCRATIC_SUPERVISION))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 供应商列表
const supplierList = ref()
// 导出
const printType = ref("DemocraticSupervisionCanteenExport")

// 弹窗标题
const dialogTitle = ref("图片详情")
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value && data[key].value !== "" && data[key].value !== "全部" && data[key].value.length > 0) {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0] || ""
        params.end_date = data[key].value[1] || ""
      }
    }
  }
  return params
}
// 搜索
const changeSearch = (model: any, type: string, itemType: string) => {
  console.log("changeSearch model, type, itemType", type, itemType)
  if (itemType === "source_organization_ids" && tabType.value === "canteen") {
    searchFormSetting.value.organization_ids.value = []
    searchFormSetting.value.organization_ids.dataList = []
    searchFormSetting.value.organization_ids.orgsId = type
  }
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}
// 打印
const gotoPrint = () => {
  let tabbleSetting = cloneDeep(tableSetting.value)
  tabbleSetting = tabbleSetting.filter((item: any) => item.prop !== "imgs")
  setLocalStorage("print_setting", tabbleSetting)
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: printType.value,
      print_title: tabType.value === "canteen" ? "民主反馈-食堂" : "民主反馈-供应商",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiBackgroundFundSupervisionChannelCanteenManagementChannelDemocraticFeedbackListPost",
      params: JSON.stringify({
        ...formatQueryParams(searchFormSetting.value),
        page: 1,
        page_size: 9999,
        feedback_type: tabType.value === "canteen" ? "org" : "supplier"
      })
    }
  })
  window.open(href, "_blank")
}

// 导出
const goToExport = () => {
  console.log("goToExport")
  const option = {
    type: printType.value,
    api: apiBackgroundFundSupervisionChannelCanteenManagementChannelDemocraticFeedbackListExportPost,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize,
      feedback_type: tabType.value === "canteen" ? "org" : "supplier"
    }
  }
  exportHandle(option)
}

// 查看照片
// const handlerShowPhoto = (row: any) => {
//   console.log(row)
//   const img = row ? row.imgs : ""
//   if (!img) {
//     return ElMessage.error("防制图片不存在")
//   }
//   imageList.value = [img]
//   imageVisible.value = true
// }
// 反馈图片
const handlerShowDetail = (row: any) => {
  console.log(row)
  const img = row ? row.images : []
  if (!img || img.length === 0) {
    return ElMessage.error("图片不存在")
  }
  console.log("handlerShowDetail", img)
  imageList.value = img
  imageVisible.value = true
}
// 获取账号名称与账号
const getOperatorName = (row: any) => {
  let operatorMemberName = row.operator_member_name || ""
  let operatorName = row.operator_name || ""
  if (!operatorMemberName && !operatorName) {
    return ""
  }
  return (operatorMemberName ? operatorMemberName : "--") + "(" + (operatorName ? operatorName : "--") + ")"
}

onMounted(() => {
  setTabDataHandle()
})
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionChannelCanteenManagementChannelDemocraticFeedbackListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize,
      feedback_type: tabType.value === "canteen" ? "org" : "supplier"
    })
  )
  tableLoading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    let results = data.results || []
    tableData.value = results || []
    pageConfig.total = data.count || 0
  } else {
    ElMessage.error(res.msg)
  }
}

// 切换tab
const changeTabHandle = (e: any) => {
  console.log("changeTabHandle", e)
  setTabDataHandle()
}

// 设置tab数据
const setTabDataHandle = () => {
  if (tabType.value === "canteen") {
    searchFormSetting.value = cloneDeep(SEARCH_FORM_SETTING_DEMOCRATIC_SUPERVISION)
    tableSetting.value = cloneDeep(TABLE_SETTING_DEMOCRATIC_SUPERVISION)
    printType.value = "DemocraticSupervisionCanteenExport"
  } else {
    searchFormSetting.value = cloneDeep(SEARCH_FORM_SETTING_DEMOCRATIC_SUPERVISION_SUPPLIER)
    tableSetting.value = cloneDeep(TABLE_SETTING_DEMOCRATIC_SUPERVISION_SUPPLIER)
    printType.value = "DemocraticSupervisionSupplierExport"
    getSupervisionsList()
  }
  console.log("setTabDataHandle", tabType.value, tableSetting.value)

  // initPrintSetting()
  tableData.value = []
  getDataList()
}
// 获取供应商列表
const getSupervisionsList = async () => {
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionSupplierManageBackgroundSupplierManageListPost({ only_enter: 0 })
  )
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    let results = data.results || []
    supplierList.value = results || []
    searchFormSetting.value.supplier_manage_ids.dataList = cloneDeep(results)
  } else {
    ElMessage.error(res.msg)
  }
}
</script>
<style lang="scss">
.pest-control {
  padding: 0 20px;

  .el-popper {
    max-width: 300px;
  }
}
</style>
