<template>
  <div class="record-management container-wrapper">
    <el-drawer
      :model-value="showDrawer"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      cancelText="关闭"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div class="table-content position-relative">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          tooltipEffect="debt-statistics-detailsDrawer-tooltips"
        >
          <ps-column :table-headers="tableSetting">
            <template #certificate="{ row }">
              <el-button
                plain
                link
                size="small"
                :disabled="!row.file.length"
                @click="handlerShowPhoto(row)"
                type="primary"
              >
                查看
              </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
      <div class="dialog-footer m-t-20px">
        <el-button type="primary" @click="closeDialog" v-loading="confirmLoading"> 关闭 </el-button>
      </div>
    </el-drawer>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue"
import { cloneDeep } from "lodash"
import { TABLE_SETTING_DEBT_DETALIS_RECORD } from "../../constants"
import { apiBackgroundFundSupervisionBusLiabilitySupLiabilityDetailList } from "@/api/asset"
import { to } from "await-to-js"
import { ElMessage } from "element-plus"

const props = defineProps({
  showDrawer: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: "title"
  },
  width: {
    type: String,
    default: "70%"
  },
  drawerData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const confirmLoading = ref(false)

// table数据
const tableData = ref([])
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_DEBT_DETALIS_RECORD))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// 初始化数据
const initData = () => {
  getStatisticsDetailsList()
}
// 获取记录
const getStatisticsDetailsList = async () => {
  tableLoading.value = true
  let params: any = {
    bing_org_id: props.drawerData.channel_org_id,
    org_id: props.drawerData.org_id,
    date_str: props.drawerData.date_str,
    page: pageConfig.currentPage,
    page_size: pageConfig.pageSize
  }
  const [err, res]: any[] = await to(apiBackgroundFundSupervisionBusLiabilitySupLiabilityDetailList(params))
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 弹窗关闭
const closeDialog = () => {
  tableData.value = []
  pageConfig.currentPage = 1
  emit("cancelDialog")
}
// 查看照片
const handlerShowPhoto = (row: any) => {
  imageList.value = row.file
  imageVisible.value = true
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getStatisticsDetailsList()
}
onMounted(() => {
  initData()
})
</script>
<style lang="scss">
.debt-statistics-detailsDrawer-tooltips {
  white-space: normal; /* 允许换行 */
  word-break: break-all; /* 长单词自动换行 */
  max-width: 200px; /* 设置最大宽度，超出后换行 */
}
</style>
