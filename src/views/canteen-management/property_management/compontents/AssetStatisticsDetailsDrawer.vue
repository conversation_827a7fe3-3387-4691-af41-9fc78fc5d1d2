<template>
  <div class="record-management container-wrapper">
    <el-drawer
      :model-value="showDrawer"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      cancelText="关闭"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div class="table-content position-relative">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          tooltipEffect="asset-statistics-details-drawer-tooltips"
        >
          <ps-column :table-headers="tableSetting" />
        </ps-table>
      </div>
      <div class="dialog-footer m-t-20px">
        <el-button type="primary" @click="closeDialog" v-loading="confirmLoading"> 关闭 </el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue"
import { cloneDeep } from "lodash"
import { TABLE_SETTING_ASSET_DETALIS_RECORD } from "../../constants"
import { apiBackgroundFundSupervisionAssetChannelAssetInfoStatisticsDetailsList } from "@/api/asset"
import { to } from "await-to-js"
import { ElMessage } from "element-plus"

const props = defineProps({
  showDrawer: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: "title"
  },
  width: {
    type: String,
    default: "75%"
  },
  asset_info_statistics_id: {
    type: Number,
    default: -1
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const confirmLoading = ref(false)

// table数据
const tableData = ref([])
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_ASSET_DETALIS_RECORD))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 初始化数据
const initData = () => {
  getStatisticsDetailsList()
}
// 获取记录
const getStatisticsDetailsList = async () => {
  tableLoading.value = true
  let params: any = {
    asset_info_statistics_id: props.asset_info_statistics_id,
    page: pageConfig.currentPage,
    page_size: pageConfig.pageSize
  }
  const [err, res]: any[] = await to(apiBackgroundFundSupervisionAssetChannelAssetInfoStatisticsDetailsList(params))
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 弹窗关闭
const closeDialog = () => {
  tableData.value = []
  pageConfig.currentPage = 1
  emit("cancelDialog")
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getStatisticsDetailsList()
}
onMounted(() => {
  initData()
})
</script>
<style lang="scss">
.asset-statistics-details-drawer-tooltips {
  white-space: normal; /* 允许换行 */
  word-break: break-all; /* 长单词自动换行 */
  max-width: 200px; /* 设置最大宽度，超出后换行 */
}
</style>
