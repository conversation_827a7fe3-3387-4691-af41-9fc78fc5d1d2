<template>
  <div class="DebtStatistics container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div>
          流动负债合计：{{ divide(summaryData.liability_price) }}元，非流动负债合计：{{
            divide(summaryData.non_liability_price)
          }}元
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowDetail(row)" type="primary"> 查看明细 </el-button>
              <el-button plain link size="small" @click="goToExport(row)" type="primary"> 下载 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!--查看明细 -->
    <debt-statistics-details-drawer
      :showDrawer="isShowDebtDetailsDrawer"
      v-if="isShowDebtDetailsDrawer"
      :title="dialogTitle"
      :drawerData="drawerData"
      @cancel-dialog="handlerClose"
    />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import DebtStatisticsDetailsDrawer from "./compontents/DebtStatisticsDetailsDrawer.vue"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import { SEARCH_FORM_SETTING_DEBT_STATISTICS, TABLE_SETTING_DEBT_STATISTICS } from "../constants"
import {
  apiBackgroundFundSupervisionBusLiabilitySupLiabilityStatisticsList,
  apiBackgroundFundSupervisionBusLiabilitySupLiabilityDetailExport,
  apiBackgroundFundSupervisionBusLiabilitySupLiabilityTotal
} from "@/api/asset"
import { exportHandle } from "@/utils/exportExcel"
import { ElMessage } from "element-plus"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { divide } from "@/utils/index"
const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// table数据
const tableData = ref([])
const loading = ref(false)
const searchFormSetting = ref(SEARCH_FORM_SETTING_DEBT_STATISTICS)
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_DEBT_STATISTICS))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 合计
const summaryData = ref<any>({})
// 查看明细
const isShowDebtDetailsDrawer = ref(false)
const dialogTitle = ref("详情")
const drawerData = ref({})
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0] + "-01"
        let endDate = new Date(data[key].value[1])
        // 设置为最后一天
        let endDateNum = new Date(endDate.getFullYear(), endDate.getMonth() + 1, 0).getDate()
        params.end_date = data[key].value[1] + "-" + endDateNum
      }
    }
  }
  return params
}
// 搜索
const changeSearch = (model: any, type: any, itemType: string) => {
  if (itemType === "bing_org_id_list") {
    searchFormSetting.value.org_id_list.value = []
    searchFormSetting.value.org_id_list.orgsId = type
  }
  pageConfig.currentPage = 1
  getDataList()
  getSupLiabilityTotal()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
  getSupLiabilityTotal()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 导出
const goToExport = (item: any) => {
  console.log("goToExport")
  let params = {
    bing_org_id: item.channel_org_id,
    org_id: item.org_id,
    date_str: item.date_str
  }
  const option = {
    api: apiBackgroundFundSupervisionBusLiabilitySupLiabilityDetailExport,
    params: params
  }
  exportHandle(option)
}

const handlerShowDetail = (row: any) => {
  dialogTitle.value = row.channel_org_name
  drawerData.value = row
  isShowDebtDetailsDrawer.value = true
}
// 弹窗关闭
const handlerClose = () => {
  isShowDebtDetailsDrawer.value = false
}

onMounted(() => {
  getDataList()
  getSupLiabilityTotal()
})
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionBusLiabilitySupLiabilityStatisticsList({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 获取合计
const getSupLiabilityTotal = async () => {
  tableLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionBusLiabilitySupLiabilityTotal({
      ...formatQueryParams(searchFormSetting.value)
    })
  )
  tableLoading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    summaryData.value = res.data
  } else {
    ElMessage.error(res.msg)
  }
}
</script>
<style lang="scss" scoped>
.DebtStatistics {
  padding: 0 20px;
}
</style>
