<template>
  <div class="asset-statist container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div>
          固定资产合计：{{ divide(summaryData.gd_total) }}元，流动资产合计：{{ divide(summaryData.ld_total) }}元
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowDetail(row)" type="primary"> 查看明细 </el-button>
              <el-button plain link size="small" @click="goToExport(row)" type="primary"> 下载 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!--查看明细 -->
    <asset-statistics-details-drawer
      :showDrawer="isShowAssetDetailsDrawer"
      v-if="isShowAssetDetailsDrawer"
      :title="dialogTitle"
      :asset_info_statistics_id="assetInfoStatisticsId"
      @cancel-dialog="handlerClose"
    />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import AssetStatisticsDetailsDrawer from "./compontents/AssetStatisticsDetailsDrawer.vue"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import { SEARCH_FORM_SETTING_ASSET_STATISTICS, TABLE_SETTING_ASSET_STATISTICS } from "../constants"
import {
  apiBackgroundFundSupervisionAssetChannelAssetInfoStatisticsList,
  apiBackgroundFundSupervisionAssetChannelAssetInfoStatisticsListExport
} from "@/api/asset"
import { ElMessage } from "element-plus"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { exportHandle } from "@/utils/exportExcel"
import dayjs from "dayjs"
import { divide } from "@/utils/index"
const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// table数据
const tableData = ref([])
const loading = ref(false)
const searchFormSetting = ref(SEARCH_FORM_SETTING_ASSET_STATISTICS)
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_ASSET_STATISTICS)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 合计
const summaryData = ref<any>({})

// 查看明细
const isShowAssetDetailsDrawer = ref(false)
const dialogTitle = ref("")
const assetInfoStatisticsId = ref()
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0] + "-01"
        let endDate = new Date(data[key].value[1])
        // 设置为最后一天
        let endDateNum = new Date(endDate.getFullYear(), endDate.getMonth() + 1, 0).getDate()
        params.end_date = data[key].value[1] + "-" + endDateNum
      }
    }
  }
  return params
}
// 搜索
const changeSearch = (model: any, type: any, itemType: string) => {
  if (itemType === "supervision_organization_ids") {
    searchFormSetting.value.organization_ids.value = []
    searchFormSetting.value.organization_ids.orgsId = type
  }
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 导出
const goToExport = (item: any) => {
  const option = {
    type: "AssetStatistics",
    api: apiBackgroundFundSupervisionAssetChannelAssetInfoStatisticsListExport,
    params: {
      asset_info_statistics_id: item.id,
      page: 1,
      page_size: 9999
    }
  }
  exportHandle(option)
}

// 反馈详情
const handlerShowDetail = (row: any) => {
  dialogTitle.value = row.supervision_organization_name
  assetInfoStatisticsId.value = row.id
  isShowAssetDetailsDrawer.value = true
}
// 弹窗关闭
const handlerClose = () => {
  isShowAssetDetailsDrawer.value = false
}

onMounted(() => {
  getDataList()
})
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionAssetChannelAssetInfoStatisticsList({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results.map((v: any) => {
      v.statistics_date = dayjs(v.statistics_date).format("YYYY-MM")
      return v
    })
    pageConfig.total = res.data.count
    summaryData.value = res.data.summary_data
  } else {
    ElMessage.error(res.msg)
  }
}
</script>
<style lang="scss">
.asset-statist {
  padding: 0 20px;
}
</style>
