<template>
  <div class="sample-record container-wrapper" v-loading="mainLoading">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
        labelWidth="160px"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="table-button">
          <el-button
            type="primary"
            @click="goToExport"
            v-permission="['background_fund_supervision.channel_canteen_management.food_reserved_sample_record_export']"
            >导出</el-button
          >
          <el-button type="primary" @click="gotoPrint">打印</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #entryCupboard="{ row }">
              {{ row.entry_cupboard ? "是" : "否" }}
            </template>
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowPhoto(row.food_image)" type="primary">
                查看
              </el-button>
            </template>
            <template #reservedUserName="{ row }">
              <div class="person-tag line-1" @click="handlerShowPhoto(row.reserved_user_image)">
                {{ row.reserved_user_name }}
              </div>
            </template>
            <template #sampleEntryUser="{ row }">
              <div class="person-tag line-1" @click="handlerShowUser(row.sample_entry_user)">
                {{ getNameByList(row.sample_entry_user) }}
              </div>
            </template>
            <template #sampleExitUser="{ row }">
              <div class="person-tag line-1" @click="handlerShowUser(row.sample_exit_user)">
                {{ getNameByList(row.sample_exit_user) }}
              </div>
            </template>
            <!--@click="handlerShowModifyReason(row.not_reserved_reason, 'notReserved', row.id)"-->
            <template #notReservedReason="{ row }">
              <div class="person-tag color-red line-1">
                {{ row.not_reserved_reason ? row.not_reserved_reason : "" }}
              </div>
            </template>
            <!--@click="handlerShowModifyReason(row.not_entry_reason, 'notEntry', row.id)"-->
            <template #notEntryReason="{ row }">
              <div class="person-tag color-red line-1" v-if="!row.entry_cupboard">
                {{ row.not_entry_reason ? row.not_entry_reason : "" }}
              </div>
            </template>
            <template #temperature="{ row }"> {{ row.temperature ? row.temperature + "°C" : "" }} </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
    <!-- 修改原因-->
    <modify-reason-dialog
      v-model:isShow="modifyReasonDialogVisible"
      :title="modifyReasonDialogTitle"
      :type="modifyReasonDialogType"
      :id="modifyReasonDialogId"
      :content="modifyReasonDialogContent"
      @confirmDialog="confirmModifyReason"
      @closeDialog="cancelModifyReason"
    />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted, onUnmounted, watch } from "vue"
import { SEARCH_FORM_SETTING_SAMPLE_RECORD, TABLE_SETTING_SAMPLE_RECORD } from "./constants"
import { ElMessage } from "element-plus"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { exportHandle } from "@/utils/exportExcel"
import { useRouter } from "vue-router"
import {
  apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordPost,
  apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordExportPost,
  apiBackgroundDeviceDeviceListPost,
  apiBackgroundMenuTypeListPost
} from "@/api/canteen"
import { setLocalStorage } from "@/utils/storage"
import ModifyReasonDialog from "./compontents/ModifyReasonDialog.vue"
const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)
const router = useRouter()

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// table数据
const tableData = ref([])
const loading = ref(false)
const mainLoading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_SAMPLE_RECORD))
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_SAMPLE_RECORD))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 菜谱列表
const menuList = ref<Array<any>>([])
// 导出
const printType = "SupervisionCanteenSampleRecord"
// 修改原因dialog
const modifyReasonDialogVisible = ref(false)
const modifyReasonDialogTitle = ref("")
const modifyReasonDialogType = ref("")
const modifyReasonDialogId = ref(-1)
const modifyReasonDialogContent = ref("")

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "" && data[key].value !== "all") {
      if (key === "entry_cupboard") {
        params.entry_cupboard = data[key].value
      } else if (key === "meal_type" && data[key].value.length > 0) {
        params.meal_type = data[key].value
      } else if (key === "menu_type" && data[key].value) {
        params.menu_id = data[key].value
        let findItem = menuList.value.find((item: any) => item.id === data[key].value)
        params.menu_type = findItem?.menu_type
      } else if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}
// 搜索
const changeSearch = (e: any) => {
  pageConfig.currentPage = 1
  console.log("changeSearch", e)
  getDataList()
}

// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 导出
const goToExport = () => {
  console.log("goToExport")
  const option = {
    type: printType,
    api: apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordExportPost,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    }
  }
  exportHandle(option)
}

// 查看照片
const handlerShowPhoto = (imgUrl: any) => {
  const img = imgUrl || ""
  if (!img) {
    return ElMessage.error("图片不存在")
  }
  imageList.value = [img]
  imageVisible.value = true
}
// 打印
const gotoPrint = () => {
  let tabbleSetting = cloneDeep(tableSetting.value)
  tabbleSetting = tabbleSetting.filter((item: any) => item.prop !== "img")
  setLocalStorage("print_setting", tabbleSetting)
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: printType,
      print_title: "留样记录",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordPost",
      params: JSON.stringify({
        ...formatQueryParams(searchFormSetting.value),
        page: 1,
        page_size: 9999
      })
    }
  })
  window.open(href, "_blank")
}

// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
// 获取列表名字
const getNameByList = (list: any) => {
  console.log("getNameByList", list)
  if (!list || list.length === 0) {
    return ""
  }
  if (typeof list === "object") {
    let newList = []
    for (let key in list) {
      newList.push(list[key].name)
    }
    return newList.join("、")
  } else if (Array.isArray(list) && list.length > 0) {
    let newList = list.map((item: any) => item.name)
    return newList.join("、")
  }
}
// 显示用户信息
const handlerShowUser = async (list: any) => {
  console.log("handlerShowUser", list)
  if (!list || list.length === 0) {
    return "暂未图片"
  }
  if (typeof list === "object") {
    let newList: any[] = []
    for (let key in list) {
      newList.push(list[key].face_url)
    }
    imageList.value = newList
    imageVisible.value = true
  }
}
// const getUserPicInfo = (id: string, callBack: any) => {
//   mainLoading.value = true
//   apiBackgroundFundSupervisionChannelCanteenManagementGetAccountFaceUrlPost({
//     id
//   })
//     .then((res: any) => {
//       mainLoading.value = false
//       if (res && res.code === 0) {
//         callBack(res.data)
//       }
//     })
//     .catch((error) => {
//       mainLoading.value = false
//       console.log("error", error)
//     })
// }
// 确认修改
const confirmModifyReason = () => {
  modifyReasonDialogVisible.value = false
  getDataList()
}
// 显示修改原因
// const handlerShowModifyReason = (content: string, type: string, id: number) => {
//   modifyReasonDialogTitle.value = type === "notEntry" ? "未入柜原因" : "未留样原因"
//   modifyReasonDialogContent.value = content
//   modifyReasonDialogType.value = type
//   modifyReasonDialogId.value = id
//   modifyReasonDialogVisible.value = true
// }
// 取消修改原因
const cancelModifyReason = () => {
  modifyReasonDialogVisible.value = false
  modifyReasonDialogContent.value = ""
}
// 获取字典列表
const getDicList = async () => {
  // 获取设备列表
  const [err, res]: any[] = await to(
    apiBackgroundDeviceDeviceListPost({
      device_type: "LYG",
      page: 1,
      page_size: 9999
    })
  )
  console.log("getDicList", err, res)
  if (res && res.code === 0) {
    const data = res.data || {}
    let results = data.results || []
    if (results && results.length > 0 && searchFormSetting.value) {
      results = results.map((item: any) => {
        return {
          label: item.device_name,
          value: item.device_no
        }
      })
      searchFormSetting.value.entry_device_ids.dataList = cloneDeep(results)
    }
  }
  // 获取菜谱数据
  let params = {
    start_date: searchFormSetting.value.selecttime.value[0],
    end_date: searchFormSetting.value.selecttime.value[1]
  }
  if (searchFormSetting.value.org_id.value) {
    Reflect.set(params, "organization_ids", [searchFormSetting.value.org_id.value])
  }
  const [errMenu, resMenu]: any[] = await to(apiBackgroundMenuTypeListPost(params))
  console.log("apiBackgroundMenuTypeListPost", errMenu, resMenu)
  if (resMenu && resMenu.code === 0) {
    let dataMenu = resMenu.data || []
    console.log("dataMenu", dataMenu)

    if (dataMenu && dataMenu.length > 0 && searchFormSetting.value) {
      menuList.value = cloneDeep(dataMenu)
      dataMenu = dataMenu.map((item: any) => {
        return {
          label: item.name,
          value: item.id
        }
      })
      searchFormSetting.value.menu_type.value = ""
      searchFormSetting.value.menu_type.dataList = cloneDeep(dataMenu)
    }
  }
}
watch(
  () => [searchFormSetting.value.selecttime, searchFormSetting.value.org_id],
  ([newValue1, newValue2]) => {
    console.log("newValue1", newValue1, "newValue2", newValue2)
    getDicList()
  },
  { deep: true }
)

onMounted(() => {
  getDataList()
  getDicList()
})
onUnmounted(() => {
  console.log("onUnmounted SampleRecord")
})
</script>
<style lang="scss">
.sample-record {
  padding: 0 20px;

  .person-tag {
    color: var(--el-color-primary);
    width: 130px;
  }

  .el-popper {
    max-width: 300px;
  }

  .color-red {
    color: var(--el-color-red) !important;
  }
}
</style>
