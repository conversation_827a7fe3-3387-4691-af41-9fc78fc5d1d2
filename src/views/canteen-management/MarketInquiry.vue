<template>
  <div class="market-inquiry container-wrapper">
    <div ref="searchRef">
      <!-- <el-radio-group v-model="tabPosition">
        <el-radio-button value="inquiry">询价记录</el-radio-button>
        <el-radio-button value="comparison">比价记录</el-radio-button>
      </el-radio-group> -->
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="flex items-center" />
        <div class="table-button">
          <el-button type="primary" @click="handlerAdd">新建</el-button>
          <el-button type="primary" plain @click="handlerGotoRecord">历史记录</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #inquiryWarehouse="{ row }">
              <div class="cursor-pointer ps-origin-txt" @click="handlerShowDetail(row, 'inquiryWarehouse')">查看</div>
            </template>
            <template #materialsName="{ row }">
              <div class="cursor-pointer ps-origin-txt" @click="handlerShowDetail(row, 'materialsName')">查看</div>
            </template>
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowImport(row.id)" type="primary">
                导入物资
              </el-button>
              <el-button plain link size="small" @click="handlerDelete(row)" type="primary" color="red">
                删除
              </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
    <!--弹窗-->
    <inquiry-record-detail-dialog
      ref="inquiryRecordDialogRef"
      :is-show="isShowInquireRecordDialog"
      :type="dialogType"
      :title="dialogTitle"
      @cancel-dialog="handlerClose"
      @confirm-dialog="handlerConfirm"
    />
    <!--add弹窗-->
    <inquiry-detail-add-dialog
      ref="feedbackAddDialogRef"
      :is-show="isShowInquireAddDetailDialog"
      :type="dialogAddType"
      :title="dialogAddTitle"
      @cancel-dialog="handlerAddClose"
      @confirm-dialog="handlerAddConfirm"
    />
    <!-- 导入-->
    <import-excel-dialog
      ref="importExcelDataDialogRef"
      :visible="dialogImportVisible"
      :import-type="importType"
      :template-url="templateUrl"
      :header-len="headerLen"
      :is-delete-top-tips="isDeleteTopTips"
      @close-dialog="closeImportDialog"
      :import-api="importApi"
      @confirm-dialog="confirmImportDialog"
      dialogTitle="导入询价物资"
      btnConfirmTxt="确认并导入"
      tipStepTxt1="导入请先下载数据模板"
      tipStepTxt2="上传导入文件"
      :is-return-json="true"
      :id="selectId"
    />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted, watch } from "vue"
import {
  SEARCH_FORM_SETTING_MARKET_INQUIRY,
  TABLE_SETTING_MARKET_INQUIRY,
  SEARCH_FORM_SETTING_COMPARE_RECORD,
  TABLE_SETTING_COMPARE_RECORD
} from "./constants"
import { ElMessage } from "element-plus"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
// import { exportHandle } from "@/utils/exportExcel"
import InquiryRecordDetailDialog from "./compontents/InquiryRecordDetailDialog.vue"
import InquiryDetailAddDialog from "./compontents/InquiryDetailAddDialog .vue"
import { confirm } from "@/utils/message"
import {
  apiBackgroundFundSupervisionFundMarketInquiryListPost,
  apiBackgroundFundSupervisionFundMarketInquiryImportInquiryDetailPost,
  apiBackgroundFundSupervisionFundMarketInquiryDeletePost
} from "@/api/canteen"
import { downloadJsonExcel } from "@/utils/exportExcel"
// 菜单
const tabPosition = ref("inquiry")
// table组件
const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// table数据
const tableData = ref([])
const loading = ref(false)
const searchFormSetting = ref<any>(cloneDeep(SEARCH_FORM_SETTING_MARKET_INQUIRY))
const tableLoading = ref(false)
const tableSetting = ref<any>(cloneDeep(TABLE_SETTING_MARKET_INQUIRY))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100, 500]
})
// 导出
// const importType = "FoodSafetyTraceabilityExport"

// 弹窗
const isShowInquireRecordDialog = ref(false)
const dialogType = ref("")
const dialogTitle = ref("")
const inquiryRecordDialogRef = ref()

//add 弹窗
const isShowInquireAddDetailDialog = ref(false)
const dialogAddType = ref("")
const dialogAddTitle = ref("")
const feedbackAddDialogRef = ref()

// ref
const importExcelDataDialogRef = ref()
// 导入弹窗
const dialogImportVisible = ref(false)
// 导入模板链接
const templateUrl = location.origin + "/api/temporary/template_excel/channel/市场询价导入.xlsx"
// 表头长度
const headerLen = 0
// 是否显示导入弹窗的提示
const isDeleteTopTips = false
// 导入API
const importApi = apiBackgroundFundSupervisionFundMarketInquiryImportInquiryDetailPost
// 导入类型
const importType = "FundSupervisionMarketInquiryImport"
// 导入ID
const selectId = ref(-1)
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}
// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}

// 更新
// const updateListByKey = async (searchList: Array<any>) => {
//   const list: any[] = await getSearchTestData(
//     tableData,
//     tabPosition.value === "inquiry" ? inquirData : compareData,
//     searchList,
//     "",
//     tableLoading,
//     pageConfig.currentPage
//   )
//   pageConfig.total = list?.length || 0
// }
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 导出
// const goToExport = () => {
//   console.log("goToExport")
//   const option = {
//     type: importType,
//     api: apiCardGroupListPost,
//     params: {
//       ...formatQueryParams(searchFormSetting.value),
//       page: pageConfig.currentPage,
//       page_size: pageConfig.pageSize
//     }
//   }
//   exportHandle(option)
// }

// 查看照片
// const handlerShowPhoto = (row: any) => {
//   console.log(row)
//   const img = row ? row.imgs : ""
//   if (!img) {
//     return ElMessage.error("防制图片不存在")
//   }
//   imageList.value = [img]
//   imageVisible.value = true
// }
// 弹窗关闭
const handlerClose = () => {
  isShowInquireRecordDialog.value = false
}
// 弹窗确认
const handlerConfirm = () => {
  isShowInquireRecordDialog.value = false
}
// 弹窗add关闭
const handlerAddClose = () => {
  isShowInquireAddDetailDialog.value = false
}
// 弹窗add确认
const handlerAddConfirm = () => {
  isShowInquireAddDetailDialog.value = false
  getDataList()
}
// 新增
const handlerAdd = () => {
  dialogAddType.value = "add"
  dialogAddTitle.value = "新建询价"
  isShowInquireAddDetailDialog.value = true
}
// 根据类型显示详情
const handlerShowDetail = (row: any, type: string) => {
  console.log(row, "row")
  if (type === "inquiryWarehouse") {
    dialogAddType.value = "inquiryWarehouse"
    dialogAddTitle.value = "询价商家"
  } else if (type === "materialsName") {
    dialogAddType.value = "materialsName"
    dialogAddTitle.value = "询价物资"
  } else {
    dialogAddType.value = "purchaseDetail"
    dialogAddTitle.value = "询价详情"
  }
  // 设置弹窗数据
  if (feedbackAddDialogRef.value) {
    feedbackAddDialogRef.value.setDialogData(row, type)
  }
  console.log(row, "dialogType", dialogAddType.value)
  isShowInquireAddDetailDialog.value = true
}
// 删除
const handlerDelete = (row: any) => {
  console.log(row, "row")
  const name = row.name || ""
  confirm({ content: `确定要删除${name}的询价信息，删除后不可恢复，请谨慎操作` }, () => {
    deleteDataList(row.id)
  })
}
// 根据ID删除条目
const deleteDataList = async (id: number) => {
  const [err, res]: [any, any] = await to(
    apiBackgroundFundSupervisionFundMarketInquiryDeletePost({
      id: id
    })
  )
  if (err) {
    return
  }
  if (res && res.code === 0) {
    ElMessage.success("删除成功")
    getDataList()
  } else {
    ElMessage.error(res.msg)
    return
  }
}
// 跳转比较记录
const handlerGotoRecord = (row: any) => {
  console.log(row)
  dialogType.value = "detail"
  dialogTitle.value = "历史记录"
  isShowInquireRecordDialog.value = true
}
// 格式化导入失败的数据，通过xlsx方式下载显示
const formatImportFailureResult = (result: any[]) => {
  const purchaseNameObject: any = {
    merchant_name: 0,
    material_name: 1,
    inquiry_unit: 2,
    inquiry_price: 3,
    result: 4
  }
  let failureJson = [["询价商家", "物资名称", "规格", "单价", "导入结果"]]
  let json = result.map((v) => {
    let current: any[] = []
    Object.keys(purchaseNameObject).forEach((k) => {
      current[purchaseNameObject[k]] = v[k]
    })
    return current
  })
  failureJson = failureJson.concat(json)
  // 下载数据
  downloadJsonExcel(failureJson, null, "询价物资导入结果.xlsx")
}

watch(
  () => tabPosition.value,
  (newValue) => {
    if (newValue === "inquiry") {
      setTimeout(() => {
        searchFormSetting.value = cloneDeep(SEARCH_FORM_SETTING_MARKET_INQUIRY)
        tableSetting.value = cloneDeep(TABLE_SETTING_MARKET_INQUIRY)
        getDataList()
      }, 100)
    } else if (newValue === "comparison") {
      setTimeout(() => {
        searchFormSetting.value = cloneDeep(SEARCH_FORM_SETTING_COMPARE_RECORD)
        tableSetting.value = cloneDeep(TABLE_SETTING_COMPARE_RECORD)
        getDataList()
      }, 100)
    }
  }
)
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionFundMarketInquiryListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
// 导入显示
const handlerShowImport = (id: number) => {
  dialogImportVisible.value = true
  selectId.value = id
}
// 确认导入
const confirmImportDialog = (json: any, id: number) => {
  console.log("confirmImportDialog", json)
  handlerImportData(json, id)
}
// 关闭导入
const closeImportDialog = () => {
  dialogImportVisible.value = false
  if (importExcelDataDialogRef.value) {
    importExcelDataDialogRef.value.clearTableData()
  }
}
// 导入数据
const handlerImportData = async (json: any, id: number) => {
  console.log("handlerImportData", json)
  let list: any[] = []
  if (json && json.length > 0) {
    json.splice(0, 1)
    list = json.map((item: any) => {
      return {
        merchant_name: item[0],
        material_name: item[1],
        inquiry_unit: item[2],
        inquiry_price: item[3]
      }
    })
  }
  let params = {
    inquiry_id: id,
    detail: list
  }
  const [err, res]: any[] = await to(apiBackgroundFundSupervisionFundMarketInquiryImportInquiryDetailPost(params))
  if (err) {
    return
  }
  if (res && res.code === 0) {
    ElMessage.success("操作成功")
    dialogImportVisible.value = false
    let successList = res.data?.success || []
    if (successList && successList.length > 0) {
      successList = successList.map((item: any) => {
        item.result = "导入成功"
        return item
      })
    }
    let failureList = res.data?.failure || []
    formatImportFailureResult(successList.concat(failureList))
    getDataList()
  } else {
    ElMessage.error(res.msg || "操作失败")
  }
}

onMounted(() => {
  getDataList()
})
</script>
<style lang="scss">
.market-inquiry {
  padding: 0 20px;

  .el-popper {
    max-width: 300px;
  }
}
</style>
