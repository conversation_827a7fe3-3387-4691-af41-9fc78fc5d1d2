<template>
  <div class="ration-recipe container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header flex flex-justify-between flex-align-center">
        <div class="ps-flex col-center">
          <el-tooltip
            effect="dark"
            placement="top-start"
            content="监管组织是绑定的组织，所属组织是因为监管组织的数据有多个来源，所属组织是包含监管组织及其下级组织"
          >
            <img :src="IcQuestionBlack" class="w-32px h-32px" />
          </el-tooltip>
        </div>
        <div class="table-button">
          <el-button
            type="primary"
            @click="goToExport('list')"
            v-permission="['background_fund_supervision.supervision_ration_recipe.export_ration_recipe_list']"
            >导出</el-button
          >
          <el-button type="primary" @click="goToPrint">打印</el-button>
          <el-button type="primary" @click="DRIsFormDrawerShow = true">DRIs（2023）</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #date="{ row }"> {{ row.week_start_date }} ~ {{ row.week_end_date }} </template>
            <template #operation="{ row }">
              <el-button type="text" @click="checkRecipe(row)">查看菜谱</el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>

    <!-- 抽屉 -->
    <el-drawer
      v-model="drawerShow"
      title="带量食谱"
      :direction="'rtl'"
      :show-close="false"
      class="ps-drawer"
      size="50%"
    >
      <div class="p-20">
        <div class="flex flex-justify-between flex-align-center mb-20px">
          <div>{{ selectData.org_name }}</div>
          <el-button @click="goToExport('food')">导出</el-button>
        </div>
        <el-table
          :data="drawerTableData"
          :span-method="objectSpanMethod"
          border
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column
            v-for="(item, index) in drawerTableSetting"
            :key="index"
            :prop="item.key"
            :label="item.label"
            align="center"
          />
        </el-table>
      </div>
      <div class="dialog-footer m-t-20px">
        <el-button @click="drawerShow = false"> 取 消 </el-button>
      </div>
    </el-drawer>

    <!-- DRIs -->
    <el-drawer
      v-model="DRIsFormDrawerShow"
      title="DRIs(2023)"
      :direction="'rtl'"
      :show-close="false"
      class="ps-drawer"
      size="90%"
    >
      <el-table :data="DRIsFormTableData" style="width: 100%">
        <el-table-column prop="age" label="年龄/阶段" align="center" />
        <el-table-column label="能量" align="center">
          <el-table-column label="男性kcal/d" align="center">
            <el-table-column prop="man_pal1" label="PAL：轻" align="center" />
            <el-table-column prop="man_pal2" label="PAL：中" align="center" />
            <el-table-column prop="man_pal3" label="PAL：重" align="center" />
          </el-table-column>
          <el-table-column label="女性kcal/d" align="center">
            <el-table-column prop="woman_pal1" label="PAL：轻" align="center" />
            <el-table-column prop="woman_pal2" label="PAL：中" align="center" />
            <el-table-column prop="woman_pal3" label="PAL：重" align="center" />
          </el-table-column>
        </el-table-column>
        <el-table-column label="蛋白质" align="center">
          <el-table-column prop="AMDR1" label="AMDR/%E" align="center" />
        </el-table-column>
        <el-table-column label="脂肪" align="center">
          <el-table-column prop="AMDR2" label="AMDR/%E" align="center" />
        </el-table-column>
        <el-table-column label="碳水化合物" align="center">
          <el-table-column label="总碳水化合物" align="center">
            <el-table-column prop="AMDR3" label="AMDR/%E" align="center" />
          </el-table-column>
          <el-table-column label="膳食纤维" align="center">
            <el-table-column prop="AMDR4" label="AI/(g·d⁻¹)" align="center" />
          </el-table-column>
          <el-table-column label="添加糖*" align="center">
            <el-table-column prop="AMDR5" label="AMDR/%E" align="center" />
          </el-table-column>
        </el-table-column>
        <el-table-column label="水" align="center">
          <el-table-column label="适宜摄入量：mL/d" align="center">
            <el-table-column label="">
              <el-table-column prop="water_man" label="男" align="center" />
            </el-table-column>
            <el-table-column label="">
              <el-table-column prop="water_woman" label="女" align="center" />
            </el-table-column>
          </el-table-column>
        </el-table-column>
      </el-table>
      <div class="dialog-footer m-t-20px">
        <el-button @click="DRIsFormDrawerShow = false"> 关闭 </el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import IcQuestionBlack from "@/assets/layouts/ic_question_black.png"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import to from "await-to-js"
import {
  apiBackgroundFundSupervisionSupervisionRationRecipeListPost,
  apiBackgroundFundSupervisionSupervisionRationRecipeViewRecipePost,
  apiBackgroundFundSupervisionSupervisionRationRecipeExportRationRecipeListPost,
  apiBackgroundFundSupervisionSupervisionRationRecipeExportRecipePost
} from "@/api/supervision"
import { ElMessage } from "element-plus"
import dayjs from "dayjs"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// 搜索相关
const searchFormSetting = ref<any>({
  selecttime: {
    type: "week",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "日期筛选",
    labelWidth: "100px",
    placeholder: "请选择",
    value: dayjs().format("YYYY-MM-DD")
  },
  supervise_orgs_id: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  },
  orgs_id: {
    type: "canteenOrgs",
    label: "所属组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    orgsId: []
  },
  recipe_name: {
    type: "select",
    label: "食谱名称",
    labelWidth: "100px",
    value: "",
    placeholder: "请选择食谱名称",
    clearable: true,
    maxlength: 20,
    multiple: false,
    dataList: []
  }
})
const loading = ref<boolean>(false)
const changeSearch = (model: any, type: string, itemType: string) => {
  if (itemType === "supervise_orgs_id") {
    searchFormSetting.value.orgs_id.value = []
    searchFormSetting.value.orgs_id.dataList = []
    searchFormSetting.value.orgs_id.orgsId = type
  }
  pageConfig.currentPage = 1
  getDataList()
}
const handlerReset = () => {}

// 表格相关
const tableData = ref<any>([])
const tableSetting = ref<any>([
  { label: "日期", prop: "date", slot: "date" },
  { label: "监管组织", prop: "org_name" },
  { label: "所属组织", prop: "org_name" },
  { label: "食谱名称", prop: "recipe_name" },
  { label: "操作", prop: "operation", slot: "operation" }
])
const tableLoading = ref<boolean>(false)

const getAllRecipeList = async () => {
  let date = dayjs(searchFormSetting.value.selecttime.value).format("YYYY-MM-DD")
  let startDate = dayjs(date).startOf("week").add(1, "day").format("YYYY-MM-DD")
  let endDate = dayjs(date).endOf("week").add(1, "day").format("YYYY-MM-DD")
  let params = {
    page: 1,
    page_size: 9999,
    start_date: searchFormSetting.value.selecttime.value ? startDate : undefined,
    end_date: searchFormSetting.value.selecttime.value ? endDate : undefined,
    orgs_id: searchFormSetting.value.orgs_id.value.length ? searchFormSetting.value.orgs_id.value : undefined,
    supervise_orgs_id: searchFormSetting.value.supervise_orgs_id.value.length
      ? searchFormSetting.value.supervise_orgs_id.value
      : undefined,
    recipe_name: searchFormSetting.value.recipe_name.value || undefined
  }
  const [err, res]: [any, any] = await to(apiBackgroundFundSupervisionSupervisionRationRecipeListPost(params))
  tableLoading.value = false
  if (err) {
    ElMessage.error(err.msg)
  }
  if (res.code === 0) {
    searchFormSetting.value.recipe_name.dataList = res.data.results.map((item: any) => {
      let obj = {
        value: item.recipe_name,
        label: item.recipe_name
      }
      return obj
    })
  } else {
    ElMessage.error(res.msg)
  }
}
const getDataList = async () => {
  tableLoading.value = true
  console.log("searchFormSetting.value.selecttime.value", searchFormSetting.value.selecttime.value)
  let date = dayjs(searchFormSetting.value.selecttime.value).format("YYYY-MM-DD")
  let startDate = dayjs(date).startOf("week").add(1, "day").format("YYYY-MM-DD")
  let endDate = dayjs(date).endOf("week").add(1, "day").format("YYYY-MM-DD")
  let params = {
    page: pageConfig.currentPage,
    page_size: pageConfig.pageSize,
    start_date: searchFormSetting.value.selecttime.value ? startDate : undefined,
    end_date: searchFormSetting.value.selecttime.value ? endDate : undefined,
    orgs_id: searchFormSetting.value.orgs_id.value.length ? searchFormSetting.value.orgs_id.value : undefined,
    supervise_orgs_id: searchFormSetting.value.supervise_orgs_id.value.length
      ? searchFormSetting.value.supervise_orgs_id.value
      : undefined,
    recipe_name: searchFormSetting.value.recipe_name.value || undefined
  }
  const [err, res]: [any, any] = await to(apiBackgroundFundSupervisionSupervisionRationRecipeListPost(params))
  tableLoading.value = false
  if (err) {
    ElMessage.error(err.msg)
  }
  if (res.code === 0) {
    pageConfig.total = res.data.count
    tableData.value = cloneDeep(res.data.results)
  } else {
    ElMessage.error(res.msg)
  }
}
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

getAllRecipeList()
onMounted(() => {
  getDataList()
})

// 弹窗相关
import { DRIS_FORM_TABLE_DATA, WEEK_LIST, MEAL_TYPES } from "./constants"
import { divide } from "@/utils/index"
import { mergeHandle, mergeRowAction } from "@/utils/table"

interface SpanMethodType {
  row: any
  column: any
  rowIndex: number
  columnIndex: number
}

const drawerShow = ref<boolean>(false)
const DRIsFormDrawerShow = ref<boolean>(false)
const drawerTableData = ref<any>([])
const drawerTableSetting = ref<any>([
  { label: "日期", key: "date" },
  { label: "餐次", key: "meal_period_type" },
  { label: "食物名称", key: "food_name" },
  { label: "菜品重量/份", key: "food_weight" },
  { label: "菜品价格/份", key: "food_price" },
  { label: "配料", key: "ingredients" },
  { label: "食材重量", key: "ingredients_weight" }
])
const selectData = ref<any>({})
const DRIsFormTableData = ref<any>(DRIS_FORM_TABLE_DATA)
const mergeOpts: any = {
  useKeyList: {
    date: ["meal_period_type"],
    meal_period_type: ["food_name", "food_weight", "food_price"]
  }, // 是否根據固定key進行合并, 有值則根據key為唯一性進行字段表格合并，無則不做處理
  mergeKeyList: ["date"] // 通用的合并字段，根據值合并
}
const rowMergeArrs = ref<any>([])
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }: SpanMethodType) => {
  console.log("使用了")
  let keys = Object.keys(mergeOpts.useKeyList)
  let useKey = mergeOpts.useKeyList && keys.length
  console.log("useKey", useKey, keys)
  if (useKey) {
    for (const key in mergeOpts.useKeyList) {
      if (mergeOpts.useKeyList[key].includes(column.property)) {
        console.log("返回看看1", mergeRowAction(rowMergeArrs.value, column.property, rowIndex, columnIndex))
        return mergeRowAction(rowMergeArrs.value, column.property, rowIndex, columnIndex)
      }
    }
  }
  if (mergeOpts.mergeKeyList && mergeOpts.mergeKeyList.length && mergeOpts.mergeKeyList.includes(column.property)) {
    console.log("返回看看2", mergeRowAction(rowMergeArrs.value, column.property, rowIndex, columnIndex))
    return mergeRowAction(rowMergeArrs.value, column.property, rowIndex, columnIndex)
  }
}
const checkRecipe = async (data: any) => {
  tableLoading.value = true
  const [err, res]: [any, any] = await to(
    apiBackgroundFundSupervisionSupervisionRationRecipeViewRecipePost({
      id: data.id
    })
  )
  tableLoading.value = false
  if (err) {
    ElMessage.error(err.msg)
  }
  if (res.code === 0) {
    console.log("获取成功", res.data)
    let foodData = res.data
    let list: any = []
    for (const key in foodData) {
      for (const mealKey in foodData[key]) {
        let info = {
          date: "" as string,
          meal_period_type: "" as string,
          food_name: "" as string,
          food_weight: "" as string,
          food_price: "" as string,
          ingredients: "" as string,
          ingredients_weight: "" as string
        }
        let week = WEEK_LIST.filter((item) => item.value === dayjs(key).day())[0].label
        info.date = `${dayjs(key).format("YYYY年MM月DD日")} ${week}`
        info.meal_period_type = MEAL_TYPES.filter((item) => item.value === mealKey)[0].label
        if (foodData[key][mealKey].length) {
          foodData[key][mealKey].forEach((item: any) => {
            info.food_name = item.food_name
            info.food_weight = `${item.weight}g`
            info.food_price = `${divide(item.food_price)}元`
            if (item.ingredient_info_list.length) {
              // 找食材的数据，依次
              item.ingredient_info_list.forEach((itemIn: any) => {
                let newObj = cloneDeep(info)
                newObj.ingredients = itemIn.ingredient_name
                newObj.ingredients_weight = `${itemIn.ingredient_weight}g`
                list.push(newObj)
              })
            } else {
              info.ingredients = "--"
              info.ingredients_weight = "--"
              list.push(info)
            }
          })
        }
      }
    }
    drawerTableData.value = cloneDeep(list)
    rowMergeArrs.value = mergeHandle(drawerTableData.value, mergeOpts)
    selectData.value = cloneDeep(data)
    drawerShow.value = true
  } else {
    ElMessage.error(res.msg)
  }
}

// 导出相关
import { exportHandle } from "@/utils/exportExcel"

const goToExport = (type: string) => {
  let params: any = {}
  if (type === "list") {
    let date = dayjs(searchFormSetting.value.selecttime.value).format("YYYY-MM-DD")
    let startDate = dayjs(date).startOf("week").add(1, "day").format("YYYY-MM-DD")
    let endDate = dayjs(date).endOf("week").add(1, "day").format("YYYY-MM-DD")
    params = {
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize,
      start_date: searchFormSetting.value.selecttime.value ? startDate : undefined,
      end_date: searchFormSetting.value.selecttime.value ? endDate : undefined,
      orgs_id: searchFormSetting.value.orgs_id.value.length ? searchFormSetting.value.orgs_id.value : undefined,
      supervise_orgs_id: searchFormSetting.value.supervise_orgs_id.value.length
        ? searchFormSetting.value.supervise_orgs_id.value
        : undefined,
      recipe_name: searchFormSetting.value.recipe_name.value || undefined
    }
  } else {
    params = {
      id: selectData.value.id
    }
  }
  const option = {
    type: "",
    api:
      type === "list"
        ? apiBackgroundFundSupervisionSupervisionRationRecipeExportRationRecipeListPost
        : apiBackgroundFundSupervisionSupervisionRationRecipeExportRecipePost,
    params: params
  }
  exportHandle(option)
}

// 打印相关
import { useRouter } from "vue-router"
import { setLocalStorage } from "@/utils/storage"
const router = useRouter()
const goToPrint = () => {
  setLocalStorage("print_setting", tableSetting.value)
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: "RationRecipe",
      print_title: "带量食谱",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiBackgroundFundSupervisionSupervisionRationRecipeListPost", // 请求的api
      params: JSON.stringify({
        page: 1,
        page_size: 10
      })
    }
  })
  window.open(href, "_blank")
}
</script>

<style lang="scss">
.ration-recipe {
  padding: 0px 20px;
}
</style>
