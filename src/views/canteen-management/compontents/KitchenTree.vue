<template>
  <div class="orgs-tree-container" :style="{ height: height + 'px' }" ref="containerRef">
    <el-scrollbar :height="height">
      <div>
        <el-input
          v-model="treeFilterText"
          placeholder="请输入"
          class="w-230px search-input p-l-20px p-r-20px m-t-24px"
          @input="handlerInputChange"
        />
        <el-tree
          v-loading="treeLoading"
          :data="treeList"
          :props="treeProps"
          :check-on-click-node="true"
          :expand-on-click-node="false"
          :highlight-current="true"
          :current-node-key="selectId"
          class="max-w-278px"
          ref="treeRef"
          node-key="id"
          default-expand-all
          @node-click="treeHandleNodeClick($event)"
        >
          <template #default="{ node, data }">
            <div :class="['custom-tree-node flex items-center flex-1', node.isCurrent ? 'active-menu' : '']">
              <div @click="toggleChild(node, data)" class="h-16px">
                <!-- 没有子级所展示的图标 -->
                <div v-if="data.isJian" class="menu-icon">监</div>
              </div>
              <div :class="['m-l-5px flex flex-1 items-center']">
                <div class="text-ellipsis" :style="{ maxWidth: 278 - data.level * 18 + 'px' }">
                  {{ node.label }}
                </div>
              </div>
            </div>
          </template>
        </el-tree>
        <div
          v-if="treeCount > treeSize"
          class="ps-pagination justify-start"
          style="padding: 20px 2px; margin-top: 20px; text-align: right"
        >
          <el-pagination
            @current-change="treePaginationChange"
            :current-page="treePage"
            :page-size="treeSize"
            :pager-count="4"
            :small="true"
            layout="prev, pager, next"
            background
            class="ps-text"
            popper-class="ps-popper-select"
            :total="treeCount"
            :disabled="paginationLoading"
          />
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref, watch, nextTick } from "vue"
import { apiBackgroundFundSupervisionSupervisionChannelListPost } from "@/api/supervision/index"
import { ElMessage } from "element-plus"
import { OrganizationParams, type OrgsForm } from "../../organizational-supervision/index.d"
import { useUserStore } from "@/store/modules/user"
import useScreenHook from "@/hooks/useScreen"
import testData from "./test.json"
import { log } from "console"

// 回调事件
const emit = defineEmits([
  "treeClick", // 树点击
  "cancelDialog" // 弹窗确认
])

// 获取用户信息缓存
const userStore = useUserStore()
const containerRef = ref()
const height = useScreenHook(containerRef, 120).maxHeight

const treeFilterText = ref("")
const selectId = ref<number>(0)
const treeLoading = ref(false)
const treeList = ref<Array<any>>([])
// const isLazy = true
const treeCount = ref(0)
const treePage = ref(1)
const treeSize = ref(20)
const paginationLoading = ref(false)
const treeRef = ref<HTMLElement>()
const treeProps = {
  children: "children_list",
  label: "name",
  isLeaf: (data: any) => {
    return !data.has_children
  }
}
// 第一次进来获取数据，默认要点击第一条
const isFirstTime = ref(true)

// 初始化数据
const initData = async () => {
  const params = {
    page: treePage.value,
    page_size: treeSize.value
  }
  const userInfo = userStore.getUserInfo
  if (userInfo && userInfo.supervision_channel_id) {
    Reflect.set(params, "parent__in", userInfo.supervision_channel_id)
  } else {
    Reflect.set(params, "parent__is_null", "1")
  }
  if (treeFilterText.value && treeFilterText.value.trim().length > 0) {
    Reflect.set(params, "name", treeFilterText.value)
  }
  // treeList.value = await getChannelList(params)
  treeList.value = testData
  if (isFirstTime.value && treeList.value && treeList.value.length > 0) {
    // 第一次默认选中第一条
    selectId.value = treeList.value[0].id

    nextTick(() => {
      if (treeRef.value) {
        // @ts-ignore
        treeRef.value.setCurrentKey(treeList.value[0].id)
      }
    })

    isFirstTime.value = false
    console.log("selectId", selectId.value)
    emit("treeClick", treeList.value[0])
  }
}

// 树形点击事件
const toggleChild = (node: any, data: any) => {
  console.log("toggleChild", node, data)
  node.expanded = !node.expanded
}

// 懒加载组织结构
const loadTree = async (tree: any, resolve: any) => {
  // 0级直接退出执行
  if (tree.level === 0) {
    return
  }
  const params: OrganizationParams = {
    // status__in: ["enable", "disable"],
    page: 1,
    page_size: 99999
  }
  if (tree.data && tree.data.id) {
    params.parent__in = tree.data.id
    paginationLoading.value = true
  } else {
    params.parent__is_null = "1"
    treeLoading.value = true
  }
  const list = await getChannelList(params)
  console.log("loadTree", list)

  resolve(list)
}
// 获取组织结构列表
const getChannelList = (params: any): Promise<Array<any>> => {
  return new Promise((resolve) => {
    treeLoading.value = true
    paginationLoading.value = true
    apiBackgroundFundSupervisionSupervisionChannelListPost(params)
      .then((res) => {
        treeLoading.value = false
        paginationLoading.value = false
        if (res && res.code === 0) {
          if (treeFilterText.value && treeFilterText.value.trim().length > 0) {
            // 他是搜索组织的，那么它的内容都是组织
            const data: any = res.data || {}
            treeCount.value = data.count || 0
            if (data.results && data.results.length > 0) {
              data.results.forEach((item: any) => {
                item.isJian = false
              })
            }
            resolve(data.results || [])
          } else {
            // 监管+ 组织数据的要做处理
            const data: any = res.data || {}
            const results: OrgsForm[] = data.results || []
            // 递归处理数据
            if (results && results.length > 0) {
              setTreeList(results)
            }
            console.log("getChannelList", results)

            return resolve(results || [])
          }
        } else {
          ElMessage.error(res.msg || "获取失败")
          resolve([])
        }
      })
      .catch((error) => {
        treeLoading.value = false
        paginationLoading.value = false
        ElMessage.error(error.message)
      })
  })
}
// 设置树形数据
const setTreeList = (list: Array<OrgsForm>) => {
  // console.log("setTreeList", list)
  if (!list || (Array.isArray(list) && list.length === 0)) {
    return
  }
  // 原来的数据不变，加上isJian的属性
  list.forEach((item: OrgsForm) => {
    if (typeof item === "object" && !Reflect.has(item, "isJian")) {
      Reflect.set(item, "isJian", true)
    }
    // 组织数据要进行处理
    // console.log("setTreeList binded_org_info", item.binded_org_info)
    if (
      item.binded_org_info &&
      item.binded_org_info !== undefined &&
      Array.isArray(item.binded_org_info) &&
      item.binded_org_info.length > 0
    ) {
      const newChildList = item.binded_org_info.map((childItem: any) => {
        childItem.isJian = false
        childItem.level = item.level ? item.level + 1 : 0
        childItem.name = childItem.org_name
        childItem.id = childItem.org_id
        return childItem
      })
      let children = item.children_list || []
      children = children.concat(newChildList)
      item.children_list = children
    }
    if (item.children_list && Array.isArray(item.children_list) && item.children_list.length > 0) {
      setTreeList(item.children_list)
    }
  })
}
// 组织树点击事件
const treeHandleNodeClick = async (e: any) => {
  console.log("treeHandleNodeClick", e)
  selectId.value = e.id
  emit("treeClick", e)
}

// 组织树的分页
const treePaginationChange = async (e: any) => {
  treePage.value = e
  initData()
}
// 组织树筛选
const handlerInputChange = (e: any) => {
  console.log("handlerInputChange", e)
  treeFilterText.value = e
  nextTick(() => {
    treeList.value = searchOrganizations(treeList.value, treeFilterText.value)
  })
  console.log("treeList.value", treeList.value)
}

function searchOrganizations(orgs: any[], query: string): any[] {
  const lowerQuery = query.toLowerCase()

  function matchName(org: any) {
    return org.name.toLowerCase().includes(lowerQuery)
  }

  function searchRecursively(org: any): any | null {
    if (matchName(org)) {
      // 如果当前节点匹配，创建一个新的对象避免修改原始数据
      const matchedOrg = { ...org }
      if (matchedOrg.children_list) {
        matchedOrg.children_list = matchedOrg.children_list.map(searchRecursively).filter(Boolean) as any[]
      }
      return matchedOrg
    } else if (org.children_list) {
      // 如果当前节点不匹配但有子节点，检查子节点
      const matchedChildren = org.children_list.map(searchRecursively).filter(Boolean) as any[]

      if (matchedChildren.length > 0) {
        // 如果有匹配的子节点，保留当前节点及其匹配的子节点
        return { ...org, children_list: matchedChildren }
      }
    }

    return null
  }

  // 搜索顶层节点并过滤掉null值
  return orgs.map(searchRecursively).filter(Boolean) as any[]
}

onMounted(() => {
  initData()
})

watch(
  () => treeFilterText.value,
  (newValue) => {
    console.log("newValue", newValue)
    initData()
  }
)
</script>

<style lang="scss" scoped>
.orgs-tree-container {
  width: 280px;
  min-width: 280px;
  background-color: #fff;
  height: 100%;
  border-radius: 16px;
  overflow: hidden;

  :deep(.el-tree-node__content) {
    max-width: 278px;
    height: 40px;
  }
  :deep(.expanded.el-tree-node__expand-icon) {
    margin-left: 10px;
  }

  .custom-tree-node {
    .menu-icon {
      width: 16px;
      height: 16px;
      padding: 2px 1px 2px 1px;
      border-radius: 3px;
      background: var(--el-color-primary);
      font-size: 12px;
      color: #fff;
    }
  }

  :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
    background: var(--el-menu-liner-bg);
    height: 40px;
  }

  .active-menu {
    color: var(--el-color-primary);
  }
}
</style>
