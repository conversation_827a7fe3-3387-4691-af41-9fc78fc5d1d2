<template>
  <el-dialog v-model="state.visible" :title="title" @close="handleClose" customClass="ps-dialog" :width="width">
    <div>
      <el-input v-model="reason" style="width: 380px" :rows="2" type="textarea" placeholder="请输入" />
    </div>
    <template #footer>
      <div class="dialog-footer" style="margin-top: 20px">
        <el-button type="primary" class="ps-origin-btn-plain" @click="handleClose" v-loading="isLoading">
          取消
        </el-button>
        <el-button type="primary" @click="confirmDialog" v-loading="isLoading"> 保存 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive, ref, watch } from "vue"
import { useVModel } from "@vueuse/core"
import to from "await-to-js"
import { ElMessage } from "element-plus"
import {
  apiBackgroundFundSupervisionChannelCanteenManagementModifyNotEntryReasonPost,
  apiBackgroundFundSupervisionChannelCanteenManagementModifyNotReservedReasonPost
} from "@/api/canteen"
const emits = defineEmits(["closeDialog", "update:isShow", "confirmDialog"])
const props = defineProps({
  loading: Boolean,
  title: {
    type: String,
    default: "修改原因"
  },
  width: {
    type: String,
    default: "410px"
  },
  images: {
    type: Object,
    default() {
      return {}
    }
  },
  name: {
    type: String,
    default: ""
  },
  isShow: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: "notEntry" // 类型 notEntry:未上架  notReserved:未预定
  },
  id: {
    type: Number,
    default: -1
  },
  content: {
    type: String,
    default: ""
  }
})
const isLoading = ref(false)
const reason = ref("")

const state = reactive({
  visible: useVModel(props, "isShow", emits)
})
// 关闭对话框
const handleClose = () => {
  isLoading.value = false
  state.visible = false
  emits("closeDialog")
}
// 确认对话框
const confirmDialog = () => {
  modifyReason()
}
// 修改原因
const modifyReason = async () => {
  let params: any = {
    id: props.id,
    reason: reason.value
  }
  isLoading.value = true
  const [err, res]: any[] = await to(
    props.type === "notEntry"
      ? apiBackgroundFundSupervisionChannelCanteenManagementModifyNotEntryReasonPost(params)
      : apiBackgroundFundSupervisionChannelCanteenManagementModifyNotReservedReasonPost(params)
  )
  isLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    ElMessage.success("修改成功")
    emits("confirmDialog", reason.value)
  } else {
    ElMessage.error(res.msg || "修改失败")
  }
}
watch(
  () => props.content,
  (newVal) => {
    reason.value = newVal
  }
)
</script>

<style lang="scss" scoped>
.pic-tag {
  width: 250px;
  height: 400px;
}

.pic-tag2 {
  width: 500px;
  height: 400px;
}

.font-size-24 {
  font-size: 24px;
}
</style>
