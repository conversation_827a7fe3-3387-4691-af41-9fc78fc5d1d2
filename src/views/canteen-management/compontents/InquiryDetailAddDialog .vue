<template>
  <div class="inquiry-detail container-wrapper">
    <el-drawer
      v-model="dialogFormVisible"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div>
        <el-form :model="ruleForm" :rules="rules" ref="formRef" label-width="120px" label-position="left">
          <el-form-item label="询价名称" v-if="type === 'add'" prop="inquiry_name">
            <el-input v-model="ruleForm.inquiry_name" placeholder="请输入询价名称" maxlength="30" show-word-limit />
          </el-form-item>
          <el-form-item label="比价截止日期" v-if="type === 'purchaseDetail'">
            <el-date-picker v-model="ruleForm.valid_time" type="date" placeholder="选择日期" style="width: 100%" />
          </el-form-item>
          <el-form-item label="询价类别" v-if="type === 'purchaseDetail'">
            <el-select v-model="ruleForm.type" placeholder="请选择询价类别">
              <el-option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="询价商家" v-if="false" prop="supplierList">
            <div>
              <div
                v-for="(item, index) in ruleForm.supplierList"
                :key="index"
                :class="['flex', 'items-center', index > 0 ? 'm-t-20px' : '']"
              >
                <div class="w-90px">商家{{ index + 1 }}：</div>
                <el-form-item :prop="'supplierList.' + index + '.name'" :rules="rules.name">
                  <el-input v-model="item.name" placeholder="请输入供应商名称" class="m-l-10px" style="width: 250px" />
                </el-form-item>
                <el-icon class="w-32px h-32px m-l-10px cursor-pointer el-tag-my" @click="addSupplier(index)" size="32">
                  <CirclePlus />
                </el-icon>
                <el-icon
                  class="w-32px h-32px m-l-10px cursor-pointer el-tag-my"
                  @click="delSupplier(index)"
                  v-if="index > 0"
                  size="32"
                >
                  <Remove />
                </el-icon>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <!--询价详情列表-->
      <div class="table-content position-relative" v-if="type === 'purchaseDetail'">
        <div class="m-t-20px m-b-10px">询价商品</div>
        <div class="btn-right">
          <el-button link plain @click="addGoods" type="primary">添加商品</el-button>
          <el-button link plain> 导入 </el-button>
        </div>
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          maxHeight="400px"
          v-if="type === 'purchaseDetail'"
        >
          <ps-column :table-headers="tableSetting" />
        </ps-table>
      </div>
      <!--询价商家-->
      <div v-if="type === 'inquiryWarehouse'">
        <div class="inquiry-detail">
          <div class="tag-content">
            <div
              v-for="(item, index) in ruleForm.supplierList"
              :key="index"
              :class="['m-r-10px', index > 0 ? 'm-t-10px' : '']"
            >
              商家{{ index + 1 }}： {{ item }}
            </div>
            <el-empty v-if="ruleForm.supplierList.length === 0" />
          </div>
        </div>
      </div>
      <!--询价物资-->
      <div v-if="type === 'materialsName'" class="container-wrapper">
        <!--表格展示物资-->
        <div class="table-wrapper">
          <div class="table-header">
            <div class="table-button">
              <el-button
                type="primary"
                plain
                @click="goToExport"
                v-permission="['background_fund_supervision.fund_market_inquiry.list_export']"
                >导出</el-button
              >
            </div>
          </div>
          <ps-table
            :tableData="tableDataMaterials"
            ref="psTableRef"
            v-loading="tableLoading"
            :show-pagination="false"
            :pageConfig="pageConfigMaterials"
          >
            <ps-column :table-headers="tableSettingMaterials" />
          </ps-table>
        </div>
      </div>
      <div class="dialog-footer m-t-20px">
        <el-button
          type="primary"
          class="ps-origin-btn-plain"
          @click="closeDialog"
          v-loading="confirmLoading"
          v-if="type === 'add'"
          :disabled="confirmLoading"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          @click="confirmDialog"
          v-loading="confirmLoading"
          v-if="type === 'add'"
          :disabled="confirmLoading"
        >
          保存
        </el-button>
        <el-button
          type="primary"
          @click="closeDialog"
          plain
          v-if="type === 'inquiryWarehouse' || type === 'materialsName'"
        >
          关闭
        </el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue"
import { ElMessage } from "element-plus"
// import { confirm } from "@/utils/message"
import type { TInquiryDetailForm } from "../index.d"
import { cloneDeep } from "lodash"
import { TABLE_SETTING_INQUIRY_DETAIL, TABLE_SETTING_MATERIALS_DETAIL } from "../constants"
import { CirclePlus, Remove } from "@element-plus/icons-vue"
import { exportHandle } from "@/utils/exportExcel"
import {
  apiBackgroundFundSupervisionFundMarketInquiryListExportPost,
  apiBackgroundFundSupervisionFundMarketInquiryAddPost
} from "@/api/canteen"
import { to } from "await-to-js"
const props = defineProps({
  title: {
    type: String,
    default: "反馈详情"
  },
  width: {
    type: String,
    default: "648px"
  },
  isShow: {
    type: Boolean,
    default: false
  },
  type: {
    type: String, //  purchaseDetail:询价  compareDetail:比价,  add 新增
    default: "purchaseDetail"
  },
  orgId: {
    type: Number,
    default: -1
  },
  alreadyChooseIds: {
    type: Array,
    default: () => {
      return []
    }
  },
  // 选择节点数据
  organizationData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const dialogFormVisible = ref(false)
const confirmLoading = ref(false)
const ruleForm = reactive<TInquiryDetailForm>({
  id: "",
  inquiry_name: "",
  valid_time: "",
  inquiry_num: "",
  type: "",
  inquiry_warehouse: "",
  inquiry_person: "",
  supplierList: [] as any[]
})

// table数据
const tableData = ref<Array<any>>([])
const tableDataMaterials = ref<Array<any>>([]) // 物资表格数据
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_INQUIRY_DETAIL))
const tableSettingMaterials = ref(cloneDeep(TABLE_SETTING_MATERIALS_DETAIL)) // 物资表格数据
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
const pageConfigMaterials = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 9999
})
// 类型
const typeList = [{ label: " ", value: "果蔬类" }]

const formRef = ref()
const rules = reactive({
  inquiry_name: [{ required: true, message: "请输入询价名称", trigger: ["blur", "change"] }],
  name: [{ required: true, message: "请输入供应商名称", trigger: ["blur", "change"] }]
})

// 弹窗确认
const confirmDialog = async () => {
  if (!formRef.value) return

  try {
    formRef.value.validate().then(async () => {
      if (props.type === "add") {
        addInquiryData()
      }
    })
  } catch (error) {
    ElMessage.error("请完善必填信息")
  }
}

// 添加询价数据
const addInquiryData = async () => {
  confirmLoading.value = true
  const [err, res]: [any, any] = await to(
    apiBackgroundFundSupervisionFundMarketInquiryAddPost({
      name: ruleForm.inquiry_name
    })
  )
  confirmLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    ElMessage.success("添加成功")
    emit("confirmDialog")
  } else {
    ElMessage.error(res.msg || "添加失败")
  }
}

// 弹窗关闭
const closeDialog = () => {
  // 清除校验
  if (formRef.value) {
    setTimeout(() => {
      ruleForm.id = ""
      ruleForm.inquiry_name = ""
      ruleForm.valid_time = ""
      ruleForm.inquiry_num = ""
      ruleForm.type = ""
      ruleForm.inquiry_warehouse = ""
      ruleForm.inquiry_person = ""
      ruleForm.supplierList = []
      tableData.value = []
      tableDataMaterials.value = []
      formRef.value.clearValidate()
      emit("cancelDialog")
    }, 100)
  }
}
// 设置数据
const setDialogData = (data: any, type: string) => {
  console.log("setDialogData", data, type)
  if (data && typeof data === "object") {
    ruleForm.id = data.id
    switch (type) {
      case "purchaseDetail":
        ruleForm.inquiry_name = data.inquiry_name
        ruleForm.valid_time = data.valid_time
        ruleForm.type = data.type
        ruleForm.supplierList = data.supplierList
        break
      case "inquiryWarehouse":
        ruleForm.supplierList = data.merchant_info
        break
      case "materialsName":
        tableDataMaterials.value = data.detail_info || []
        pageConfigMaterials.total = tableDataMaterials.value.length || 0
        break
    }
  }
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
}
// 添加供应商
const addSupplier = (index: number) => {
  console.log("addSupplier", index)
  ruleForm.supplierList.push({ name: "" })
}
// 删除供应商
const delSupplier = (index: number) => {
  ruleForm.supplierList.splice(index, 1)
}
// 添加物资
const addGoods = () => {}

// 导出
const goToExport = () => {
  console.log("goToExport")
  if (!tableDataMaterials.value || tableDataMaterials.value.length === 0) {
    ElMessage.error("暂无数据导出")
    return
  }
  const option = {
    type: "inquiryDetailExport",
    api: apiBackgroundFundSupervisionFundMarketInquiryListExportPost,
    params: {
      id: ruleForm.id
      // page: pageConfigMaterials.currentPage,
      // page_size: pageConfigMaterials.total
    }
  }
  exportHandle(option)
}
watch(
  () => ({
    isShow: props.isShow
  }),
  (newValue) => {
    console.log("watch dialog", newValue)
    dialogFormVisible.value = newValue.isShow
    if (newValue.isShow) {
      // initData()
    }
  }
)
defineExpose({ setDialogData })
</script>
<style lang="scss" scoped>
.container-wrapper .table-content {
  padding: 0 !important;
  position: relative;
}

.container-wrapper .table-wrapper {
  box-shadow: none;
}

.btn-right {
  position: absolute;
  top: 0;
  right: 20px;
  z-index: 111;
}

.inquiry-detail {
  .tag-title {
    width: 120px;
    min-width: 120px;
    background: #f4f6fc;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-left: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }

  .tag-content {
    min-width: 300px;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }

  .border-bottom {
    border-bottom: 1px solid #e9ecf1;
  }

  .el-tag-my {
    color: var(--el-color-primary);
  }
}
</style>
