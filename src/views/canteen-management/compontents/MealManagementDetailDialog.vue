<template>
  <div class="inquiry-detail container-wrapper">
    <el-drawer
      v-model="dialogFormVisible"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div>
        <div class="flex">
          <div class="tag-title">陪餐人</div>
          <div class="tag-content">{{ ruleForm.person }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">备餐间（含服务态度）</div>
          <div class="tag-content">{{ ruleForm.pantry }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">就餐区（含餐饮浪费情况）</div>
          <div class="tag-content">{{ ruleForm.dining_area }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">其他加工操作区</div>
          <div class="tag-content">{{ ruleForm.other_area }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">餐具消洗</div>
          <div class="tag-content">{{ ruleForm.dishware_cleaning }}</div>
        </div>
        <div class="flex">
          <div class="tag-title border-bottom">明厨亮灶运作</div>
          <div class="tag-content border-bottom">{{ ruleForm.bright_kitchen }}</div>
        </div>
      </div>
      <!--菜品信息-->
      <div class="table-content position-relative">
        <div class="m-t-20px m-b-10px">菜品信息及直观评价</div>
        <ps-table
          :class="[type !== 'purchaseDetail' ? 'm-t-30px' : '']"
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="false"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
        >
          <ps-column :table-headers="tableSetting">
            <template #name="{ row }">
              <div>{{ getFoodName(row) }}</div>
            </template>
            <template #operationNew="{ row }">
              <el-button
                plain
                link
                size="small"
                @click="handlerShowPhoto(row.image ? [row.image] : [], '', 0, row.name)"
                type="primary"
              >
                查看
              </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
      <div>
        <div class="m-t-20px m-b-10px">陪餐实况</div>
        <div class="flex">
          <div :class="[index > 0 ? 'm-l-20px' : '']" v-for="(item, index) in ruleForm.personImgList" :key="index">
            <img
              :src="item"
              class="w-100px h-100px cursor-pointer"
              @click="handlerShowPhoto(ruleForm.personImgList, 'peican', index, '陪餐实况')"
            />
            <div>{{ item.name }}</div>
          </div>
        </div>
      </div>
      <div>
        <div class="m-t-20px m-b-10px">陪餐人员签字</div>
        <div class="flex">
          <div
            :class="[signIndex > 0 ? 'm-l-20px' : '']"
            v-for="(item, signIndex) in ruleForm.signImgList"
            :key="signIndex"
          >
            <img
              :src="item"
              class="w-100px h-100px cursor-pointer"
              @click="handlerShowPhoto(ruleForm.signImgList, 'sign', signIndex, '陪餐人员签字')"
            />
            <div>{{ item.name }}</div>
          </div>
        </div>
      </div>
      <div class="m-t-20px">备注：{{ ruleForm.remark || "无" }}</div>
      <div class="dialog-footer m-t-20px">
        <!-- <el-button type="primary" class="ps-origin-btn-plain" @click="closeDialog" v-loading="confirmLoading">
          取消
        </el-button> -->
        <el-button type="primary" @click="confirmDialog" v-loading="confirmLoading"> 关闭 </el-button>
      </div>
      <!-- 图片预览-->
      <image-preview-dialog
        v-model="imageVisible"
        :imgs="imageList"
        :currentIndex="currentIndex"
        :title="dialogTitle"
      />
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue"
// import { confirm } from "@/utils/message"
import type { TPersonMealForm } from "../index.d"
import { cloneDeep } from "lodash"
import { TABLE_SETTING_MEAL_MANAGEMENT_DETAIL } from "../constants"
import { getTestData, divide } from "@/utils/index"
import { ElMessage } from "element-plus"
import { getNameByType, getPersonNameByList } from "../utils"
import ImagePreviewDialog from "@/components/ImagePreviewDialog/index.vue"

const props = defineProps({
  title: {
    type: String,
    default: "详情"
  },
  width: {
    type: String,
    default: "1048px"
  },
  isShow: {
    type: Boolean,
    default: false
  },
  type: {
    type: String, //  purchaseDetail:询价  compareDetail:比价
    default: "purchaseDetail"
  },
  orgId: {
    type: Number,
    default: -1
  },
  alreadyChooseIds: {
    type: Array,
    default: () => {
      return []
    }
  },
  // 选择节点数据
  organizationData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const dialogFormVisible = ref(false)
const confirmLoading = ref(false)
const ruleForm = reactive<TPersonMealForm>({
  person: "",
  pantry: "",
  dining_area: "",
  other_area: "",
  dishware_cleaning: "",
  bright_kitchen: "",
  personImgList: [],
  signImgList: [],
  remark: ""
})
// 定义 tableData 的类型
interface ITableDataRow {
  name: string
  price: number
  image?: string // 根据实际数据结构调整
  [key: string]: any // 允许扩展字段
}
// table数据
const tableData = ref<ITableDataRow[]>([])
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_MEAL_MANAGEMENT_DETAIL))
const cloneTableData = cloneDeep(tableData)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 9999
})

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])
const currentIndex = ref(0)
const dialogTitle = ref("图片预览")

// 弹窗确认
const confirmDialog = () => {
  emit("confirmDialog")
}

// 弹窗关闭
const closeDialog = () => {
  ruleForm.person = ""
  ruleForm.pantry = ""
  ruleForm.dining_area = ""
  ruleForm.other_area = ""
  ruleForm.dishware_cleaning = ""
  ruleForm.bright_kitchen = ""
  ruleForm.personImgList = []
  ruleForm.signImgList = []
  ruleForm.remark = ""
  tableData.value = []
  emit("cancelDialog")
}
// 设置数据
const setDialogData = (data: any) => {
  console.log("setDialogData", props, data)

  if (data && typeof data === "object") {
    ruleForm.person = getPersonList(data.person_record_list)
    ruleForm.pantry =
      getCurrentNameByType("room_clean_type", data.room_clean_type) +
      "、" +
      getCurrentNameByType("room_attitude_type", data.room_attitude_type)
    ruleForm.dining_area =
      getCurrentNameByType("area_clean_type", data.area_clean_type) +
      "、" +
      getCurrentNameByType("area_waste_type", data.area_waste_type)
    ruleForm.other_area =
      getCurrentNameByType("oa_clean_type", data.oa_clean_type) +
      "、" +
      getCurrentNameByType("oa_operate_type", data.oa_operate_type)
    ruleForm.dishware_cleaning =
      getCurrentNameByType("tda_clean_type", data.tda_clean_type) +
      "、" +
      getCurrentNameByType("tda_disinfection_type", data.tda_disinfection_type)
    ruleForm.bright_kitchen = getCurrentNameByType("operation_type", data.operation_type)
    ruleForm.personImgList = data.images || []
    ruleForm.signImgList = data.face_url ? [data.face_url] : []
    ruleForm.remark = data.remark
    let foodRecordList: Array<ITableDataRow> = data.food_record_list || []
    let setMealRecordList: Array<ITableDataRow> = data.set_meal_record_list || []
    if (foodRecordList.length > 0 || setMealRecordList.length > 0) {
      tableData.value = foodRecordList.concat(setMealRecordList)
    }
  }
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getTestData(tableData, cloneTableData.value, tableLoading, pageConfig.currentPage)
}

// 查看照片
const handlerShowPhoto = (img: Array<any>, type: string, index: number, title: string) => {
  console.log(img)
  currentIndex.value = index
  dialogTitle.value = title
  if (img && img.length > 0) {
    if (type === "peican") {
      imageList.value = img
      imageVisible.value = true
      return
    }
    if (type === "sign") {
      imageList.value = img
      imageVisible.value = true
      return
    }
    imageList.value = [img]
    imageVisible.value = true
  } else {
    ElMessage.error("无查看图片")
  }
}
// 获取人员列表
const getPersonList = (list: any[]) => {
  return getPersonNameByList(list)
}
// 获取名称
const getCurrentNameByType = (type: string, value: any) => {
  console.log("getCurrentNameByType", type, value)
  return getNameByType(type, value)
}

// 获取详情菜品名称
const getFoodName = (row: any) => {
  let name = row.name
  let price = row.price ? divide(row.price) : "0.00"
  return `${name}  ¥${price}/份`
}
watch(
  () => ({
    isShow: props.isShow
  }),
  (newValue) => {
    console.log("watch dialog", newValue)
    dialogFormVisible.value = newValue.isShow
    if (newValue.isShow) {
      // initData()
    }
  }
)
defineExpose({ setDialogData })
</script>
<style lang="scss" scoped>
.container-wrapper .table-content {
  padding: 0 !important;
  position: relative;
}
.btn-layout {
  position: absolute;
  top: -40px;
  right: 20px;
  z-index: 111;
}
.inquiry-detail {
  .tag-title {
    width: 250px;
    min-width: 120px;
    background: #f4f6fc;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-left: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .tag-content {
    min-width: 300px;
    max-width: 300px;
    font-size: 14px;
    padding: 15px 20px;
    word-break: break-all;
    border-top: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .border-bottom {
    border-bottom: 1px solid #e9ecf1;
  }
}
</style>
