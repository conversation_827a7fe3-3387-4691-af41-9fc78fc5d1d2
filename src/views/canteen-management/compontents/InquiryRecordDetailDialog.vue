<template>
  <div class="inquiry-record-detail container-wrapper">
    <el-drawer
      v-model="dialogFormVisible"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div class="container-wrapper">
        <div ref="searchRef">
          <el-form :model="searchForm" ref="formRef" label-position="right">
            <div class="flex">
              <el-form-item label="操作时间">
                <el-date-picker
                  v-model="searchForm.operate_time"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  :disabled-date="disabledDate"
                  size="default"
                  style="width: 400px"
                  :clearable="false"
                  @change="handlerDateChange"
                />
              </el-form-item>
              <el-form-item label="操作" class="m-l-20px" label-width="120px">
                <el-select
                  v-model="searchForm.operate_type"
                  placeholder="请选择"
                  multiple
                  clearable
                  style="width: 200px"
                  @change="handlerSelectChange"
                >
                  <el-option
                    v-for="(item, index) in operateList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
      <!--询价详情列表-->
      <div class="table-content position-relative">
        <div class="text-red">历史记录只能查询最近30天的数据。</div>
        <ps-table
          class="m-t-20px"
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          maxHeight="580px"
        >
          <ps-column :table-headers="tableSetting">
            <template #operator="{ row }">
              <div>{{ getAccountName(row) }}</div>
            </template>
          </ps-column>
        </ps-table>
      </div>
      <div class="dialog-footer m-t-20px">
        <el-button type="primary" @click="confirmDialog" v-loading="confirmLoading"> 关闭 </el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue"
import { cloneDeep } from "lodash"
import { TABLE_SETTING_INQUIRY_RECORD_DETAIL } from "../constants"
import { getPreDate } from "@/utils/date"
import to from "await-to-js"
import { apiBackgroundFundSupervisionChannelOperationListPost } from "@/api/user"
import { ElMessage } from "element-plus"
const props = defineProps({
  title: {
    type: String,
    default: "历史记录"
  },
  width: {
    type: String,
    default: "1048px"
  },
  isShow: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: "detail"
  },
  orgId: {
    type: Number,
    default: -1
  },
  alreadyChooseIds: {
    type: Array,
    default: () => {
      return []
    }
  },
  // 选择节点数据
  organizationData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const dialogFormVisible = ref(false)
const confirmLoading = ref(false)

// 搜索
const searchForm = reactive({
  operate_time: [getPreDate(3, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
  operate_type: []
})
const operateList = [
  {
    label: "新增",
    value: "新建"
  },
  {
    label: "导入",
    value: "导入"
  },
  {
    label: "删除",
    value: "删除"
  }
]
// table数据
const tableData = ref([])
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_INQUIRY_RECORD_DETAIL))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100, 500]
})

// 获取列表
const getDataList = async () => {
  let params: any = {
    page: pageConfig.currentPage,
    page_size: pageConfig.pageSize,
    operate_type: "market_inquiry"
  }
  if (searchForm.operate_time) {
    params.start_time = searchForm.operate_time[0] + " 00:00:00"
    params.end_time = searchForm.operate_time[1] + " 23:59:59"
  }
  if (searchForm.operate_type.length > 0) {
    params.handle_type = searchForm.operate_type
  }
  tableLoading.value = true
  const [err, res]: any[] = await to(apiBackgroundFundSupervisionChannelOperationListPost(params))
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 弹窗确认
const confirmDialog = () => {
  emit("confirmDialog")
}

// 弹窗关闭
const closeDialog = () => {
  tableData.value = []
  searchForm.operate_time = [getPreDate(3, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")]
  searchForm.operate_type = []
  emit("cancelDialog")
}
// 设置数据
const setDialogData = (data: any) => {
  console.log("setDialogData", props, data)
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}
// 只能选最近一个月的数据
const disabledDate = (time: Date) => {
  const currentDate = new Date()
  const beforeDate = new Date(getPreDate(31, "YYYY-MM-DD"))
  return time > currentDate || time < beforeDate
}
// 获取操作人姓名
const getAccountName = (row: any) => {
  const accountName = row.account_name || ""
  const userName = row.user_name || ""
  return userName ? `${accountName}(${userName})` : accountName
}

// 选择时间改变
const handlerDateChange = (value: any) => {
  console.log("value", value)
  pageConfig.currentPage = 1
  getDataList()
}

// 选择询价名称改变
const handlerSelectChange = (value: any) => {
  console.log("value", value)
  pageConfig.currentPage = 1
  getDataList()
}

watch(
  () => ({
    isShow: props.isShow
  }),
  (newValue) => {
    console.log("watch dialog", newValue)
    dialogFormVisible.value = newValue.isShow
    if (newValue.isShow) {
      getDataList()
    }
  }
)
defineExpose({ setDialogData })
</script>
<style lang="scss" scoped>
.container-wrapper .table-content {
  padding: 0 !important;
  position: relative;
}

.btn-layout {
  position: absolute;
  top: -40px;
  right: 20px;
  z-index: 111;
}

.inquiry-record-detail {
  .tag-title {
    width: 120px;
    min-width: 120px;
    background: #f4f6fc;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-left: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }

  .tag-content {
    min-width: 300px;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }

  .border-bottom {
    border-bottom: 1px solid #e9ecf1;
  }
}
</style>
