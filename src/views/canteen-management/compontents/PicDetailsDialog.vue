<template>
  <el-dialog v-model="state.visible" :title="title" @close="handleClose" customClass="ps-dialog" :width="width">
    <div v-if="images" class="ps-flex">
      <div>
        <img :src="images.person_face_image" class="pic-tag" />
        <div class="m-t-10 text-center font-size-24">{{ name }}</div>
      </div>
      <img :src="images.picture_img" class="pic-tag2 m-l-10" />
      <img :src="images.picture_back_img" class="pic-tag2 m-l-10" />
    </div>
    <div v-else>
      <span style="color: #999">暂无图片</span>
    </div>
    <template #footer>
      <div class="dialog-footer" style="margin-top: 20px; text-align: right">
        <el-button :disabled="isLoading" class="ps-btn" type="primary" @click="clickCancleHandle"> 关闭 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive, ref } from "vue"
import { useVModel } from "@vueuse/core"

// 添加defineOptions以确保组件名称被正确识别
defineOptions({
  name: "PicDetailsDialog"
})

const emits = defineEmits(["close", "update:isShow"])
const props = defineProps({
  loading: Boolean,
  title: {
    type: String,
    default: "晨检图片"
  },
  width: {
    type: String,
    default: "1310px"
  },
  images: {
    type: Object,
    default() {
      return {}
    }
  },
  name: {
    type: String,
    default: ""
  },
  isShow: {
    type: Boolean,
    default: false
  }
})
// loading
const isLoading = ref(false)

const state = reactive({
  visible: useVModel(props, "isShow", emits)
})
const clickCancleHandle = () => {
  state.visible = false
}
const handleClose = () => {
  isLoading.value = false
  state.visible = false
}
</script>

<style lang="scss" scoped>
.pic-tag {
  width: 250px;
  height: 400px;
}

.pic-tag2 {
  width: 500px;
  height: 400px;
}

.font-size-24 {
  font-size: 24px;
}
.dialog-footer {
  text-align: center !important;
}
</style>
