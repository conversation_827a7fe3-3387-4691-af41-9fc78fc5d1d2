<template>
  <div class="inquiry-detail container-wrapper">
    <el-drawer
      v-model="dialogFormVisible"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div v-if="type === 'purchaseDetail'">
        <div class="flex">
          <div class="tag-title">询价名称</div>
          <div class="tag-content">{{ ruleForm.inquiry_name }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">比价截止日期</div>
          <div class="tag-content">{{ ruleForm.valid_time }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">询价物资数量</div>
          <div class="tag-content">{{ ruleForm.inquiry_num }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">询价类别</div>
          <div class="tag-content">{{ ruleForm.type }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">询价供应商</div>
          <div class="tag-content">{{ ruleForm.inquiry_warehouse }}</div>
        </div>
        <div class="flex">
          <div class="tag-title border-bottom">询价人</div>
          <div class="tag-content border-bottom">{{ ruleForm.inquiry_person }}</div>
        </div>
      </div>
      <!--询价详情列表-->
      <div class="table-content position-relative">
        <div class="m-t-20px m-b-10px" v-if="type === 'purchaseDetail'">询价物资</div>
        <div class="btn-layout" v-else>
          <el-button type="primary">导出</el-button>
          <el-button type="primary" @click="gotoPrint(tableSetting, cloneTableData, title)">打印</el-button>
        </div>
        <ps-table
          :class="[type !== 'purchaseDetail' ? 'm-t-30px' : '']"
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :maxHeight="type === 'purchaseDetail' ? '300px' : '680px'"
        >
          <ps-column :table-headers="tableSetting" />
        </ps-table>
      </div>
      <div class="dialog-footer m-t-20px">
        <!-- <el-button type="primary" class="ps-origin-btn-plain" @click="closeDialog" v-loading="confirmLoading">
          取消
        </el-button> -->
        <el-button type="primary" @click="confirmDialog" v-loading="confirmLoading"> 关闭 </el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue"
// import { confirm } from "@/utils/message"
import type { TInquiryDetailForm } from "../index.d"
import { cloneDeep } from "lodash"
import { TABLE_SETTING_INQUIRY_DETAIL, TABLE_SETTING_COMPARE_DETAIL } from "../constants"
import { getTestData, gotoPrint } from "@/utils/index"
const props = defineProps({
  title: {
    type: String,
    default: "反馈详情"
  },
  width: {
    type: String,
    default: "1048px"
  },
  isShow: {
    type: Boolean,
    default: false
  },
  type: {
    type: String, //  purchaseDetail:询价  compareDetail:比价
    default: "purchaseDetail"
  },
  orgId: {
    type: Number,
    default: -1
  },
  alreadyChooseIds: {
    type: Array,
    default: () => {
      return []
    }
  },
  // 选择节点数据
  organizationData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const dialogFormVisible = ref(false)
const confirmLoading = ref(false)
const ruleForm = reactive<TInquiryDetailForm>({
  inquiry_name: "",
  valid_time: "",
  inquiry_num: "",
  type: "",
  inquiry_warehouse: "",
  inquiry_person: "",
  supplierList: []
})

// table数据
const tableData = ref([])
const tableLoading = ref(false)
const tableSetting = ref(
  cloneDeep(props.type === "purchaseDetail" ? TABLE_SETTING_INQUIRY_DETAIL : TABLE_SETTING_COMPARE_DETAIL)
)
const cloneTableData = cloneDeep(tableData)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

// 弹窗确认
const confirmDialog = () => {
  emit("confirmDialog")
}

// 弹窗关闭
const closeDialog = () => {
  ruleForm.inquiry_name = ""
  ruleForm.valid_time = ""
  ruleForm.inquiry_num = ""
  ruleForm.type = ""
  ruleForm.inquiry_warehouse = ""
  ruleForm.inquiry_person = ""
  tableData.value = []
  emit("cancelDialog")
}
// 设置数据
const setDialogData = (data: any, type: string) => {
  console.log("setDialogData", props)

  if (data && typeof data === "object") {
    ruleForm.inquiry_name = data.inquiry_name
    ruleForm.valid_time = data.valid_time
    ruleForm.inquiry_num = data.inquiry_num
    ruleForm.type = data.type
    ruleForm.inquiry_warehouse = data.inquiry_warehouse
    ruleForm.inquiry_person = data.inquiry_person
    cloneTableData.value = cloneDeep(data.tableData || [])
    if (data.tableData) {
      pageConfig.currentPage = 1
      tableSetting.value =
        type === "purchaseDetail" ? cloneDeep(TABLE_SETTING_INQUIRY_DETAIL) : cloneDeep(TABLE_SETTING_COMPARE_DETAIL)
      getTestData(tableData, cloneTableData.value, tableLoading, pageConfig.currentPage)
      pageConfig.total = data.tableData.length || 0
    }
  }
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getTestData(tableData, cloneTableData.value, tableLoading, pageConfig.currentPage)
}
watch(
  () => ({
    isShow: props.isShow
  }),
  (newValue) => {
    console.log("watch dialog", newValue)
    dialogFormVisible.value = newValue.isShow
    if (newValue.isShow) {
      // initData()
    }
  }
)
defineExpose({ setDialogData })
</script>
<style lang="scss" scoped>
.container-wrapper .table-content {
  padding: 0 !important;
  position: relative;
}
.btn-layout {
  position: absolute;
  top: -40px;
  right: 20px;
  z-index: 111;
}
.inquiry-detail {
  .tag-title {
    width: 120px;
    min-width: 120px;
    background: #f4f6fc;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-left: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .tag-content {
    min-width: 300px;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .border-bottom {
    border-bottom: 1px solid #e9ecf1;
  }
}
</style>
