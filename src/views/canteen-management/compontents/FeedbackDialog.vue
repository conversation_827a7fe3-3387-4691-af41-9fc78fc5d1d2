<template>
  <div class="feedback container-wrapper">
    <el-drawer
      v-model="dialogFormVisible"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div>
        <div class="flex">
          <div class="tag-title">学校</div>
          <div class="tag-content">{{ ruleForm.school_name }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">反馈人</div>
          <div class="tag-content">{{ ruleForm.feedback_person }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">人员编号</div>
          <div class="tag-content">{{ ruleForm.person_no }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">手机号</div>
          <div class="tag-content">{{ ruleForm.mobile }}</div>
        </div>
      </div>
      <!--物资信息-->
      <div class="table-content">
        <div class="flex">
          <div class="tag-title">反馈时间</div>
          <div class="tag-content">{{ ruleForm.create_time }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">反馈类型</div>
          <div class="tag-content">{{ ruleForm.type }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">反馈内容</div>
          <div class="tag-content">{{ ruleForm.content }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">反馈图片</div>
          <div class="tag-content">
            <img :src="ruleForm.imgs" alt="" class="w-200px h-100px" v-if="ruleForm.imgs" />
          </div>
        </div>
        <div class="flex">
          <div class="tag-title">回复</div>
          <div class="tag-content">
            <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules">
              <el-form-item label="" prop="reply">
                <el-input
                  v-model="ruleForm.reply"
                  placeholder="请输入"
                  type="textarea"
                  maxlength="100"
                  show-word-limit
                  :rows="6"
                  clearable
                  style="width: 370px"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
      <div class="dialog-footer m-t-20px">
        <el-button type="primary" class="ps-origin-btn-plain" @click="closeDialog" v-loading="confirmLoading">
          取消
        </el-button>
        <el-button type="primary" @click="confirmDialog" v-loading="confirmLoading"> 确认 </el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue"
// import { confirm } from "@/utils/message"
import type { TFeedbackForm } from "../index.d"

const props = defineProps({
  title: {
    type: String,
    default: "反馈详情"
  },
  width: {
    type: String,
    default: "448px"
  },
  isShow: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: "detail"
  },
  orgId: {
    type: Number,
    default: -1
  },
  alreadyChooseIds: {
    type: Array,
    default: () => {
      return []
    }
  },
  // 选择节点数据
  organizationData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const ruleFormRef = ref()
const dialogFormVisible = ref(false)
const confirmLoading = ref(false)
const ruleForm = reactive<TFeedbackForm>({
  school_name: "花溪实验中学",
  feedback_person: "匿名",
  person_no: "",
  mobile: "",
  create_time: "2024-11-22",
  type: "建议",
  content: "建议开个宵夜档口吧",
  imgs: "https://www.uviewui.com/index/banner_1920x1080.png",
  reply: ""
})
// 规则
const rules = reactive({
  reply: [
    {
      required: true,
      message: "请输入回复内容",
      trigger: "blur"
    }
  ]
})

// 弹窗确认
const confirmDialog = () => {
  if (ruleFormRef.value) {
    ruleFormRef.value.validate((valid: any) => {
      if (valid) {
        // 确认回复
        console.log("ruleForm", ruleForm)
        confirmLoading.value = true
        setTimeout(() => {
          confirmLoading.value = false
          emit("confirmDialog")
        }, 200)
      } else {
        return false
      }
    })
  }
}

// 弹窗关闭
const closeDialog = () => {
  ruleForm.school_name = ""
  ruleForm.feedback_person = ""
  ruleForm.person_no = ""
  ruleForm.mobile = ""
  ruleForm.create_time = ""
  ruleForm.type = ""
  ruleForm.content = ""
  ruleForm.imgs = ""
  ruleForm.reply = ""
  emit("cancelDialog")
}
// 设置数据
const setDialogData = (data: any) => {
  if (data && typeof data === "object") {
    ruleForm.school_name = data.school_name
    ruleForm.feedback_person = data.feedback_person
    ruleForm.person_no = data.person_no
    ruleForm.mobile = data.mobile
    ruleForm.create_time = data.create_time
    ruleForm.type = data.type
    ruleForm.content = data.content
    ruleForm.imgs = data.imgs
    ruleForm.reply = data.reply
  }
}

watch(
  () => ({
    isShow: props.isShow
  }),
  (newValue) => {
    console.log("watch dialog", newValue)
    dialogFormVisible.value = newValue.isShow
    if (newValue.isShow) {
      // initData()
    }
  }
)
defineExpose({ setDialogData })
</script>
<style lang="scss" scoped>
.container-wrapper .table-content {
  padding: 0 !important;
}
.feedback {
  .tag-title {
    width: 120px;
    min-width: 120px;
    background: #f4f6fc;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-left: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .tag-content {
    min-width: 300px;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .border-bottom {
    border-bottom: 1px solid #e9ecf1;
  }
}
</style>
