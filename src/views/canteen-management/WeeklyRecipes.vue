<template>
  <div class="container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper pt-20px" ref="tableWrapperRef">
      <div class="flex flex-justify-end flex-item-center mb-10px pr-20px pl-20px">
        <div class="table-button">
          <el-button type="primary">打印</el-button>
          <el-button type="primary">导出</el-button>
          <el-button type="primary" @click="detailDrawerShow = true">食谱营养比对</el-button>
          <el-button type="primary" @click="DRIsFormDrawerShow = true">DRIs(2023)</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          ref="psTableRef"
          v-loading="tableLoading"
          :tableData="tableData"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
          :isCustom="true"
        >
          <ps-column :table-headers="tableSetting" />
        </ps-table>
      </div>
    </div>

    <!-- 详情 -->
    <el-drawer
      v-model="detailDrawerShow"
      title="食谱营养比对"
      :direction="'rtl'"
      :show-close="false"
      class="ps-drawer"
      size="50%"
    >
      <el-table :data="formTableData" v-loading="formTableLoading" border style="width: 100%; margin-top: 20px">
        <el-table-column label="食物种类">
          <el-table-column prop="foodType1" />
          <el-table-column prop="foodType2" />
        </el-table-column>
        <el-table-column label="6-8岁">
          <el-table-column prop="sixReferenceQuantity" label="参考量" />
          <el-table-column prop="sixPlannedQuantity" label="计划量" />
        </el-table-column>
        <el-table-column label="9-11岁">
          <el-table-column prop="nineReferenceQuantity" label="参考量" />
          <el-table-column prop="ninePlannedQuantity" label="计划量" />
        </el-table-column>
        <el-table-column label="12-15岁">
          <el-table-column prop="twelveReferenceQuantity" label="参考量" />
          <el-table-column prop="twelvePlannedQuantity" label="计划量" />
        </el-table-column>
        <el-table-column label="16-18岁">
          <el-table-column prop="sixteenReferenceQuantity" label="参考量" />
          <el-table-column prop="sixteenPlannedQuantity" label="计划量" />
        </el-table-column>
      </el-table>
      <div class="dialog-footer m-t-20px">
        <el-button @click="detailDrawerShow = false"> 关闭 </el-button>
      </div>
    </el-drawer>

    <!-- DRIs -->
    <el-drawer
      v-model="DRIsFormDrawerShow"
      title="DRIs(2023)"
      :direction="'rtl'"
      :show-close="false"
      class="ps-drawer"
      size="90%"
    >
      <el-table :data="DRIsFormTableData" style="width: 100%">
        <el-table-column prop="age" label="年龄/阶段" align="center" />
        <el-table-column label="能量" align="center">
          <el-table-column label="男性kcal/d" align="center">
            <el-table-column prop="man_pal1" label="PAL：轻" align="center" />
            <el-table-column prop="man_pal2" label="PAL：中" align="center" />
            <el-table-column prop="man_pal3" label="PAL：重" align="center" />
          </el-table-column>
          <el-table-column label="女性kcal/d" align="center">
            <el-table-column prop="woman_pal1" label="PAL：轻" align="center" />
            <el-table-column prop="woman_pal2" label="PAL：中" align="center" />
            <el-table-column prop="woman_pal3" label="PAL：重" align="center" />
          </el-table-column>
        </el-table-column>
        <el-table-column label="蛋白质" align="center">
          <el-table-column prop="AMDR1" label="AMDR/%E" align="center" />
        </el-table-column>
        <el-table-column label="脂肪" align="center">
          <el-table-column prop="AMDR2" label="AMDR/%E" align="center" />
        </el-table-column>
        <el-table-column label="碳水化合物" align="center">
          <el-table-column label="总碳水化合物" align="center">
            <el-table-column prop="AMDR3" label="AMDR/%E" align="center" />
          </el-table-column>
          <el-table-column label="膳食纤维" align="center">
            <el-table-column prop="AMDR4" label="AI/(g·d⁻¹)" align="center" />
          </el-table-column>
          <el-table-column label="添加糖*" align="center">
            <el-table-column prop="AMDR5" label="AMDR/%E" align="center" />
          </el-table-column>
        </el-table-column>
        <el-table-column label="水" align="center">
          <el-table-column label="适宜摄入量：mL/d" align="center">
            <el-table-column label="">
              <el-table-column prop="water_man" label="男" align="center" />
            </el-table-column>
            <el-table-column label="">
              <el-table-column prop="water_woman" label="女" align="center" />
            </el-table-column>
          </el-table-column>
        </el-table-column>
      </el-table>
      <div class="dialog-footer m-t-20px">
        <el-button @click="DRIsFormDrawerShow = false"> 关闭 </el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from "lodash"
import { ref, reactive, watch } from "vue"
import { SEARCH_FORM_WEEKLY_RECIPES, TABLE_SETTING_WEEKLY_RECIPES, WEEKLY_RECIPES_TABLE_DATA } from "./constants"
import useTableHeightHook from "@/hooks/useTableHeight"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// table数据
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_WEEKLY_RECIPES))
const loading = ref(false)
const tableData = ref<any>([])
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_WEEKLY_RECIPES)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
const getDataList = () => {
  tableData.value = cloneDeep(WEEKLY_RECIPES_TABLE_DATA)
}

// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 改变页面
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 抽屉
const detailDrawerShow = ref(false)
const DRIsFormDrawerShow = ref(false)
const formTableLoading = ref(false)
const formTableData = ref<any>([
  {
    foodType1: "谷薯类",
    foodType2: "谷薯类",
    sixReferenceQuantity: "250~300",
    sixPlannedQuantity: "275",
    nineReferenceQuantity: "300~350",
    ninePlannedQuantity: "280",
    twelveReferenceQuantity: "350~400",
    twelvePlannedQuantity: "366",
    sixteenReferenceQuantity: "350~400",
    sixteenPlannedQuantity: "389"
  },
  {
    foodType1: "蔬菜水果类",
    foodType2: "蔬菜类",
    sixReferenceQuantity: "300~350",
    sixPlannedQuantity: "345",
    nineReferenceQuantity: "350~400",
    ninePlannedQuantity: "420",
    twelveReferenceQuantity: "400~450",
    twelvePlannedQuantity: "421",
    sixteenReferenceQuantity: "450~500",
    sixteenPlannedQuantity: "485"
  },
  {
    foodType1: "蔬菜水果类",
    foodType2: "水果类",
    sixReferenceQuantity: "150~200",
    sixPlannedQuantity: "100",
    nineReferenceQuantity: "200~250",
    ninePlannedQuantity: "220",
    twelveReferenceQuantity: "250~300",
    twelvePlannedQuantity: "285",
    sixteenReferenceQuantity: "300~350",
    sixteenPlannedQuantity: "315"
  },
  {
    foodType1: "鱼禽肉蛋类",
    foodType2: "畜禽肉类",
    sixReferenceQuantity: "30~40",
    sixPlannedQuantity: "40",
    nineReferenceQuantity: "40~50",
    ninePlannedQuantity: "46",
    twelveReferenceQuantity: "50~60",
    twelvePlannedQuantity: "56",
    sixteenReferenceQuantity: "60~70",
    sixteenPlannedQuantity: "72"
  },
  {
    foodType1: "鱼禽肉蛋类",
    foodType2: "鱼虾类",
    sixReferenceQuantity: "30~40",
    sixPlannedQuantity: "36",
    nineReferenceQuantity: "40~50",
    ninePlannedQuantity: "44",
    twelveReferenceQuantity: "50~60",
    twelvePlannedQuantity: "52",
    sixteenReferenceQuantity: "60~70",
    sixteenPlannedQuantity: "52"
  },
  {
    foodType1: "鱼禽肉蛋类",
    foodType2: "蛋类",
    sixReferenceQuantity: "50",
    sixPlannedQuantity: "40",
    nineReferenceQuantity: "50",
    ninePlannedQuantity: "60",
    twelveReferenceQuantity: "75",
    twelvePlannedQuantity: "77",
    sixteenReferenceQuantity: "75",
    sixteenPlannedQuantity: "74"
  },
  {
    foodType1: "奶、大豆类及坚果类",
    foodType2: "奶及奶制品",
    sixReferenceQuantity: "300",
    sixPlannedQuantity: "200",
    nineReferenceQuantity: "300",
    ninePlannedQuantity: "310",
    twelveReferenceQuantity: "300",
    twelvePlannedQuantity: "270",
    sixteenReferenceQuantity: "300",
    sixteenPlannedQuantity: "330"
  },
  {
    foodType1: "奶、大豆类及坚果类",
    foodType2: "大豆类及其制品和坚果",
    sixReferenceQuantity: "30",
    sixPlannedQuantity: "40",
    nineReferenceQuantity: "35",
    ninePlannedQuantity: "50",
    twelveReferenceQuantity: "40",
    twelvePlannedQuantity: "43",
    sixteenReferenceQuantity: "50",
    sixteenPlannedQuantity: "59"
  },
  {
    foodType1: "植物油",
    foodType2: "植物油",
    sixReferenceQuantity: "25",
    sixPlannedQuantity: "30",
    nineReferenceQuantity: "25",
    ninePlannedQuantity: "40",
    twelveReferenceQuantity: "30",
    twelvePlannedQuantity: "33",
    sixteenReferenceQuantity: "30",
    sixteenPlannedQuantity: "33"
  },
  {
    foodType1: "盐",
    foodType2: "盐",
    sixReferenceQuantity: "＜4",
    sixPlannedQuantity: "6",
    nineReferenceQuantity: "＜4",
    ninePlannedQuantity: "6.5",
    twelveReferenceQuantity: "＜5",
    twelvePlannedQuantity: "7.2",
    sixteenReferenceQuantity: "＜5",
    sixteenPlannedQuantity: "8.6"
  }
])
const DRIsFormTableData = ref<any>([
  {
    age: "0岁~",
    man_pal1: "—",
    man_pal2: "90kcal/(kg·d)",
    man_pal3: "—",
    woman_pal1: "—",
    woman_pal2: "90kcal/(kg·d)",
    woman_pal3: "—",
    AMDR1: "—",
    AMDR2: "48(AI)",
    AMDR3: "—",
    AMDR4: "—",
    AMDR5: "—",
    water_man: "—",
    water_woman: "—"
  },
  {
    age: "0.5岁~",
    man_pal1: "—",
    man_pal2: "75kcal/(kg·d)",
    man_pal3: "—",
    woman_pal1: "—",
    woman_pal2: "75kcal/(kg·d)",
    woman_pal3: "—",
    AMDR1: "—",
    AMDR2: "40(AI)",
    AMDR3: "—",
    AMDR4: "—",
    AMDR5: "—",
    water_man: "—",
    water_woman: "—"
  },
  {
    age: "1岁~",
    man_pal1: "—",
    man_pal2: "900",
    man_pal3: "—",
    woman_pal1: "—",
    woman_pal2: "800",
    woman_pal3: "—",
    AMDR1: "—",
    AMDR2: "35(AI)",
    AMDR3: "50~65",
    AMDR4: "5~10",
    AMDR5: "—",
    water_man: "—",
    water_woman: "—"
  },
  {
    age: "2岁~",
    man_pal1: "—",
    man_pal2: "1100",
    man_pal3: "—",
    woman_pal1: "—",
    woman_pal2: "1000",
    woman_pal3: "—",
    AMDR1: "—",
    AMDR2: "35(AI)",
    AMDR3: "50~65",
    AMDR4: "5~10",
    AMDR5: "—",
    water_man: "—",
    water_woman: "—"
  },
  {
    age: "3岁~",
    man_pal1: "—",
    man_pal2: "1250",
    man_pal3: "—",
    woman_pal1: "—",
    woman_pal2: "1150",
    woman_pal3: "—",
    AMDR1: "—",
    AMDR2: "35(AI)",
    AMDR3: "50~65",
    AMDR4: "5~10",
    AMDR5: "—",
    water_man: "—",
    water_woman: "—"
  },
  {
    age: "4岁~",
    man_pal1: "—",
    man_pal2: "1300",
    man_pal3: "—",
    woman_pal1: "—",
    woman_pal2: "1250",
    woman_pal3: "—",
    AMDR1: "8~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "10~15",
    AMDR5: "<10",
    water_man: "800",
    water_woman: "800"
  },
  {
    age: "5岁~",
    man_pal1: "—",
    man_pal2: "1400",
    man_pal3: "—",
    woman_pal1: "—",
    woman_pal2: "1300",
    woman_pal3: "—",
    AMDR1: "8~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "10~15",
    AMDR5: "<10",
    water_man: "800",
    water_woman: "800"
  },
  {
    age: "6岁~",
    man_pal1: "1400",
    man_pal2: "1600",
    man_pal3: "1800",
    woman_pal1: "1300",
    woman_pal2: "1450",
    woman_pal3: "1650",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "10~15",
    AMDR5: "<10",
    water_man: "800",
    water_woman: "800"
  },
  {
    age: "7岁~",
    man_pal1: "1500",
    man_pal2: "1700",
    man_pal3: "1900",
    woman_pal1: "1350",
    woman_pal2: "1550",
    woman_pal3: "1750",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "15~20",
    AMDR5: "<10",
    water_man: "1000",
    water_woman: "1000"
  },
  {
    age: "8岁~",
    man_pal1: "1600",
    man_pal2: "1850",
    man_pal3: "2100",
    woman_pal1: "1450",
    woman_pal2: "1700",
    woman_pal3: "1900",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "15~20",
    AMDR5: "<10",
    water_man: "1000",
    water_woman: "1000"
  },
  {
    age: "9岁~",
    man_pal1: "1700",
    man_pal2: "1950",
    man_pal3: "2200",
    woman_pal1: "1550",
    woman_pal2: "1800",
    woman_pal3: "2000",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "15~20",
    AMDR5: "<10",
    water_man: "1000",
    water_woman: "1000"
  },
  {
    age: "10岁~",
    man_pal1: "1800",
    man_pal2: "2050",
    man_pal3: "2300",
    woman_pal1: "1650",
    woman_pal2: "1900",
    woman_pal3: "2100",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "15~20",
    AMDR5: "<10",
    water_man: "1000",
    water_woman: "1000"
  },
  {
    age: "11岁~",
    man_pal1: "1900",
    man_pal2: "2200",
    man_pal3: "2450",
    woman_pal1: "1750",
    woman_pal2: "2000",
    woman_pal3: "2250",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "15~20",
    AMDR5: "<10",
    water_man: "1000",
    water_woman: "1000"
  },
  {
    age: "12岁~",
    man_pal1: "2300",
    man_pal2: "2600",
    man_pal3: "2900",
    woman_pal1: "1950",
    woman_pal2: "2200",
    woman_pal3: "2450",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "20~25",
    AMDR5: "<10",
    water_man: "1300",
    water_woman: "1100"
  },
  {
    age: "15岁~",
    man_pal1: "2600",
    man_pal2: "2950",
    man_pal3: "3300",
    woman_pal1: "2100",
    woman_pal2: "2350",
    woman_pal3: "2650",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "25~30",
    AMDR5: "<10",
    water_man: "1400",
    water_woman: "1200"
  },
  {
    age: "18岁~",
    man_pal1: "2150",
    man_pal2: "2550",
    man_pal3: "3000",
    woman_pal1: "1700",
    woman_pal2: "2100",
    woman_pal3: "2450",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "25~30",
    AMDR5: "<10",
    water_man: "1700",
    water_woman: "1500"
  },
  {
    age: "30岁~",
    man_pal1: "2050",
    man_pal2: "2500",
    man_pal3: "2950",
    woman_pal1: "1700",
    woman_pal2: "2050",
    woman_pal3: "2400",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "25~30",
    AMDR5: "<10",
    water_man: "1700",
    water_woman: "1500"
  },
  {
    age: "50岁~",
    man_pal1: "1950",
    man_pal2: "2400",
    man_pal3: "2800",
    woman_pal1: "1600",
    woman_pal2: "1950",
    woman_pal3: "2300",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "25~30",
    AMDR5: "<10",
    water_man: "1700",
    water_woman: "1500"
  },
  {
    age: "65岁~",
    man_pal1: "1900",
    man_pal2: "2300",
    man_pal3: "—",
    woman_pal1: "1550",
    woman_pal2: "1850",
    woman_pal3: "—",
    AMDR1: "15~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "25~30",
    AMDR5: "<10",
    water_man: "1700",
    water_woman: "1500"
  },
  {
    age: "75岁~",
    man_pal1: "1800",
    man_pal2: "2200",
    man_pal3: "—",
    woman_pal1: "1500",
    woman_pal2: "1750",
    woman_pal3: "—",
    AMDR1: "15~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "25~30",
    AMDR5: "<10",
    water_man: "1700",
    water_woman: "1500"
  },
  {
    age: "孕早期",
    man_pal1: "—",
    man_pal2: "—",
    man_pal3: "—",
    woman_pal1: "+0",
    woman_pal2: "+0",
    woman_pal3: "+0",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "+0",
    AMDR5: "<10",
    water_man: "—",
    water_woman: "+0"
  },
  {
    age: "孕中期",
    man_pal1: "—",
    man_pal2: "—",
    man_pal3: "—",
    woman_pal1: "+250",
    woman_pal2: "+250",
    woman_pal3: "+250",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "+4",
    AMDR5: "<10",
    water_man: "—",
    water_woman: "+200"
  },
  {
    age: "孕晚期",
    man_pal1: "—",
    man_pal2: "—",
    man_pal3: "—",
    woman_pal1: "+400",
    woman_pal2: "+400",
    woman_pal3: "+400",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "+4",
    AMDR5: "<10",
    water_man: "—",
    water_woman: "+200"
  },
  {
    age: "乳母",
    man_pal1: "—",
    man_pal2: "—",
    man_pal3: "—",
    woman_pal1: "+400",
    woman_pal2: "+400",
    woman_pal3: "+400",
    AMDR1: "10~20",
    AMDR2: "20~30",
    AMDR3: "50~65",
    AMDR4: "+4",
    AMDR5: "<10",
    water_man: "—",
    water_woman: "+600"
  }
])
</script>

<style scoped></style>
