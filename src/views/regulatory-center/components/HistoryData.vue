<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue"
import { cloneDeep } from "lodash"
import dayjs from "dayjs"
import to from "await-to-js"
const psTableRef = ref()
const maxHeight = ref("400px")

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const isLoading = ref(false)
const tableData = ref([])
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

const searchFormSetting = reactive({
  select_time: {
    type: "daterange",
    label: "时间",
    value: [dayjs().subtract(1, "week").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
    clearable: false
  },
  username: {
    type: "input",
    label: "被操作账号",
    value: "",
    placeholder: "请输入",
    maxlength: 20
  }
})

const tableSetting = [
  { label: "操作时间", prop: "operation_time" },
  { label: "操作人", prop: "operator" },
  { label: "被操作问卷", prop: "questionnaire_name" },
  { label: "操作", prop: "operation_alias" },
  { label: "操作内容", prop: "context", slot: "context" }
]

const initAnswers = (form: any, arr: any) => {
  // Vue3 不再需要 Vue.set 来确保响应性
  form.answers = arr.map((question: any) => {
    let answer
    if (question.question_type === 1) {
      // 多选题返回空数组
      answer = []
    } else if (question.question_type === 3) {
      // 评价题返回空对象
      answer = {}
      question.choices.forEach((item: any, choiceIndex: number) => {
        answer[choiceIndex] = 0
      })
    } else {
      // 其他题型返回空值
      answer = ""
    }
    return answer
  })
}

import { apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireOperateLogListPost } from "@/api/supervision"
const getDataList = async () => {
  isLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireOperateLogListPost({
      start_time: searchFormSetting.select_time.value[0] + " 00:00:00",
      end_time: searchFormSetting.select_time.value[1] + " 23:59:59",
      username: searchFormSetting.username.value || undefined,
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  isLoading.value = false
  if (err) {
    return
  }
  if (res.code === 0) {
    pageConfig.total = res.data.count
    tableData.value = res.data.results.map((item: any) => {
      if (item.operation === "modify") {
        Object.assign(item, {
          afterForm: { answers: [] },
          beforeForm: { answers: [] }
        })
        if (Object.keys(item.context).length) {
          initAnswers(item.beforeForm, item.context.before)
          initAnswers(item.afterForm, item.context.after)
        }
      }
      console.log("item", item)
      return item
    })
  }
}

// 确保多选题的值始终是数组
const handleChange = (value: any, index: number, form: any) => {
  if (Array.isArray(value)) {
    form.answers[index] = value
  }
}

const searchHandle = () => {
  pageConfig.currentPage = 1
  getDataList()
}

// 翻页
const handleSizeChange = (val: number) => {
  pageConfig.pageSize = val
  getDataList()
}
const handleCurrentChange = (val: number) => {
  pageConfig.currentPage = val
  getDataList()
}

watch(
  () => props.show,
  (newVal) => {
    if (newVal) {
      getDataList()
    }
  },
  { immediate: true }
)

onMounted(() => {
  if (props.show) {
    getDataList()
  }
})

const searchRef = ref<any>()
const resetField = () => {
  if (searchRef.value) {
    searchRef.value.handlerReset()
  }
  tableData.value = []
}
// 向父组件暴露验证方法
defineExpose({
  resetField
})
</script>

<template>
  <div class="container-wrapper">
    <div>
      <search-form
        ref="searchRef"
        :form-setting="searchFormSetting"
        @change-search="searchHandle"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="isLoading"
          :show-pagination="true"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #context="{ row }">
              <el-popover placement="left" :width="1000" trigger="click" v-if="row.operation === 'modify'">
                <template #reference>
                  <el-button link size="small" type="primary">详情</el-button>
                </template>
                <div class="flex h-600px">
                  <div class="flex-1 h-100% overflow-auto">
                    <div class="pos-sticky pos-top-none w-100% bg-white">修改前：</div>
                    <el-form ref="previewForm" :model="row.beforeForm" label-position="top">
                      <!-- 问题列表 -->
                      <div class="preview-content">
                        <div v-for="(question, index) in row.context.before" :key="index" class="question-item">
                          <el-form-item
                            :label="`${index + 1}. ${question.caption}`"
                            :prop="`answers.${index}`"
                            :rules="{ required: question.required, message: '此题为必答题', trigger: 'change' }"
                          >
                            <!-- 单选题 -->
                            <template v-if="question.question_type === 0">
                              <el-radio-group v-model="row.beforeForm.answers[index]">
                                <div class="flex flex-col">
                                  <div v-for="(choice, choiceIndex) in question.choices" :key="choiceIndex">
                                    <el-radio
                                      :label="choiceIndex"
                                      disabled
                                      :style="choice.other_content ? {} : { display: 'inline-block' }"
                                    >
                                      {{ choice.description }}
                                    </el-radio>
                                    <div class="m-l-20px w-350px">
                                      <el-input
                                        v-if="choice.other_content"
                                        type="textarea"
                                        :autosize="{ minRows: 3 }"
                                        :placeholder="choice.other_content"
                                        disabled
                                      />
                                    </div>
                                  </div>
                                </div>
                              </el-radio-group>
                            </template>

                            <!-- 多选题 -->
                            <template v-if="question.question_type === 1">
                              <el-checkbox-group
                                v-model="row.beforeForm.answers[index]"
                                :min="question.least_choose_count || 0"
                                @change="(val) => handleChange(val, index, row.beforeForm)"
                              >
                                <div class="flex flex-col">
                                  <div v-for="(choice, choiceIndex) in question.choices" :key="choiceIndex">
                                    <el-checkbox
                                      :label="choiceIndex"
                                      disabled
                                      :style="choice.other_content ? {} : { display: 'inline-block' }"
                                    >
                                      {{ choice.description }}
                                    </el-checkbox>
                                    <div class="m-l-20px w-350px">
                                      <el-input
                                        v-if="choice.other_content"
                                        type="textarea"
                                        :autosize="{ minRows: 3 }"
                                        :placeholder="choice.other_content"
                                        disabled
                                      />
                                    </div>
                                  </div>
                                </div>
                              </el-checkbox-group>
                            </template>

                            <!-- 评分题 -->
                            <template v-if="question.question_type === 2">
                              <div class="mark-topic-content">
                                <div class="mark-topic-content-item">
                                  <div class="mark-topic-content-item-top">
                                    <div class="point">1</div>
                                    <div class="point">{{ question.top_score }}</div>
                                  </div>
                                  <div class="mark-topic-content-item-bottom">
                                    <div
                                      v-for="item in question.top_score"
                                      :key="item"
                                      :class="['mark', 'selectScore']"
                                    >
                                      {{ item }}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </template>

                            <!-- 评价题 -->
                            <template v-if="question.question_type === 3">
                              <div
                                v-for="(choice, choiceIndex) in question.choices"
                                :key="choiceIndex"
                                class="evaluate-item"
                              >
                                <span class="evaluate-label">{{ choice.description }}</span>
                                <el-rate
                                  v-model="row.beforeForm.answers[index][choiceIndex]"
                                  :max="question.top_score"
                                  disabled
                                />
                              </div>
                            </template>

                            <!-- 填空题 -->
                            <template v-if="question.question_type === 4">
                              <el-input
                                v-model="row.beforeForm.answers[index]"
                                type="textarea"
                                :autosize="{ minRows: 3 }"
                                placeholder="请输入您的答案"
                                disabled
                              />
                            </template>

                            <!-- 文件上传 -->
                            <template v-if="question.question_type === 6">
                              <el-upload
                                class="upload-demo"
                                action="https://jsonplaceholder.typicode.com/posts/"
                                multiple
                                disabled
                              >
                                <div class="upload-demo-button flex flex-items-center flex-justify-center">
                                  <div class="flex flex-col flex-items-center">
                                    <el-icon size="28" color="#909399"><UploadFilled /></el-icon>
                                    <div class="el-upload__tip">[不超过20M]</div>
                                  </div>
                                </div>
                              </el-upload>
                            </template>

                            <!-- 图片上传 -->
                            <template v-if="question.question_type === 5">
                              <el-upload
                                action="https://jsonplaceholder.typicode.com/posts/"
                                list-type="picture-card"
                                disabled
                              >
                                <el-icon><plus /></el-icon>
                              </el-upload>
                            </template>
                          </el-form-item>
                        </div>
                      </div>
                    </el-form>
                  </div>
                  <div class="flex-1 h-100% overflow-auto">
                    <div class="pos-sticky pos-top-none w-100% bg-white">修改后：</div>
                    <el-form ref="previewForm" :model="row.beforeForm" label-position="top">
                      <!-- 问题列表 -->
                      <div class="preview-content">
                        <div v-for="(question, index) in row.context.after" :key="index" class="question-item">
                          <el-form-item
                            :label="`${index + 1}. ${question.caption}`"
                            :prop="`answers.${index}`"
                            :rules="{ required: question.required, message: '此题为必答题', trigger: 'change' }"
                          >
                            <!-- 单选题 -->
                            <template v-if="question.question_type === 0">
                              <el-radio-group v-model="row.afterForm.answers[index]">
                                <div class="flex flex-col">
                                  <div v-for="(choice, choiceIndex) in question.choices" :key="choiceIndex">
                                    <el-radio
                                      :label="choiceIndex"
                                      disabled
                                      :style="choice.other_content ? {} : { display: 'inline-block' }"
                                    >
                                      {{ choice.description }}
                                    </el-radio>
                                    <div class="m-l-20px w-350px">
                                      <el-input
                                        v-if="choice.other_content"
                                        type="textarea"
                                        :autosize="{ minRows: 3 }"
                                        :placeholder="choice.other_content"
                                        disabled
                                      />
                                    </div>
                                  </div>
                                </div>
                              </el-radio-group>
                            </template>

                            <!-- 多选题 -->
                            <template v-if="question.question_type === 1">
                              <el-checkbox-group
                                v-model="row.afterForm.answers[index]"
                                :min="question.least_choose_count || 0"
                                @change="(val) => handleChange(val, index, row.afterForm)"
                              >
                                <div class="flex flex-col">
                                  <div v-for="(choice, choiceIndex) in question.choices" :key="choiceIndex">
                                    <el-checkbox
                                      :label="choiceIndex"
                                      disabled
                                      :style="choice.other_content ? {} : { display: 'inline-block' }"
                                    >
                                      {{ choice.description }}
                                    </el-checkbox>
                                    <div class="m-l-20px w-350px">
                                      <el-input
                                        v-if="choice.other_content"
                                        type="textarea"
                                        :autosize="{ minRows: 3 }"
                                        :placeholder="choice.other_content"
                                        disabled
                                      />
                                    </div>
                                  </div>
                                </div>
                              </el-checkbox-group>
                            </template>

                            <!-- 评分题 -->
                            <template v-if="question.question_type === 2">
                              <div class="mark-topic-content">
                                <div class="mark-topic-content-item">
                                  <div class="mark-topic-content-item-top">
                                    <div class="point">1</div>
                                    <div class="point">{{ question.top_score }}</div>
                                  </div>
                                  <div class="mark-topic-content-item-bottom">
                                    <div
                                      v-for="item in question.top_score"
                                      :key="item"
                                      :class="['mark', 'selectScore']"
                                    >
                                      {{ item }}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </template>

                            <!-- 评价题 -->
                            <template v-if="question.question_type === 3">
                              <div
                                v-for="(choice, choiceIndex) in question.choices"
                                :key="choiceIndex"
                                class="evaluate-item"
                              >
                                <span class="evaluate-label">{{ choice.description }}</span>
                                <el-rate
                                  v-model="row.afterForm.answers[index][choiceIndex]"
                                  :max="question.top_score"
                                  disabled
                                />
                              </div>
                            </template>

                            <!-- 填空题 -->
                            <template v-if="question.question_type === 4">
                              <el-input
                                v-model="row.afterForm.answers[index]"
                                type="textarea"
                                :autosize="{ minRows: 3 }"
                                placeholder="请输入您的答案"
                                disabled
                              />
                            </template>

                            <!-- 文件上传 -->
                            <template v-if="question.question_type === 6">
                              <el-upload
                                class="upload-demo"
                                action="https://jsonplaceholder.typicode.com/posts/"
                                multiple
                                disabled
                              >
                                <div class="upload-demo-button flex flex-items-center flex-justify-center">
                                  <div class="flex flex-col flex-items-center">
                                    <el-icon size="28" color="#909399"><UploadFilled /></el-icon>
                                    <div class="el-upload__tip">[不超过20M]</div>
                                  </div>
                                </div>
                              </el-upload>
                            </template>

                            <!-- 图片上传 -->
                            <template v-if="question.question_type === 5">
                              <el-upload
                                action="https://jsonplaceholder.typicode.com/posts/"
                                list-type="picture-card"
                                disabled
                              >
                                <el-icon><plus /></el-icon>
                              </el-upload>
                            </template>
                          </el-form-item>
                        </div>
                      </div>
                    </el-form>
                  </div>
                </div>
              </el-popover>
              <div v-else>{{ "--" }}</div>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// 变量定义
$primary-color: #409eff;
$border-color: #ebeef5;
$text-primary: #303133;
$spacing-base: 8px;

@mixin section-padding {
  padding: $spacing-base * 3 $spacing-base * 2.5;
}

@mixin hover-effect {
  transition: all 0.3s ease;
  &:hover {
    border-color: $primary-color;
    box-shadow: 0 0 8px rgba($primary-color, 0.1);
  }
}

// 重置最后一个元素的边距
@mixin reset-last-margin {
  &:last-child {
    margin-bottom: 0;
  }
}

.preview-content {
  @include section-padding;

  .question-item {
    height: 100%;
    margin-bottom: $spacing-base * 3;
    padding: $spacing-base * 2.5;
    border: 1px solid $border-color;
    border-radius: 4px;
    @include hover-effect;
    @include reset-last-margin;

    :deep(.el-form-item__label) {
      font-size: 16px;
      font-weight: 500;
      color: $text-primary;
      margin-bottom: $spacing-base * 2;
    }
  }
}
.evaluate-item {
  flex-direction: column;
  align-items: flex-start;

  .evaluate-label {
    width: 100%;
    margin-bottom: $spacing-base;
  }
}
.mark-topic {
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  &-content {
    margin-left: 23px;
    margin-top: 10px;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    &-item {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      border: 1px solid #e7ecf2;
      border-radius: 6px;
      padding: 5px;
      &-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 5px;
        border-bottom: 1px solid #e7ecf2;
        .point {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      &-bottom {
        margin-top: 5px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .mark {
          width: 32px;
          height: 32px;
          margin: 0 5px;
          border: 1px solid #e7ecf2;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .selectScore {
          background-color: #f5f7fa;
          color: #c0c4cc;
        }
      }
    }
  }
}
</style>
