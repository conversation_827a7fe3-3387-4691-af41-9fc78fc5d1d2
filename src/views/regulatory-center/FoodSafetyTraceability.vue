<template>
  <div class="scence-manage container-wrapper">
    <el-radio-group v-model="data.tableType" @change="changeTableType" class="m-b-20px">
      <el-radio-button
        label="菜品溯源"
        value="food"
        v-permission="['background_fund_supervision.supervision_food_safety_source.food_source_list']"
      />
      <el-radio-button
        label="就餐溯源"
        value="meal"
        v-permission="['background_fund_supervision.supervision_food_safety_source.dine_source_list']"
      />
    </el-radio-group>
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting.value"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
        v-permission="['supervision_center']"
      />
    </div>
    <div class="table-wrapper pt-20px" ref="tableWrapperRef">
      <div class="table-content">
        <ps-table
          :tableData="tableData.value"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="data.tableSetting">
            <template #food_name="{ row }">
              <el-tooltip effect="dark" :content="row.food_name" placement="top-start">
                <div class="w-200px text-ellipsis">{{ row.food_name }}</div>
              </el-tooltip>
            </template>
            <template #featureNameList="{ row }">
              {{ getFeatureName(row.feature_name_list) }}
            </template>
            <template #food_ingredient="{ row }">
              <el-tooltip placement="top" effect="dark">
                <template #content>
                  <div class="w-300px">
                    <span v-for="(item, index) in row.food_ingredient" :key="index">
                      {{ item.name }}{{ index < row.food_ingredient.length - 1 ? "、" : "" }}
                    </span>
                  </div>
                </template>
                <div class="w-150px text-ellipsis">
                  <span v-for="(item, index) in row.food_ingredient" :key="index">
                    {{ item.name }}{{ index < row.food_ingredient.length - 1 ? "、" : "" }}
                  </span>
                </div>
              </el-tooltip>
            </template>
            <template #total_count="{ row }"> {{ row.total_count }}份 </template>
            <template #operation="{ row }">
              <el-button plain link size="small" @click="showDetail(row)" type="primary"> 查看 </el-button>
            </template>
            <template #operationNew="{ row }">
              <el-button
                plain
                link
                size="small"
                @click="goToExport(row)"
                type="primary"
                v-permission="['background_fund_supervision.supervision_food_safety_source.download_order']"
              >
                下载
              </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>

    <!-- 抽屉 -->
    <el-drawer
      v-model="drawerShow"
      title="溯源详情"
      :direction="'rtl'"
      :show-close="false"
      class="ps-drawer"
      size="50%"
    >
      <el-form :model="form" label-width="auto" class="pt-20px">
        <el-form-item label="组织名称：">
          <div>{{ form.org_name }}</div>
        </el-form-item>
        <el-form-item label="日期：">
          <div>{{ form.date }}</div>
        </el-form-item>
        <el-form-item label="餐段：">
          <div>{{ form.meal_period }}</div>
        </el-form-item>
        <el-form-item label="菜品名称：">
          <div>{{ form.dish_name }}</div>
        </el-form-item>
        <el-form-item label="留样信息：">
          <ps-table
            :tableData="form.sample_retention_information"
            ref="psTableRef"
            :show-pagination="false"
            style="width: 100%"
          >
            <ps-column :table-headers="sampleRetentionInformationTableSetting">
              <template #foodWeight="{ row }"> {{ row.food_weight }}g </template>
              <template #images="{ row }">
                <el-button type="text" size="small" class="ps-text" @click="handlerShowPhoto(row, 'food_image')"
                  >查看</el-button
                >
              </template>
            </ps-column>
          </ps-table>
        </el-form-item>
        <el-form-item label="值班人员：">
          <div class="flex flex-justify-end w-full m-b-20">
            <el-button type="primary" size="small" @click="gotoPath('MorningInspectionRecord')">晨检信息</el-button>
          </div>
          <div class="drawer-table-box w-full">
            <el-table
              v-for="item in 2"
              :key="item"
              :data="form.officer_on_duty[`arr${item}`]"
              ref="psTableRef"
              style="width: 100%"
              stripe
              header-row-class-name="ps-table-header-row"
              empty-text="暂无数据，请查询"
            >
              <el-table-column prop="name" label="姓名" align="center" :show-overflow-tooltip="true" />
              <el-table-column prop="job_title" label="岗位" align="center" :show-overflow-tooltip="true" />
              <el-table-column prop="image" label="健康证" align="center">
                <template #default="{ row }">
                  <el-button type="text" size="small" class="ps-text" @click="handlerShowPhoto(row, 'health_image')"
                    >查看</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
        <el-form-item label="订单溯源：">
          <el-button
            type="primary"
            size="small"
            @click="goToExport"
            v-permission="['background_fund_supervision.supervision_food_safety_source.export_order_source']"
            >导出</el-button
          >
        </el-form-item>
        <el-form-item label="食材溯源：">
          <div class="flex flex-col source-data">
            <!-- 循环食材 -->
            <div
              v-for="(item, index) in form.traceability_path"
              :class="['source-data-food', index < form.traceability_path.length - 1 ? 'm-b-20' : '']"
              :key="index"
            >
              <div>关联食材（{{ index + 1 }}）：{{ item.ingredient_name }}</div>
              <div class="source-data-food-content">
                <div
                  v-for="(item1, index1) in item.detail"
                  :key="index1"
                  :class="['source-data-food-content-item', index1 < item.detail.length - 1 ? 'm-b-20' : '']"
                >
                  <div
                    :class="[
                      'flex',
                      'flex-justify-between',
                      'flex-align-center',
                      'source-data-food-content-item-head',
                      'm-b-20'
                    ]"
                  >
                    <div>出库单：{{ item1.exist_trade_no }}</div>
                  </div>
                  <div
                    v-for="(item2, index2) in item1.detail"
                    :key="index2"
                    :class="['source-data-food-content-item-detail', index2 < item1.detail.length - 1 ? 'm-b-20' : '']"
                  >
                    <div>供应商{{ index2 + 1 }}：{{ item2.supplier_manage_name }}</div>
                    <div class="flex flex-align-center">
                      <div>入库单：{{ item2.entry_trade_no }}</div>
                    </div>
                    <div class="flex flex-justify-between flex-align-center" v-if="item2.purchase_order">
                      <div>采购单：{{ item2.purchase_order }}</div>
                      <el-button type="text" size="small" @click="gotoPath('PurchaseOrder')">查看</el-button>
                    </div>
                    <div class="flex flex-justify-between flex-align-center" v-if="item2.delivery_order">
                      <div>配送单：{{ item2.delivery_order }}</div>
                      <el-button type="text" size="small" @click="gotoPath('DeliveryOrder')">查看</el-button>
                    </div>
                    <div class="flex flex-justify-between flex-align-center" v-if="item2.receipt_order">
                      <div>收货单：{{ item2.receipt_order }}</div>
                      <el-button type="text" size="small" @click="gotoPath('ReceiptOrder')">查看</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="dialog-footer m-t-20px">
        <el-button @click="drawerShow = false"> 关 闭 </el-button>
      </div>
    </el-drawer>

    <el-drawer
      v-model="repastDrawerShow"
      title="就餐数据"
      :direction="'rtl'"
      :show-close="false"
      class="ps-drawer"
      size="55%"
    >
      <div class="flex flex-col" ref="tableWrapperRef">
        <div class="flex flex-justify-end mb-10px">
          <div class="table-button">
            <el-button type="primary" @click="goToExport">导出</el-button>
            <el-button type="primary" @click="goToExport">打印</el-button>
          </div>
        </div>
        <div class="mb-20px">
          <ps-table :tableData="repastDrawerData" ref="psTableRef" :show-pagination="false">
            <ps-column :table-headers="repastDrawerTableSetting" />
          </ps-table>
        </div>
      </div>
      <div class="dialog-footer m-t-20px">
        <el-button @click="repastDrawerShow = false"> 取消 </el-button>
        <el-button type="primary" @click="repastDrawerShow = false"> 保存 </el-button>
      </div>
    </el-drawer>

    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import {
  SEARCH_FORM_SETTING_FOOD_SAFETY,
  TABLE_SETTING_FOOD_SAFETY,
  SEARCH_FORM_SETTING_MEAL_SAFETY,
  TABLE_SETTING_MEAL_SAFETY
} from "./constants"
import {
  apiFoodSafetySourceFoodSourceListPost,
  apiBackgroundFundSupervisionSupervisionFoodSafetySourceGetFoodIngredientSourcePost,
  apiBackgroundFundSupervisionSupervisionFoodSafetySourceGetReservedSampleInfoPost,
  apiBackgroundFundSupervisionSupervisionFoodSafetySourceGetSchedulePersonPost,
  apiBackgroundFundSupervisionSupervisionFoodSafetySourceExportOrderSourcePost,
  apiBackgroundFundSupervisionSupervisionFoodSafetySourceDineSourceListPost,
  apiBackgroundFundSupervisionSupervisionFoodSafetySourceDownloadOrderPost
} from "@/api/supervision"
import { ElMessage } from "element-plus"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// table数据
const data = reactive({
  tableType: "food",
  tableSetting: cloneDeep(TABLE_SETTING_FOOD_SAFETY)
})

interface MyObject {
  [key: string]: any // 允许任意字符串作为键，值类型为 string
}
const searchFormSetting = reactive<MyObject>(cloneDeep(SEARCH_FORM_SETTING_FOOD_SAFETY))
const tableData = reactive<any>({})

const loading = ref(false)
const tableLoading = ref(false)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 导出
const importType = "FoodSafetyTraceabilityExport"

const formatQueryParams = (data: any) => {
  console.log("data1111", data)
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value && value !== "全部") {
          params[key] = data[key].value
        } else {
          params[key] = undefined
        }
      } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}

// 搜索
const changeSearch = async () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}

// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  console.log(searchFormSetting.value, 999)
  const [err, res] = await to(
    data.tableType === "food"
      ? apiFoodSafetySourceFoodSourceListPost({
          ...formatQueryParams(searchFormSetting.value),
          page: pageConfig.currentPage,
          page_size: pageConfig.pageSize
        })
      : apiBackgroundFundSupervisionSupervisionFoodSafetySourceDineSourceListPost({
          ...formatQueryParams(searchFormSetting.value),
          page: pageConfig.currentPage,
          page_size: pageConfig.pageSize
        })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

const changeTableType = (e: any) => {
  if (e === "food") {
    searchFormSetting.value = cloneDeep(SEARCH_FORM_SETTING_FOOD_SAFETY)
    data.tableSetting = cloneDeep(TABLE_SETTING_FOOD_SAFETY)
  } else if (e === "meal") {
    searchFormSetting.value = cloneDeep(SEARCH_FORM_SETTING_MEAL_SAFETY)
    data.tableSetting = cloneDeep(TABLE_SETTING_MEAL_SAFETY)
  }
  getDataList()
}
// 查看照片
const handlerShowPhoto = (row: any, type: string) => {
  console.log(row)
  imageList.value = [row[type]]
  imageVisible.value = true
}

// 初始化判断当前应该显示哪个页面
import { useUserStoreHook } from "@/store/modules/user"
const { roles } = useUserStoreHook()
const setTabType = () => {
  let flag: any[] = []
  const permissionArr: any[] = [
    {
      permission: "background_fund_supervision.supervision_food_safety_source.food_source_list",
      value: "food"
    },
    {
      permission: "background_fund_supervision.supervision_food_safety_source.dine_source_list",
      value: "meal"
    }
  ]
  permissionArr.forEach((item: any) => {
    if (roles.includes(item.permission)) {
      flag.push(item)
    }
  })
  if (flag.length && flag.length === 1) {
    data.tableType = flag[0].value
  } else if (flag.length && flag.length === 2) {
    data.tableType = "food"
  } else {
    data.tableType = ""
  }
  console.log("data.tableType", data.tableType)
}

onMounted(() => {
  // getFeatureList()
  setTabType()
  changeTableType(data.tableType)
  getDataList()
})
// 获取功能权限列表
// const getFeatureList = async () => {
//   loading.value = true
//   const [err, res]: any[] = await to(apiDepartmentListPost({}))
//   loading.value = false
//   if (err) {
//     return
//   }
//   if (res && res.code === 0) {
//     let list: Array<any> = res.data || []
//     if (list) {
//       list = getValueKeyList(list)
//     }
//     // searchFormSetting.value.feature.dataList = cloneDeep(list)
//     console.log("list", list)
//   }
// }
// 根据列表转换成label跟value 模式
const getValueKeyList = (list: Array<any>): Array<any> => {
  return list.map((item) => {
    item.label = item.verbose_name
    item.value = item.key
    if (item.level && item.level >= 1) {
      item.children = null
    }
    if (item.children && item.children.length > 0) {
      item.children = getValueKeyList(item.children)
    }
    return item
  })
}
// 获取功能名称
const getFeatureName = (list: Array<string>) => {
  if (!list || !Array.isArray(list) || list.length === 0) {
    return ""
  }
  return list.join(";")
}

// 抽屉相关
const drawerShow = ref<any>(false)
const sampleRetentionInformationTableSetting = [
  { label: "留样时间", prop: "reserved_time" },
  { label: "所属组织", prop: "org_name" },
  { label: "餐段", prop: "meal_type_alias" },
  { label: "菜品名称", prop: "food_name" },
  { label: "留样重量", prop: "food_weight", slot: "foodWeight" },
  { label: "留样图片", prop: "food_image", slot: "images" },
  { label: "留样员", prop: "sample_person" }
]
const form = ref<any>({
  org_name: "",
  date: "",
  meal_period: "",
  dish_name: "",
  officer_on_duty: {} as any,
  sample_retention_information: [] as any[],
  traceability_path: [] as any[]
})

const repastDrawerShow = ref(false)
const repastDrawerData = ref<any>([])
const repastDrawerTableSetting = [
  {
    label: "学校",
    prop: "school",
    width: "130px"
  },
  {
    label: "分组",
    prop: "class",
    width: "130px"
  },
  {
    label: "姓名",
    prop: "name",
    width: "100px"
  },
  {
    label: "联系电话",
    prop: "phone",
    width: "130px"
  },
  {
    label: "下单时间",
    prop: "time"
  },
  {
    label: "消费订单",
    prop: "order_no"
  }
]

const groupBy = (data: any, key: string) => {
  // 使用
  let obj = data.reduce((accumulator: any, current: any) => {
    const keyValue = current[key]

    // 如果 accumulator 中已经有该 ingredient_id，则将其添加到对应的数组中
    if (accumulator[keyValue]) {
      accumulator[keyValue].push(current)
    } else {
      // 否则创建一个新的数组
      accumulator[keyValue] = [current]
    }
    return accumulator
  }, {})
  return obj
}
// 获取食材溯源
const getSourceData = async (data: any): Promise<boolean> => {
  let params = {
    org_id: data.org_id,
    meal_type: data.meal_type,
    date: data.pay_date,
    ingredient_list: data.ingredient_id
  }
  const [err, res]: [any, any] = await to(
    apiBackgroundFundSupervisionSupervisionFoodSafetySourceGetFoodIngredientSourcePost(params)
  )
  if (err) {
    ElMessage.error(err.msg)
    return false
  }
  if (res.code === 0) {
    console.log("成功了")
    let resultData = res.data || []
    // 先按食材分类
    let resArr = Object.entries(groupBy(resultData, "ingredient_id")).map(([id, items]: [string, any]) => ({
      ingredient_id: items[0].ingredient_id,
      ingredient_name: items[0].ingredient_name,
      detail: items
    }))
    // 再将每个食材按照出库单分类
    resArr.forEach((item) => {
      let newItem = Object.entries(groupBy(item.detail, "exist_trade_no")).map(([id, items]: [string, any]) => ({
        exist_trade_no: id,
        detail: items
      }))
      item.detail = cloneDeep(newItem)
    })
    form.value.traceability_path = cloneDeep(resArr)
    return true
  } else {
    ElMessage.error(res.msg)
    return false
  }
}

// 获取留样信息
const getSampleData = async (data: any): Promise<boolean> => {
  let params = {
    org_id: data.org_id,
    food_id: data.food_id,
    meal_type: data.meal_type,
    reserved_date: data.pay_date
  }
  const [err, res]: [any, any] = await to(
    apiBackgroundFundSupervisionSupervisionFoodSafetySourceGetReservedSampleInfoPost(params)
  )
  if (err) {
    ElMessage.error(err.msg)
    return false
  }
  if (res.code === 0) {
    console.log("成功了")
    form.value.sample_retention_information = cloneDeep(res.data)
    return true
  } else {
    ElMessage.error(res.msg)
    return false
  }
}

// 获取值班人员
const getPersonList = async (data: any): Promise<boolean> => {
  let params = {
    use_date: data.pay_date,
    org_id: data.org_id
  }
  const [err, res]: [any, any] = await to(
    apiBackgroundFundSupervisionSupervisionFoodSafetySourceGetSchedulePersonPost(params)
  )
  if (err) {
    ElMessage.error(err.msg)
    return false
  }
  if (res.code === 0) {
    console.log("成功了")
    let obj = {
      arr1: [] as any[],
      arr2: [] as any[]
    }
    if (res.data.job_person && res.data.job_person.length > 0) {
      res.data.job_person.forEach((item: any, index: number) => {
        if (index % 2 === 0) {
          obj.arr1.push(item)
        } else {
          obj.arr2.push(item)
        }
      })
    }
    form.value.officer_on_duty = { ...obj }
    return true
  } else {
    ElMessage.error(res.msg)
    return false
  }
}

const rowData = ref<any>({})
const showDetail = async (row: any) => {
  console.log("row--", row)
  rowData.value = cloneDeep(row)
  console.log("rowData--", rowData.value)
  if (data.tableType === "food") {
    tableLoading.value = true
    form.value.org_name = row.org_name
    form.value.date = row.pay_date
    form.value.meal_period = row.meal_type_alias
    form.value.dish_name = row.food_name
    const flag1 = await getSourceData(row)
    const flag2 = await getSampleData(row)
    const flag3 = await getPersonList(row)
    console.log("flag1", flag1, flag2, flag3)
    if (flag1 && flag2 && flag3) {
      tableLoading.value = false
      drawerShow.value = true
    }
  } else {
    repastDrawerData.value = row.detail
    repastDrawerShow.value = true
  }
}
// 导出
import { exportHandle } from "@/utils/exportExcel"
const goToExport = (item?: any) => {
  const option = {
    type: importType,
    api:
      data.tableType === "food"
        ? apiBackgroundFundSupervisionSupervisionFoodSafetySourceExportOrderSourcePost
        : apiBackgroundFundSupervisionSupervisionFoodSafetySourceDownloadOrderPost,
    params: {
      trade_no_list: data.tableType === "food" ? rowData.value.trade_no : item.trade_no_list
    }
  }
  console.log("option", option)
  exportHandle(option)
}

import useGoToPage from "@/hooks/useGoToPage"
const path = "/canteen_management/morning_inspection_record"
const { goToPage } = useGoToPage()
const gotoPath = (name: string) => {
  switch (name) {
    case "MorningInspectionRecord":
      goToPage({ path: "/canteen_management/morning_inspection_record" })
      break
    case "PurchaseOrder":
      goToPage({ path: "/document_management/purchase_order" })
      break
    case "DeliveryOrder":
      goToPage({ path: "/document_management/delivery_order" })
      break
    case "ReceiptOrder":
      goToPage({ path: "/document_management/receipt_order" })
      break
  }
}
</script>
<style lang="scss">
.scence-manage {
  padding: 0 20px;

  .el-popper {
    max-width: 300px;
  }
}
.drawer-table-box {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-column-gap: 10px;
}

.source-data {
  width: 500px;
  &-food {
    border: 1px solid #ebeef5;
    padding: 20px;
    border-radius: 8px;
    &-content {
      &-item {
        background-color: #f2f2f2;
        padding: 20px;
        border-radius: 8px;
        &-head {
          background-color: #d7d7d7;
          padding: 20px;
          border-radius: 8px;
        }
        &-detail {
          background-color: #fff;
          padding: 20px;
          border-radius: 8px;
        }
      }
    }
  }
}
</style>
