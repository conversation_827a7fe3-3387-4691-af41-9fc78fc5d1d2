<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from "vue"
import dayjs from "dayjs"
import ChannelTree from "@/components/ChannelTree/index.vue"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import Questionnaire from "@/components/Questionnaire/index.vue"
import useTableHeightHook from "@/hooks/useTableHeight"
import { ElMessage } from "element-plus"
const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

const searchFormSetting = ref<any>({
  selecttime: {
    type: "daterange",
    filterable: true,
    clearable: false,
    label: "创建时间",
    labelWidth: "68px",
    value: [dayjs().subtract(1, "year").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  questionnaire_type: {
    type: "select",
    label: "类型",
    value: "全部",
    multiple: false,
    clearable: false,
    collapseTags: true,
    dataList: [
      {
        label: "全部",
        value: ""
      },
      {
        label: "普通问卷",
        value: "common"
      },
      {
        label: "供应商专项检查",
        value: "supplier"
      }
    ]
  },
  issuing_channel_ids: {
    type: "select",
    label: "下发渠道",
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    dataList: []
  },
  issuing_channel_org_ids: {
    type: "select",
    label: "下发组织",
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    dataList: []
  },
  visible_user: {
    type: "select",
    value: [],
    clearable: true,
    label: "可见用户",
    multiple: true,
    collapseTags: true,
    dataList: [
      {
        label: "内部用户",
        value: 0
      },
      {
        label: "商户管理员",
        value: 1
      },
      {
        label: "其他",
        value: 2
      }
    ]
  },
  name: {
    type: "input",
    label: "问卷名称",
    labelWidth: "100px",
    value: "",
    placeholder: "请输入问卷名称",
    clearable: true,
    maxlength: 20
  },
  enable: {
    type: "select",
    value: "",
    clearable: true,
    label: "状态",
    multiple: false,
    collapseTags: true,
    dataList: [
      {
        label: "全部",
        value: ""
      },
      {
        label: "已发布",
        value: 1
      },
      {
        label: "未发布",
        value: 0
      }
    ]
  }
})
const searchFormRef = ref<any>()

const loading = ref(false)
const tableSetting = [
  { label: "创建时间", prop: "create_time" },
  { label: "问卷名称", prop: "name" },
  { label: "类型", prop: "questionnaire_type_alias" },
  { label: "下发渠道", prop: "issuing_channel_alias" },
  { label: "下发组织", prop: "issuing_channel_organization_alias" },
  { label: "可见用户", prop: "visible_user_alias" },
  { label: "填写限制", prop: "write_limit_alias" },
  { label: "状态", prop: "enable", slot: "enable" },
  { label: "填写份数", prop: "answered_num" },
  { label: "操作", prop: "operation", slot: "operation", width: "250px" }
]
const tableLoading = ref(false)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

// 获取列表
import { apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireListPost } from "@/api/supervision"
import to from "await-to-js"
import { cloneDeep } from "lodash"
const tableData = ref<any>([])
const formatQueryParams = (data: any) => {
  let params: Record<string, any> = {}
  for (const key in data) {
    if (key !== "selecttime") {
      const value = data[key].value
      if ((value && value !== "全部" && value.length) || typeof value === "number") {
        params[key] = data[key].value
      } else {
        params[key] = undefined
      }
    } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
      params.start_date = data[key].value[0]
      params.end_date = data[key].value[1]
    }
  }
  console.log("params", params)
  return params
}
const getTableData = async () => {
  tableLoading.value = true
  let params: any = {
    channel_id: channelId.value,
    page: pageConfig.currentPage,
    page_size: pageConfig.pageSize,
    ...formatQueryParams(searchFormSetting.value)
  }
  const [err, res]: any[] = await to(apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireListPost(params))
  tableLoading.value = false
  if (err) {
    return
  }
  if (res.code === 0) {
    tableData.value = cloneDeep(res.data.results)
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getTableData()
}
const changeSearch = async () => {
  pageConfig.currentPage = 1
  getTableData()
}
const handlerReset = () => {
  pageConfig.currentPage = 1
  getTableData()
}

// 改变渠道
const channelId = ref<number>(0)
const handlerTreeChange = (e: any) => {
  tableLoading.value = true
  channelId.value = e.id
  searchFormRef.value.handlerReset()
  getChannelList(e.id)
  getOrgList(e.id)
  getTableData()
}

import { apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireModifyStatusPost } from "@/api/supervision"
import { ElMessageBox } from "element-plus"
// 改变问卷状态
const handleEnableChange = async (row: any) => {
  row.enable = !row.enable
  ElMessageBox.confirm(
    !row.enable ? "开始收集数据，是否发布？" : "停止后，问卷将无法填写，确认吗？",
    !row.enable ? "发布" : "停止发布",
    {
      confirmButtonText: !row.enable ? "发布" : "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  )
    .then(async () => {
      let params = {
        id: row.id,
        enable: !row.enable
      }
      const [err, res]: any[] = await to(
        apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireModifyStatusPost(params)
      )
      if (err) return
      if (res.code === 0) {
        row.enable = !row.enable
        ElMessage.success(row.enable ? "发布成功" : "停止发布成功")
      } else {
        ElMessage.error(res.msg)
      }
    })
    .catch(() => {
      ElMessage.info("已取消")
    })
}

const qrUrl = ref<string>("")
// 生成链接
const setQrUrl = (data: any) => {
  switch (import.meta.env.VITE_NODE_ENV) {
    case "development":
      qrUrl.value = `http://192.168.50.58:9527/#/questionnaire-detail?type=qrCode&id=${data.id}`
      break
    case "staging":
      qrUrl.value = `http://192.168.50.37/#/questionnaire-detail?type=qrCode&id=${data.id}`
      break
    case "production":
      qrUrl.value = `https://zjjg.packertec.com/#/questionnaire-detail?type=qrCode&id=${data.id}`
      break
  }
}
const handleLink = (data: any) => {
  qrUrl.value = ""
  setQrUrl(data)
  console.log("新的qrUrl", qrUrl.value)
  if (qrUrl.value) {
    // 创建一个临时input元素
    const input = document.createElement("input")
    // 设置input的值为qrUrl
    input.value = qrUrl.value
    // 将input添加到文档中
    document.body.appendChild(input)
    // 选中input中的所有内容
    input.select()
    // 执行复制命令
    document.execCommand("copy")
    // 移除临时input元素
    document.body.removeChild(input)
    // 提示用户复制成功
    ElMessage.success("链接已复制到剪贴板")
  } else {
    ElMessage.warning("没有可复制的链接")
  }
}

import QrCode from "@/components/QrCode/index.vue"
const dialogShow = ref(false)
const dialogTitle = ref<string>("")
const handleQrCode = (data: any) => {
  qrUrl.value = ""
  setQrUrl(data)
  dialogTitle.value = data.name
  dialogShow.value = true
}

// 跳转
import useGoToPage from "@/hooks/useGoToPage"
const { goToPage } = useGoToPage()
const gotoPath = (row: any) => {
  let obj = {
    id: row.id,
    type: row.questionnaire_type
  }
  setSessionStorage("surveyDetail", obj)
  goToPage({
    path: "/regulatory_center/audit_configuration"
  })
}

// 问卷相关
import {
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAddPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireModifyPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireDeletePost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireDetailPost
} from "@/api/supervision"
const openType = ref<string>("")
const selectedRow = ref<any>({})
const computedDrawerTitle = computed(() => {
  switch (openType.value) {
    case "add":
      return "新增问卷"
    case "edit":
      return "编辑问卷"
    default:
      return "详情"
  }
})
import type { FormInstance, FormRules } from "element-plus"
const surveyDrawerFormRef = ref<FormInstance>()
const surveyDrawerShow = ref(false)
const surveyDrawerFromLoading = ref(false)
const notToEdit = ref(false)
const surveyDrawerForm = ref<any>({
  questionnaire_type: "common",
  name: "",
  overview: "",
  issuing_type: [] as any,
  issuing_channel_ids: [] as any,
  issuing_channel_org_ids: [] as any,
  visible_user: [] as any,
  channel_role_ids: [] as any,
  supplier_ids: [] as any,
  validity: [] as any,
  write_limit: 1,
  commit_type: "real_name",
  upper_limit: null as number | null,
  questions: [] as any
})
const surveyDrawerFormRules = reactive<FormRules<any>>({
  questionnaire_type: [{ required: true, message: "请填写", trigger: ["blur", "change"] }],
  name: [
    { required: true, message: "请填写", trigger: ["blur", "change"] },
    { pattern: /^.*\S+.*$/, message: "不能全为空格", trigger: ["blur", "change"] }
  ],
  overview: [
    { required: true, message: "请填写", trigger: ["blur", "change"] },
    { pattern: /^.*\S+.*$/, message: "不能全为空格", trigger: ["blur", "change"] }
  ],
  issuing_type: [{ required: true, message: "请选择", trigger: ["blur", "change"] }],
  issuing_channel_ids: [{ required: true, message: "请选择", trigger: ["blur", "change"] }],
  issuing_channel_org_ids: [{ required: true, message: "请选择", trigger: ["blur", "change"] }],
  visible_user: [{ required: true, message: "请选择", trigger: ["blur", "change"] }],
  channel_role_ids: [{ required: true, message: "请选择", trigger: ["blur", "change"] }],
  supplier_ids: [{ required: true, message: "请选择", trigger: ["blur", "change"] }],
  write_limit: [
    { required: true, message: "请填写", trigger: ["blur", "change"] },
    { pattern: /^-?\d+$/, message: "只能输入整数", trigger: ["blur", "change"] },
    { pattern: /^(?:[1-9]\d*(?:\.\d+)?|0\.\d*[1-9]\d*)$/, message: "必须大于0", trigger: ["blur", "change"] }
  ],
  commit_type: [{ required: true, message: "请填写", trigger: ["blur", "change"] }],
  upper_limit: [
    { required: true, message: "请填写", trigger: ["blur", "change"] },
    { pattern: /^-?\d+$/, message: "只能输入整数", trigger: ["blur", "change"] },
    { pattern: /^(?:[1-9]\d*(?:\.\d+)?|0\.\d*[1-9]\d*)$/, message: "必须大于0", trigger: ["blur", "change"] }
  ],
  questions: [{ required: true, message: "请填写", trigger: ["blur", "change"] }]
})
const getQuestionnaireDetail = async (id: number) => {
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireDetailPost({ id })
  )
  if (err) return
  if (res.code === 0) {
    surveyDrawerForm.value = cloneDeep(res.data)
    console.log("surveyDrawerForm.value", surveyDrawerForm.value)
    surveyDrawerForm.value.validity =
      res.data.effective_time && res.data.expiration_time
        ? [dayjs(res.data.effective_time).format("YYYY-MM-DD"), dayjs(res.data.expiration_time).format("YYYY-MM-DD")]
        : []
    setTimeout(() => {
      surveyDrawerForm.value.supplier_ids = res.data.supplier_ids
    }, 100)
    surveyDrawerForm.value.questions = res.data.questions.map((item: any) => {
      let obj: any = {
        question_type: item.question_type,
        order_in_list: item.order_in_list
      }
      switch (item.question_type) {
        case 0: {
          Object.assign(obj, {
            caption: item.caption,
            choices: [] as any[],
            required: item.required
          })
          obj.choices = item.choices.map((itemIn: any) => {
            let obj = {
              type: itemIn.other_content ? "other" : "default",
              description: itemIn.description,
              other_content: itemIn.other_content,
              multi_choice: itemIn.multi_choice,
              order_in_list: itemIn.order_in_list
            }
            return obj
          })
          break
        }
        case 1: {
          Object.assign(obj, {
            caption: item.caption,
            choices: [] as any[],
            least_choose_count: item.least_choose_count,
            required: item.required
          })
          obj.choices = item.choices.map((itemIn: any) => {
            let obj = {
              type: itemIn.other_content ? "other" : "default",
              description: itemIn.description,
              other_content: itemIn.other_content,
              multi_choice: itemIn.multi_choice,
              order_in_list: itemIn.order_in_list
            }
            return obj
          })
          break
        }
        case 2: {
          Object.assign(obj, {
            caption: item.caption,
            top_score: item.top_score,
            required: item.required
          })
          break
        }
        case 3: {
          Object.assign(obj, {
            caption: item.caption,
            choices: [] as any[],
            top_score: 5,
            required: item.required
          })
          obj.choices = item.choices.map((itemIn: any) => {
            let obj = {
              description: itemIn.description,
              order_in_list: itemIn.order_in_list
            }
            return obj
          })
          break
        }
        case 4: {
          Object.assign(obj, {
            caption: item.caption,
            required: item.required
          })
          break
        }
        case 5: {
          Object.assign(obj, {
            caption: item.caption,
            upload_max_num: item.upload_max_num,
            required: item.required
          })
          break
        }
        case 6: {
          Object.assign(obj, {
            caption: item.caption,
            upload_max_num: item.upload_max_num,
            required: item.required
          })
          break
        }
      }
      return obj
    })
  } else {
    ElMessage.error(res.msg)
  }
}
const openDrawer = async (type: string, row?: any) => {
  tableLoading.value = true
  surveyDrawerFromLoading.value = true
  openType.value = type
  selectedRow.value = row
  switch (type) {
    case "add":
      notToEdit.value = false
      surveyDrawerForm.value.questionnaire_type = "common"
      surveyDrawerForm.value.name = "" as any
      surveyDrawerForm.value.overview = "" as any
      surveyDrawerForm.value.issuing_type = [] as any
      surveyDrawerForm.value.issuing_channel_ids = [] as any
      surveyDrawerForm.value.issuing_channel_org_ids = [] as any
      surveyDrawerForm.value.visible_user = [] as any
      surveyDrawerForm.value.channel_role_ids = [] as any
      surveyDrawerForm.value.supplier_ids = [] as any
      surveyDrawerForm.value.validity = [] as any
      surveyDrawerForm.value.write_limit = 1
      surveyDrawerForm.value.commit_type = "real_name"
      surveyDrawerForm.value.upper_limit = null as number | null
      surveyDrawerForm.value.questions = [] as any
      surveyDrawerShow.value = true
      setTimeout(() => {
        surveyDrawerFormRef.value?.clearValidate()
      }, 100)
      break
    case "edit":
      notToEdit.value = false
      await getQuestionnaireDetail(row.id)
      surveyDrawerShow.value = true
      break
    case "check":
      notToEdit.value = true
      await getQuestionnaireDetail(row.id)
      surveyDrawerShow.value = true
      break
    case "history":
      historyDrawerShow.value = true
      break
  }
  tableLoading.value = false
  surveyDrawerFromLoading.value = false
}
watch(
  () => surveyDrawerForm.value.issuing_type,
  (newVal) => {
    if (!newVal.includes("channel")) {
      surveyDrawerForm.value.issuing_channel_ids = []
    }
    if (!newVal.includes("channel_org")) {
      surveyDrawerForm.value.issuing_channel_org_ids = []
    }
  }
)

const questionnaireRef = ref<any>(null)
const deleteHandle = async (row: any) => {
  ElMessageBox.confirm("删除后，无法查看已收集的数据，是否继续？", "删除", {
    confirmButtonText: "继续",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      secondConfirm(row)
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "已取消"
      })
    })
}
const secondConfirm = async (row: any) => {
  ElMessageBox.confirm("是否确定删除？", "删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      tableLoading.value = true
      const [err, res]: any[] = await to(
        apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireDeletePost({
          id: row.id
        })
      )
      tableLoading.value = false
      if (err) return
      if (res.code === 0) {
        ElMessage.success("删除成功")
        getTableData()
      } else {
        ElMessage.error(res.msg)
      }
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "已取消"
      })
    })
}
const historyDrawerRef = ref<any>()
const cancelHandle = async (type: any) => {
  if (!type) {
    return
  } else {
    switch (type) {
      case "surveyDrawerFormRef":
        if (surveyDrawerFormRef.value) {
          surveyDrawerFormRef.value.resetFields()
        }
        surveyDrawerShow.value = false
        break
      case "historyDrawerRef":
        if (historyDrawerRef.value) {
          historyDrawerRef.value.resetField()
        }
        historyDrawerShow.value = false
        break
    }
  }
}
const confirmHandle = async (isPublish: boolean) => {
  surveyDrawerFromLoading.value = true
  try {
    // 调用问卷组件的验证方法
    const questionnaireValidate = await questionnaireRef.value?.checkQuestionnaire()

    // 如果问卷有验证错误，则终止提交
    if (questionnaireValidate && questionnaireValidate.includes(false)) {
      ElMessage.error("问卷内容验证失败，请检查问卷题目设置")
      surveyDrawerFromLoading.value = false
      return
    }

    // 实现问卷提交逻辑
    if (surveyDrawerFormRef.value) {
      await surveyDrawerFormRef.value.validate(async (valid: any) => {
        if (valid) {
          const params = {
            ...surveyDrawerForm.value,
            effective_time:
              surveyDrawerForm.value.validity && surveyDrawerForm.value.validity.length
                ? surveyDrawerForm.value.validity[0] + " 00:00:00"
                : undefined,
            expiration_time:
              surveyDrawerForm.value.validity && surveyDrawerForm.value.validity.length
                ? surveyDrawerForm.value.validity[1] + " 23:59:59"
                : undefined,
            enable: isPublish // 是否立即发布
          }

          // 这里添加实际的API调用
          const [err, res]: any[] = await to(
            openType.value === "edit"
              ? apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireModifyPost({
                  ...params,
                  id: selectedRow.value.id
                })
              : apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAddPost({ ...params })
          )

          if (err) {
            ElMessage.error("保存问卷失败")
            surveyDrawerFromLoading.value = false
            return
          }

          if (res && res.code === 0) {
            ElMessage.success(isPublish ? "保存并发布成功" : "保存成功")
            surveyDrawerShow.value = false
            getTableData() // 刷新表格数据
          } else {
            ElMessage.error(res?.msg || "操作失败")
          }
        } else {
          ElMessage.error("问卷内容验证失败，请检查问卷题目设置")
        }
      })
    }
  } catch (error) {
    console.error("保存问卷异常", error)
    ElMessage.error("系统异常，请稍后再试")
  } finally {
    surveyDrawerFromLoading.value = false
    getTableData()
  }
}
import { setSessionStorage } from "@/utils/storage"
import { useRouter } from "vue-router"
const router = useRouter()
const gotoPreview = () => {
  // 这里实现预览功能
  setSessionStorage("SurveyData", JSON.stringify(surveyDrawerForm.value))
  const { href } = router.resolve({
    path: "/preview"
  })
  window.open(href, "_blank")
}
const changeDefault = (arr: number[]) => {
  if (arr.length && arr.includes(2)) {
    surveyDrawerForm.value.commit_type = "anonymous"
  }
}

// 获取必要的列表
import { apiBackgroundFundSupervisionSupervisionChannelListPost } from "@/api/supervision"
import {
  apiBackgroundFundSupervisionChannelRoleListPost,
  apiBackgroundFundSupervisionSupervisionChannelGetAllOrgPost
} from "@/api/user"
import { apiBackgroundFundSupervisionAppropriationSupplierManageListPost } from "@/api/supplier"
const getItem = (newArr: any, targetArr: any) => {
  if (targetArr.length) {
    targetArr.forEach((item: any) => {
      newArr.push(item)
      if (item.children_list.length) {
        getItem(newArr, item.children_list)
      }
    })
  }
}
const channelList = ref<any>([])
const getChannelList = async (id?: number) => {
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionSupervisionChannelListPost({
      id: id || undefined,
      page: 1,
      page_size: 9999
    })
  )
  if (err) return
  if (res.code === 0) {
    channelList.value = []
    let arr: any = []
    getItem(arr, res.data.results)
    channelList.value = cloneDeep(arr)
    searchFormSetting.value.issuing_channel_ids.dataList = arr.map((item: any) => {
      let obj = {
        label: item.name,
        value: item.id
      }
      return obj
    })
  } else {
    ElMessage.error(res.msg)
  }
}
const allOrgList = ref<any>([])
const getOrgList = async (id?: number) => {
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionSupervisionChannelGetAllOrgPost({
      channel_id: id || undefined,
      page: 1,
      page_size: 9999
    })
  )
  if (err) return
  if (res.code === 0) {
    allOrgList.value = cloneDeep(res.data.results)
    searchFormSetting.value.issuing_channel_org_ids.dataList = res.data.results.map((item: any) => {
      let obj = {
        label: item.name,
        value: item.id
      }
      return obj
    })
  } else {
    ElMessage.error(res.msg)
  }
}
const channelRoleList = ref<any>([])
const getChannelRoleList = async (ids: number[]) => {
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionChannelRoleListPost({
      page: 1,
      page_size: 9999,
      supervision_channel_ids: ids
    })
  )
  if (err) return
  if (res.code === 0) {
    channelRoleList.value = res.data.results.map((item: any) => {
      let obj = {
        value: item.id,
        label: item.name
      }
      return obj
    })
  } else {
    ElMessage.error(res.msg)
  }
}
const supplierManageList = ref<any>([])
const getSupplierManageList = async (ids: number[]) => {
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionAppropriationSupplierManageListPost({
      page: 1,
      page_size: 9999,
      channel_ids: ids
    })
  )
  if (err) return
  if (res.code === 0) {
    supplierManageList.value = cloneDeep(res.data.results)
  } else {
    ElMessage.error(res.msg)
  }
}
onMounted(() => {
  getChannelList()
  getOrgList()
})
watch(
  () => surveyDrawerForm.value.issuing_channel_ids,
  (newVal) => {
    if (newVal) {
      surveyDrawerForm.value.supplier_ids = []
      if (surveyDrawerForm.value.questionnaire_type === "supplier") {
        getSupplierManageList(newVal)
      } else {
        getChannelRoleList(newVal)
      }
    }
  },
  { immediate: true, deep: true }
)

// 历史记录
import HistoryData from "./components/HistoryData.vue"
const historyDrawerShow = ref(false)

// 导出相关
import { exportHandle } from "@/utils/exportExcel"
import { apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireListExportPost } from "@/api/supervision"
const goToExport = () => {
  let params: any = {}
  params = {
    channel_id: channelId.value,
    page: pageConfig.currentPage,
    page_size: pageConfig.pageSize,
    ...formatQueryParams(searchFormSetting.value)
  }
  const option = {
    type: "",
    api: apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireListExportPost,
    params: params
  }
  exportHandle(option)
}

// 打印相关
import { setLocalStorage } from "@/utils/storage"
const goToPrint = () => {
  let newSetting: any[] = tableSetting.slice(0, -1)
  setLocalStorage("print_setting", newSetting)
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: "AuditRecords",
      print_title: "问卷缉查",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireListPost", // 请求的api
      params: JSON.stringify({
        channel_id: channelId.value,
        page: pageConfig.currentPage,
        page_size: pageConfig.pageSize,
        ...formatQueryParams(searchFormSetting.value)
      })
    }
  })
  window.open(href, "_blank")
}
</script>

<template>
  <div class="audit-records">
    <div class="left">
      <channel-tree @tree-click="handlerTreeChange" />
    </div>
    <div class="right container-wrapper">
      <div ref="searchRef">
        <search-form
          ref="searchFormRef"
          :form-setting="searchFormSetting"
          @change-search="changeSearch"
          @reset="handlerReset"
          v-loading="loading"
          search-mode="normal"
        />
      </div>
      <div class="table-wrapper" ref="tableWrapperRef">
        <div class="table-header">
          <div class="table-button">
            <el-button type="primary" plain @click="openDrawer('add')">新增问卷</el-button>
            <el-button type="primary" @click="openDrawer('history')">历史记录</el-button>
            <el-button type="primary" @click="goToExport">导出</el-button>
            <el-button type="primary" @click="goToPrint">打印</el-button>
          </div>
        </div>
        <div class="table-content">
          <ps-table
            :tableData="tableData"
            ref="psTableRef"
            v-loading="tableLoading"
            :show-pagination="true"
            @pagination-change="handleCurrentChange"
            :pageConfig="pageConfig"
            :max-height="maxHeight"
          >
            <ps-column :table-headers="tableSetting">
              <template #enable="{ row }">
                <el-switch v-model="row.enable" @change="handleEnableChange(row)" />
              </template>
              <template #operation="{ row }">
                <el-button link size="small" type="primary" @click="openDrawer('check', row)">详情</el-button>
                <el-button
                  link
                  size="small"
                  type="primary"
                  v-if="row.visible_user.includes(2) || row.questionnaire_type === 'supplier'"
                  @click="handleLink(row)"
                  >链接</el-button
                >
                <el-button
                  link
                  size="small"
                  type="primary"
                  v-if="row.visible_user.includes(2) || row.questionnaire_type === 'supplier'"
                  @click="handleQrCode(row)"
                  >二维码</el-button
                >
                <el-button
                  link
                  size="small"
                  type="primary"
                  v-if="!row.enable && !row.answered_num"
                  @click="openDrawer('edit', row)"
                  >编辑</el-button
                >
                <el-button link size="small" type="primary" @click="gotoPath(row)">数据明细</el-button>
                <el-button
                  link
                  size="small"
                  type="primary"
                  v-if="!row.answered_num && !row.enable"
                  @click="deleteHandle(row)"
                  >删除</el-button
                >
              </template>
            </ps-column>
          </ps-table>
        </div>
      </div>
    </div>

    <!-- 抽屉 -->
    <el-drawer
      class="ps-drawer"
      v-model="surveyDrawerShow"
      :title="computedDrawerTitle"
      :show-close="false"
      size="45%"
      trap-focus
    >
      <el-form
        v-if="surveyDrawerShow"
        class="survey-drawer-form"
        ref="surveyDrawerFormRef"
        :model="surveyDrawerForm"
        label-position="top"
        :rules="surveyDrawerFormRules"
        v-loading="surveyDrawerFromLoading"
      >
        <el-form-item prop="questionnaire_type">
          <div class="form-item">
            <div class="form-label">
              <span class="color-red m-r-5px">*</span>
              <span class="font-700">类型</span>
            </div>
            <el-radio-group v-model="surveyDrawerForm.questionnaire_type" :disabled="notToEdit">
              <el-radio value="common" label="普通问卷" />
              <el-radio value="supplier" label="供应商专项检查" />
            </el-radio-group>
          </div>
        </el-form-item>
        <el-form-item prop="name">
          <div class="form-item">
            <div class="form-label">
              <span class="color-red m-r-5px">*</span>
              <span class="font-700">问卷名称</span>
            </div>
            <div class="w-100%">
              <el-input v-model="surveyDrawerForm.name" maxlength="30" :disabled="notToEdit" />
            </div>
          </div>
        </el-form-item>
        <el-form-item prop="overview">
          <div class="form-item">
            <div class="form-label">
              <span class="color-red m-r-5px">*</span>
              <span class="font-700">概述</span>
            </div>
            <div class="w-100%">
              <el-input v-model="surveyDrawerForm.overview" maxlength="50" :disabled="notToEdit" />
            </div>
          </div>
        </el-form-item>
        <div v-if="surveyDrawerForm.questionnaire_type === 'common'">
          <el-form-item prop="issuing_type">
            <div class="form-item">
              <div class="form-label">
                <span class="color-red m-r-5px">*</span>
                <span class="font-700">下发组织</span>
              </div>
              <div class="questionnaire-form">
                <el-checkbox-group v-model="surveyDrawerForm.issuing_type" :disabled="notToEdit">
                  <el-checkbox value="channel" label="渠道可见" />
                  <el-checkbox value="merchant" label="下发到商户组织" />
                </el-checkbox-group>
                <el-form-item
                  prop="issuing_channel_ids"
                  v-if="surveyDrawerForm.issuing_type.length && surveyDrawerForm.issuing_type.includes('channel')"
                >
                  <div class="w-350px">
                    <el-select
                      v-model="surveyDrawerForm.issuing_channel_ids"
                      multiple
                      collapse-tags
                      collapse-tags-tooltip
                      placeholder="请选择下发渠道"
                      class="m-b-5px"
                      :disabled="notToEdit"
                    >
                      <el-option v-for="item in channelList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </div>
                </el-form-item>
                <el-form-item
                  prop="issuing_channel_org_ids"
                  v-if="surveyDrawerForm.issuing_type.length && surveyDrawerForm.issuing_type.includes('merchant')"
                >
                  <div class="w-350px">
                    <el-select
                      v-model="surveyDrawerForm.issuing_channel_org_ids"
                      multiple
                      collapse-tags
                      collapse-tags-tooltip
                      placeholder="请选择下发渠道下的绑定组织"
                      :disabled="notToEdit"
                    >
                      <el-option v-for="item in allOrgList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </div>
                </el-form-item>
              </div>
            </div>
          </el-form-item>
          <el-form-item prop="visible_user">
            <div class="form-item">
              <div class="form-label">
                <span class="color-red m-r-5px">*</span>
                <span class="font-700">可见用户</span>
              </div>
              <el-checkbox-group
                v-model="surveyDrawerForm.visible_user"
                :disabled="notToEdit"
                @change="changeDefault(surveyDrawerForm.visible_user)"
              >
                <el-checkbox
                  :value="0"
                  v-if="
                    !(surveyDrawerForm.issuing_type.length === 1 && surveyDrawerForm.issuing_type.includes('channel'))
                  "
                  label="内部用户"
                  >内部用户</el-checkbox
                >
                <el-checkbox :value="1" label="商户管理员" />
                <el-checkbox :value="2" label="其他(仅支持匿名提交)" />
              </el-checkbox-group>
            </div>
          </el-form-item>
          <el-form-item
            prop="channel_role_ids"
            v-if="surveyDrawerForm.issuing_type.includes('channel') && surveyDrawerForm.visible_user.includes(1)"
          >
            <div class="form-item">
              <div class="form-label">
                <span class="color-red m-r-5px">*</span>
                <span class="font-700">可见角色</span>
              </div>
              <div class="w-350px">
                <el-select
                  v-model="surveyDrawerForm.channel_role_ids"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  placeholder="请选择"
                  class="w-100-p"
                  :disabled="notToEdit"
                >
                  <el-option
                    v-for="(item, index) in channelRoleList"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
          </el-form-item>
        </div>
        <div v-if="surveyDrawerForm.questionnaire_type === 'supplier'">
          <el-form-item prop="issuing_channel_ids">
            <div class="form-item">
              <div class="form-label">
                <span class="color-red m-r-5px">*</span>
                <span class="font-700">选择渠道</span>
              </div>
              <div class="w-350px">
                <el-select
                  v-model="surveyDrawerForm.issuing_channel_ids"
                  placeholder="请选择渠道"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  :disabled="notToEdit"
                >
                  <el-option v-for="item in channelList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </div>
            </div>
          </el-form-item>
          <el-form-item prop="supplier_ids">
            <div class="form-item">
              <div class="form-label">
                <span class="color-red m-r-5px">*</span>
                <span class="font-700">选择供应商</span>
              </div>
              <div class="w-350px">
                <el-select
                  v-model="surveyDrawerForm.supplier_ids"
                  placeholder="请选择供应商"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  :disabled="notToEdit || !surveyDrawerForm.issuing_channel_ids.length"
                >
                  <el-option v-for="item in supplierManageList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </div>
            </div>
          </el-form-item>
        </div>
        <el-form-item prop="validity">
          <div class="form-item">
            <div class="form-label">
              <span style="width: 1em" />
              <span class="font-700">有效期</span>
            </div>
            <div>
              <el-date-picker
                v-model="surveyDrawerForm.validity"
                :disabled="notToEdit"
                type="daterange"
                value-format="YYYY-MM-DD"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </div>
          </div>
        </el-form-item>
        <div v-if="surveyDrawerForm.questionnaire_type === 'common'">
          <el-form-item prop="write_limit">
            <div class="form-item">
              <div class="form-label">
                <span class="color-red m-r-5px">*</span>
                <span class="font-700">填写限制</span>
              </div>
              <div class="flex flex-items-center">
                <span style="width: 7em">同个用户可填写</span>
                <div class="w-80px m-l-5px m-r-5px">
                  <el-input
                    v-model="surveyDrawerForm.write_limit"
                    placeholder="请填写"
                    maxlength="5"
                    :disabled="notToEdit"
                  />
                </div>
                <span>次</span>
              </div>
            </div>
          </el-form-item>
          <el-form-item prop="commit_type">
            <div class="form-item">
              <div class="form-label">
                <span class="color-red m-r-5px">*</span>
                <span class="font-700">提交方式</span>
              </div>
              <el-radio-group v-model="surveyDrawerForm.commit_type" :disabled="notToEdit">
                <el-radio value="anonymous" label="匿名提交" />
                <el-radio
                  value="real_name"
                  label="实名提交"
                  :disabled="surveyDrawerForm.visible_user.length === 1 && surveyDrawerForm.visible_user.includes(2)"
                />
              </el-radio-group>
            </div>
          </el-form-item>
        </div>
        <el-form-item prop="upper_limit">
          <div class="form-item">
            <div class="form-label">
              <span class="color-red m-r-5px">*</span>
              <span class="font-700">问卷收集上限</span>
            </div>
            <div class="w-205px">
              <el-input v-model="surveyDrawerForm.upper_limit" maxlength="5" :disabled="notToEdit">
                <template #append>
                  <span>份</span>
                </template>
              </el-input>
            </div>
          </div>
        </el-form-item>
        <el-form-item prop="questions">
          <Questionnaire
            class="questionnaire-form"
            ref="questionnaireRef"
            v-model:drawerShow="surveyDrawerShow"
            v-model:questionsList="surveyDrawerForm.questions"
            v-model:disabled="notToEdit"
          />
        </el-form-item>
      </el-form>
      <div class="dialog-footer m-t-20px">
        <el-button class="w-100px" @click="cancelHandle('surveyDrawerFormRef')">取消</el-button>
        <el-button type="primary" class="w-100px" @click="gotoPreview">预览</el-button>
        <el-button type="primary" class="w-100px" v-if="!notToEdit" @click="confirmHandle(false)">保存</el-button>
        <el-button type="primary" class="w-100px" v-if="!notToEdit" @click="confirmHandle(true)">保存并发布</el-button>
      </div>
    </el-drawer>

    <el-drawer :title="'历史记录'" v-model="historyDrawerShow" :show-close="false" size="80%">
      <history-data ref="historyDrawerRef" v-model:show="historyDrawerShow" />
      <div class="dialog-footer m-t-20px">
        <el-button class="w-100px" @click="cancelHandle('historyDrawerRef')">关闭</el-button>
      </div>
    </el-drawer>

    <el-dialog :title="dialogTitle" v-model="dialogShow" width="20%" center :show-close="false">
      <div class="w-100-p flex flex-items-center flex-justify-center">
        <qr-code v-if="dialogShow" :value="qrUrl" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="dialogShow = false">关 闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.audit-records {
  display: flex;
  .right {
    flex: 1;
    padding-right: 20px;
  }
}

// 表单样式
.survey-drawer-form {
  .form-item {
    width: 100%;
    display: flex;
    align-items: flex-start;

    .form-label {
      min-width: 8em; // 统一宽度为最长label宽度
      padding-right: 15px; // 统一间距为5px
      text-align: right;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }

  :deep(.el-form-item__error) {
    margin-left: 10em;
    font-size: 12px;
  }
  .questionnaire-form {
    :deep(.el-form-item__error) {
      margin-left: 0;
    }
  }
}
</style>
