<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import useTableHeightHook from "@/hooks/useTableHeight"
import { ElMessage } from "element-plus"
import dayjs from "dayjs"
const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

const searchFormSetting = ref<any>({
  selecttime: {
    type: "daterange",
    filterable: true,
    clearable: false,
    label: "提交时间",
    labelWidth: "100px",
    value: [dayjs().subtract(1, "year").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  name: {
    type: "input",
    label: "填写人",
    labelWidth: "100px",
    value: "",
    placeholder: "请输入",
    clearable: true,
    maxlength: 20
  }
})
const tableSetting = ref<any>([])
const tableData = ref<any>([])
const tableLoading = ref(false)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100, 500, 1000]
})

import { apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAnswerDetailPost } from "@/api/supervision"
import to from "await-to-js"
import { cloneDeep } from "lodash"
import { getSessionStorage } from "@/utils/storage"
const surveyInfo = getSessionStorage("surveyDetail")

const moveToFront = (arr: any[], key: string) => {
  tableSetting.value.forEach((item: any, index: number) => {
    if (item.prop === key) {
      const element = arr.splice(index, 1)[0]
      arr.unshift(element)
      return arr
    }
  })
}
const getTableData = async () => {
  tableSetting.value = []
  tableLoading.value = true
  const [err, res]: any = await to(
    apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAnswerDetailPost({
      id: parseInt(surveyInfo.id as string) || undefined,
      start_date: searchFormSetting.value.selecttime.value[0],
      end_date: searchFormSetting.value.selecttime.value[1],
      name: searchFormSetting.value.name.value || undefined,
      supplier_ids: searchFormSetting.value.supplier_ids ? [...searchFormSetting.value.supplier_ids.value] : undefined,
      is_anonymous: searchFormSetting.value.is_anonymous.value || undefined,
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) return
  if (res.code === 0) {
    pageConfig.total = res.data.count
    for (let key in res.data.results[0]) {
      let obj = { label: "", prop: "", data: "", slot: key }
      obj.label =
        key !== "number" && key !== "commit_time" && key !== "name" && key !== "supplier"
          ? res.data.results[0][key].caption
          : res.data.results[0][key]
      obj.data =
        key !== "number" && key !== "commit_time" && key !== "name" && key !== "supplier"
          ? res.data.results[0][key]
          : {}
      obj.prop = key
      tableSetting.value.push(obj)
    }
    moveToFront(tableSetting.value, "supplier")
    moveToFront(tableSetting.value, "commit_time")
    moveToFront(tableSetting.value, "number")
    tableData.value = res.data.results.filter((item: any, index: number) => index >= 1)
    console.log("tableData", tableData.value, tableSetting.value)
  } else {
    ElMessage.error(res.msg)
  }
  tableLoading.value = false
}

import { apiBackgroundFundSupervisionSupervisionQuestionnaireGetQuestionnaireSupplierPost } from "@/api/supervision"
const supplierList = ref<any>([])
const getSupplierList = async (id: number) => {
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionSupervisionQuestionnaireGetQuestionnaireSupplierPost({
      id
    })
  )
  if (err) return
  if (res.code === 0) {
    supplierList.value = res.data
  } else {
    ElMessage.error(res.msg)
  }
}

const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getTableData()
}
const changeSearch = async () => {
  pageConfig.currentPage = 1
  getTableData()
}
const handlerReset = () => {
  pageConfig.currentPage = 1
  getTableData()
}
import { nextTick } from "vue"
onMounted(async () => {
  if (surveyInfo.type === "supplier") {
    Object.assign(searchFormSetting.value, {
      supplier_ids: {
        type: "select",
        label: "供应商",
        value: [] as any[],
        multiple: true,
        clearable: true,
        collapseTags: true,
        dataList: []
      }
    })
    await getSupplierList(parseInt(surveyInfo.id as string))
    await nextTick(() => {
      searchFormSetting.value.supplier_ids.dataList = supplierList.value.map((item: any) => {
        return {
          label: item.name,
          value: item.id
        }
      })
    })
  }
  Object.assign(searchFormSetting.value, {
    is_anonymous: {
      type: "checkbox",
      labelText: "只看匿名",
      value: ""
    }
  })
  getTableData()
  console.log("searchFormSetting", searchFormSetting.value)
})

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])
const handleClick = (data: any, key: string) => {
  if (data[key].url.length > 0) {
    imageList.value = cloneDeep(data[key].url)
    imageVisible.value = true
  } else {
    ElMessage.warning("暂无图片")
  }
}

import axios from "axios"
const downloadUrl = async (data: any, key: string) => {
  if (data[key].url.length > 0) {
    data[key].url.forEach(async (item: any) => {
      try {
        const response = await axios({
          url: item,
          method: "GET",
          responseType: "blob" // 重要：设置响应类型为 blob
        })
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement("a")
        const lastSlashIndex = item.lastIndexOf("/")
        const result = item.substring(lastSlashIndex + 1)
        link.href = url
        console.log("result", result)
        link.download = `${result}` // 设置下载的文件名
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } catch (error) {
        console.error("下载失败:", error)
      }
    })
  } else {
    ElMessage.warning("暂无附件")
  }
}

// 导出相关
import { exportHandle } from "@/utils/exportExcel"
import { apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAnswerDetailExportPost } from "@/api/supervision"
const goToExport = () => {
  let params: any = {}
  params = {
    id: parseInt(surveyInfo.id as string) || undefined,
    start_date: searchFormSetting.value.selecttime.value[0],
    end_date: searchFormSetting.value.selecttime.value[1],
    name: searchFormSetting.value.name.value || undefined,
    supplier_ids: searchFormSetting.value.supplier_ids ? [...searchFormSetting.value.supplier_ids.value] : undefined,
    is_anonymous: searchFormSetting.value.is_anonymous.value || undefined,
    page: pageConfig.currentPage,
    page_size: pageConfig.pageSize
  }
  const option = {
    type: "",
    api: apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAnswerDetailExportPost,
    params: params
  }
  exportHandle(option)
}

// 打印相关
import { setLocalStorage } from "@/utils/storage"
import { useRouter } from "vue-router"
const router = useRouter()
const goToPrint = () => {
  setLocalStorage("print_setting", tableSetting.value)
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: "AuditConfiguration",
      print_title: "数据明细",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAnswerDetailPost", // 请求的api
      params: JSON.stringify({
        id: surveyInfo.id,
        start_date: searchFormSetting.value.selecttime.value[0],
        end_date: searchFormSetting.value.selecttime.value[1],
        name: searchFormSetting.value.name.value || undefined,
        page: pageConfig.currentPage,
        page_size: pageConfig.pageSize
      })
    }
  })
  window.open(href, "_blank")
}
</script>

<template>
  <div class="container-wrapper p-l-20px p-r-20px">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="table-button">
          <el-button type="primary" @click="goToExport">导出</el-button>
          <el-button type="primary" @click="goToPrint">打印</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <el-table-column :prop="item.prop" :label="item.label" v-for="(item, index) in tableSetting" :key="index">
            <template #default="{ row }">
              <div
                v-if="
                  item.prop !== 'number' &&
                  item.prop !== 'commit_time' &&
                  item.prop !== 'name' &&
                  item.prop !== 'supplier'
                "
              >
                <div v-if="item.data.question_type === 0">
                  {{ row[item.prop] }}
                </div>
                <div v-else-if="item.data.question_type === 1">
                  <div v-for="(itemIn, indexIn) in row[item.prop]" :key="indexIn">{{ itemIn }}{{ ";" }}</div>
                </div>
                <div v-else-if="item.data.question_type === 2">{{ row[item.prop].score }}分</div>
                <div v-else-if="item.data.question_type === 3">
                  <div v-for="(itemIn, indexIn) in row[item.prop]" :key="indexIn">
                    {{ itemIn.description }}: {{ itemIn.score }}星
                  </div>
                </div>
                <div v-else-if="item.data.question_type === 4">
                  {{ row[item.prop].text }}
                </div>
                <div v-else-if="item.data.question_type === 5">
                  <el-button type="text" size="small" class="ps-text" @click="handleClick(row, item.prop)"
                    >查看</el-button
                  >
                </div>
                <div v-else>
                  <el-button type="text" size="small" class="ps-text" @click="downloadUrl(row, item.prop)"
                    >下载</el-button
                  >
                </div>
              </div>
              <div v-else>{{ row[item.prop] || "--" }}</div>
            </template>
          </el-table-column>
        </ps-table>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
  </div>
</template>

<style lang="scss" scoped></style>
