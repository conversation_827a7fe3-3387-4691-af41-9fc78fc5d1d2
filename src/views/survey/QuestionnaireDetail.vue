<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { useRoute } from "vue-router"
import { getSuffix } from "@/utils"
import { getToken } from "@/utils/cache/cookies"
import { ElMessage } from "element-plus"
import CompletedImage from "@/assets/images/Completed.png"
const route = useRoute()

// 看看是否是移动端打开
const isMobile = ref(false)
const checkDeviceType = () => {
  const ua = navigator.userAgent
  isMobile.value = /iPhone|iPad|iPod|Android/i.test(ua)
}

// 响应式数据
const loading = ref(false)
const title = ref("问卷调查")
const questionnaireData = ref<any>({})
const questionnaireList = ref<any>([])
const formData = ref<any>({})
const isDataReady = ref(false)
const uploadingForImg = ref(false)
const uploadingForFile = ref(false)
const isFinish = ref(false)
const previewForm = ref<any>()

// 常量
const serverUrl =
  route.query.type === "qrCode"
    ? "/api/background_fund_supervision/supervision_questionnaire/upload"
    : "/api/background/file/channel_upload_file"
const uploadParams = {
  prefix: "incumbents",
  key: "incumbents" + new Date().getTime() + Math.floor(Math.random() * 150)
}
const token = getToken()
const headersOpts = {
  TOKEN: token
}

// 方法
import {
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireDetailPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireDetailNoLoginPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAccountAnswerPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAnswerPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireGetQuestionnaireSupplierPost
} from "@/api/supervision"
import { cloneDeep } from "lodash"
import to from "await-to-js"

const canSubmit = ref(false)
const getQuestionnaireList = async (id: number) => {
  loading.value = true
  try {
    const [err, res]: any[] = await to(
      apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireDetailPost({
        id,
        is_answer: true
      })
    )
    if (err) return
    if (res.code === 0) {
      questionnaireData.value = cloneDeep(res.data)
      questionnaireList.value = cloneDeep(res.data.questions)
      generateQuestionnaire()
    } else if (res.code === 1) {
      ElMessage.error(res.msg)
      canSubmit.value = true
    } else {
      ElMessage.error(res.msg)
      canSubmit.value = true
    }
    console.log("能点嘛:", canSubmit)
  } catch (error) {
    console.error("获取问卷详情失败:", error)
    ElMessage.error("获取问卷详情失败")
  } finally {
    loading.value = false
  }
}

const getQuestionnaireListNotLogin = async (id: number) => {
  loading.value = true
  try {
    const [err, res]: any[] = await to(
      apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireDetailNoLoginPost({
        id
      })
    )
    if (err) return
    if (res.code === 0) {
      questionnaireData.value = cloneDeep(res.data)
      questionnaireList.value = cloneDeep(res.data.questions)
      generateQuestionnaire()
    } else if (res.code === 1) {
      ElMessage.error(res.msg)
      canSubmit.value = true
    } else {
      ElMessage.error(res.msg)
      canSubmit.value = true
    }
  } catch (error) {
    console.error("获取问卷详情失败:", error)
    ElMessage.error("获取问卷详情失败")
  } finally {
    loading.value = false
  }
}

const generateQuestionnaire = () => {
  questionnaireList.value.forEach((item: any) => {
    let defaultValue: any = ""
    if ([1, 3, 5, 6].includes(item.question_type)) {
      defaultValue = []
    } else if (item.question_type === 2) {
      defaultValue = 0
    }

    formData.value[item.id] = {
      ...item,
      value: defaultValue,
      fileList: item.question_type === 5 || item.question_type === 6 ? [] : undefined
    }

    if (item.choices && item.choices.length) {
      item.choices.forEach((choice: any) => {
        choice.other_value = item.question_type === 3 ? 0 : ""
      })
    }
  })
  console.log("看看赋值完", formData.value)
}

const supplierList = ref<any>([])
const dialogVisible = ref(false)
const supplierId = ref<any>("")
const getSupplierList = async (id: number) => {
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionSupervisionQuestionnaireGetQuestionnaireSupplierPost({
      id
    })
  )
  if (err) return
  if (res.code === 0) {
    supplierList.value = res.data
  } else {
    ElMessage.error(res.msg)
  }
}

const changeRate = (item: any, itemIn: any, indexIn: number, value: any) => {
  console.log("changeRate", item, itemIn, indexIn, value)
  if (!item || !item.id || !formData.value[item.id]) return

  let obj = {
    id: itemIn.id,
    score: value
  }
  if (
    formData.value[item.id].value.length &&
    formData.value[item.id].value.find((child: any) => child.id === itemIn.id)
  ) {
    formData.value[item.id].value = formData.value[item.id].value.map((child: any) => {
      if (child.id === itemIn.id) {
        child = { ...obj }
      }
      return child
    })
  } else {
    formData.value[item.id].value = [...formData.value[item.id].value, { ...obj }]
  }
}

const computedRules: any = (data: any) => {
  let rules: any = [{ required: data.required, message: "此题为必选题", trigger: ["change", "blur"] }]
  if (data.question_type === 1 && data.required) {
    let obj: any = {
      trigger: ["change", "blur"],
      validator: (rule: any, value: any, callback: any) => {
        if (!value || !value.length) {
          callback(new Error("请选择"))
        } else if (value.length < data.least_choose_count) {
          callback(new Error(`至少选${data.least_choose_count}项`))
        } else {
          callback()
        }
      }
    }
    rules.push(obj)
  } else if (data.question_type === 3 && data.required) {
    let obj: any = {
      trigger: ["change", "blur"],
      validator: (rule: any, value: any, callback: any) => {
        if (!value || !value.length) {
          callback(new Error("请选择"))
        } else if (value.length < data.choices.length) {
          callback(new Error(`有评价未填写`))
        } else {
          callback()
        }
      }
    }
    rules.push(obj)
  }
  return rules
}

const uploadSuccess = (res: any, file: any, fileList: any, type: string, item: any) => {
  if (type === "img") {
    if (res && res.code === 0) {
      ElMessage.success("上传成功")
      formData.value[item.id].value = [...formData.value[item.id].value, res.data.public_url]
    } else {
      ElMessage.error(res.msg)
    }
    uploadingForImg.value = false
  } else {
    if (res && res.code === 0) {
      ElMessage.success("上传成功")
      formData.value[item.id].value = [...formData.value[item.id].value, res.data.public_url]
    } else {
      ElMessage.error(res.msg)
    }
    uploadingForFile.value = false
  }
}

import type { UploadRawFile } from "element-plus"
const beforeUpload = (file: UploadRawFile, type: string) => {
  if (type === "img") {
    const unUploadType = [".jpeg", ".jpg", ".png", ".gif"]
    const isLt2M = file.size / 1024 / 1024 <= 5
    if (!unUploadType.includes(getSuffix(file.name))) {
      ElMessage.error("上传图片只能是 GIF格式、JPG 格式或PNG格式!")
      return false
    }
    if (!isLt2M) {
      ElMessage.error("上传图片大小不能超过 5MB!")
      return false
    }
    uploadingForImg.value = true
  } else {
    const unUploadType = [
      ".jpeg",
      ".jpg",
      ".xls",
      ".xlsx",
      ".png",
      ".gif",
      ".mp4",
      ".txt",
      ".zip",
      ".docx",
      ".doc",
      ".apk",
      ".tiff",
      ".JPEG",
      ".PNG",
      ".TIFF",
      ".WEBP",
      ".HEIF",
      ".JPG",
      ".exe",
      ".rar",
      ".ZIP",
      ".RAR"
    ]
    const isLt2M = file.size / 1024 / 1024 <= 10
    if (!unUploadType.includes(getSuffix(file.name))) {
      ElMessage.error("该文件格式不支持上传!")
      return false
    }
    if (!isLt2M) {
      ElMessage.error("上传文件大小不能超过 10MB!")
      return false
    }
    uploadingForFile.value = true
  }
}

const handleExceed = (data: any, files: any, uploadFiles: any) => {
  console.log(
    "看看超出",
    formData.value[data.id].value,
    formData.value[data.id].fileList,
    formData.value[data.id].upload_max_num,
    files,
    uploadFiles
  )
  // if (formData.value[data.id].value.length > formData.value[data.id].upload_max_num) {
  //   ElMessage.warning("超出上传数量上限")
  // }
  ElMessage.warning("超出上传数量上限")
}

const selectScore = (item: any, index: number) => {
  if (!isFinish.value) {
    if (!item || !item.id || !formData.value[item.id]) return
    formData.value[item.id].value = index
  }
}

const setChoices = (data: any): any => {
  let arr: any[] = []
  switch (data.question_type) {
    case 0:
    case 2: {
      if (data.value) {
        arr = [data.value]
      } else {
        arr = []
      }
      break
    }
    case 1:
    case 3: {
      if (data.value.length) {
        arr = [...data.value]
      } else {
        arr = []
      }
      break
    }
  }
  return arr
}

const submit = async () => {
  try {
    await previewForm.value.validate(async (valid: any) => {
      if (!valid) {
        ElMessage.error("请完成必填项")
        return false
      } else {
        const hasEmptyRequired = Object.values(formData.value).some((item: any) => {
          if (item.required) {
            switch (item.question_type) {
              case 0: {
                console.log("item.value", item)
                let obj: any = item.choices.find((itemIn: any) => itemIn.id === item.value)
                console.log("obj", obj)
                if (obj.other_content) {
                  if (!obj.other_value) {
                    return true
                  } else {
                    return false
                  }
                } else {
                  return false
                }
              }
              case 1: {
                let arr: boolean[] = []
                item.value.forEach((itemIn: any) => {
                  let newObj = item.choices.find((choicesItem: any) => itemIn === choicesItem.id)
                  console.log("newObj", newObj)
                  if (newObj.other_content) {
                    if (!newObj.other_value) {
                      arr.push(true)
                    } else {
                      arr.push(false)
                    }
                  } else {
                    arr.push(false)
                  }
                })
                if (arr.find((flag: boolean) => flag)) {
                  return true
                } else {
                  return false
                }
              }
            }
          } else {
            return false
          }
        })
        if (hasEmptyRequired) {
          ElMessage.error("请填写选择题中的勾选其他项的填空")
          return false
        }

        let answerList = []
        for (let key in formData.value) {
          answerList.push(cloneDeep(formData.value[key]))
        }
        answerList = answerList.map((item) => {
          let obj = {
            id: item.id,
            question_type: item.question_type,
            file_url: item.question_type === 5 || item.question_type === 6 ? item.value : [],
            text: item.question_type === 4 ? item.value : "",
            choices: [] as any[]
          }
          obj.choices = setChoices(item)
          // 处理一下choices
          if ([0, 1, 2].includes(item.question_type) && obj.choices.length) {
            obj.choices = obj.choices.map((choice: any) => {
              let itemIn =
                item.choices && item.choices.length ? item.choices.find((itemIn: any) => itemIn.id === choice) : null
              if (!itemIn) {
                return {
                  id: null,
                  other_content: "",
                  score: item.question_type === 2 ? item.value : 0
                }
              } else {
                return {
                  id: choice,
                  other_content: [0, 1].includes(item.question_type) ? itemIn.other_value || "" : "",
                  score: item.question_type === 2 ? item.value : 0
                }
              }
            })
          }
          return obj
        })
        try {
          const [err, res]: any[] = await to(
            route.query.type === "qrCode"
              ? apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAnswerPost({
                  id: route.query.id,
                  questions: [...answerList],
                  supplier_id:
                    questionnaireData.value && questionnaireData.value.questionnaire_type !== "common"
                      ? supplierId.value
                      : undefined
                })
              : apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAccountAnswerPost({
                  id: route.query.id,
                  questions: [...answerList],
                  supplier_id:
                    questionnaireData.value && questionnaireData.value.questionnaire_type !== "common"
                      ? supplierId.value
                      : undefined
                })
          )
          if (err) return
          if (res.code === 0) {
            ElMessage.success("提交成功")
            isFinish.value = true
          } else {
            ElMessage.error(res.msg || "提交失败，请稍后重试")
          }
        } catch (error) {
          console.error("提交问卷错误:", error)
          ElMessage.error("提交失败，请稍后重试")
        }
      }
    })
  } catch (error) {
    console.error("提交问卷错误:", error)
    ElMessage.error("提交失败，请稍后重试")
  }
}

// 生命周期钩子
onMounted(async () => {
  let id: any = route.query.id
  getSupplierList(id)
  checkDeviceType()
  console.log("route.query.type", route.query.type)
  if (route.query.type === "qrCode") {
    await getQuestionnaireListNotLogin(id)
  } else {
    await getQuestionnaireList(id)
  }

  if (questionnaireData.value && questionnaireData.value.name) {
    title.value = questionnaireData.value.name
  }

  if (Object.keys(questionnaireData.value).length && questionnaireData.value.questionnaire_type !== "common") {
    dialogVisible.value = true
  }
  isDataReady.value = true
})

const checkSupplierId = () => {
  if (supplierId.value) {
    dialogVisible.value = false
  } else {
    ElMessage.error("请选择供应商")
  }
}

const customImage = ref(CompletedImage)

// 图片预览
import type { UploadProps } from "element-plus"
const imageVisible = ref(false)
const imageList = ref<any[]>([])
// 查看照片
const handlerShowPhoto: UploadProps["onPreview"] = (url: any) => {
  if (url) {
    imageList.value = [url!]
    imageVisible.value = true
  } else {
    ElMessage.error("暂无图片")
  }
}
const deleteImg = (index: number, data: any) => {
  formData.value[data.id].value.splice(index, 1)
  formData.value[data.id].fileList.splice(index, 1)
}

const deleteFile = (data: any, file: any, fileList: any) => {
  console.log("点删除前长这样", formData.value[data.id].fileList)
  let index = fileList.indexOf(file)
  formData.value[data.id].fileList.splice(index, 1)
  console.log("点完删除长这样", formData.value[data.id].fileList)
}
</script>

<template>
  <div class="bg-white min-h-dvh">
    <el-row>
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <el-row justify="center">
          <el-col
            :xs="24"
            :sm="20"
            :md="16"
            :lg="8"
            :xl="6"
            v-if="!isFinish"
            :class="['bg-white', isMobile ? '' : 'm-t-20px', 'border-rd-8px', 'p-20px']"
          >
            <el-form
              ref="previewForm"
              :model="formData"
              label-position="top"
              v-loading="loading"
              v-if="isDataReady"
              class="flex flex-col flex-items-center"
            >
              <!-- 问卷标题 -->
              <div class="flex flex-col flex-items-center">
                <div class="font-size-18px font-bold">{{ title }}</div>
                <div class="font-size-14px m-t-10px">{{ questionnaireData.overview }}</div>
                <!-- {{ route.query.type }} -->
              </div>

              <!-- 问题列表 -->
              <div class="w-100%">
                <div v-for="(item, index) in questionnaireList" :key="index">
                  <el-form-item
                    :label="`${index + 1}. ${item.caption}`"
                    :prop="`${item.id}.value`"
                    :rules="computedRules(item)"
                  >
                    <!-- 单选 -->
                    <template v-if="item.question_type === 0 && formData[item.id]">
                      <el-radio-group v-model="formData[item.id].value" :disabled="isFinish">
                        <div class="flex flex-col">
                          <div v-for="(itemIn, indexIn) in item.choices" :key="indexIn" class="flex flex-col">
                            <el-radio :label="itemIn.id">
                              {{ itemIn.description }}
                            </el-radio>
                            <div class="m-l-20px w-300px">
                              <el-input
                                v-if="itemIn.other_content"
                                type="textarea"
                                :autosize="{ minRows: 3 }"
                                v-model="itemIn.other_value"
                                :placeholder="itemIn.other_content"
                                :disabled="isFinish"
                                :maxlength="100"
                              />
                            </div>
                          </div>
                        </div>
                      </el-radio-group>
                    </template>

                    <!-- 多选 -->
                    <template v-else-if="item.question_type === 1 && formData[item.id]">
                      <el-checkbox-group v-model="formData[item.id].value" :disabled="isFinish">
                        <div class="flex flex-col">
                          <div v-for="(itemIn, indexIn) in item.choices" :key="indexIn">
                            <el-checkbox :label="itemIn.id">{{ itemIn.description }}</el-checkbox>
                            <div class="m-l-20px w-300px">
                              <el-input
                                v-if="itemIn.other_content"
                                type="textarea"
                                :autosize="{ minRows: 3 }"
                                v-model="itemIn.other_value"
                                :placeholder="itemIn.other_content"
                                :disabled="isFinish"
                                :maxlength="100"
                              />
                            </div>
                          </div>
                        </div>
                      </el-checkbox-group>
                    </template>

                    <!-- 评分 -->
                    <template v-else-if="item.question_type === 2 && formData[item.id]">
                      <div class="mark-topic-content">
                        <div class="mark-topic-content-item">
                          <div class="mark-topic-content-item-top">
                            <div class="point">1</div>
                            <div class="point">{{ item.top_score }}</div>
                          </div>
                          <div class="mark-topic-content-item-bottom">
                            <div
                              v-for="(itemIn, index) in item.top_score"
                              :key="itemIn"
                              :class="[
                                isMobile ? 'mark-mobile' : 'mark-pc',
                                index + 1 <= formData[item.id].value ? 'selectScore' : '',
                                isFinish ? 'disabled' : ''
                              ]"
                              @click="selectScore(item, itemIn)"
                            >
                              {{ itemIn }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>

                    <!-- 评价 -->
                    <template v-else-if="item.question_type === 3 && formData[item.id]">
                      <div class="flex flex-col">
                        <div v-for="(itemIn, indexIn) in item.choices" :key="indexIn">
                          <div class="flex flex-col flex-items-start flex-justify-start">
                            <div class="m-r-20px">{{ itemIn.description }}</div>
                            <el-rate
                              :colors="['#ff9b45', '#ff9b45', '#ff9b45']"
                              void-color="#b2b2b2"
                              gutter="20"
                              size="large"
                              v-model="itemIn.other_value"
                              @change="(value) => changeRate(item, itemIn, indexIn, value)"
                              :disabled="isFinish"
                            />
                          </div>
                        </div>
                      </div>
                    </template>

                    <!-- 填空 -->
                    <template v-else-if="item.question_type === 4 && formData[item.id]">
                      <el-input
                        v-model="formData[item.id].value"
                        type="textarea"
                        :autosize="{ minRows: 3 }"
                        placeholder="请输入"
                        :disabled="isFinish"
                        :maxlength="100"
                      />
                    </template>

                    <!-- 照片 -->
                    <template v-else-if="item.question_type === 5 && formData[item.id]">
                      <el-upload
                        v-loading="uploadingForImg"
                        element-loading-text="上传中"
                        class="upload-w"
                        ref="fileUpload"
                        :action="serverUrl"
                        :data="uploadParams"
                        v-model:file-list="formData[item.id].fileList"
                        :on-success="(response, file, fileList) => uploadSuccess(response, file, fileList, 'img', item)"
                        :before-upload="(file) => beforeUpload(file, 'img')"
                        :limit="item.upload_max_num"
                        :on-exceed="(files, uploadFiles) => handleExceed(item, files, uploadFiles)"
                        :multiple="false"
                        :headers="headersOpts"
                        :disabled="isFinish"
                        :show-file-list="false"
                      >
                        <div class="flex w-100% flex-wrap">
                          <div
                            class="m-5px"
                            v-for="(itemIn, indexIn) in formData[item.id].value"
                            :key="indexIn"
                            style="position: relative"
                          >
                            <img
                              style="width: 120px; height: 120px"
                              :src="itemIn"
                              @click.stop="handlerShowPhoto(itemIn)"
                            />
                            <div class="triangle">
                              <el-icon
                                style="position: absolute; top: -14px; right: -14px; color: #fff; z-index: 999"
                                @click.stop="deleteImg(indexIn, item)"
                                ><Delete
                              /></el-icon>
                            </div>
                          </div>
                          <div
                            class="flex flex-col flex-items-center flex-justify-center upload-border m-5px"
                            v-if="formData[item.id].fileList.length < item.upload_max_num"
                          >
                            <el-icon class="m-t-15px" size="28" color="#dcdfe6"><Plus /></el-icon>
                            <div class="el-upload__tip">[不超过5M]</div>
                          </div>
                        </div>
                      </el-upload>
                    </template>

                    <!-- 文件 -->
                    <template v-else-if="formData[item.id]">
                      <el-upload
                        v-loading="uploadingForFile"
                        element-loading-text="上传中"
                        class="upload-w"
                        ref="fileUpload"
                        :action="serverUrl"
                        :data="uploadParams"
                        v-model:file-list="formData[item.id].fileList"
                        :on-success="
                          (response, file, fileList) => uploadSuccess(response, file, fileList, 'file', item)
                        "
                        :before-upload="(file) => beforeUpload(file, 'file')"
                        :limit="item.upload_max_num"
                        :on-exceed="(files, uploadFiles) => handleExceed(item, files, uploadFiles)"
                        :on-remove="(uploadFile, uploadFiles) => deleteFile(item, uploadFile, uploadFiles)"
                        :multiple="false"
                        :show-file-list="true"
                        :headers="headersOpts"
                        :disabled="isFinish"
                      >
                        <div class="flex flex-items-center flex-justify-center">
                          <el-button
                            :type="'primary'"
                            icon="Plus"
                            :disabled="formData[item.id].fileList.length >= item.upload_max_num"
                            >添加文件</el-button
                          >
                          <div class="el-upload__tip m-l-10px">[不超过5M]</div>
                        </div>
                      </el-upload>
                    </template>
                  </el-form-item>
                </div>
              </div>

              <!-- 提交按钮 -->
              <div class="m-b-20px">
                <el-button :type="'primary'" size="large" @click="submit" :disabled="isFinish || canSubmit">
                  提交问卷
                </el-button>
              </div>
            </el-form>

            <div class="flex flex-items-center flex-justify-center p-b-20px font-size-14px">
              本次问卷以{{ questionnaireData.commit_type === "real_name" ? "实名" : "匿名" }}形式提交
            </div>
          </el-col>
          <el-col v-else>
            <el-empty :image="customImage" description="您的答卷已提交，感谢您的参与！" />
          </el-col>
        </el-row>
      </el-col>
    </el-row>

    <el-dialog v-model="dialogVisible" title="选择供应商" width="300" :show-close="false" :close-on-click-modal="false">
      <el-select v-model="supplierId" placeholder="请选择供应商" size="large">
        <el-option v-for="(item, index) in supplierList" :key="index" :label="item.name" :value="item.id" />
      </el-select>
      <template #footer>
        <div class="dialog-footer">
          <el-button :type="'primary'" @click="checkSupplierId" :disabled="!supplierId"> 确认 </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
  </div>
</template>

<style lang="scss" scoped>
// 变量定义
$primary-color: #409eff;
$success-color: #79c32a;
$border-color: #ebeef5;
$text-primary: #303133;
$text-regular: #606266;
$bg-color: #f5f7fa;
$container-width: 800px;
$spacing-base: 8px;

// 混合器
@mixin flex-center {
  display: flex;
  align-items: center;
}

@mixin hover-effect {
  transition: all 0.3s ease;
  &:hover {
    border-color: $primary-color;
    box-shadow: 0 0 8px rgba($primary-color, 0.1);
  }
}

@mixin section-padding {
  padding: $spacing-base * 3 $spacing-base * 2.5;
}

// 重置最后一个元素的边距
@mixin reset-last-margin {
  &:last-child {
    margin-bottom: 0;
  }
}

.mark-topic {
  width: 100%;
  margin-top: $spacing-base * 2;

  &-content {
    width: 100%;
    margin-top: $spacing-base * 2;

    &-item {
      width: 100%;
      border: 1px solid $border-color;
      border-radius: 6px;
      padding: $spacing-base * 2;

      &-top {
        display: flex;
        justify-content: space-between;
        padding-bottom: $spacing-base;
        border-bottom: 1px solid $border-color;

        .point {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: $text-regular;
        }
      }

      &-bottom {
        margin-top: $spacing-base;
        display: flex;
        justify-content: space-between;
        flex-wrap: nowrap;
        gap: $spacing-base;

        .mark-pc {
          width: 24px;
          height: 24px;
          border: 1px solid $border-color;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: $text-regular;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: $primary-color;
            color: $primary-color;
          }

          &.selectScore {
            background-color: $primary-color;
            color: #fff;
            border-color: $primary-color;
          }
        }

        .mark-mobile {
          width: 24px;
          height: 24px;
          border: 1px solid $border-color;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: $text-regular;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: $primary-color;
            color: $primary-color;
          }

          &.selectScore {
            background-color: $primary-color;
            color: #fff;
            border-color: $primary-color;
          }
        }
      }
    }
  }
}

.mobile-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-items: flex-start;
  &-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-items: flex-start;
  }
  &-content {
    width: 100%;
  }
  &-footer {
    width: 100%;
  }
}

.upload-demo {
  &-button {
    border: 2px dotted #d2d5da;
    width: 150px;
    height: 150px;
    border-radius: 8px;
    background-color: #fafafa;
  }
}

.upload-border {
  border: 1px dotted #dcdfe6;
  width: 120px;
  height: 120px;
  border-radius: 8px;
}

.triangle {
  width: 0px;
  height: 0px;
  position: absolute;
  top: 0px;
  right: 0px;
  border-top: 20px solid $primary-color;
  border-right: 20px solid $primary-color;
  border-bottom: 20px solid transparent;
  border-left: 20px solid transparent;
}
</style>
