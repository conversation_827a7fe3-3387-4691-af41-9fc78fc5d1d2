<template>
  <div class="preview-wrapper">
    <div class="preview-container">
      <el-form
        ref="previewForm"
        :model="formData"
        label-position="top"
        element-loading-text="加载中..."
        v-loading="loading"
      >
        <!-- 问卷标题 -->
        <div class="preview-header">
          <h2>{{ questionnaireData.name }}</h2>
          <p class="overview">{{ questionnaireData.overview }}</p>
        </div>

        <!-- 问题列表 -->
        <div class="preview-content">
          <div v-for="(question, index) in questionnaireData.questions" :key="index" class="question-item">
            <el-form-item
              :label="`${index + 1}. ${question.caption}`"
              :prop="`answers.${index}`"
              :rules="{ required: question.required, message: '此题为必答题', trigger: 'change' }"
            >
              <!-- 单选题 -->
              <template v-if="question.question_type === 0">
                <el-radio-group v-model="formData.answers[index]">
                  <div class="flex flex-col">
                    <div v-for="(choice, choiceIndex) in question.choices" :key="choiceIndex">
                      <el-radio
                        :label="choiceIndex"
                        disabled
                        :style="choice.type === 'default' ? {} : { display: 'inline-block' }"
                      >
                        {{ choice.description }}
                      </el-radio>
                      <div class="m-l-20px w-350px">
                        <el-input
                          v-if="choice.type !== 'default'"
                          type="textarea"
                          :autosize="{ minRows: 3 }"
                          :placeholder="choice.other_content"
                          disabled
                        />
                      </div>
                    </div>
                  </div>
                </el-radio-group>
              </template>

              <!-- 多选题 -->
              <template v-if="question.question_type === 1">
                <el-checkbox-group
                  v-model="formData.answers[index]"
                  :min="question.least_choose_count || 0"
                  @change="(val) => handleChange(val, index)"
                >
                  <div class="flex flex-col">
                    <div v-for="(choice, choiceIndex) in question.choices" :key="choiceIndex">
                      <el-checkbox
                        :label="choiceIndex"
                        disabled
                        :style="choice.type === 'default' ? {} : { display: 'inline-block' }"
                      >
                        {{ choice.description }}
                      </el-checkbox>
                      <div class="m-l-20px w-350px">
                        <el-input
                          v-if="choice.type !== 'default'"
                          type="textarea"
                          :autosize="{ minRows: 3 }"
                          :placeholder="choice.other_content"
                          disabled
                        />
                      </div>
                    </div>
                  </div>
                </el-checkbox-group>
              </template>

              <!-- 评分题 -->
              <template v-if="question.question_type === 2">
                <div class="mark-topic-content">
                  <div class="mark-topic-content-item">
                    <div class="mark-topic-content-item-top">
                      <div class="point">1</div>
                      <div class="point">{{ question.top_score }}</div>
                    </div>
                    <div class="mark-topic-content-item-bottom">
                      <div v-for="item in question.top_score" :key="item" :class="['mark', 'selectScore']">
                        {{ item }}
                      </div>
                    </div>
                  </div>
                </div>
              </template>

              <!-- 评价题 -->
              <template v-if="question.question_type === 3">
                <div v-for="(choice, choiceIndex) in question.choices" :key="choiceIndex" class="evaluate-item">
                  <span class="evaluate-label">{{ choice.description }}</span>
                  <el-rate v-model="formData.answers[index][choiceIndex]" :max="question.top_score" disabled />
                </div>
              </template>

              <!-- 填空题 -->
              <template v-if="question.question_type === 4">
                <el-input
                  v-model="formData.answers[index]"
                  type="textarea"
                  :autosize="{ minRows: 3 }"
                  placeholder="请输入您的答案"
                  disabled
                />
              </template>

              <!-- 文件上传 -->
              <template v-if="question.question_type === 6">
                <el-upload class="upload-demo" action="https://jsonplaceholder.typicode.com/posts/" multiple disabled>
                  <div class="flex flex-items-center flex-justify-start">
                    <el-button :type="'primary'" icon="Plus" :disabled="true">添加文件</el-button>
                    <div class="el-upload__tip m-l-10px">[不超过5M]</div>
                  </div>
                </el-upload>
              </template>

              <!-- 图片上传 -->
              <template v-if="question.question_type === 5">
                <el-upload
                  action="https://jsonplaceholder.typicode.com/posts/"
                  :on-preview="handlePictureCardPreview"
                  :on-remove="handleRemove"
                  disabled
                >
                  <div class="flex flex-col flex-items-center flex-justify-center upload-border m-5px">
                    <el-icon class="m-t-15px" size="28" color="#dcdfe6"><Plus /></el-icon>
                    <div class="el-upload__tip">[不超过5M]</div>
                  </div>
                </el-upload>
              </template>
            </el-form-item>
          </div>
        </div>

        <!-- 添加提交按钮 -->
        <div class="preview-footer">
          <el-button type="primary" size="large">提交问卷</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue"
import { getSessionStorage } from "@/utils/storage"

const previewForm = ref(null)
const loading = ref(false)
const dialogImageUrl = ref("")
const dialogVisible = ref(false)

const questionnaireData = reactive({
  name: "问卷标题",
  overview: "问卷概述",
  questions: []
})

const formData = reactive({
  answers: []
})

onMounted(() => {
  const data = getSessionStorage("SurveyData")
  Object.assign(questionnaireData, JSON.parse(data))
  console.log("this.questionnaireData", questionnaireData)
  // 初始化答案数组
  initAnswers()
})

// 确保多选题的值始终是数组
const handleChange = (value, index) => {
  if (Array.isArray(value)) {
    formData.answers[index] = value
  }
}

const initAnswers = () => {
  // Vue3 不再需要 Vue.set 来确保响应性
  formData.answers = questionnaireData.questions.map((question) => {
    let answer
    if (question.question_type === 1) {
      // 多选题返回空数组
      answer = []
    } else if (question.question_type === 3) {
      // 评价题返回空对象
      answer = {}
      question.choices.forEach((_, choiceIndex) => {
        answer[choiceIndex] = 0
      })
    } else {
      // 其他题型返回空值
      answer = ""
    }
    return answer
  })
  console.log("初始化答案数组：", formData.answers)
}

const handleRemove = (file, fileList) => {
  console.log(file, fileList)
}

const handlePictureCardPreview = (file) => {
  dialogImageUrl.value = file.url
  dialogVisible.value = true
}
</script>

<style lang="scss" scoped>
// 变量定义
$primary-color: #409eff;
$border-color: #ebeef5;
$text-primary: #303133;
$text-regular: #606266;
$bg-color: #f5f7fa;
$container-width: 800px;
$spacing-base: 8px;

// 混合器
@mixin flex-center {
  display: flex;
  align-items: center;
}

@mixin hover-effect {
  transition: all 0.3s ease;
  &:hover {
    border-color: $primary-color;
    box-shadow: 0 0 8px rgba($primary-color, 0.1);
  }
}

@mixin section-padding {
  padding: $spacing-base * 3 $spacing-base * 2.5;
}

// 重置最后一个元素的边距
@mixin reset-last-margin {
  &:last-child {
    margin-bottom: 0;
  }
}

.preview-wrapper {
  min-height: 100vh;
  background-color: $bg-color;
  padding: 20px 0;
  margin: 0;
}

.preview-container {
  max-width: $container-width;
  margin: 0 auto;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(#000, 0.1);
  border-radius: 10px;

  .preview-header {
    text-align: center;
    margin-bottom: $spacing-base * 3;
    @include section-padding;

    h2 {
      margin: 0 0 $spacing-base * 1.5;
      color: $text-primary;
      font-size: 24px;
      font-weight: 500;
    }

    .overview {
      color: $text-regular;
      font-size: 14px;
      margin: 0;
      line-height: 1.6;
    }
  }

  .preview-content {
    @include section-padding;

    .question-item {
      height: 100%;
      margin-bottom: $spacing-base * 3;
      padding: $spacing-base * 2.5;
      border: 1px solid $border-color;
      border-radius: 4px;
      @include hover-effect;
      @include reset-last-margin;

      :deep(.el-form-item__label) {
        font-size: 16px;
        font-weight: 500;
        color: $text-primary;
        margin-bottom: $spacing-base * 2;
      }
    }
  }

  .evaluate-item {
    @include flex-center;
    margin-bottom: $spacing-base * 1.5;
    @include reset-last-margin;

    .evaluate-label {
      min-width: 120px;
      margin-right: $spacing-base * 2;
      color: $text-regular;
    }
  }

  .preview-footer {
    text-align: center;
    margin-top: $spacing-base * 4;
    @include section-padding;

    .el-button {
      width: $spacing-base * 25; // 200px
      height: $spacing-base * 5.5; // 44px
      font-size: 16px;
    }
  }
}

.mark-topic {
  width: 100%;
  margin-top: $spacing-base * 2;

  &-content {
    width: 100%;
    margin-top: $spacing-base * 2;

    &-item {
      width: 100%;
      border: 1px solid $border-color;
      border-radius: 6px;
      padding: $spacing-base * 2;

      &-top {
        display: flex;
        justify-content: space-between;
        padding-bottom: $spacing-base;
        border-bottom: 1px solid $border-color;

        .point {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: $text-regular;
        }
      }

      &-bottom {
        margin-top: $spacing-base;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: $spacing-base;

        .mark {
          width: 32px;
          height: 32px;
          border: 1px solid $border-color;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: $text-regular;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: $primary-color;
            color: $primary-color;
          }

          &.selectScore {
            background-color: $primary-color;
            color: #fff;
            border-color: $primary-color;
          }
        }
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: #{$container-width + $spacing-base * 4}) {
  .preview-container {
    margin: 0 $spacing-base * 2;
  }
}

@media screen and (max-width: 480px) {
  .preview-container {
    margin: 0;

    .preview-content {
      padding: $spacing-base * 2;

      .question-item {
        padding: $spacing-base * 2;
      }
    }

    .evaluate-item {
      flex-direction: column;
      align-items: flex-start;

      .evaluate-label {
        width: 100%;
        margin-bottom: $spacing-base;
      }
    }

    .mark-topic {
      &-content {
        &-item {
          &-bottom {
            justify-content: center;
          }
        }
      }
    }
  }
}

.upload-demo {
  &-button {
    border: 2px dotted #d2d5da;
    width: 150px;
    height: 150px;
    border-radius: 8px;
    background-color: #fafafa;
  }
}
.upload-border {
  border: 1px dotted #dcdfe6;
  width: 120px;
  height: 120px;
  border-radius: 8px;
}
</style>
