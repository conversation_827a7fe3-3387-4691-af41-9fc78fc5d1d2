<template>
  <div class="scence-manage container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="ps-flex col-center">
          <el-tooltip
            effect="dark"
            placement="top-start"
            content="人工上传的记录会实时更新，系统记录的数据凌晨更新前一天的数据进行汇总。"
          >
            <img :src="IcQuestionBlack" class="w-32px h-32px" />
          </el-tooltip>
          <div class="m-l-20">收入总金额：￥{{ totalIncome }}</div>
          <div class="m-l-20">支出总金额：￥{{ totalOutline }}</div>
        </div>
        <div class="table-button">
          <el-button
            type="primary"
            @click="goToExport"
            v-permission="['background_fund_supervision.business_report.income_statistics_list_export']"
            >导出</el-button
          >
          <el-button type="primary" @click="gotoPrint">打印</el-button>
          <!-- <el-button type="primary" plain @click="goToExport">报表设置</el-button> -->
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #featureNameList="{ row }">
              {{ getFeatureName(row.feature_name_list) }}
            </template>

            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowPhoto(row)" type="primary"> 查看 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted, watch } from "vue"
import { SEARCH_FORM_SETTING_STATISTICS, TABLE_SETTING_STATISTICS } from "./constants"
import {
  apiBackgroundFundSupervisionFinanceReportIncomeStatisticsListPost,
  apiBackgroundFundSupervisionFinanceReportIncomeStatisticsListExportPost,
  apiBackgroundFundSupervisionFinanceReportIncomeStatisticsTotalPost
} from "@/api/supervision"
import { ElMessage } from "element-plus"
import IcQuestionBlack from "@/assets/layouts/ic_question_black.png"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { exportHandle } from "@/utils/exportExcel"
import { useRouter, useRoute } from "vue-router"
import { divide, formatQueryParams } from "@/utils/index"
import { setLocalStorage } from "@/utils/storage"
import { formatTimeByFormat } from "@/utils/date"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)
const router = useRouter()
const route = useRoute()

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// table数据
const tableData = ref([])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_STATISTICS))
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_STATISTICS))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
const totalIncome = ref()
const totalOutline = ref()
// 导出
const importType = "IncomeExpenseStatistics"

// 搜索
const changeSearch = (model: any, type: any, itemType: string) => {
  if (itemType === "org_id") {
    searchFormSetting.value.organization_ids.value = []
    searchFormSetting.value.organization_ids.orgsId = type
  }
  pageConfig.currentPage = 1
  getDataList()
  getTotalList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
  getTotalList()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 导出
const goToExport = () => {
  console.log("goToExport")
  const option = {
    type: importType,
    api: apiBackgroundFundSupervisionFinanceReportIncomeStatisticsListExportPost,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.total ? pageConfig.total : 9999
    }
  }
  exportHandle(option)
}

// 查看照片
const handlerShowPhoto = (row: any) => {
  console.log(row)
  imageList.value = [
    "https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/mapp_static/info/kefu.png",
    "https://h5-v4.debug.packertec.com/static/icons/tab_home_s.png"
  ]
  imageVisible.value = true
}
onMounted(() => {
  getQueryData()
  getDataList()
  getTotalList()
})

// 获取查询数据传参
const getQueryData = () => {
  const query = route.query || {}
  const type = query.type || ""
  const warnTime: any = query.warnTime || ""
  const orgsId: any = query.orgsId || ""
  if (type && type === "operate" && warnTime) {
    let preMonth = new Date(warnTime)
    // 设置上一个月的第一天
    const lastMonthStartDate = new Date(preMonth.getFullYear(), preMonth.getMonth() - 1, 1)
    // 获取上一个月的最后一天
    // 通过设置下一个月的第一天，然后减去一天来得到上一个月的最后一天
    const lastMonthEndDate = new Date(preMonth.getFullYear(), preMonth.getMonth(), 0)
    searchFormSetting.value.selecttime.value = [
      formatTimeByFormat(lastMonthStartDate, "YYYY-MM-DD"),
      formatTimeByFormat(lastMonthEndDate, "YYYY-MM-DD")
    ]
    searchFormSetting.value.org_id.value = orgsId ? [parseInt(orgsId)] : []
  }
}
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionFinanceReportIncomeStatisticsListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
// 获取汇总
const getTotalList = async () => {
  loading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionFinanceReportIncomeStatisticsTotalPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  loading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    const data: any = res.data || {}
    totalIncome.value = divide(data.consume_price_total || 0)
    totalOutline.value = divide(data.refund_price_total || 0)
  }
}
// 获取功能名称
const getFeatureName = (list: Array<string>) => {
  if (!list || !Array.isArray(list) || list.length === 0) {
    return ""
  }
  return list.join(";")
}

// 打印
const gotoPrint = () => {
  let tabbleSetting = cloneDeep(tableSetting.value)
  tabbleSetting = tabbleSetting.filter((item: any) => item.prop !== "img")
  setLocalStorage("print_setting", tabbleSetting)
  let collectList = [
    {
      label: "收入总金额：￥",
      value: totalIncome.value
    },
    {
      label: "支出总金额：￥",
      value: totalOutline.value
    }
  ]
  setLocalStorage("collect" + importType, collectList)
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: importType,
      print_title: "经营收支统计表",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiBackgroundFundSupervisionFinanceReportIncomeStatisticsListPost",
      params: JSON.stringify({
        ...formatQueryParams(searchFormSetting.value),
        page: 1,
        page_size: 9999
      }),
      isShowCollect: "1"
    }
  })
  window.open(href, "_blank")
}

watch(
  () => searchFormSetting.value.flow_type.value,
  (newValue) => {
    if (newValue) {
      let findItem = searchFormSetting.value.flow_type.dataList.find((item: any) => item.value === newValue)
      if (findItem) {
        let dataList = findItem.typeList || []
        searchFormSetting.value.account_type.disabled = false
        searchFormSetting.value.account_type.value = ""
        searchFormSetting.value.account_type.dataList = cloneDeep(dataList)
      }
    } else {
      searchFormSetting.value.account_type.value = ""
      searchFormSetting.value.account_type.disabled = true
    }
  }
)
</script>
<style lang="scss">
.scence-manage {
  padding: 0 20px;

  .el-popper {
    max-width: 300px;
  }
}
</style>
