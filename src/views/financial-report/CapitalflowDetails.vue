<template>
  <div class="scence-manage container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="table-button">
          <el-button
            type="primary"
            @click="goToExport"
            v-permission="['background_fund_supervision.finance_report.fund_water_export']"
            >导出</el-button
          >
          <el-button type="primary" @click="gotoPrint">打印</el-button>
          <!-- <el-button type="primary" plain @click="goToExport">报表设置</el-button> -->
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #featureNameList="{ row }">
              {{ getFeatureName(row.feature_name_list) }}
            </template>
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowPhoto(row)" type="primary"> 查看 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted, watch } from "vue"
import { SEARCH_FORM_CAPITAL_FLOW_DETAILS, TABLE_SETTING_CAPITAL_FLOW_DETAILS } from "./constants"
import {
  apiBackgroundFundSupervisionBusinessReportFundWaterListPost,
  apiBackgroundFundSupervisionBusinessReportFundWaterExportPost
} from "@/api/financial/index"
import { ElMessage } from "element-plus"
// import IcQuestionBlack from "@/assets/layouts/ic_question_black.png"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@//components/PsColumn/index.vue"
import { exportHandle } from "@/utils/exportExcel"
import { useRouter } from "vue-router"
// import { moneyThousandFormat, divide } from "@/utils/index"
import { setLocalStorage } from "@/utils/storage"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)
const router = useRouter()

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// table数据
const tableData = ref([])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_CAPITAL_FLOW_DETAILS))
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_CAPITAL_FLOW_DETAILS))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 收入总金额
// const incomeTotal = ref()
// 支出总金额
// const outlineTotal = ref()
// 导出
const importType = "CapitalflowDetails"

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value && data[key].value !== "" && data[key].value.length > 0) {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}
// 搜索
const changeSearch = async (model: any, type: any, itemType: string) => {
  console.log("changeSearch", model, type, itemType)
  if (itemType === "channel_org_ids") {
    searchFormSetting.value.org_ids.value = []
    searchFormSetting.value.org_ids.orgsId = type
  }
  pageConfig.currentPage = 1
  initData()
}
// 重置
const handlerReset = () => {
  initData()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  initData()
}

// 导出
const goToExport = () => {
  console.log("goToExport")
  const option = {
    type: importType,
    api: apiBackgroundFundSupervisionBusinessReportFundWaterExportPost,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    }
  }
  exportHandle(option)
}

// 查看照片
const handlerShowPhoto = (row: any) => {
  console.log(row)
  imageList.value = [
    "https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/mapp_static/info/kefu.png",
    "https://h5-v4.debug.packertec.com/static/icons/tab_home_s.png"
  ]
  imageVisible.value = true
}
const initData = () => {
  getDataList()
}

// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionBusinessReportFundWaterListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 获取功能名称
const getFeatureName = (list: Array<string>) => {
  if (!list || !Array.isArray(list) || list.length === 0) {
    return ""
  }
  return list.join(";")
}

// 打印
const gotoPrint = () => {
  let tabbleSetting = cloneDeep(tableSetting.value)
  tabbleSetting = tabbleSetting.filter((item: any) => item.prop !== "img")
  setLocalStorage("print_setting", tabbleSetting)
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: importType,
      print_title: "资金流水明细",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiBackgroundFundSupervisionBusinessReportFundWaterListPost",
      params: JSON.stringify({
        ...formatQueryParams(searchFormSetting.value),
        page: 1,
        page_size: 9999
      })
    }
  })
  window.open(href, "_blank")
}
watch(
  () => searchFormSetting.value.flow_type_list.value,
  (newValue) => {
    console.log("watch searchFormSetting", newValue)
    let type: any[] = newValue
    let findItemList = searchFormSetting.value.flow_type_list.dataList.filter((item: any) => type.includes(item.value))
    if (findItemList) {
      let newList: any[] = []
      findItemList.forEach((subItem) => {
        newList = newList.concat(subItem.typeList)
      })
      searchFormSetting.value.data_type_list.dataList = cloneDeep(newList)
      searchFormSetting.value.data_type_list.value = []
      searchFormSetting.value.data_type_list.disabled = false
    }
  }
)
onMounted(() => {
  initData()
})
</script>
<style lang="scss">
.scence-manage {
  padding: 0 20px;

  .el-popper {
    max-width: 300px;
  }
}
</style>
