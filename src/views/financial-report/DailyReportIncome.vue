<template>
  <div class="daily-report container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="flex items-center">
          <el-tooltip placement="top-start">
            <template #content> {{ tipTxt }} </template>
            <img :src="IcQuestionBlack" class="w-32px h-32px" />
          </el-tooltip>
          <div class="m-l-10px" v-loading="totalLoading">
            <span>合计收入：¥{{ incomeTotal }}，</span>
            <span>其中营业性收入：¥{{ inPrice }}，</span>
            <span>非营业性收入：¥{{ nonInPrice }}，</span>
            <span>合计支出：¥{{ outlineTotal }}，</span>
            <span>其中营业性支出：¥{{ outlineInPrice }}，</span>
            <span>非营业性支出：¥{{ outlineNonInPrice }}</span>
            <!-- <span>营业性收入占比：{{ incomePercent }}，</span>
            <span>非营业性收入占比：{{ outlinePercent }}</span> -->
          </div>
        </div>
        <div class="table-button">
          <el-button
            type="primary"
            @click="goToExport"
            v-permission="['background_fund_supervision.finance_report.fund_day_report_export']"
            >导出</el-button
          >
          <el-button type="primary" class="ps-origin-btn-light" @click="gotoPrint">打印</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowPhoto(row)" type="primary"> 查看 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import { SEARCH_FORM_DAILY_INCOME_REPORT, TABLE_SETTING_DAILY_INCOME_REPORT } from "./constants"
import {
  apiBackgroundFundSupervisionFinanceReportFundDayReportListPost,
  apiBackgroundFundSupervisionFinanceReportFundDayReportTotalPost,
  apiBackgroundFundSupervisionFinanceReportFundDayReportExportPost
} from "@/api/financial"
import { ElMessage } from "element-plus"
import IcQuestionBlack from "@/assets/layouts/ic_question_black.png"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { exportHandle } from "@/utils/exportExcel"
import { moneyThousandFormat } from "@/utils/index"
import { divide } from "@/utils/index"
import { setLocalStorage } from "@/utils/storage"
import { useRouter } from "vue-router"
const router = useRouter()
const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])
// 数据提示
const tipTxt = "核算数据会根据上传数据进行变更，所以会导致历史数据变更，数据统计截止到前一天。"

// table数据
const tableData = ref<Array<any>>([])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_DAILY_INCOME_REPORT))
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_DAILY_INCOME_REPORT))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// loading
const totalLoading = ref(false)
// 收入总金额
const incomeTotal = ref("0")
// 营业性收入
const inPrice = ref("0")
// 非营业性收入
const nonInPrice = ref("0")

// 支出总金额
const outlineTotal = ref()
// 营业性支出
const outlineInPrice = ref("0")
// 非营业性支出
const outlineNonInPrice = ref("0")
// 营业收入占比
const incomePercent = ref("0")
// 非营业收入占比
const outlinePercent = ref("0")

// 导出
const importType = "DailyReportIncomeExport"

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (
      data[key].value !== null &&
      data[key].value !== "" &&
      data[key].value !== "全部" &&
      data[key].value.length > 0
    ) {
      if (key !== "select_time") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}
// 搜索
const changeSearch = async () => {
  pageConfig.currentPage = 1
  initData()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  initData()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  initData()
}

// 导出
const goToExport = () => {
  console.log("goToExport")
  const option = {
    type: importType,
    api: apiBackgroundFundSupervisionFinanceReportFundDayReportExportPost,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    }
  }
  exportHandle(option)
}

// 查看照片
const handlerShowPhoto = (row: any) => {
  console.log(row)
  imageList.value = [
    "https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/mapp_static/info/kefu.png",
    "https://h5-v4.debug.packertec.com/static/icons/tab_home_s.png"
  ]
  imageVisible.value = true
}
onMounted(() => {
  initData()
})
// 初始化数据
const initData = () => {
  getDataList()
  getTotal()
}
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionFinanceReportFundDayReportListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    let results = data.results || []
    tableData.value = cloneDeep(results)
    pageConfig.total = data.count
  } else {
    ElMessage.error(res.msg)
  }
}
// 获取汇总总数据
const getTotal = async () => {
  totalLoading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionFinanceReportFundDayReportTotalPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  totalLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    console.log("getTotal", res)
    let data = res.data || {}
    incomeTotal.value = data.in_total ? moneyThousandFormat(divide(data.in_total)) : "0"
    inPrice.value = data.in_price ? moneyThousandFormat(divide(data.in_price)) : "0"
    nonInPrice.value = data.non_in_price ? moneyThousandFormat(divide(data.non_in_price)) : "0"
    outlineTotal.value = data.out_total ? moneyThousandFormat(divide(data.out_total)) : "0"
    outlineInPrice.value = data.out_price ? moneyThousandFormat(divide(data.out_price)) : "0"
    outlineNonInPrice.value = data.non_out_price ? moneyThousandFormat(divide(data.non_out_price)) : "0"
    incomePercent.value = data.in_price_rate
    outlinePercent.value = data.non_in_price_rate
  } else {
    ElMessage.error(res.msg)
  }
}
// 获取合计列表
const getCollectList = () => {
  let collectList = [
    {
      label: "合计收入：¥",
      value: incomeTotal.value
    },
    {
      label: "其中营业性收入：¥",
      value: inPrice.value
    },
    {
      label: "非营业性收入：¥",
      value: nonInPrice.value
    },
    {
      label: "合计支出：¥",
      value: outlineTotal.value
    },
    {
      label: "其中营业性支出：¥",
      value: outlineInPrice.value
    },
    {
      label: "非营业性支出：",
      value: outlineNonInPrice.value
    }
  ]
  return collectList
}
// 打印
const gotoPrint = () => {
  let tabbleSetting = cloneDeep(tableSetting.value)
  tabbleSetting = tabbleSetting.filter((item: any) => item.prop !== "img")
  setLocalStorage("print_setting", tabbleSetting)
  setLocalStorage("collect" + importType, getCollectList())
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: importType,
      print_title: "资金收入日报表",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiBackgroundFundSupervisionFinanceReportFundDayReportListPost",
      params: JSON.stringify({
        ...formatQueryParams(searchFormSetting.value),
        page: 1,
        page_size: 9999
      }),
      isShowCollect: "1"
    }
  })
  window.open(href, "_blank")
}
</script>
<style lang="scss">
.daily-report {
  padding: 0 20px;

  .el-popper {
    max-width: 300px;
  }
}
</style>
