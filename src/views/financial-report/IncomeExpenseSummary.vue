<template>
  <div class="scence-manage container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="ps-flex col-center">
          <el-tooltip
            effect="dark"
            placement="top-start"
            content="核算数据会根据上传数据进行变更，所以会导致历史数据变更，数据截止到前一天。收支汇总数据来源收支统计表。"
          >
            <img :src="IcQuestionBlack" class="w-32px h-32px" />
          </el-tooltip>
          <div class="m-l-20">收入总金额：￥{{ totalIncome }}</div>
          <div class="m-l-20">支出总金额：￥{{ totalOutline }}</div>
          <!-- <div class="m-l-20">总利润：￥{{ totalProfit }}</div>
          <div class="m-l-20">总利润率：{{ totalprofitPercet !== "--" ? totalprofitPercet + "%" : "--" }}</div> -->
        </div>
        <div class="table-button">
          <el-button
            type="primary"
            @click="goToExport"
            v-permission="['background_fund_supervision.business_report.income_summary_list_export']"
            >导出</el-button
          >
          <el-button type="primary" @click="gotoPrint">打印</el-button>
          <!-- <el-button type="primary" plain @click="goToExport">报表设置</el-button> -->
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #featureNameList="{ row }">
              {{ getFeatureName(row.feature_name_list) }}
            </template>
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowPhoto(row)" type="primary"> 查看 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted, computed } from "vue"
import { SEARCH_FORM_SETTING_CANTEEN_MONTH, TABLE_SETTING_SUMMARY } from "./constants"
import {
  apiBackgroundFundSupervisionFinanceReportIncomeSummaryListPost,
  apiBackgroundFundSupervisionFinanceReportIncomeSummaryListExportPost,
  apiBackgroundFundSupervisionFinanceReportIncomeSummaryTotalPost
} from "@/api/supervision"
import { ElMessage } from "element-plus"
import IcQuestionBlack from "@/assets/layouts/ic_question_black.png"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { exportHandle } from "@/utils/exportExcel"
import { useRouter } from "vue-router"
import { moneyThousandFormat, moneyThousandNotFormat, divide } from "@/utils/index"
import { setLocalStorage } from "@/utils/storage"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)
const router = useRouter()

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// table数据
const tableData = ref([])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_CANTEEN_MONTH))
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_SUMMARY))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
const totalIncome = ref()
const totalOutline = ref()
const totalProfit = computed(() => {
  console.log("totalIncome", totalIncome.value, "totalOutline", totalOutline.value)
  let income = moneyThousandNotFormat(totalIncome.value)
  let outline = moneyThousandNotFormat(totalOutline.value)
  return (income - outline).toFixed(2)
})
const totalprofitPercet = computed(() => {
  let income = moneyThousandNotFormat(totalIncome.value)
  let outline = moneyThousandNotFormat(totalOutline.value)
  console.log("income", income, "outline", outline)
  if (income === 0) {
    return "--"
  } else if (income || outline) {
    return (((income - outline) / income) * 100).toFixed(2)
  } else {
    return 0
  }
})
// 导出
const importType = "IncomeExpenseSummary"
// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
  getTotalList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
  getTotalList()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
  getTotalList()
}

// 导出
const goToExport = () => {
  console.log("goToExport")
  const option = {
    type: importType,
    api: apiBackgroundFundSupervisionFinanceReportIncomeSummaryListExportPost,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    }
  }
  exportHandle(option)
}
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0] + "-01"
        let endDate = new Date(data[key].value[1])
        // 设置为最后一天
        let endDateNum = new Date(endDate.getFullYear(), endDate.getMonth() + 1, 0).getDate()
        params.end_date = data[key].value[1] + "-" + endDateNum
      }
    }
  }
  return params
}

// 查看照片
const handlerShowPhoto = (row: any) => {
  console.log(row)
  imageList.value = [
    "https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/mapp_static/info/kefu.png",
    "https://h5-v4.debug.packertec.com/static/icons/tab_home_s.png"
  ]
  imageVisible.value = true
}
onMounted(() => {
  getDataList()
  getTotalList()
})
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionFinanceReportIncomeSummaryListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
// 获取功能权限列表
const getTotalList = async () => {
  loading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionFinanceReportIncomeSummaryTotalPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  loading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    console.log("res", res.data)
    const data: any = res.data || {}
    totalIncome.value = moneyThousandFormat(divide(data.consume_price_total || 0))
    totalOutline.value = moneyThousandFormat(divide(data.refund_price_total || 0))
  }
}

// 获取功能名称
const getFeatureName = (list: Array<string>) => {
  if (!list || !Array.isArray(list) || list.length === 0) {
    return ""
  }
  return list.join(";")
}

// 打印
const gotoPrint = () => {
  let tabbleSetting = cloneDeep(tableSetting.value)
  tabbleSetting = tabbleSetting.filter((item: any) => item.prop !== "img")
  setLocalStorage("print_setting", tabbleSetting)
  let collectList = [
    {
      label: "收入总金额：￥",
      value: totalIncome.value
    },
    {
      label: "支出总金额：￥",
      value: totalOutline.value
    }
  ]
  setLocalStorage("collect" + importType, collectList)
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: importType,
      print_title: "经营收支汇总表",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiBackgroundFundSupervisionFinanceReportIncomeSummaryListPost",
      params: JSON.stringify({
        ...formatQueryParams(searchFormSetting.value),
        page: 1,
        page_size: 9999
      }),
      isShowCollect: "1"
    }
  })
  window.open(href, "_blank")
}
</script>
<style lang="scss">
.scence-manage {
  padding: 0 20px;

  .el-popper {
    max-width: 300px;
  }
}
</style>
