import { getPreDate } from "@/utils/date"
export const OPERATING_INCOME_TYPE = [
  {
    label: "储值消费",
    value: "store_consume"
  },
  {
    label: "教师储值消费",
    value: "teacher_store_consume"
  },
  {
    label: "补贴消费",
    value: "subsidy_consume"
  },
  {
    label: "教师补贴消费",
    value: "teacher_subsidy_consume"
  },
  {
    label: "第三方消费",
    value: "third_consume"
  },
  {
    label: "其他收入",
    value: "operating_other_income"
  }
]

export const NON_OPERATING_INCOME_TYPE = [
  {
    label: "政府补助",
    value: "government_subsidy"
  },
  {
    label: "公益捐赠",
    value: "public_welfare_donation"
  },
  {
    label: "其他收入",
    value: "non_operating_other_income"
  }
]

export const OPERATING_COST_TYPE = [
  {
    label: "原材料成本",
    value: "raw_material_cost"
  },
  {
    label: "水电气成本",
    value: "utilities"
  },
  {
    label: "人工成本",
    value: "labor_cost"
  },
  {
    label: "其他成本",
    value: "operating_other_costs"
  }
]

export const NON_OPERATING_COST_TYPE = [
  {
    label: "其他成本",
    value: "non_operating_other_costs"
  }
]
// 资金流水 营业性收入
export const WARTER_INCOME_TYPE = [
  {
    label: "预充值收入",
    value: "recharge"
  },
  {
    label: "教师预充值收入",
    value: "teacher_recharge"
  },
  {
    label: "第三方消费收入",
    value: "third_consume_revenue"
  },
  {
    label: "伙食缴费收入",
    value: "huoshi_jiao_fei"
  },
  // {
  //   label: "采购退款",
  //   value: ""
  // },
  {
    label: "其他收入",
    value: "operating_other_income"
  }
]
// 资金流水 非营业性收入
export const WARTER_NON_INCOME_TYPE = [
  {
    label: "公益捐赠",
    value: "public_welfare_donation"
  },
  {
    label: "政府补助",
    value: "government_subsidy"
  },
  {
    label: "其他收入",
    value: "non_operating_other_income"
  }
]

// 资金流水 营业性支出
export const WARTER_EXPRESS_TYPE = [
  {
    label: "预充值退款",
    value: "recharge_refund"
  },
  // {
  //   label: "教师充值退款",
  //   value: ""
  // },
  {
    label: "预充值提现",
    value: "order_withdraw"
  },
  // {
  //   label: "教师预充值提现",
  //   value: ""
  // },
  {
    label: "第三方消费退款",
    value: "third_consume_refund"
  },
  {
    label: "伙食缴费退款",
    value: "jiao_fei_refund"
  },
  {
    label: "采购成本",
    value: "drp_invoices"
  },
  {
    label: "人工成本",
    value: "labor_cost"
  },
  {
    label: "水电气成本",
    value: "utilities"
  },
  {
    label: "其他成本",
    value: "operating_other_costs"
  }
]
// 资金流水 非营业性支出
export const WARTER_NON_EXPRESS_TYPE = [
  {
    label: "其他成本",
    value: "non_operating_other_costs"
  }
]

// 食堂月核算报表
export const SEARCH_FORM_SETTING_CANTEEN_MONTH = {
  selecttime: {
    type: "monthrange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "日期筛选",
    labelWidth: "100px",
    value: [getPreDate(0, "YYYY-MM"), getPreDate(0, "YYYY-MM")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  org_ids: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  }
}

// 食堂月核算报表
export const TABLE_SETTING_CANTEEN_MONTH = [
  {
    label: "统计日期",
    prop: "date_str"
  },
  {
    label: "监管组织",
    prop: "organization_name"
  },
  {
    label: "收入",
    prop: "revenue",
    type: "price"
  },
  {
    label: "支出",
    prop: "expend",
    type: "price"
  },
  {
    label: "支出项目",
    prop: "expenditure",
    align: "center",
    children: [
      {
        label: "原材料支出",
        prop: "raw_material_expenses",
        align: "center",
        children: [
          {
            label: "上月剩余",
            prop: "last_remainder",
            type: "price"
          },
          {
            label: "本月领用",
            prop: "accept",
            type: "price"
          },
          {
            label: "本月剩余",
            prop: "remainder",
            type: "price"
          },
          {
            label: "本月消耗",
            prop: "use",
            type: "price"
          }
        ]
      },
      {
        label: "水电气费用",
        prop: "weg_fee",
        type: "price"
      }
    ]
  },
  {
    label: "间接成本",
    prop: "cost",
    type: "price"
  },
  {
    label: "间接成本率",
    prop: "cost_rate"
  }
]

// 收支汇总表
export const TABLE_SETTING_SUMMARY = [
  {
    label: "统计日期",
    prop: "pay_date"
  },
  {
    label: "监管组织",
    prop: "org_name"
  },
  {
    label: "收入总额",
    prop: "revenue",
    type: "price",
    align: "center",
    children: [
      {
        label: "营业收入",
        prop: "in_price",
        type: "price"
      },
      {
        label: "非营业收入",
        prop: "non_in_price",
        type: "price"
      }
    ]
  },
  {
    label: "支出总额",
    prop: "expend",
    type: "price",
    align: "center",
    children: [
      {
        label: "营业成本",
        prop: "out_price",
        type: "price"
      },
      {
        label: "非营业成本",
        prop: "non_out_price",
        type: "price"
      }
    ]
  },
  {
    label: "收入合计",
    prop: "consume_price_total",
    type: "price"
  },
  {
    label: "支出合计",
    prop: "refund_price_total",
    type: "price"
  },
  {
    label: "间接成本",
    prop: "profit",
    type: "price"
  },
  {
    label: "间接成本率",
    prop: "profit_rate"
  }
]

// 收支统计表
export const SEARCH_FORM_SETTING_STATISTICS = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "日期筛选",
    labelWidth: "100px",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
    // disabledDate: (time: any) => {
    //   const now = new Date()
    //   // 设置时间为当前月的前一个月的同一天（例如今天是10号，则设置为9月10号）
    //   const oneMonthBefore = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
    //   // 如果当前时间小于或等于前一个月的日期，则禁用该日期
    //   return time.getTime() > now.getTime() || time.getTime() < oneMonthBefore.getTime()
    // }
  },
  org_id: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: [] as Array<any>,
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  },
  organization_ids: {
    type: "canteenOrgs",
    label: "所属组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    orgsId: []
  },
  flow_type: {
    type: "select",
    label: "流水类别",
    labelWidth: "100px",
    value: "",
    dataList: [
      {
        label: "营业收入",
        value: "revenue_in",
        value1: "operating_income",
        typeList: OPERATING_INCOME_TYPE
      },
      {
        label: "非营业收入",
        value: "non_revenue_in",
        value1: "non_operating_income",
        typeList: NON_OPERATING_INCOME_TYPE
      },
      {
        label: "营业成本",
        value: "cost_out",
        value1: "operating_cost",
        typeList: OPERATING_COST_TYPE
      },
      {
        label: "非营业成本",
        value: "non_cost_out",
        value1: "non_operating_cost",
        typeList: NON_OPERATING_COST_TYPE
      }
    ],
    placeholder: "请选择流水类别",
    clearable: true,
    maxlength: 20
  },
  account_type: {
    type: "select",
    label: "流水类型",
    labelWidth: "100px",
    value: "",
    dataList: [] as Array<any>,
    placeholder: "请选择流水类型",
    clearable: true,
    disabled: true,
    maxlength: 20
  },
  data_source: {
    type: "select",
    label: "数据来源",
    labelWidth: "100px",
    value: "",
    dataList: [
      {
        label: "系统记录",
        value: "system"
      },
      {
        label: "人工上传",
        value: "manual"
      }
    ],
    placeholder: "请选择数据来源",
    clearable: true,
    maxlength: 20
  }
}

// 收支统计表
export const TABLE_SETTING_STATISTICS = [
  {
    label: "统计日期",
    prop: "pay_date",
    width: "200px"
  },
  {
    label: "监管组织",
    prop: "channel_org_name"
  },
  {
    label: "所属组织",
    prop: "org_name"
  },
  {
    label: "流水类别",
    prop: "flow_type"
  },
  {
    label: "流水类型",
    prop: "account_type"
  },
  {
    label: "记账金额",
    prop: "price",
    type: "price"
  },
  {
    label: "数据来源",
    prop: "data_source"
  }
  // {
  //   label: "备注",
  //   prop: "remark",
  //   "show-overflow-tooltip": true,
  //   width: "200px"
  // }
]

// 资金收入日报表筛选设置
export const SEARCH_FORM_DAILY_INCOME_REPORT = {
  select_time: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "日期筛选",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  channel_org_ids: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  }
}

// 资金收入日报表表格设置
export const TABLE_SETTING_DAILY_INCOME_REPORT = [
  {
    label: "统计日期",
    prop: "date_str"
  },
  {
    label: "监管组织",
    prop: "org_name"
  },
  {
    label: "营业性收入",
    prop: "operating_income",
    align: "center",
    children: [
      {
        label: "预充值收入",
        prop: "recharge",
        type: "price"
      },
      // {
      //   label: "教师预充值收入",
      //   prop: "teacher_recharge",
      //   type: "price"
      // },
      {
        label: "第三方消费收入",
        prop: "three_consume",
        type: "price"
      },
      {
        label: "伙食缴费收入",
        prop: "jiao_fei",
        type: "price"
      },
      // {
      //   label: "采购退款",
      //   prop: "purchase_refund",
      //   type: "price"
      // },
      {
        label: "其他收入",
        prop: "operating_other_income",
        type: "price"
      }
    ]
  },
  {
    label: "非营业性收入",
    prop: "non_operating_income",
    align: "center",
    children: [
      {
        label: "财政补助",
        prop: "government_subsidy",
        type: "price"
      },
      {
        label: "公益捐赠",
        prop: "public_welfare_donation",
        type: "price"
      },
      {
        label: "其他收入",
        prop: "non_operating_other_income",
        type: "price"
      }
    ]
  },
  {
    label: "营业性支出",
    prop: "operating_expenses",
    align: "center",
    children: [
      {
        label: "采购成本",
        prop: "drp_invoices",
        type: "price"
      },
      {
        label: "水电气成本",
        prop: "utilities",
        type: "price"
      },
      {
        label: "人工成本",
        prop: "labor_cost",
        type: "price"
      },
      {
        label: "预充值退款",
        prop: "recharge_refund",
        type: "price"
      },
      // {
      //   label: "教师预充值退款",
      //   prop: "teacher_recharge_refund",
      //   type: "price"
      // },
      {
        label: "预充值提现",
        prop: "order_withdraw",
        type: "price"
      },
      // {
      //   label: "教师预充值提现",
      //   prop: "teacher_order_withdraw",
      //   type: "price"
      // },
      {
        label: "第三方消费退款",
        prop: "three_consume_refund",
        type: "price"
      },
      {
        label: "伙食缴费退款",
        prop: "jiao_fei_refund",
        type: "price"
      },
      {
        label: "其他成本",
        prop: "operating_other_costs",
        type: "price"
      }
    ]
  },
  {
    label: "非营业性支出",
    prop: "non_operating_other",
    children: [
      {
        label: "其他成本",
        prop: "non_operating_other_costs",
        type: "price"
      }
    ]
  },
  {
    label: "营业性收入汇总",
    prop: "in_price_total",
    type: "price"
  },
  {
    label: "非营业性收入汇总",
    prop: "non_in_price_total",
    type: "price"
  },
  {
    label: "营业性支出汇总",
    prop: "out_price_total",
    type: "price"
  },
  {
    label: "非营业性支出汇总",
    prop: "non_out_price_total",
    type: "price"
  }
]

// 资金收入月报表筛选设置
export const SEARCH_FORM_MONTH_INCOME_REPORT = {
  select_time: {
    type: "monthrange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "月份筛选",
    value: [getPreDate(7, "YYYY-MM"), getPreDate(0, "YYYY-MM")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  channel_org_ids: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  }
}

// 资金收入月报表表格设置
export const TABLE_SETTING_MONTH_INCOME_REPORT = [
  {
    label: "统计月份",
    prop: "date_str"
  },
  {
    label: "监管组织",
    prop: "org_name"
  },
  {
    label: "营业性收入",
    prop: "operating_income",
    align: "center",
    // "class-name": "header-bg-blue",
    children: [
      {
        label: "预充值收入",
        prop: "recharge",
        type: "price"
      },
      // {
      //   label: "教师预充值收入",
      //   prop: "teacher_recharge",
      //   type: "price"
      // },
      {
        label: "第三方消费收入",
        prop: "three_consume",
        type: "price"
      },
      {
        label: "伙食缴费收入",
        prop: "jiao_fei",
        type: "price"
      },
      // {
      //   label: "采购退款",
      //   prop: "purchase_refund",
      //   type: "price"
      // },
      {
        label: "其他收入",
        prop: "operating_other_income",
        type: "price"
      }
    ]
  },
  {
    label: "非营业性收入",
    prop: "non_operating_income",
    align: "center",
    children: [
      {
        label: "财政补助",
        prop: "government_subsidy",
        type: "price"
      },
      {
        label: "公益捐赠",
        prop: "public_welfare_donation",
        type: "price"
      },
      {
        label: "其他收入",
        prop: "non_operating_other_income",
        type: "price"
      }
    ]
  },
  {
    label: "营业性支出",
    prop: "operating_expenses",
    align: "center",
    children: [
      {
        label: "采购成本",
        prop: "drp_invoices",
        type: "price"
      },
      {
        label: "水电气成本",
        prop: "utilities",
        type: "price"
      },
      {
        label: "人工成本",
        prop: "labor_cost",
        type: "price"
      },
      {
        label: "预充值退款",
        prop: "recharge_refund",
        type: "price"
      },
      // {
      //   label: "教师预充值退款",
      //   prop: "teacher_recharge_refund",
      //   type: "price"
      // },
      {
        label: "预充值提现",
        prop: "order_withdraw",
        type: "price"
      },
      // {
      //   label: "教师预充值提现",
      //   prop: "teacher_order_withdraw",
      //   type: "price"
      // },
      {
        label: "第三方消费退款",
        prop: "three_consume_refund",
        type: "price"
      },
      {
        label: "伙食缴费退款",
        prop: "jiao_fei_refund",
        type: "price"
      },
      {
        label: "其他成本",
        prop: "operating_other_costs",
        type: "price"
      }
    ]
  },
  {
    label: "非营业性支出",
    prop: "non_operating_other",
    children: [
      {
        label: "其他成本",
        prop: "non_operating_other_costs",
        type: "price"
      }
    ]
  },
  {
    label: "营业性收入汇总",
    prop: "in_price_total",
    type: "price"
  },
  {
    label: "非营业性收入汇总",
    prop: "non_in_price_total",
    type: "price"
  },
  {
    label: "营业性支出汇总",
    prop: "out_price_total",
    type: "price"
  },
  {
    label: "非营业性支出汇总",
    prop: "non_out_price_total",
    type: "price"
  }
]

// 资金流水明细筛选设置
export const SEARCH_FORM_CAPITAL_FLOW_DETAILS = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "日期筛选",
    value: [getPreDate(90, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  channel_org_ids: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  },
  org_ids: {
    type: "canteenOrgs",
    label: "所属组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    orgsId: []
  },
  flow_type_list: {
    type: "select",
    label: "流水类别",
    labelWidth: "100px",
    value: [],
    dataList: [
      {
        label: "营业性收入",
        value: "operating_income",
        typeList: WARTER_INCOME_TYPE
      },
      {
        label: "非营业性收入",
        value: "non_operating_income",
        typeList: WARTER_NON_INCOME_TYPE
      },
      {
        label: "营业性支出",
        value: "operating_cost",
        typeList: WARTER_EXPRESS_TYPE
      },
      {
        label: "非营业性支出",
        value: "non_operating_cost",
        typeList: WARTER_NON_EXPRESS_TYPE
      }
    ],
    placeholder: "请选择流水类别",
    multiple: true,
    clearable: true,
    collapseTags: true
  },
  data_type_list: {
    type: "select",
    label: "流水类型",
    labelWidth: "100px",
    disabled: true,
    value: [],
    dataList: [] as Array<any>,
    placeholder: "请选择流水类型",
    multiple: true,
    clearable: true,
    collapseTags: true
  }
}

// 资金流水明细表格设置
export const TABLE_SETTING_CAPITAL_FLOW_DETAILS = [
  {
    label: "交易时间",
    prop: "pay_date"
  },
  {
    label: "监管组织",
    prop: "channel_org_name"
  },
  {
    label: "所属组织",
    prop: "org_name"
  },
  {
    label: "原订单号",
    prop: "order_no"
  },
  {
    label: "流水类别",
    prop: "flow_type"
  },
  {
    label: "流水类型",
    prop: "account_type"
  },
  {
    label: "进账对象",
    prop: "payout_object"
  },
  {
    label: "交易金额",
    prop: "price",
    type: "price"
  },
  {
    label: "数据来源",
    prop: "data_source_ails"
  },
  {
    label: "备注",
    prop: "remark",
    "show-overflow-tooltip": true
  }
]
