import { getPreDate } from "@/utils/date"
// 低消费统计 筛选设置
export const SEARCH_FORM_SETTING_LOW_CONSUMPTION_STATISTICS = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "日期筛选",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  org_name: {
    type: "input",
    label: "组织名称",
    value: "",
    placeholder: "请输入组织名称",
    clearable: true,
    maxlength: 20
  },
  name1: {
    type: "select",
    label: "统计维度",
    value: "",
    dataList: [
      {
        label: "top100",
        value: "100"
      },
      {
        label: "top50",
        value: "50"
      },
      {
        label: "top10",
        value: "10"
      }
    ],
    placeholder: "请选择",
    clearable: true
  }
}
// 低消费统计列表设置
export const TABLE_SETTING_LOW_CONSUMPTION_STATISTICS = [
  {
    label: "统计日期",
    prop: "date_str",
    width: "200px",
    "show-overflow-tooltip": true
  },
  {
    label: "组织名称",
    prop: "organization_name",
    width: "300px",
    "show-overflow-tooltip": true
  },
  {
    label: "姓名",
    prop: "user_name",
    width: "200px"
  },
  {
    label: "人员编号",
    prop: "person_no"
  },
  {
    label: "手机号",
    prop: "person_no"
  },
  {
    label: "消费金额",
    prop: "average_price"
  }
]

// 低消费统计列表设置
export const TABLE_SETTING_LOW_ = [
  {
    label: "统计日期",
    prop: "name",
    width: "200px",
    "show-overflow-tooltip": true
  },
  {
    label: "组织名称",
    prop: "feature_name_list",
    slot: "featureNameList",
    width: "300px",
    "show-overflow-tooltip": true
  },
  {
    label: "姓名",
    prop: "name",
    width: "200px"
  },
  {
    label: "人员编号",
    prop: "person_no"
  },
  {
    label: "供应组织",
    prop: "create_time"
  },
  {
    label: "手机号",
    prop: "mobile"
  },
  {
    label: "消费金额",
    prop: "consume_fee"
  }
]
// 挂起查询统计 筛选设置
export const SEARCH_FORM_SETTING_SUSPEND_QUERY = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "创建时间",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  name: {
    type: "input",
    label: "查询模块",
    value: "",
    placeholder: "请输入查询模块",
    clearable: true,
    maxlength: 20
  },
  status: {
    type: "select",
    label: "状态",
    value: "",
    dataList: [
      {
        label: "查询中",
        value: "1"
      },
      {
        label: "成功",
        value: "2"
      },
      {
        label: "失败",
        value: "3"
      }
    ],
    placeholder: "请选择",
    clearable: true
  }
}
// 挂起查询列表设置
export const TABLE_SETTING_SUSPEND_QUERY = [
  {
    label: "创建时间",
    prop: "create_time",
    "show-overflow-tooltip": true
  },
  {
    label: "查询模块",
    prop: "feature_name_list",
    slot: "featureNameList",
    "show-overflow-tooltip": true
  },
  {
    label: "状态",
    prop: "status",
    slot: "status"
  },
  {
    label: "有效期",
    prop: "valid_time"
  },
  {
    label: "操作人",
    prop: "creator"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operationNew"
  }
]
// 挂起查询详情列表设置
export const TABLE_SETTING_SUSPEND_QUERY_DETAIL = [
  {
    label: "统计日期",
    prop: "create_time",
    "show-overflow-tooltip": true
  },
  {
    label: "项目名称",
    prop: "feature_name_list",
    slot: "featureNameList",
    "show-overflow-tooltip": true
  },
  {
    label: "收入金额",
    prop: "status"
  },
  {
    label: "支出金额",
    prop: "price"
  },
  {
    label: "利润",
    prop: "lirun"
  },
  {
    label: "利润率",
    prop: "operation"
  }
]

// 下载记录 筛选设置
export const SEARCH_FORM_SETTING_DOWNLOAD_RECORD = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "创建时间",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  name: {
    type: "input",
    label: "文件名称",
    value: "",
    placeholder: "请输入文件名称",
    clearable: true,
    maxlength: 20
  },
  status: {
    type: "select",
    label: "状态",
    value: "",
    dataList: [
      {
        label: "进行中",
        value: "1"
      },
      {
        label: "成功",
        value: "2"
      },
      {
        label: "失败",
        value: "3"
      }
    ],
    placeholder: "请选择",
    clearable: true
  }
}
// 下载记录列表设置
export const TABLE_SETTING_DOWNLOAD_RECORD = [
  {
    label: "创建时间",
    prop: "create_time",
    "show-overflow-tooltip": true
  },
  {
    label: "文件名称",
    prop: "feature_name_list",
    slot: "featureNameList",
    "show-overflow-tooltip": true
  },
  {
    label: "状态",
    prop: "status",
    slot: "status"
  },
  {
    label: "有效期",
    prop: "valid_time"
  },
  {
    label: "最新下载时间",
    prop: "update_time"
  },
  {
    label: "下载次数",
    prop: "download_count"
  },
  {
    label: "操作人",
    prop: "creator"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operationNew"
  }
]

// 资金专户记录 筛选设置
export const SEARCH_FORM_FUNDS_ACCOUNT_RECORD = {
  selecttime: {
    type: "year",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "年度筛选",
    value: [getPreDate(0, "YYYY")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  school_name: {
    type: "select",
    label: "学校名称",
    value: "全部",
    dataList: [
      {
        label: "全部",
        value: "全部"
      },
      {
        label: "华溪实验中学",
        value: "华溪实验中学"
      },
      {
        label: "田家炳实验小学",
        value: "田家炳实验小学"
      },
      {
        label: "启元中学",
        value: "启元中学"
      }
    ] as Array<any>,
    placeholder: "请选择",
    multiple: false,
    clearable: false,
    collapseTags: true
  }
}

// 资金专户记录列表设置
export const TABLE_SETTING_FUNDS_ACCOUNT_RECORD = [
  {
    label: "学校名称",
    prop: "school_name",
    "show-overflow-tooltip": true
  },
  {
    label: "资金总额",
    prop: "price_total",
    "show-overflow-tooltip": true
  },
  {
    label: "资金余额",
    prop: "price_balance"
  },
  {
    label: "资金支出",
    prop: "price_expend"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operationNew"
  }
]

// 专户收支明细 筛选设置
export const SEARCH_FORM_FUNDS_ACCOUNT_INCOME_DETAIL = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "创建时间",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  school_name: {
    type: "select",
    label: "学校名称",
    value: "全部",
    dataList: [
      {
        label: "全部",
        value: "全部"
      },
      {
        label: "华溪实验中学",
        value: "华溪实验中学"
      },
      {
        label: "田家炳实验小学",
        value: "田家炳实验小学"
      },
      {
        label: "启元中学",
        value: "启元中学"
      }
    ] as Array<any>,
    placeholder: "请选择",
    multiple: false,
    clearable: false,
    collapseTags: true
  },
  status: {
    type: "select",
    label: "资金流向",
    value: "全部",
    dataList: [
      {
        label: "全部",
        value: "全部"
      },
      {
        label: "收入",
        value: "收入"
      },
      {
        label: "支出",
        value: "支出"
      }
    ],
    placeholder: "请选择",
    clearable: false
  },
  type: {
    type: "select",
    label: "类目",
    value: "全部",
    dataList: [
      {
        label: "全部",
        value: "全部"
      },
      {
        label: "政府拨款",
        value: "政府拨款"
      },
      {
        label: "购买食材",
        value: "购买食材"
      },
      {
        label: "社会捐款",
        value: "社会捐款"
      }
    ],
    placeholder: "请选择",
    clearable: false
  }
}

// 专户收支明细列表设置
export const TABLE_SETTING_ACCOUNT_INCOME_DETAIL = [
  {
    label: "日期",
    prop: "create_time",
    "show-overflow-tooltip": true
  },
  {
    label: "学校名称",
    prop: "school_name",
    "show-overflow-tooltip": true
  },
  {
    label: "资金流向",
    prop: "price_flow",
    "show-overflow-tooltip": true
  },
  {
    label: "类目",
    prop: "type"
  },
  {
    label: "具体项目",
    prop: "details"
  },
  {
    label: "对象",
    prop: "company"
  },
  {
    label: "凭证",
    prop: "voucher",
    slot: "voucher"
  },
  {
    label: "金额",
    prop: "price"
  },
  {
    label: "专户金额",
    prop: "fund_price"
  },
  {
    label: "备注说明",
    prop: "remark"
  },
  {
    label: "审批人",
    prop: "approver"
  }
]
