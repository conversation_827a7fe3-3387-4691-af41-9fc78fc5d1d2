<template>
  <div class="large-sceen-main">
    <div class="left">
      <channel-tree @tree-click="handlerTreeChange" />
    </div>
    <div class="large-sceen" :style="{ height: height + 'px' }" v-loading="loading">
      <div class="large-sceen-list">
        <div
          v-for="(item, index) in ScreenList"
          :key="index"
          :class="['screen-tag', index % 4 !== 0 ? 'm-l-20px' : '', index > 3 ? 'm-t-20px' : '']"
          @click="handlerSeeDetail(item.link_address)"
        >
          <img :src="getImgUrl(item.img_list)" class="bg" />
          <div class="title">{{ item.name }}</div>
          <img
            :src="icSearchWhite"
            class="right-bottom-img1 w-18px h-18px"
            @click.stop="handlerShowPhoto(item.img_list)"
          />
          <img
            :src="icShareWhite"
            class="right-bottom-img2 w-18px h-18px"
            @click.stop="handlerShare(item.link_address)"
          />
        </div>
      </div>
      <!-- 弹窗-->
      <el-dialog v-model="dialogVisible" title="分享" width="800">
        <div class="flex">
          <el-input ref="shareInputRef" v-model="shareUrl" placeholder="请输入分享链接" class="" />
          <el-button type="primary" @click="handlerCopy" class="m-l-20px">复制链接</el-button>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="dialogVisible = false"> 关闭 </el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 图片预览-->
      <image-preview v-model="imageVisible" :imgs="imageList" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue"
import bgShiTang from "@/assets/test/bg_shitang.png"
import bgZijin from "@/assets/test/bg_zijin.png"
import bgShiangGongshi from "@/assets/test/bg_shiang_gongshi.png"
import bgCaiwuGongshi from "@/assets/test/bg_caiwu_gongshi.png"
import icSearchWhite from "@/assets/layouts/ic_search_white.png"
import icShareWhite from "@/assets/layouts/ic_share_white.png"
import useScreenHook from "@/hooks/useScreen"
import { ElMessage } from "element-plus"
import useClipboard from "vue-clipboard3"
import { getChannelScreen } from "@/api/financial"
import to from "await-to-js"
import ChannelTree from "@/components/ChannelTree/index.vue"
import { cloneDeep } from "lodash"
import { useUserStore } from "@/store/modules/user"
// 个人信息
const userStore = useUserStore()
type ImgItem = {
  name: string
  link_address: string
  icon: string
  img_list: any[]
}
// 大屏列表
const ScreenList = ref<Array<ImgItem>>()
// 渠道Id
const chanelId = ref()
const roleId = ref()
const list = [
  {
    name: "资金监管数据驾驶舱",
    link_address: "https://dp.debug.packertec.com/#/chart/preview/18?organization_id=14&company_id=6",
    icon: bgZijin,
    img_list: [bgZijin]
  },
  {
    name: "食堂监管数据驾驶舱",
    link_address: "https://dp.debug.packertec.com/#/chart/preview/17?organization_id=14&company_id=6",
    icon: bgShiTang,
    img_list: [bgShiTang]
  },
  {
    name: "食堂信息公示监管",
    link_address: "https://dp.debug.packertec.com/#/chart/preview/7?organization_id=14&company_id=6",
    icon: bgShiangGongshi,
    img_list: [bgShiangGongshi]
  },
  {
    name: "财务公示数据驾驶舱",
    link_address: "https://dp.debug.packertec.com/#/chart/preview/10?organization_id=14&company_id=6",
    icon: bgCaiwuGongshi,
    img_list: [bgCaiwuGongshi]
  }
]

// 高度计算
const rightRef = ref()
const height = useScreenHook(rightRef, 20).maxHeight

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// 弹窗显示
const dialogVisible = ref(false)
const shareUrl = ref("")
const { toClipboard } = useClipboard()

// loading
const loading = ref(false)

// 查看照片
const handlerShowPhoto = (img: any[]) => {
  console.log(img)
  if (img) {
    imageList.value = [img[0].url]
    imageVisible.value = true
  }
}
// 分享
const handlerShare = (url: string) => {
  shareUrl.value = url
  dialogVisible.value = true
}
// 查看详情
const handlerSeeDetail = (url: string) => {
  window.open(url, "_blank")
  // window.location.href = url
}
// 复制链接
const handlerCopy = async () => {
  // navigator.clipboard.writeText(shareUrl.value)
  try {
    await toClipboard(shareUrl.value)
    ElMessage.success("复制成功")
  } catch (error) {
    ElMessage.error("复制失败！")
  }
}
// 获取列表
const getDataList = async () => {
  loading.value = true
  const [err, res] = await to(getChannelScreen({ page: 1, page_size: 9999, channel_id: chanelId.value }))
  loading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    let resultsList = data.results || []
    if (resultsList && resultsList.length > 0) {
      resultsList = resultsList.map((item: any) => {
        let url = item.link_address || ""
        if (url) {
          if (url.indexOf("?") !== -1) {
            url = url + "&channel_id=" + chanelId.value + "&role_id=" + roleId.value
          } else {
            url = url + "?channel_id=" + chanelId.value + "&role_id=" + roleId.value
          }
        }
        item.link_address = url
        return item
      })
      ScreenList.value = cloneDeep(resultsList)
    } else {
      ScreenList.value = []
    }
  } else {
    ElMessage.error(res.msg)
  }
}
// 获取图片地址
const getImgUrl = (imgList: any[]) => {
  if (imgList && imgList.length > 0) {
    return imgList[0].url
  }
  return ""
}

// 树选择改变
const handlerTreeChange = (val: any) => {
  console.log("handlerTreeChange", val)
  chanelId.value = val.id || ""
  getDataList()
}

onMounted(() => {
  roleId.value = userStore.getRoleId
})
</script>
<style lang="scss" scoped>
.large-sceen-main {
  margin-right: 20px;
  display: flex;

  .large-sceen {
    width: 100%;
    background-color: #fff;
    border-radius: 16px;
    overflow: auto;
    padding: 24px;
    .large-sceen-list {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
    }

    .screen-tag {
      width: 300px;
      height: 180px;
      border-radius: 8px;
      cursor: pointer;
      position: relative;
      align-items: flex-start;
      background-color: red;

      .bg {
        border-radius: 8px;
        width: 100%;
        height: 100%;
      }

      .title {
        position: absolute;
        left: 12px;
        bottom: 12px;
        color: #fff;
        font-size: 18px;
      }

      .right-bottom-img1 {
        position: absolute;
        bottom: 12px;
        right: 48px;
      }

      .right-bottom-img2 {
        position: absolute;
        bottom: 12px;
        right: 12px;
      }
    }
    .screen-tag:hover {
      transform: scale(1.02);
    }
  }
}
</style>
