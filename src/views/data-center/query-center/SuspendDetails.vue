<template>
  <div class="suspend-query-details container-wrapper" v-loading="loading">
    <div ref="searchRef" />
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="table-button">
          <el-button @click="goToExport" type="primary"> 导出 </el-button>
          <el-button plain @click="goToExport" class="ps-origin-btn-light"> 打印 </el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting" />
        </ps-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import { TABLE_SETTING_SUSPEND_QUERY_DETAIL } from "../constants"
import { apiCardGroupListPost } from "@/api/user"
import { ElMessage } from "element-plus"
import useTableHeightHook from "@/hooks/useTableHeight"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { exportHandle } from "@/utils/exportExcel"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// table数据
const tableData = ref([])
const loading = ref(false)
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_SUSPEND_QUERY_DETAIL)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 导出
const importType = "FoodSafetyTraceabilityExport"

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 导出
const goToExport = () => {
  console.log("goToExport")
  const option = {
    type: importType,
    api: apiCardGroupListPost,
    params: {
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    }
  }
  exportHandle(option)
}

onMounted(() => {
  getDataList()
})
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiCardGroupListPost({
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
</script>
<style lang="scss">
.suspend-query-details {
  padding: 0 20px;
  .status-tag {
    display: inline-block;
    min-width: 61px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    font-size: 12px;
    padding: 0 8px;
    border-radius: 12px;
  }
  .default {
    color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-3);
  }
  .success {
    color: #28ce59;
    background-color: #eafbef;
  }
  .warn {
    color: #ff5656;
    background-color: #f5e7ec;
  }

  .el-popper {
    max-width: 300px;
  }
}
</style>
