<template>
  <div class="download-record container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="">
          <el-tooltip placement="top-start">
            <template #content> {{ tipTxt }} </template>
            <img :src="IcQuestionBlack" class="w-32px h-32px" />
          </el-tooltip>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #status="{ row }">
              <div>
                <div
                  :class="[
                    'status-tag',
                    row.status === '进行中'
                      ? 'default'
                      : row.status === '已成功'
                        ? 'success'
                        : row.status === '已失败'
                          ? 'warn'
                          : ''
                  ]"
                >
                  {{ row.status ? " • " + row.status : "--" }}
                </div>
              </div>
            </template>
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="goToExport()" type="primary"> 下载 </el-button>
              <span style="margin: 0 5px; color: #e2e8f0">|</span>
              <el-button plain link size="small" color="#ff5656" @click="handlerDelete(row)"> 删除 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import { SEARCH_FORM_SETTING_DOWNLOAD_RECORD, TABLE_SETTING_DOWNLOAD_RECORD } from "../constants"
import { apiCardGroupListPost, apiDepartmentListPost } from "@/api/user"
import { ElMessage } from "element-plus"
import IcQuestionBlack from "@/assets/layouts/ic_question_black.png"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { exportHandle } from "@/utils/exportExcel"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)
import { confirmBefore } from "@/utils/message"

// table数据
const tableData = ref([
  { status: "进行中" },
  { status: "已成功" },
  { status: "已失败" },
  {},
  {},
  {},
  {},
  {},
  {},
  {},
  {},
  {},
  {},
  {},
  {},
  {},
  {}
])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_DOWNLOAD_RECORD))
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_DOWNLOAD_RECORD)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 导出
const importType = "FoodSafetyTraceabilityExport"
const tipTxt = "下载记录最多保留50天，到期后自动清除。或数据量达到50条后根据创建时间进行清除。"
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "feature") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.feature = data[key].value
      }
    }
  }
  return params
}
// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 导出
const goToExport = () => {
  console.log("goToExport")
  const option = {
    type: importType,
    api: apiCardGroupListPost,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    }
  }
  exportHandle(option)
}

// 删除
const handlerDelete = (row: any) => {
  console.log("handleDelete", row)
  confirmBefore(
    {
      content: "确定删除该下载记录么？",
      title: "提示"
    },
    async (action: any, instance: any, done: any) => {
      if (action === "confirm") {
        instance.confirmButtonLoading = true
        // const flag = await deletePaySetting([row.id], props.orgId);
        instance.confirmButtonLoading = false
      }
      done()
    },
    (action: any) => {
      if (action === "confirm") {
        getDataList()
      }
    }
  )
}
onMounted(() => {
  getFeatureList()
  getDataList()
})
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiCardGroupListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
// 获取功能权限列表
const getFeatureList = async () => {
  loading.value = true
  const [err, res]: any[] = await to(apiDepartmentListPost({}))
  loading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    let list: Array<any> = res.data || []
    if (list) {
      list = getValueKeyList(list)
    }
    // searchFormSetting.value.feature.dataList = cloneDeep(list)
    console.log("list", list)
  }
}
// 根据列表转换成label跟value 模式
const getValueKeyList = (list: Array<any>): Array<any> => {
  return list.map((item) => {
    item.label = item.verbose_name
    item.value = item.key
    if (item.level && item.level >= 1) {
      item.children = null
    }
    if (item.children && item.children.length > 0) {
      item.children = getValueKeyList(item.children)
    }
    return item
  })
}
</script>
<style lang="scss">
.download-record {
  padding: 0 20px;
  .status-tag {
    display: inline-block;
    min-width: 61px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    font-size: 12px;
    padding: 0 8px;
    border-radius: 12px;
  }
  .default {
    color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-3);
  }
  .success {
    color: #28ce59;
    background-color: #eafbef;
  }
  .warn {
    color: #ff5656;
    background-color: #f5e7ec;
  }

  .el-popper {
    max-width: 300px;
  }
}
</style>
