<template>
  <div class="scence-manage container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="">
          <el-tooltip placement="top-start">
            <template #content> {{ tipTxt }} </template>
            <img :src="IcQuestionBlack" class="w-32px h-32px" />
          </el-tooltip>
        </div>
        <div class="table-button">
          <el-button type="primary" @click="goToExport">导出</el-button>
          <el-button type="primary" class="ps-origin-btn-light" @click="goToExport">打印</el-button>
          <el-button type="primary" class="ps-origin-btn-plain" @click="goToExport">报表设置</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #featureNameList="{ row }">
              {{ getFeatureName(row.feature_name_list) }}
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import { SEARCH_FORM_SETTING_LOW_CONSUMPTION_STATISTICS, TABLE_SETTING_LOW_CONSUMPTION_STATISTICS } from "../constants"
import {
  apiBackgroundFundSupervisionBusinessReportListPost,
  apiBackgroundFundSupervisionBusinessReportListExportPost
} from "@/api/supervision"
import { ElMessage } from "element-plus"
import IcQuestionBlack from "@/assets/layouts/ic_question_black.png"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { exportHandle } from "@/utils/exportExcel"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// table数据
const tableData = ref([])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_LOW_CONSUMPTION_STATISTICS))
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_LOW_CONSUMPTION_STATISTICS)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

const tipTxt =
  "根据每个用户平均消费最低金额进行月维度统计，当天凌晨更新前一天的数据。只能查看直属关联及下级关联的组织数据"
// 导出
const importType = "FoodSafetyTraceabilityExport"

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}
// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 导出
const goToExport = () => {
  console.log("goToExport")
  const option = {
    type: importType,
    api: apiBackgroundFundSupervisionBusinessReportListExportPost,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    }
  }
  exportHandle(option)
}

onMounted(() => {
  getDataList()
})
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionBusinessReportListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 获取功能名称
const getFeatureName = (list: Array<string>) => {
  if (!list || !Array.isArray(list) || list.length === 0) {
    return ""
  }
  return list.join(";")
}
</script>
<style lang="scss">
.scence-manage {
  padding: 0 20px;

  .el-popper {
    max-width: 300px;
  }
}
</style>
