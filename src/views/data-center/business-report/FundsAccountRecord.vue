<template>
  <div class="funds-account-record container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="flex items-center" />
        <div class="table-button">
          <el-button type="primary" @click="goToExport">导出</el-button>
          <el-button
            type="primary"
            class="ps-origin-btn-light"
            @click="gotoPrint(tableSetting, fundsAccountRecordData, '资金专户记录')"
            >打印</el-button
          >
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerGotoDetail(row)" type="primary"> 查看明细 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import { SEARCH_FORM_FUNDS_ACCOUNT_RECORD, TABLE_SETTING_FUNDS_ACCOUNT_RECORD } from "../constants"
import { apiCardGroupListPost } from "@/api/user"
import { ElMessage } from "element-plus"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { exportHandle } from "@/utils/exportExcel"
import { moneyThousandFormat, getTestData, getSearchTestData, gotoPrint } from "@/utils/index"
import { fundsAccountRecordData } from "../data"
import router from "@/router"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// table数据
const tableData = ref([{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_FUNDS_ACCOUNT_RECORD))
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_FUNDS_ACCOUNT_RECORD)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 导出
const importType = "FoodSafetyTraceabilityExport"

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "feature") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.feature = data[key].value
      }
    }
  }
  return params
}
// 搜索
const changeSearch = async () => {
  const params = formatQueryParams(searchFormSetting.value)
  console.log("changeSearch", params)
  if (params.school_name && params.school_name !== "全部") {
    const list: any[] = await getSearchTestData(
      tableData,
      fundsAccountRecordData,
      "school_name",
      params.school_name,
      tableLoading,
      pageConfig.currentPage
    )
    pageConfig.total = list?.length || 0
  } else {
    getTestData(tableData, fundsAccountRecordData, tableLoading, pageConfig.currentPage)
    pageConfig.total = fundsAccountRecordData?.length || 0
  }
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getTestData(tableData, fundsAccountRecordData, tableLoading, pageConfig.currentPage)
  pageConfig.total = fundsAccountRecordData?.length || 0
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  changeSearch()
}

// 导出
const goToExport = () => {
  console.log("goToExport")
  const option = {
    type: importType,
    api: apiCardGroupListPost,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    }
  }
  exportHandle(option)
}

// 查看照片
const handlerShowPhoto = (row: any) => {
  console.log(row)
  imageList.value = [
    "https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/mapp_static/info/kefu.png",
    "https://h5-v4.debug.packertec.com/static/icons/tab_home_s.png"
  ]
  imageVisible.value = true
}
onMounted(() => {
  // getFeatureList()
  // getDataList()
  getTestData(tableData, fundsAccountRecordData, tableLoading, pageConfig.currentPage)
  pageConfig.total = fundsAccountRecordData?.length || 0
})
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiCardGroupListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
// 跳转详情
const handlerGotoDetail = (row: any) => {
  console.log(row)
  const schoolName = row.school_name || ""
  router.push({
    name: "AccountIncomeExpenditureDetails",
    query: {
      schoolName: schoolName
    }
  })
}
</script>
<style lang="scss">
.funds-account-record {
  padding: 0 20px;

  .el-popper {
    max-width: 300px;
  }
}
</style>
