<template>
  <div class="export-excel">
    <div class="await-tip-wrapper">
      <div v-if="asyncType == 'asyncTypeState' && excelStatus == 'success'" class="sync-box">
        <i class="el-icon-check icon-success" />
        <div class="tip-text">处理已完成</div>
      </div>
      <div v-else>
        <div class="tip-text" v-if="excelStatus == 'processing'">
          正在生成中，请勿关闭和刷新本页面。
          <div class="percentage-box">
            <el-progress :percentage="progress" />
          </div>
        </div>
        <div v-if="remainder" class="remainder">
          预估剩余时间:
          <count-down v-model="remainder" />
        </div>
        <div class="tip-text" v-if="excelStatus == 'failure' || reDownload">生成失败，请点击下面按钮重试。</div>
        <div class="tip-text" v-if="excelStatus == 'success'">已生成，请点击下面按钮进行下载。</div>
        <div class="wran-text" v-if="excelStatus == 'processing'">数据量比较多，请耐心等待</div>
        <div class="wran-text" v-if="excelStatus == 'failure' || reDownload" />
        <div class="wran-text" v-if="excelStatus == 'success'">
          如果没有自动下载或者取消下载后，请点击下面按钮重新下载。
        </div>
      </div>
      <el-button type="primary" size="small" :loading="excelStatus == 'processing'" v-if="excelStatus == 'processing'">
        正在生成
      </el-button>
      <el-button
        type="primary"
        size="small"
        v-if="excelStatus == 'failure' || (reDownload && asyncType != 'asyncTypeState')"
        @click="startQueryHandle(true)"
      >
        重新生成
      </el-button>
      <el-button
        type="primary"
        size="small"
        v-if="excelStatus == 'success' && asyncType != 'asyncTypeState'"
        @click="downloadExcel"
      >
        点击下载
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, reactive, toRefs } from "vue"
// import * as XLSX from "xlsx"
import { useRoute } from "vue-router"
import to from "await-to-js"
import { apiExportQueryPost } from "@/api"
import { ElMessage } from "element-plus"
const route = useRoute()

// 变量定义
const data = reactive({
  queryId: route.query.query_id,
  remainder: 0,
  progress: 0,
  startTime: 0,
  excelStatus: "processing",
  reDownload: false,
  asyncType: "",
  downLoadExcelUrl: ""
})
const { queryId, remainder, progress, startTime, excelStatus, reDownload, asyncType, downLoadExcelUrl } = toRefs(data)

let timerId: NodeJS.Timeout | null = null

onMounted(() => {
  startQueryHandle() // 可以在这里初始化一些状态或监听事件
})

onUnmounted(() => {
  timerId && clearInterval(timerId)
})

const startQueryHandle = (restart: Boolean = false) => {
  // restart 重试需要看是否需要重置当前的开始时间
  // 如果当前的进度也是0的时候需要重置开始时间了
  if (restart && progress.value === 0) {
    startTime.value = Date.now()
  }
  getExcelUrl()
  timerId = setInterval(() => {
    getExcelUrl()
  }, 1000)
}

const getExcelUrl = async () => {
  try {
    excelStatus.value = "processing"
    reDownload.value = false
    // await this.$sleep(2000)
    const [err, res]: any[] = await to(
      apiExportQueryPost({
        query_id: queryId.value
      })
    )
    const { code, msg } = res!
    if (err) {
      return
    }
    if (code === 0) {
      excelStatus.value = res.data.status
      progress.value = res.data.progress
      if (res.data.status === "success") {
        downLoadExcelUrl.value = res.data.url
        timerId && clearInterval(timerId)
        reDownload.value = false
        window.open(res.data.url)
      } else if (res.data.status === "failure") {
        timerId && clearInterval(timerId)
      }
    } else {
      ElMessage.error(res.msg)
      timerId && clearInterval(timerId)
      reDownload.value = true
      excelStatus.value = "failure"
    }
  } catch (error) {
    // this.progress = 0
    ElMessage.error("内部服务错误")
    timerId && clearInterval(timerId)
    reDownload.value = true
    excelStatus.value = "failure"
  }
}

const downloadExcel = () => {
  window.open(downLoadExcelUrl.value)
}
</script>

<style lang="scss">
.export-excel {
  position: relative;
  width: 100%;
  height: 100%;

  .await-tip-wrapper {
    position: absolute;
    top: 20%;
    right: 0;
    left: 0;
    width: 360px;
    // height: 170px;
    padding-bottom: 20px;
    // bottom: 0;
    margin: 0 auto;
    text-align: center;
    background-color: #fff;
    border: 2px solid #27adf4;

    .tip-text {
      padding: 20px 0;
      font-size: 16px;
      color: #27adf4;
    }

    .wran-text {
      height: 20px;
      padding-bottom: 20px;
      font-size: 12px;
      color: #f56c6c;
    }

    .remainder {
      margin-top: -10px;
      margin-bottom: 10px;
    }
  }

  .sync-box {
    padding-top: 30px;
  }

  .icon-success {
    font-size: 50px;
    color: #27adf4;
  }

  .percentage-box {
    width: 82%;
    margin: 20px auto 0;
  }
}
</style>
