import { getPreDate } from "@/utils/date"

export const SUPERVISION_MENU_LIST = [
  {
    name: "食堂信息",
    value: "base_info",
    permission: "no_permission"
  },
  // {
  //   name: "监管数据",
  //   value: "supervision_data"
  // },
  // {
  //   name: "采购记录",
  //   value: "purchase_record"
  // },
  // {
  //   name: "出入库记录",
  //   value: "storage_record"
  // },
  // {
  //   name: "每周食谱",
  //   value: "weekly_recipe"
  // },
  // {
  //   name: "明厨亮灶",
  //   value: "kitchen_fire"
  // },
  // {
  //   name: "留样记录",
  //   value: "sample_record"
  // },
  {
    name: "食安公示",
    value: "food_safety",
    permission: "no_permission"
  },
  // {
  //   name: "供应商信息",
  //   value: "supplier_information"
  // },
  {
    name: "供应汇总表",
    value: "supplier_total",
    permission: "background_fund_supervision.organization_supervision.supply_summary_table_list"
  },
  {
    name: "风险预警",
    value: "risk_warning",
    permission: "no_permission"
  },
  {
    name: "配置信息",
    value: "config_info",
    permission: "background_fund_supervision.organization_supervision.setting_info_detail"
  }
  // {
  //   name: "AI预警",
  //   value: "AI_warning"
  // },
  // {
  //   name: "经营监管",
  //   value: "business_supervision"
  // }
]
// 出入库类型
export const TYPE_INVENTORY = [
  {
    label: "采购入库",
    value: "PURCHASE_ENTRY",
    key: "PURCHASE_ENTRY"
  },
  {
    label: "调拨入库",
    value: "BORROW_ENTRY",
    key: "BORROW_ENTRY"
  },
  {
    label: "其他入库",
    value: "OTHER_ENTRY",
    key: "OTHER_ENTRY"
  },
  {
    label: "损耗出库",
    value: "EXPEND_EXIT",
    key: "EXPEND_EXIT"
  },
  {
    label: "调拨出库",
    value: "BORROW_EXIT",
    key: "BORROW_EXIT"
  },
  {
    label: "退货出库",
    value: "REFUND_EXIT",
    key: "REFUND_EXIT"
  },
  {
    label: "其他出库",
    value: "OTHER_EXIT",
    key: "OTHER_EXIT"
  },
  {
    label: "领料出库",
    value: "RECEIVE_EXIT",
    key: "RECEIVE_EXIT"
  },
  {
    label: "采购下单",
    value: "ORDER_PURCHASE",
    key: "ORDER_PURCHASE"
  }
]
// 餐段
export const TYPES_MEAL = [
  { label: "早餐", value: "breakfast" },
  { label: "午餐", value: "lunch" },
  { label: "下午茶", value: "afternoon" },
  { label: "晚餐", value: "dinner" },
  { label: "夜宵", value: "supper" },
  { label: "凌晨餐", value: "morning" }
]

// 在职人员表格设置
export const TABLE_SETTING_ON_JOB = [
  {
    label: "姓名",
    prop: "name"
  },
  {
    label: "联系电话",
    prop: "phone"
  },
  {
    label: "身份证号",
    prop: "id_number"
  },
  {
    label: "所属岗位",
    prop: "job_title"
  },
  {
    label: "用工形式",
    prop: "recruit_type_str"
  },
  {
    label: "系统账号",
    prop: "account_username"
  },
  {
    label: "人脸照片",
    prop: "face_image",
    slot: "faceImage"
  },
  {
    label: "健康证",
    prop: "operation",
    slot: "operationNew"
  },
  {
    label: "证件有效期",
    prop: "effective_time"
  }
]
// 监管概况搜索表单设置
export const SEARCH_FORM_SETTING_SUPERVISION_INFO = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "日期筛选",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  name: {
    type: "input",
    label: "组织名称",
    value: "",
    placeholder: "请输入组织名称",
    clearable: true,
    maxlength: 20
  }
}
// 监管概况表格设置
export const TABLE_SETTING_SUPERVISION_INFO = [
  {
    label: "日期",
    prop: "create_time",
    width: "200px",
    "show-overflow-tooltip": true
  },
  {
    label: "组织名称",
    prop: "name",
    width: "300px",
    "show-overflow-tooltip": true
  },
  {
    label: "利润率",
    prop: "percentage",
    width: "200px"
  },
  {
    label: "原材料支出比",
    prop: "zhichu"
  }
  // {
  //   label: "采购溢价预警数",
  //   prop: "create_time"
  // },
  // {
  //   label: "采购溢价预警数",
  //   prop: "create_time1"
  // }
]
// 监管概况预警明细表格设置
export const TABLE_SETTING_WARNING_INFO = [
  {
    label: "组织名称",
    prop: "org_name"
  },
  {
    label: "预警类型",
    prop: "warn_type"
  },
  {
    label: "预警内容",
    prop: "warn_detail",
    slot: "content"
  }
]
// 监管概况预警明细表格设置
export const TABLE_SETTING_RAW_MATERIAL_RISK = [
  {
    label: "监管组织",
    prop: "channel_org_name",
    align: "center"
  },
  {
    label: "类型",
    prop: "drp_type_alias",
    align: "center"
  },
  {
    label: "物资名称",
    prop: "materials_name",
    align: "center"
  },
  {
    label: "所属供应商",
    prop: "supplier_manage_name",
    align: "center"
  },
  {
    label: "预警情况",
    prop: "warn_text",
    width: "220px",
    align: "center"
  }
]
// 监管概况预警明细表格设置
export const TABLE_SETTING_DOCUMENT_CONTRACT_ALERTS = [
  {
    label: "监管组织",
    prop: "channel_org_name",
    align: "center"
  },
  {
    label: "所属组织",
    prop: "org_name",
    align: "center"
  },
  {
    label: "类型",
    prop: "file_type_alias",
    align: "center"
  },
  {
    label: "预警情况",
    prop: "warn_text",
    slot: "warnText",
    align: "center"
  }
]
// 月份1到31号
export const MONTH_DAYS = [
  {
    name: "1号",
    value: "1"
  },
  {
    name: "2号",
    value: "2"
  },
  {
    name: "3号",
    value: "3"
  },
  {
    name: "4号",
    value: "4"
  },
  {
    name: "5号",
    value: "5"
  },
  {
    name: "6号",
    value: "6"
  },
  {
    name: "7号",
    value: "7"
  },
  {
    name: "8号",
    value: "8"
  },
  {
    name: "9号",
    value: "9"
  },
  {
    name: "10号",
    value: "10"
  },
  {
    name: "11号",
    value: "11"
  },
  {
    name: "12号",
    value: "12"
  },
  {
    name: "13号",
    value: "13"
  },
  {
    name: "14号",
    value: "14"
  },
  {
    name: "15号",
    value: "15"
  },
  {
    name: "16号",
    value: "16"
  },
  {
    name: "17号",
    value: "17"
  },
  {
    name: "18号",
    value: "18"
  },
  {
    name: "19号",
    value: "19"
  },
  {
    name: "20号",
    value: "20"
  },
  {
    name: "21号",
    value: "21"
  },
  {
    name: "22号",
    value: "22"
  },
  {
    name: "23号",
    value: "23"
  },
  {
    name: "24号",
    value: "24"
  },
  {
    name: "25号",
    value: "25"
  },
  {
    name: "26号",
    value: "26"
  },
  {
    name: "27号",
    value: "27"
  },
  {
    name: "28号",
    value: "28"
  },
  {
    name: "29号",
    value: "29"
  },
  {
    name: "30号",
    value: "30"
  },
  {
    name: "31号",
    value: "31"
  }
]

// 采购记录搜索表单设置
export const SEARCH_FORM_SETTING_PURCHASE_RECORD = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "采购日期",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  name: {
    type: "input",
    label: "单据编号",
    value: "",
    placeholder: "请输入单据编号",
    clearable: true,
    maxlength: 20
  },
  name1: {
    type: "input",
    label: "所属仓库",
    value: "",
    placeholder: "请输入所属仓库",
    clearable: true,
    maxlength: 20
  }
}
// 采购记录表格设置
export const TABLE_SETTING_PURCHASE_RECORD = [
  {
    label: "采购日期",
    prop: "pay_time",
    width: "200px",
    "show-overflow-tooltip": true
  },
  {
    label: "单据编号",
    prop: "order_num",
    width: "300px",
    "show-overflow-tooltip": true
  },
  {
    label: "采购金额",
    prop: "price",
    width: "200px"
  },
  {
    label: "所属仓库",
    prop: "account_name"
  },
  {
    label: "创建时间",
    prop: "create_time"
  },
  {
    label: "经手人",
    prop: "creater_name"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operationNew",
    width: "180px"
  }
]

// 出入库搜索表单设置
export const SEARCH_FORM_SETTING_STORAGE_RECORD = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "操作日期",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  name: {
    type: "input",
    label: "单据编号",
    value: "",
    placeholder: "请输入单据编号",
    clearable: true,
    maxlength: 20
  },
  name1: {
    type: "input",
    label: "所属仓库",
    value: "",
    placeholder: "请输入所属仓库",
    clearable: true,
    maxlength: 20
  },
  name3: {
    type: "select",
    label: "类型",
    value: "",
    dataList: TYPE_INVENTORY,
    placeholder: "请选择出入库类型",
    multiple: true,
    clearable: true,
    collapseTags: true
  }
}
// 出入库记录表格设置
export const TABLE_SETTING_STORAGE_RECORD = [
  {
    label: "操作日期",
    prop: "name",
    width: "200px",
    "show-overflow-tooltip": true
  },
  {
    label: "单据编号",
    prop: "feature_name_list",
    slot: "featureNameList",
    width: "300px",
    "show-overflow-tooltip": true
  },
  {
    label: "所属仓库",
    prop: "logistics_company_num",
    width: "200px"
  },
  {
    label: "类型",
    prop: "account_name"
  },
  {
    label: "经手人",
    prop: "create_time1"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operationNew",
    width: "180px"
  }
]

// 留样记录搜索表单设置
export const SEARCH_FORM_SETTING_SAMPLE_RECORD = {
  name3: {
    type: "select",
    label: "餐段",
    value: "",
    dataList: TYPES_MEAL,
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true
  },
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "留样时间",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  }
}
// 留样记录表格设置
export const TABLE_SETTING_SAMPLE_RECORD = [
  {
    label: "所属组织",
    prop: "org_name",
    width: "200px",
    "show-overflow-tooltip": true
  },
  {
    label: "餐段",
    prop: "meal_type_alias",
    width: "300px",
    "show-overflow-tooltip": true
  },
  {
    label: "留样时间",
    prop: "reserved_date",
    width: "200px"
  },
  {
    label: "留样人",
    prop: "name"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operationNew",
    width: "180px"
  }
]
// 供应商搜索表单设置
export const SEARCH_FORM_SETTING_SUPPLIER_INFO = {
  name: {
    type: "input",
    label: "供应商",
    value: "",
    placeholder: "请输入供应商名称",
    clearable: true,
    maxlength: 20
  }
}
// 供应商记录表格设置
export const TABLE_SETTING_SUPPLIER_INFO = [
  {
    label: "供应商名称",
    width: "200px",
    prop: "name",
    "show-overflow-tooltip": true
  },
  // {
  //   label: "工商营业执照（社会统一信用代码）",
  //   prop: "number",
  //   width: "300px",
  //   "show-overflow-tooltip": true
  // },
  // {
  //   label: "联系人名称",
  //   prop: "contact_name",
  //   width: "200px"
  // },
  // {
  //   label: "联系电话",
  //   prop: "phone"
  // },
  // {
  //   label: "供应商地址",
  //   prop: "address"
  // },
  // {
  //   label: "操作",
  //   prop: "operation",
  //   slot: "operationNew",
  //   width: "180px"
  // }
  {
    label: "类型",
    prop: "type"
  },
  {
    label: "结算金额",
    prop: "price"
  },
  {
    label: "结算笔数",
    prop: "num"
  },
  {
    label: "待结算金额",
    prop: "fee"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operationNew",
    width: "180px"
  }
]

// 供应商汇总
export const SEARCH_FORM_SETTING_SUPPLIER_TOTAL = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "筛选日期",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  material_type: {
    type: "select",
    label: "物资分类",
    placeholder: "请选择物资分类",
    value: [],
    dataList: [],
    listNameKey: "name",
    listValueKey: "id",
    multiple: true,
    clearable: true,
    collapseTags: true
  },
  material_name: {
    type: "input",
    label: "物资名称",
    value: "",
    placeholder: "请输入物资名称",
    clearable: true,
    maxlength: 20
  },
  supply_name: {
    type: "input",
    label: "供应商",
    value: "",
    placeholder: "请输入供应商名称",
    clearable: true,
    maxlength: 20
  }
}

// 供应商汇总表格设置
export const TABLE_SETTING_SUPPLIER_TOTAL = [
  {
    label: "物资分类",
    prop: "materials_class"
  },
  {
    label: "物资名称",
    prop: "materials_name"
  },
  {
    label: "供应商名称",
    prop: "supplier_manage_name"
  },
  {
    label: "供应商类型",
    prop: "supplier_manage_type_alias"
  },
  // {
  //   label: "供应数量",
  //   prop: "count1"
  // },
  {
    label: "供应次数",
    prop: "count"
  },
  {
    label: "供应总金额",
    prop: "total_fee",
    type: "price"
  }
]

export const TABLE_DATA_SUPPLIER_TOTAL = [
  {
    categorize: "蔬菜类",
    name1: "土豆",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜批发商",
    account: "400 千克",
    num: "4 次",
    fee: "2400 元"
  },
  {
    categorize: "蔬菜类",
    name1: "西红柿",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜种植户",
    account: "300 千克",
    num: "5 次",
    fee: "2250 元"
  },
  {
    categorize: "蔬菜类",
    name1: "黄瓜",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜经销商",
    account: "350 千克",
    num: "5 次",
    fee: "1750 元"
  },
  {
    categorize: "蔬菜类",
    name1: "胡萝卜",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜批发商",
    account: "300 千克",
    num: "4 次",
    fee: "1800 元"
  },
  {
    categorize: "蔬菜类",
    name1: "白菜",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜种植户",
    account: "450 千克",
    num: "5 次",
    fee: "2250 元"
  },
  {
    categorize: "蔬菜类",
    name1: "青菜",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜经销商",
    account: "320 千克",
    num: "4 次",
    fee: "1600 元"
  },
  {
    categorize: "蔬菜类",
    name1: "芹菜",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜批发商",
    account: "280 千克",
    num: "4 次",
    fee: "1400 元"
  },
  {
    categorize: "蔬菜类",
    name1: "西兰花",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜种植户",
    account: "200 千克",
    num: "3 次",
    fee: "1500 元"
  },
  {
    categorize: "蔬菜类",
    name1: "花菜",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜经销商",
    account: "250 千克",
    num: "4 次",
    fee: "1250 元"
  },
  {
    categorize: "蔬菜类",
    name1: "冬瓜",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜批发商",
    account: "300 千克",
    num: "3 次",
    fee: "1200 元"
  },
  {
    categorize: "蔬菜类",
    name1: "南瓜",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜种植户",
    account: "250 千克",
    num: "3 次",
    fee: "1000 元"
  },
  {
    categorize: "蔬菜类",
    name1: "茄子",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜经销商",
    account: "220 千克",
    num: "4 次",
    fee: "1320 元"
  },
  {
    categorize: "蔬菜类",
    name1: "豆角",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜批发商",
    account: "200 千克",
    num: "3 次",
    fee: "1200 元"
  },
  {
    categorize: "蔬菜类",
    name1: "青椒",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜种植户",
    account: "180 千克",
    num: "3 次",
    fee: "900 元"
  },
  {
    categorize: "蔬菜类",
    name1: "红椒",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜经销商",
    account: "150 千克",
    num: "3 次",
    fee: "900 元"
  },
  {
    categorize: "蔬菜类",
    name1: "洋葱",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜批发商",
    account: "200 千克",
    num: "3 次",
    fee: "800 元"
  },
  {
    categorize: "蔬菜类",
    name1: "大蒜",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜种植户",
    account: "100 千克",
    num: "3 次",
    fee: "600 元"
  },
  {
    categorize: "蔬菜类",
    name1: "生姜",
    name2: "立恒食品批发有限责任公司",
    type: "蔬菜经销商",
    account: "80 千克",
    num: "3 次",
    fee: "480 元"
  },
  {
    categorize: "畜禽肉类",
    name1: "猪肉",
    name2: "喀什牛羊肉冷链供应公司",
    type: "肉类批发商",
    account: "300 千克",
    num: "6 次",
    fee: "15000 元"
  },
  {
    categorize: "畜禽肉类",
    name1: "牛肉",
    name2: "喀什牛羊肉冷链供应公司",
    type: "肉类经销商",
    account: "200 千克",
    num: "5 次",
    fee: "16000 元"
  },
  {
    categorize: "畜禽肉类",
    name1: "鸡肉",
    name2: "喀什牛羊肉冷链供应公司",
    type: "养殖企业",
    account: "400 千克",
    num: "8 次",
    fee: "8000 元"
  },
  {
    categorize: "畜禽肉类",
    name1: "鸭肉",
    name2: "喀什牛羊肉冷链供应公司",
    type: "肉类批发商",
    account: "350 千克",
    num: "7 次",
    fee: "7000 元"
  },
  {
    categorize: "畜禽肉类",
    name1: "羊肉",
    name2: "喀什牛羊肉冷链供应公司",
    type: "肉类经销商",
    account: "150 千克",
    num: "4 次",
    fee: "12000 元"
  }
]

export const TYPE_WARNING = [
  {
    label: "全部",
    value: ""
  },
  {
    label: "利润率盈余",
    value: "surplus"
  },
  {
    label: "利润率亏损",
    value: "loss"
  },
  {
    label: "原材料占比",
    value: "raw_material_percentage"
  }
]
export const TYPE_WARNING_STATUS = [
  {
    label: "全部",
    value: ""
  },
  {
    label: "待核实",
    value: "待核实"
  },
  {
    label: "核实中",
    value: "核实中"
  },
  {
    label: "已核实",
    value: "已核实"
  },
  {
    label: "已反馈",
    value: "已反馈"
  },
  {
    label: "审批不通过",
    value: "审批不通过"
  },
  {
    label: "审批已通过",
    value: "审批已通过"
  },
  {
    label: "已忽略",
    value: "已忽略"
  }
]

// 风险预警搜索表单设置
export const SEARCH_FORM_SETTING_RISK_WARNING = {
  // date_type: {
  //   type: "select",
  //   label: "",
  //   value: "1",
  //   dataList: [
  //     {
  //       label: "预警时间",
  //       value: "1"
  //     },
  //     {
  //       label: "更新时间",
  //       value: "2"
  //     }
  //   ],
  //   placeholder: "请选择",
  //   clearable: true
  // },
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "预警时间",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  classify: {
    type: "select",
    label: "预警指标",
    value: "business_warn",
    linkage: true,
    dataList: [
      {
        label: "经营预警",
        value: "business_warn"
      },
      {
        label: "物资风险",
        value: "material_risk"
      },
      // {
      //   label: "采购质量预警",
      //   value: "procurement_quality_warn"
      // },
      {
        label: "证件/合同预警",
        value: "document_contract_alerts"
      }
    ],
    placeholder: "请选择",
    clearable: false,
    collapseTags: true
  },
  warn_type: {
    type: "select",
    label: "类型",
    value: "",
    dataList: TYPE_WARNING as Array<any>,
    placeholder: "请选择",
    clearable: true,
    collapseTags: true
  }
}

// Ai预警搜索表单设置
export const SEARCH_FORM_SETTING_AI_WARNING = {
  date_type: {
    type: "select",
    label: "",
    value: "1",
    dataList: [
      {
        label: "预警时间",
        value: "1"
      },
      {
        label: "更新时间",
        value: "2"
      }
    ],
    placeholder: "请选择",
    clearable: true
  },
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  type: {
    type: "select",
    label: "类型",
    value: "0",
    dataList: TYPE_WARNING,
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true
  },
  status: {
    type: "select",
    label: "状态",
    value: "0",
    dataList: TYPE_WARNING_STATUS,
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true
  }
}
// ai预警记录表格设置
export const TABLE_SETTING_AI_WARNING = [
  {
    label: "预警时间",
    prop: "warn_time",
    "show-overflow-tooltip": true
  },
  {
    label: "预警类型",
    prop: "type",
    "show-overflow-tooltip": true
  },
  {
    label: "预警信息",
    prop: "message",
    width: "200px"
  },
  {
    label: "状态",
    prop: "status"
  },
  {
    label: "核实要求",
    prop: "yaoqiu"
  },
  {
    label: "忽略原因",
    prop: "reasion"
  },
  {
    label: "更新时间",
    prop: "update_time"
  },
  {
    label: "操作员",
    prop: "creator"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operationNew",
    width: "180px"
  }
]
// 经营监控搜索表单设置
export const SEARCH_FORM_SETTING_BUSINESS_MONITOR = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "日期筛选",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  }
}
// 经营监控记录表格设置
export const TABLE_SETTING_BUSINESS_MONITOR = [
  {
    label: "日期",
    prop: "warn_time",
    "show-overflow-tooltip": true
  },
  {
    label: "利润率",
    prop: "type",
    "show-overflow-tooltip": true
  },
  {
    label: "原材料支出",
    prop: "message"
  }
  // {
  //   label: "采购议价预警数",
  //   prop: "status"
  // }
]

// 采购物资表格设置
export const TABLE_SETTING_PURCHASE = [
  {
    label: "物资名称",
    prop: "materials_name",
    "show-overflow-tooltip": true
  },
  {
    label: "采购数量",
    prop: "purchase_count",
    "show-overflow-tooltip": true
  },
  {
    label: "采购价",
    prop: "purchase_price"
  },
  {
    label: "合计金额",
    prop: "total_fee"
  },
  {
    label: "供应商",
    prop: "supplier"
  }
]

// 出入库物资表格设置
export const TABLE_SETTING_INCOME = [
  {
    label: "物资名称",
    prop: "materials_name",
    "show-overflow-tooltip": true
  },
  {
    label: "采购数量",
    prop: "purchase_count",
    "show-overflow-tooltip": true
  },
  {
    label: "关联业务号",
    prop: "inventory_no"
  }
]

export const BASEINFO = [
  {
    org_id: 14,
    org_name: "华溪实验中学",
    level: "A",
    zhengList: [
      {
        name: "工商营业执照",
        code: "92440105MAD9UXG39G",
        company: "广州市惠来美食园（个体工商户）",
        type: "个体工商户",
        range:
          "餐饮业（具体经营项目等登录国家企业信用信息公示系统查询，网址：http://www.gsxt.gov.cn/。已发需经批准，经相关部门批准后方可开展经营活动）",
        date: "2024年01月22日",
        img: ""
      },
      {
        name: "餐饮服务许可证",
        company: "广州市惠来美食园",
        address: "广州海珠区",
        type: "中型餐馆",
        remark: "不含凉菜、不含裱花蛋糕、不含生食海产品",
        date: "2023年07月22日至2026年07月22日",
        img: ""
      },
      {
        name: "食品经营许可证",
        company: "广州市惠来美食园",
        code: "92440105MAD9UXG39G",
        person: "张晓丽",
        home: "广州市海珠区磨碟沙123号院66号",
        address: "广州市海珠区磨碟沙123号院66号",
        status: "餐饮服务经营者",
        type: "热食类食品制售", // 经营项目
        no: "JY241011255465212",
        tell: "12315",
        organ: "广州市海珠区市场监督管理局", // 发证机关
        date: "2026年07月22日",
        img: ""
      },
      {
        name: "卫生许可证",
        company: "广州市惠来美食园",
        person: "张晓丽",
        address: "广州市海珠区磨碟沙123号院66号",
        type: "餐饮店",
        organ: "广州市海珠区行政审批局", // 发证机关
        date: "2026年07月22日",
        img: ""
      },
      {
        name: "消防安全证书",
        company: "广州市惠来美食园", // 场所名称
        address: "广州市海珠区磨碟沙123号院66号",
        building: "校园1号食堂", //  建筑
        area: "1000平方米",
        person: "张晓丽",
        nature: "食堂", // 使用性质
        img: ""
      },
      {
        name: "税务登记证",
        company: "广州市惠来美食园", // 场所名称
        person: "张晓丽",
        address: "广州市海珠区磨碟沙123号院66号",
        register: "个体工商户",
        range:
          "餐饮业（具体经营项目等登录国家企业信用信息公示系统查询，网址：http://www.gsxt.gov.cn/。已发需经批准，经相关部门批准后方可开展经营活动）",
        tax: "依法确定", // 扣缴义务
        img: ""
      }
    ]
  },
  {
    org_id: 15,
    org_name: "田家炳实验小学",
    level: "B",
    zhengList: [
      {
        name: "工商营业执照",
        code: "92488905MAD9UXG78M",
        company: "广州市白云校园餐（个体工商户）",
        type: "个体工商户",
        range:
          "餐饮业（具体经营项目等登录国家企业信用信息公示系统查询，网址：http://www.gsxt.gov.cn/。已发需经批准，经相关部门批准后方可开展经营活动）",
        date: "2023年11月18日",
        img: ""
      },
      {
        name: "餐饮服务许可证",
        company: "广州市白云校园餐",
        address: "广州白云区",
        type: "中型餐馆",
        remark: "不含凉菜、不含裱花蛋糕、不含生食海产品",
        date: "2023年07月22日至2026年07月23日",
        img: ""
      },
      {
        name: "食品经营许可证",
        company: "广州市白云校园餐",
        code: "92488905MAD9UXG78M",
        person: "李美华",
        home: "广州市白云区",
        address: "广州市白云区",
        status: "餐饮服务经营者",
        type: "热食类食品制售", // 经营项目
        no: "JY241011255465985",
        tell: "12315",
        organ: "广州市白云区市场监督管理局", // 发证机关
        date: "2026年07月22日",
        img: ""
      },
      {
        name: "卫生许可证",
        company: "广州市白云校园餐",
        person: "李美华",
        address: "广州市白云区",
        type: "餐饮店",
        organ: "广州市白云区行政审批局", // 发证机关
        date: "2023年07月22日至2026年07月23日",
        img: ""
      },
      {
        name: "消防安全证书",
        company: "广州市白云校园餐", // 场所名称
        address: "广州市白云区",
        building: "校园1号食堂", //  建筑
        area: "1020平方米",
        person: "李美华",
        nature: "食堂", // 使用性质
        img: ""
      },
      {
        name: "税务登记证",
        company: "广州市白云校园餐", // 场所名称
        person: "李美华",
        address: "广州市白云区",
        register: "个体工商户",
        range:
          "餐饮业（具体经营项目等登录国家企业信用信息公示系统查询，网址：http://www.gsxt.gov.cn/。已发需经批准，经相关部门批准后方可开展经营活动）",
        tax: "依法确定", // 扣缴义务
        img: ""
      }
    ]
  },
  {
    org_id: 16,
    org_name: "启元中学",
    level: "B",
    zhengList: [
      {
        name: "工商营业执照",
        code: "92465165MAD9UXG75G",
        company: "广州市营养餐吧（个体工商户）",
        type: "个体工商户",
        range:
          "餐饮业（具体经营项目等登录国家企业信用信息公示系统查询，网址：http://www.gsxt.gov.cn/。已发需经批准，经相关部门批准后方可开展经营活动）",
        date: "2024年03月26日",
        img: ""
      },
      {
        name: "餐饮服务许可证",
        company: "广州市营养餐吧",
        address: "广州天河区",
        type: "中型餐馆",
        remark: "不含凉菜、不含裱花蛋糕、不含生食海产品",
        date: "2023年07月22日至2026年07月23日",
        img: ""
      },
      {
        name: "食品经营许可证",
        company: "广州市营养餐吧",
        code: "92465165MAD9UXG75G",
        person: "王月娥",
        home: "广州市天河区",
        address: "广州市天河区",
        status: "餐饮服务经营者",
        type: "热食类食品制售", // 经营项目
        no: "JY241011255445631",
        tell: "12315",
        organ: "广州市天河区市场监督管理局", // 发证机关
        date: "2026年07月22日",
        img: ""
      },
      {
        name: "卫生许可证",
        company: "广州市营养餐吧",
        person: "王月娥",
        address: "广州市天河区",
        type: "餐饮店",
        organ: "广州市天河区行政审批局", // 发证机关
        date: "2023年07月22日至2026年07月24日",
        img: ""
      },
      {
        name: "消防安全证书",
        company: "广州市营养餐吧", // 场所名称
        address: "广州市天河区",
        building: "校园1号食堂", //  建筑
        area: "1102平方米",
        person: "王月娥",
        nature: "食堂", // 使用性质
        img: ""
      },
      {
        name: "税务登记证",
        company: "广州市营养餐吧", // 场所名称
        person: "王月娥",
        address: "广州市天河区",
        register: "个体工商户",
        range:
          "餐饮业（具体经营项目等登录国家企业信用信息公示系统查询，网址：http://www.gsxt.gov.cn/。已发需经批准，经相关部门批准后方可开展经营活动）",
        tax: "依法确定", // 扣缴义务
        img: ""
      }
    ]
  }
]
