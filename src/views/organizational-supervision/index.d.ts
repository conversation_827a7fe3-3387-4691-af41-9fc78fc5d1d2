export type OrganizationParams = {
  page: number
  page_size: number
  id?: number
  status?: string
  create_time?: string
  parent?: string
  company?: string
  name?: string
  level?: string
  status__in?: string[]
  parent__in?: number
  parent__is_null?: string
  name__contains?: string
}

// 采购，出入库字段类型
export type PurchaseForm = {
  organization: string // 所属组织
  trade_no: string // 单据编号
  warehouse: string // 所属仓库
  type: string // 类型
  account_name: string // 经手人
  creator: string // 制单人
  approve_account_name: string // 审核人
  create_time: string // 创建日期
  approve_time: string // 审核日期
  purchase_time: string // 采购日期
}

// 监管组织树类型
export type OrgsForm = {
  id: number
  status?: string
  create_time?: string
  update_time?: string
  parent?: number
  name: string
  parent_names?: any
  color?: string
  supervision_organizations?: any[]
  tree_id?: number
  level?: number
  status_alias?: string
  parent_alias?: string
  children_list?: any[]
  has_children?: boolean
  get_login_token?: string
  binded_org_info?: IBindedOrgInfo[]
  organizations_data?: IOrganizationsData[]
  isJian?: boolean
}
// 组织信息
interface IBindedOrgInfo {
  org_name: string
  company_name: string
  update_time: string
  operator_name: string
  org_id: number
}

interface IOrganizationsData {
  org_id: number
  org_name: string
  company_id: number
  company_name: string
}

// AI信息类型
export type TAiForm = {
  warning_time: string // 预警时间
  organization: string // 所属组织
  origin_channel: string // 所属渠道
  type: string // 预警类型
  channel: string // 预警信息
  status: string // 状态
  time: string // 下发时间
  operator_name: string // 操作员
  request: string // 核实要求
  verify_time: string // 核实时间
  verify_info: string // 核实信息
  ignore_time: string // 忽略时间
  ignore_reason: string // 忽略原因
  remark: ""
}
