<template>
  <div class="container-wrapper sample-record">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowDetail(row)" type="primary"> 详情 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { SEARCH_FORM_SETTING_SAMPLE_RECORD, TABLE_SETTING_SAMPLE_RECORD } from "../constants"
import { apiBackgroundFundSupervisionSupervisionDataFoodReservedPost } from "@/api/supervision"
import { ElMessage } from "element-plus"
const props = defineProps({
  selectTree: {
    type: Object,
    default: () => {}
  }
})

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(20, 40, psTableRef, 80)
// table数据
const tableData = ref([{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_SAMPLE_RECORD))
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_SAMPLE_RECORD)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}
// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionSupervisionDataFoodReservedPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize,
      // org_id: props.selectTree.id || 1096
      org_id: 1096
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
// 详情
const handlerShowDetail = (row: any) => {
  console.log(row)
}

onMounted(() => {
  getDataList()
})
</script>
<style lang="scss" scoped>
.sample-record {
  .search-form-wrapper {
    box-shadow: none !important;
    min-height: 40px !important;
  }
}
</style>
