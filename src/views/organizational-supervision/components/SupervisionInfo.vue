<template>
  <div class="supervision-info-container container-wrapper" v-loading="loading">
    <div ref="searchRef">
      <div class="title-txt">监管概览</div>
      <div class="project-bg m-t-16px">
        <div
          v-for="(item, index) in supervisionList"
          :key="index"
          :class="['supervision-tag', 'bg-lin' + (index + 1), index > 0 ? 'm-l-20px' : '']"
        >
          <!-- <img class="tag-img" :src="item.icon" /> -->
          <div class="tag-name m-l-20px m-t-20px">{{ item.name }}</div>
          <div class="tag-value m-l-20px m-t-12px">{{ item.value }}</div>
        </div>
      </div>
      <div class="title-txt m-t-32px flex justify-between">
        <div>预警概览</div>
        <el-date-picker
          v-model="monthValue"
          type="month"
          @change="changMonthDate"
          style="width: 120px"
          format="YYYY-MM"
          value-format="YYYY-MM"
          :clearable="false"
          :disabled-date="disabledDate"
          class="m-r-10"
        />
      </div>
    </div>
    <!-- <div class="title-txt m-t-32px">经营风险</div>
    <div>
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="">
          <el-tooltip placement="top-start">
            <template #content> {{ tipTxt }} </template>
            <img :src="IcQuestionBlack" class="w-32px h-32px" />
          </el-tooltip>
        </div>
        <div class="table-button">
          <el-button type="primary" @click="handlerShowSetting">报表设置</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :maxHeight="maxHeight"
        >
          <ps-column :table-headers="tableSetting" />
        </ps-table>
      </div>
    </div> -->
    <div class="warning-table m-t-20px">
      <div class="project-bg m-t-16px m-b-20px">
        <div
          v-for="(item, index) in warningList"
          :key="index"
          :class="['supervision-tag', 'bg-lin' + (index + 1), index > 0 ? 'm-l-20px' : '']"
        >
          <!-- <img class="tag-img" :src="item.icon" /> -->
          <div class="tag-name m-l-20px m-t-20px">{{ item.name }}</div>
          <div class="tag-value m-l-20px m-t-12px">{{ item.value }}</div>
        </div>
      </div>
      <div class="ps-flex row-between">
        <div class="title-txt">预警明细</div>
        <div class="table-button">
          <el-radio-group v-model="dataConfig.tableType" @change="tableTypeChange">
            <el-radio-button value="business_warn">经营预警</el-radio-button>
            <el-radio-button value="raw_material_risk">物资风险</el-radio-button>
            <!-- <el-radio-button value="procurement_quality_warn">采购质量预警</el-radio-button> -->
            <el-radio-button value="document_contract_alerts">证件/合同</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="m-t-14px">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
        >
          <ps-column :table-headers="tableSetting">
            <template #warnText="{ row }">
              <div v-if="row.file_type_alias === '健康证'">
                {{ row.warn_text }}<span class="color-[#ff5656]">（ {{ row.account_name }} ）</span>
              </div>
              <div v-else>{{ row.warn_text }}</div>
            </template>
            <template #content="{ row }">
              {{ row.warn_detail }}（<span class="red-text">{{ row.warn_val }}%</span>）
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <div class="ps-flex row-between m-t-32px">
      <div class="title-txt">财务总览</div>
      <div class="w-200px">
        <!-- <supervision-orgs v-model="dataConfig.school" :multiple="true" :width="'160px'" /> -->
        <el-select v-model="dataConfig.school" multiple collapse-tags placeholder="请选择学校">
          <el-option v-for="item in dataConfig.schoolist" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </div>
    </div>
    <!-- :channel-id="selectTree.id" -->
    <supervision-data v-if="props.selectTree" :school="dataConfig.school" :channel-id="props.selectTree.id" />
    <supervision-setting :is-show="isShowSetting" @cancel-dialog="handlerCancel" @confirm-dialog="handlerConfirm" />
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue"
import { cloneDeep } from "lodash-es"
import {
  // TABLE_SETTING_SUPERVISION_INFO,
  SEARCH_FORM_SETTING_SUPERVISION_INFO,
  TABLE_SETTING_WARNING_INFO,
  TABLE_SETTING_RAW_MATERIAL_RISK,
  TABLE_SETTING_DOCUMENT_CONTRACT_ALERTS
} from "../constants"
import icSupervisionTag1 from "@/assets/images/ic_supervision_tag_1.png"
import icSupervisionTag2 from "@/assets/images/ic_supervision_tag_2.png"
import icSupervisionTag3 from "@/assets/images/ic_supervision_tag_3.png"
import icSupervisionTag4 from "@/assets/images/ic_supervision_tag_4.png"
import icSupervisionTag5 from "@/assets/images/ic_supervision_tag_5.png"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import SupervisionSetting from "./SupervisionSetting.vue"
import SupervisionData from "./SupervisionData.vue"
import to from "await-to-js"
import {
  apiBackgroundFundSupervisionOrganizationSupervisionRegulatoryPreviewData,
  apiBackgroundFundSupervisionOrganizationSupervisionWarningOverview,
  apiBackgroundFundSupervisionOrganizationSupervisionWarningDetails,
  apiCanteenChannelList
} from "@/api/supervision/index"
import { ElMessage } from "element-plus"
import { apiBackgroundFundSupervisionSupervisionChannelChannelRoleOrgsInofPost } from "@/api/user"

const props = defineProps({
  selectTree: {
    type: Object,
    default: () => {}
  }
})

// 表格最大高度计算
const psTableRef = ref()
// const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 60, psTableRef, 24)
// table数据
const tableData = ref()
const loading = ref(false)
const searchFormSetting = ref<any>(cloneDeep(SEARCH_FORM_SETTING_SUPERVISION_INFO))
const tableLoading = ref(false)
const tableSetting = ref<any>(cloneDeep(TABLE_SETTING_WARNING_INFO))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
const dataConfig = reactive({
  tableType: "business_warn",
  school: [],
  schoolist: ref<Array<any>>([])
})
const tipTxt =
  "食堂利润不得超过5%；自营食堂直接成本(原辅材料成本)不得低于伙食费标准的65%，承包、托管经营食堂直接成本(原辅材料成本)不得低于学生伙食费标准的60%。红色为超标预警，灰色为预警忽略。"

const monthValue = ref("")

const supervisionList = ref<Array<any>>([
  {
    id: 1,
    name: "监管渠道（教育局）",
    value: 0,
    icon: icSupervisionTag1
  },
  {
    id: 2,
    name: "监管数量（食堂）",
    value: 0,
    icon: icSupervisionTag2
  },
  {
    id: 3,
    name: "直属监管（食堂）",
    value: 0,
    icon: icSupervisionTag3
  },
  {
    id: 4,
    name: "自营食堂（家）",
    value: 0,
    icon: icSupervisionTag4
  },
  {
    id: 5,
    name: "承包/托管经营食堂（家）",
    value: 0,
    icon: icSupervisionTag5
  }
])

const warningList = ref<Array<any>>([
  {
    id: 1,
    name: "利润预警",
    value: 0,
    icon: icSupervisionTag1
  },
  {
    id: 2,
    name: "原材料支出预警",
    value: 0,
    icon: icSupervisionTag2
  },
  {
    id: 3,
    name: "出入库预警",
    value: 0,
    icon: icSupervisionTag3
  },
  {
    id: 4,
    name: "证件过期预警",
    value: 0,
    icon: icSupervisionTag4
  },
  {
    id: 5,
    name: "合同过期预警",
    value: 0,
    icon: icSupervisionTag5
  }
])

// 弹窗
const isShowSetting = ref(false)

// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  // getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  // getDataList()
}
// 关闭弹窗
const handlerCancel = () => {
  isShowSetting.value = false
}
// 弹窗确认
const handlerConfirm = () => {
  isShowSetting.value = false
}
// 显示报表设置
const handlerShowSetting = () => {
  isShowSetting.value = true
}

// 监管概览
const getSupervisionInfo = async () => {
  loading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionOrganizationSupervisionRegulatoryPreviewData({
      supervision_channel_id: props.selectTree.id
    })
  )
  loading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    const data = res.data || {}
    if (supervisionList.value && supervisionList.value.length >= 4) {
      supervisionList.value[0].value = data.supervision_channel_count || 0
      supervisionList.value[1].value = data.supervision_org_count || 0
      supervisionList.value[2].value = data.supervision_sub_org_count || 0
      supervisionList.value[3].value = data.operated_canteen_count || 0
      supervisionList.value[4].value = data.contract_canteen_count || 0
    }
  } else {
    ElMessage.error(res.msg)
  }
}

// 预警概览
const getWarningInfo = async () => {
  loading.value = true
  let params = {
    supervision_channel_id: props.selectTree.id
  }
  if (monthValue.value) {
    Reflect.set(params, "date", monthValue.value)
  } else {
    Reflect.deleteProperty(params, "date")
  }
  const [err, res] = await to(apiBackgroundFundSupervisionOrganizationSupervisionWarningOverview(params))
  loading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    const data = res.data || {}
    if (warningList.value && warningList.value.length >= 4) {
      warningList.value[0].value = data.profit_warn_count || 0
      warningList.value[1].value = data.materials_warn_count || 0
      warningList.value[2].value = data.materials_risk_warn_count || 0
      warningList.value[3].value = data.documents_overdue_warn_count || 0
      warningList.value[4].value = data.contract_overdue_warn_count || 0
    }
  } else {
    ElMessage.error(res.msg)
  }
}

// 预警明细
const getWarningDetails = async () => {
  loading.value = true
  let params = {
    supervision_channel_id: props.selectTree.id,
    classify: dataConfig.tableType,
    page: pageConfig.currentPage,
    page_size: pageConfig.pageSize
  }
  if (monthValue.value) {
    Reflect.set(params, "date", monthValue.value)
  } else {
    Reflect.deleteProperty(params, "date")
  }
  const [err, res] = await to(apiBackgroundFundSupervisionOrganizationSupervisionWarningDetails(params))
  loading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getWarningDetails()
}

const tableTypeChange = () => {
  pageConfig.currentPage = 1
  switch (dataConfig.tableType) {
    case "business_warn":
      tableSetting.value = cloneDeep(TABLE_SETTING_WARNING_INFO)
      break
    case "raw_material_risk":
      tableSetting.value = cloneDeep(TABLE_SETTING_RAW_MATERIAL_RISK)
      break
    case "document_contract_alerts":
      tableSetting.value = cloneDeep(TABLE_SETTING_DOCUMENT_CONTRACT_ALERTS)
      break
  }
  getWarningDetails()
}

// 获取渠道下的组织
const getCanteenChannelList = async () => {
  loading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionSupervisionChannelChannelRoleOrgsInofPost({
      supervision_channel_id: props.selectTree.id
    })
  )
  loading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    const data = res.data || {}
    const results = data.results || []
    dataConfig.schoolist = results
  } else {
    ElMessage.error(res.msg)
  }
}
// 月份改变
const changMonthDate = (value: any) => {
  console.log("changMonthDate", value)
  getWarningInfo()
  pageConfig.currentPage = 1
  getWarningDetails()
}
// 禁用未来的日期
const disabledDate = (time: any) => {
  return time.getTime() > Date.now()
}

onMounted(() => {
  // getCanteenChannelList()
  monthValue.value =
    new Date().getFullYear() + "-" + (new Date().getMonth() > 10 ? new Date().getMonth() : "0" + new Date().getMonth())
  if (props.selectTree) {
    initData()
  }
})
// 初始化数据
const initData = () => {
  getSupervisionInfo()
  getWarningInfo()
  getWarningDetails()
  getCanteenChannelList()
}

watch(
  () => props.selectTree,
  (newValue) => {
    console.log("selectTree", newValue)
    initData()
  }
)
</script>
<style lang="scss" scoped>
.supervision-info-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  .search-form-wrapper {
    box-shadow: none !important;
    min-height: 40px !important;
    :deep(.el-button) {
      margin-bottom: 0 !important;
    }
  }
  .red-text {
    color: red;
  }
  .table-wrapper {
    margin-top: 0 !important;
  }

  :deep(.el-form-item) {
    margin-bottom: 0 !important;
  }
  .el-button {
    margin-bottom: 0 !important;
  }

  .title-txt {
    color: #20201f;
    font-size: 20px;
  }

  .supervision-tag {
    display: inline-block;
    width: 247px;
    height: 104px;
    position: relative;

    .tag-img {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
    }

    .tag-name {
      color: #363636;
      font-size: 16px;
    }

    .tag-value {
      color: #191c1e;
      font-size: 40px;
    }
  }

  .warning-table {
    border: 1px #ebeef5 solid;
    padding: 20px;
  }

  .bg-lin1 {
    background: linear-gradient(180deg, #eff0f4 0%, #f8f9fa 100%);
  }

  .bg-lin2 {
    background: linear-gradient(180deg, #e7efff 0%, #f8faff 100%);
  }

  .bg-lin3 {
    background: linear-gradient(180deg, #e5f7ff 0%, #f5fbff 100%);
  }

  .bg-lin4 {
    background: linear-gradient(180deg, #f5f1ff 0%, #fefcff 100%);
  }

  .bg-lin5 {
    background: linear-gradient(180deg, #fff5ed 0%, #fffbf8 100%);
  }
  .project-bg {
    display: flex;
  }
}
</style>
