<template>
  <div class="container-wrapper storage-record">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="table-button">
          <el-button
            type="primary"
            @click="goToExport"
            v-permission="['background_fund_supervision.organization_supervision.supply_summary_table_export']"
            >导出</el-button
          >
          <el-button type="primary" @click="gotoPrint">打印</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowDetail(row)" type="primary"> 结算明细 </el-button>
              <el-button plain link size="small" @click="handlerShowDetail(row)" type="primary"> 供应商 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import {
  apiSupervisionSupplySummaryTableList,
  apiBackgroundFundSupervisionOrganizationSupervisionSupplySummaryTableExportPost,
  apiBackgroundFundSupervisionOrganizationSupervisionOrgMaterailClassificationPost
} from "@/api/supervision/index"
import {
  SEARCH_FORM_SETTING_SUPPLIER_TOTAL,
  TABLE_SETTING_SUPPLIER_TOTAL,
  TABLE_DATA_SUPPLIER_TOTAL
} from "../constants"
import { ElMessage } from "element-plus"
import { useRouter } from "vue-router"
import { exportHandle } from "@/utils/exportExcel"
import { setLocalStorage } from "@/utils/storage"

// 路由
const router = useRouter()
const props = defineProps({
  selectTree: {
    type: Object,
    required: false,
    default: () => {}
  }
})

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef, 90)
// table数据
const allTableData = cloneDeep(TABLE_DATA_SUPPLIER_TOTAL)
const tableData = ref()
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_SUPPLIER_TOTAL))
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_SUPPLIER_TOTAL))
const pageConfig = reactive({
  total: allTableData.length,
  currentPage: 1,
  pageSize: 10
})
// 导出
const printType = "SupplierTotalExport"
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "" && data[key].value.length > 0) {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}

// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getVendorManageSummary()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getVendorManageSummary()
}

// 供应商汇总
const getVendorManageSummary = async () => {
  loading.value = true
  const [err, res] = await to(
    apiSupervisionSupplySummaryTableList({
      ...formatQueryParams(searchFormSetting.value),
      org_id: props.selectTree.id,
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  loading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    let results = data.results || []
    tableData.value = cloneDeep(results)
    pageConfig.total = data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 获取物资分类
const getMaterialClassification = async () => {
  loading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionOrganizationSupervisionOrgMaterailClassificationPost({
      org_id: props.selectTree.id
    })
  )
  loading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    let data = res.data || []
    if (data && data.length > 0) {
      searchFormSetting.value.material_type.dataList = cloneDeep(data)
    }
    console.log("results", data)
  } else {
    ElMessage.error(res.msg)
  }
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getVendorManageSummary()
}

// 详情
const handlerShowDetail = (row: any) => {
  console.log(row)
  router.push({ path: "food_safety_traceability", query: { id: row.id } })
}

// 导出
const goToExport = () => {
  const option = {
    type: "SupplierTotal",
    api: apiBackgroundFundSupervisionOrganizationSupervisionSupplySummaryTableExportPost,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      org_id: props.selectTree.id,
      page: pageConfig.currentPage,
      page_size: pageConfig.total ? pageConfig.total : 9999
    }
  }
  exportHandle(option)
}
// 打印
// 打印
const gotoPrint = () => {
  let tabbleSetting = cloneDeep(tableSetting.value)
  tabbleSetting = tabbleSetting.filter((item: any) => item.prop !== "imgs")
  setLocalStorage("print_setting", tabbleSetting)
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: printType,
      print_title: "供应汇总表",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiSupervisionSupplySummaryTableList",
      params: JSON.stringify({
        ...formatQueryParams(searchFormSetting.value),
        org_id: props.selectTree.id,
        page: 1,
        page_size: pageConfig.total ? pageConfig.total : 9999
      })
    }
  })
  window.open(href, "_blank")
}

onMounted(() => {
  getVendorManageSummary()
  getMaterialClassification()
})
</script>
<style lang="scss" scoped>
.storage-record {
  .search-form-wrapper {
    box-shadow: none !important;
    min-height: 40px !important;
  }
}
.container-wrapper .table-wrapper {
  box-shadow: none;
  -webkit-box-shadow: none;
}
</style>
