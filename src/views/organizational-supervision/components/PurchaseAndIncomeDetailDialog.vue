<template>
  <div class="purchase-detail container-wrapper">
    <el-drawer
      v-model="dialogFormVisible"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div>
        <div class="flex">
          <div class="tag-title">所属组织</div>
          <div class="tag-content">{{ ruleForm.organization }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">单据编号</div>
          <div class="tag-content">{{ ruleForm.trade_no }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">所属仓库</div>
          <div class="tag-content">{{ ruleForm.warehouse }}</div>
        </div>
        <div class="flex" v-if="type == 'income'">
          <div class="tag-title">类型</div>
          <div class="tag-content">{{ ruleForm.type }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">经手人</div>
          <div class="tag-content">{{ ruleForm.account_name }}</div>
        </div>
        <div class="flex" v-if="type == 'purchase'">
          <div class="tag-title">制单人</div>
          <div class="tag-content">{{ ruleForm.creator }}</div>
        </div>
        <div class="flex" v-if="type == 'purchase'">
          <div class="tag-title">审核人</div>
          <div class="tag-content">{{ ruleForm.approve_account_name }}</div>
        </div>
        <div class="flex" v-if="type == 'purchase'">
          <div class="tag-title">审核日期</div>
          <div class="tag-content">{{ ruleForm.approve_time }}</div>
        </div>
        <div class="flex" v-if="type == 'purchase'">
          <div class="tag-title">采购日期</div>
          <div class="tag-content">{{ ruleForm.purchase_time }}</div>
        </div>
        <div class="flex">
          <div class="tag-title border-bottom">创建时间</div>
          <div class="tag-content border-bottom">{{ ruleForm.create_time }}</div>
        </div>
      </div>
      <!--物资信息-->
      <div class="table-content">
        <div class="m-t-20px m-b-10px">物资信息：</div>
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          maxHeight="300px"
        >
          <ps-column :table-headers="tableSetting" />
        </ps-table>
      </div>
      <div class="dialog-footer m-t-20px">
        <el-button type="primary" @click="confirmDialog" v-loading="confirmLoading"> 关闭 </el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue"
import type { PurchaseForm } from "../index.d"
import cloneDeep from "lodash/cloneDeep"
import { TABLE_SETTING_PURCHASE, TABLE_SETTING_INCOME } from "../constants"

const props = defineProps({
  title: {
    type: String,
    default: "采购单详情"
  },
  width: {
    type: String,
    default: "1048px"
  },
  isShow: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: "purchase"
  },
  orgId: {
    type: Number,
    default: -1
  },
  alreadyChooseIds: {
    type: Array,
    default: () => {
      return []
    }
  },
  // 选择节点数据
  organizationData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const dialogFormVisible = ref(false)
const confirmLoading = ref(false)
const ruleForm = reactive<PurchaseForm>({
  organization: "田家实验中学",
  trade_no: "***************************",
  warehouse: "食堂仓库",
  type: "领用出库",
  account_name: "张三",
  creator: "诸葛亮",
  approve_account_name: "黄务龙",
  create_time: "2024-10-17 10:10:10",
  approve_time: "2024-10-18",
  purchase_time: "2024-10-19"
})
// table数据
const tableData = ref([{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}])
const tableLoading = ref(false)
const tableSetting = cloneDeep(props.type === "purchase" ? TABLE_SETTING_PURCHASE : TABLE_SETTING_INCOME)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 9999
})

// 弹窗确认
const confirmDialog = () => {
  emit("confirmDialog")
}

// 弹窗关闭
const closeDialog = () => {
  emit("cancelDialog")
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  // getDataList()
}
watch(
  () => ({
    isShow: props.isShow
  }),
  (newValue) => {
    console.log("watch dialog", newValue)
    dialogFormVisible.value = newValue.isShow
    if (newValue.isShow) {
      // initData()
    }
  }
)
defineExpose({})
</script>
<style lang="scss" scoped>
.container-wrapper .table-content {
  padding: 0 !important;
}
.purchase-detail {
  .tag-title {
    width: 120px;
    min-width: 120px;
    height: 40px;
    line-height: 40px;
    background: #f4f6fc;
    font-size: 14px;
    padding-left: 20px;
    border-top: 1px solid #e9ecf1;
    border-left: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .tag-content {
    min-width: 880px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    padding-left: 20px;
    border-top: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .border-bottom {
    border-bottom: 1px solid #e9ecf1;
  }
}
</style>
