<template>
  <div class="container-wrapper purchase-record">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="flex">
          <div>待核实：8</div>
          <div class="m-l-20px">核实中：4</div>
          <div class="m-l-20px">已核实：4</div>
          <div class="m-l-20px">已忽略：2</div>
        </div>
        <div class="table-button">
          <el-button type="primary" class="ps-origin-btn-light" @click="handlerMulti">批量下发</el-button>
          <el-button type="primary" @click="handlerMulti">批量忽略</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowDetail(row, 'detail')" type="primary">
                详情
              </el-button>
              <span style="margin: 0 5px; color: #e2e8f0">|</span>
              <el-button
                plain
                link
                size="small"
                @click="handlerShowDetail(row, 'ignore')"
                type="primary"
                class="btn-black"
              >
                忽略
              </el-button>
              <span style="margin: 0 5px; color: #e2e8f0">|</span>
              <el-button
                plain
                link
                size="small"
                @click="handlerShowDetail(row, 'issued')"
                type="primary"
                class="btn-light"
              >
                下发核实
              </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!--弹窗-->
    <ai-detail-dialog
      :is-show="isShowDetail"
      :type="dialogType"
      :title="dialogTitle"
      :width="width"
      @confirm-dialog="handlerConfirmDetail"
      @cancel-dialog="handlerCloseDetail"
    />
  </div>
</template>
<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import AiDetailDialog from "./AiDetailDialog.vue"
import { SEARCH_FORM_SETTING_AI_WARNING, TABLE_SETTING_AI_WARNING } from "../constants"
import { apiCardGroupListPost } from "@/api/user"
import { ElMessage } from "element-plus"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(100, 40, psTableRef, 70)
// table数据
const tableData = ref([{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_AI_WARNING))
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_AI_WARNING)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 弹窗
const isShowDetail = ref(false)
const dialogType = ref("detail")
const dialogTitle = ref("预警详情")
const width = ref("648px")

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "feature") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.feature = data[key].value
      }
    }
  }
  return params
}
// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiCardGroupListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
// 详情
const handlerShowDetail = (row: any, type: string) => {
  console.log("handlerShowDetail", row, type)
  dialogType.value = type
  dialogTitle.value = type === "detail" ? "预警详情" : type === "issued" ? "下发预警" : "忽略预警"
  width.value = type === "detail" ? "648px" : "538px"
  isShowDetail.value = true
}
// 批量下发
const handlerMulti = () => {
  console.log("批量下发")
}
// 详情确认
const handlerConfirmDetail = () => {
  console.log("handlerConfirmDetail")
  isShowDetail.value = false
}
// 关闭详情
const handlerCloseDetail = () => {
  console.log("handlerCloseDetail")
  isShowDetail.value = false
}
onMounted(() => {
  // getDataList()
})
</script>
<style lang="scss" scoped>
.purchase-record {
  .search-form-wrapper {
    box-shadow: none !important;
    min-height: 40px !important;
    :deep(.el-button) {
      margin-bottom: 0 !important;
    }
  }
  :deep(.el-form-item) {
    margin-bottom: 0 !important;
  }
  .el-button {
    margin-bottom: 0 !important;
  }
  .btn-black {
    color: #363636;
  }
  .btn-light {
    color: var(--el-btn-color-light);
  }
}
</style>
