<template>
  <div class="container-wrapper storage-record">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowDetail(row)" type="primary"> 详情 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!--弹窗-->
    <purchase-and-income-detail-dialog
      :is-show="isShowDetail"
      :title="dialogTitle"
      :type="dialogType"
      @confirm-dialog="handlerCloseDetail"
    />
  </div>
</template>
<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { SEARCH_FORM_SETTING_STORAGE_RECORD, TABLE_SETTING_STORAGE_RECORD } from "../constants"
import { apiCardGroupListPost } from "@/api/user"
import { ElMessage } from "element-plus"
import PurchaseAndIncomeDetailDialog from "./PurchaseAndIncomeDetailDialog.vue"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(20, 40, psTableRef, 80)
// table数据
const tableData = ref([{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_STORAGE_RECORD))
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_STORAGE_RECORD)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

// 弹窗
const isShowDetail = ref(false)
const dialogTitle = ref("出入库详情")
const dialogType = ref("income")

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "feature") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.feature = data[key].value
      }
    }
  }
  return params
}
// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiCardGroupListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 详情
const handlerShowDetail = (row: any) => {
  console.log(row)
  isShowDetail.value = true
}
// 关闭详情
const handlerCloseDetail = () => {
  isShowDetail.value = false
}

onMounted(() => {
  // getDataList()
})
</script>
<style lang="scss" scoped>
.storage-record {
  .search-form-wrapper {
    box-shadow: none !important;
    min-height: 40px !important;
  }
}
</style>
