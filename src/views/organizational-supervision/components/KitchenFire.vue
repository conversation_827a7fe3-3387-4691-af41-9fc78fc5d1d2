<template>
  <div class="kitchen-fire">
    <div class="flex flex-wrap">
      <div
        v-for="(item, index) in imgList"
        :key="index"
        :class="['img-tag', 'm-t-16px', index != 0 && index != 3 ? 'm-l-16px' : '']"
      >
        <img :src="item" alt="" />
      </div>
    </div>
    <div class="flex justify-end m-t-5px">
      <div class="flex">
        <img :src="icMclzBlack1" class="w-18px h-18px" />
        <div class="m-l-3px">录屏</div>
      </div>
      <div class="flex m-l-24px">
        <img :src="icMclzBlack2" class="w-18px h-18px" />
        <div class="m-l-3px">抓拍</div>
      </div>
      <div class="flex m-l-24px">
        <img :src="icMclzBlack3" class="w-18px h-18px" />
        <div class="m-l-3px">拉伸</div>
      </div>
      <div class="flex m-l-24px">
        <img :src="icMclzBlack4" class="w-18px h-18px" />
        <div class="m-l-3px">布局</div>
      </div>
      <div class="flex m-l-24px">
        <img :src="icMclzBlack5" class="w-18px h-18px" />
        <div class="m-l-3px">设置</div>
      </div>
      <div class="flex m-l-24px">
        <img :src="icMclzBlack6" class="w-18px h-18px" />
        <div class="m-l-3px">全屏</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue"
import bgMclz1 from "@/assets/test/bg_mclz_1.png"
import bgMclz2 from "@/assets/test/bg_mclz_2.png"
import bgMclz3 from "@/assets/test/bg_mclz_3.png"
import bgMclz4 from "@/assets/test/bg_mclz_4.png"
import bgMclz5 from "@/assets/test/bg_mclz_5.png"
import bgMclz6 from "@/assets/test/bg_mclz_6.png"
import icMclzBlack1 from "@/assets/test/ic_mclz_black_1.png"
import icMclzBlack2 from "@/assets/test/ic_mclz_black_2.png"
import icMclzBlack3 from "@/assets/test/ic_mclz_black_3.png"
import icMclzBlack4 from "@/assets/test/ic_mclz_black_4.png"
import icMclzBlack5 from "@/assets/test/ic_mclz_black_5.png"
import icMclzBlack6 from "@/assets/test/ic_mclz_black_6.png"

// 图片列表
const imgList = ref([bgMclz1, bgMclz2, bgMclz3, bgMclz4, bgMclz5, bgMclz6])
</script>
<style lang="scss" scoped>
.kitchen-fire {
  img-tag {
    width: 428px;
    height: 321px;
    border-radius: 10px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
