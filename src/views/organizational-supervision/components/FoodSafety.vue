<template>
  <div class="food-safety-container container-wrapper">
    <div class="title-txt m-t-32px">食品卫生安全管理</div>
    <div class="flex flex-wrap m-t-16px head-box">
      <div v-for="(item, index) in safetyList" :key="index" :class="['img-box-2', index > 0 ? 'm-l-20px' : '']">
        <img :src="item.face_url" class="w-132px h-154px cursor-pointer" @click="handlerShowPhoto(item.face_url)" />
        <div class="unit-tag">{{ item.job_title }}</div>
        <div class="flex flex-col items-center">
          <div class="text-size-16px m-t-10px">{{ item.name }}</div>
          <div class="text-size-14px m-t-8px color-[#737373]">{{ item.phone }}</div>
        </div>
      </div>
    </div>
    <div class="title-txt m-t-32px">食堂管理领导小组</div>
    <div class="flex flex-wrap m-t-16px head-box">
      <div v-for="(item, index) in leaderList" :key="index" :class="['img-box-2', index > 0 ? 'm-l-20px' : '']">
        <img :src="item.face_url" class="w-132px h-154px cursor-pointer" @click="handlerShowPhoto(item.face_url)" />
        <div class="unit-tag">{{ item.job_title }}</div>
        <div class="flex flex-col items-center">
          <div class="text-size-16px m-t-10px">{{ item.name }}</div>
          <div class="text-size-14px m-t-8px color-[#737373]">{{ item.phone }}</div>
        </div>
      </div>
    </div>
    <div class="title-txt m-t-32px m-b-20px">人员配比</div>
    <div v-for="(item, index) in personnelRatio" :key="index" class="m-t-15px">
      {{ item.job_title }}: {{ item.count }}人
    </div>
    <div class="title-txt m-t-32px">在职人员（{{ pageConfig.total }} 人）</div>
    <div class="table-wrapper m-b-40px" ref="tableWrapperRef">
      <div class="table-content m-t-16px">
        <ps-table
          :tableData="personList"
          ref="psTableRef"
          v-loading="loading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
        >
          <ps-column :table-headers="tableSetting">
            <template #faceImage="{ row }">
              <el-button plain link size="small" @click="handlerShowPhoto(row.face_image)" type="primary">
                查看
              </el-button>
            </template>
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowPhoto(row.health_image)" type="primary">
                查看
              </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, toRefs } from "vue"
import { getImgByName } from "@/hooks/useTheme"
import IcTestZhiZhao from "@/assets/test/ic_test_zhizhao.png"
import { cloneDeep } from "lodash"
import { TABLE_SETTING_ON_JOB } from "../constants"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { ElMessage } from "element-plus"
import to from "await-to-js"
import {
  apiSupervisionFoodSafetyPublicity,
  apiSupervisionJobPersonList,
  apiSupervisionPersonnelRatio
} from "@/api/supervision/index"
import icJiangkang from "@/assets/images/ic_jiangkang.png"
import zhanghua from "@/assets/test/zhanghua.png"
import yanghai from "@/assets/test/yanghai.png"
import chenting from "@/assets/test/chenting.png"

const props = defineProps({
  selectTree: {
    type: Object,
    required: false,
    default: ""
  }
})
console.log("props.selectTree", props.selectTree)

// 头像列表
const imgHeadList = ref<Array<any>>([
  {
    img: zhanghua,
    name: "张华",
    mobile: "13800138045",
    unit: "食品安全责任人"
  },
  {
    img: yanghai,
    name: "杨海",
    mobile: "13587250982",
    unit: "食品安全管理员"
  },
  {
    img: chenting,
    name: "陈婷",
    mobile: "13525679087",
    unit: "所在辖区监管员"
  }
])
// 表格
const data = reactive({
  safetyList: ref<Array<any>>([]),
  leaderList: ref<Array<any>>([]),
  personnelRatio: ref<Array<any>>([]),
  personList: ref<Array<any>>([])
})
let { safetyList, leaderList, personnelRatio, personList } = toRefs(data)

const loading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_ON_JOB)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// 食安信息
const getFoodSafetyInfo = async (type: String) => {
  loading.value = true
  const [err, res] = await to(
    apiSupervisionFoodSafetyPublicity({
      id: props.selectTree.id,
      org_id: props.selectTree.id,
      admin_type: type
    })
  )
  loading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    if (type === "security") {
      safetyList.value = res.data
    } else if (type === "food") {
      leaderList.value = res.data
    }
  } else {
    ElMessage.error(res.msg)
  }
}

// 人员配比
const getPersonnelRatio = async () => {
  loading.value = true
  const [err, res] = await to(
    apiSupervisionPersonnelRatio({
      org_id: props.selectTree.id,
      admin_type: "food"
    })
  )
  loading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    personnelRatio.value = res.data
  } else {
    ElMessage.error(res.msg)
  }
}
// 在职人员
const getPersonList = async () => {
  loading.value = true
  const [err, res] = await to(
    apiSupervisionJobPersonList({
      org_id: props.selectTree.id,
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  loading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    personList.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getPersonList()
}

// 查看照片
const handlerShowPhoto = (img: any) => {
  if (img) {
    imageList.value = [img]
  } else {
    imageList.value = [icJiangkang]
  }
  imageVisible.value = true
}

onMounted(() => {
  getFoodSafetyInfo("food")
  getFoodSafetyInfo("security")
  getPersonList()
  getPersonnelRatio()
})
</script>
<style lang="scss" scoped>
.food-safety-container {
  width: 100%;
  height: 100%;
  overflow: auto;

  .title-txt {
    color: #20201f;
    font-size: 20px;
  }

  .head-box {
    .img-box-2 {
      width: 148px;
      height: 224px;
      background: #f8f8f8;
      border-radius: 10px;
      padding: 8px;
      position: relative;

      .unit-tag {
        position: absolute;
        right: 8px;
        top: 132px;
        opacity: 80%;
        height: 24px;
        line-height: 24px;
        text-align: center;
        padding: 0 8px;
        background-color: var(--el-color-primary);
        border-top-left-radius: 14px;
        border-bottom-left-radius: 14px;
        color: #fff;
      }
    }
  }
}
</style>
