<template>
  <div class="base-info-container container-wrapper">
    <div class="title-txt m-t-24px">项目信息</div>
    <div class="project-bg m-t-16px">
      <img :src="imgProject" alt="" class="project-bg-img" />
      <div class="m-l-24px left-name">{{ canteenInfo.canteen_type_alias }}</div>
      <div class="m-l-8px middle-name">{{ selectTree.org_name }}</div>
      <el-button class="ps-origin-btn-plain btn-right" @click="handlerLogin">登录</el-button>
    </div>
    <div class="title-txt m-t-32px">餐饮服务食品安全等级公示</div>
    <div class="level-bg m-t-16px">
      <SvgIcon name="level-green" v-if="canteenInfo.security_level === 'excellent'" class="icon-big m-l-30px" />
      <SvgIcon name="level-orange" v-if="canteenInfo.security_level === 'good'" class="icon-big m-l-30px" />
      <SvgIcon name="level-red" v-if="canteenInfo.security_level === 'average'" class="icon-big m-l-30px" />
      <div class="level-txt m-l-32px">
        <div class="big-txt">
          <span class="text-size-60px" v-if="canteenInfo.security_level === 'excellent'">A</span>
          <span class="text-size-60px" v-if="canteenInfo.security_level === 'good'">B</span>
          <span class="text-size-60px" v-if="canteenInfo.security_level === 'average'">C</span>
          <span class="m-l-8px">级</span>
        </div>
        <div class="tip-txt">食品安全等级</div>
      </div>
      <SvgIcon name="level-green" class="m-l-81px icon-common" />
      <div class="level-txt m-l-8px">A级：优秀</div>
      <SvgIcon name="level-orange" class="m-l-40px icon-common" />
      <div class="level-txt m-l-8px">B级：良好</div>
      <SvgIcon name="level-red" class="m-l-40px icon-common" />
      <div class="level-txt m-l-8px">C级：一般</div>
    </div>
    <!-- <div class="title-txt m-t-32px">食堂类型：自营</div> -->
    <div class="title-txt m-t-32px">营养改善计划</div>
    <el-button class="m-t-10px" type="primary" plain>{{ canteenInfo.plan ? "已参与" : "未参与" }}</el-button>
    <div class="title-txt m-t-32px">资质公示</div>
    <div class="flex flex-wrap m-t-16px">
      <div v-for="(item, index) in imgList" :key="index" :class="[index > 0 ? 'm-l-20px' : '']">
        <div class="img-box">
          <img :src="item.image" @click="openDialog(item)" class="cursor-pointer" />
        </div>
        <div class="text-size-16px m-t-10px">{{ item.qualification_type_alias }}</div>
      </div>
    </div>
    <!-- <div class="title-txt m-t-32px">食安公示信息</div>
    <div class="flex flex-wrap m-t-16px head-box">
      <div v-for="(item, index) in imgHeadList" :key="index" :class="['img-box-2', index > 0 ? 'm-l-20px' : '']">
        <img :src="item.img" class="w-132px h-154px cursor-pointer" @click="handlerShowPhoto({ img: item.img })" />
        <div class="unit-tag">{{ item.unit }}</div>
        <div class="flex flex-col items-center">
          <div class="text-size-16px m-t-10px">{{ item.name }}</div>
          <div class="text-size-14px m-t-8px color-[#737373]">{{ item.mobile }}</div>
        </div>
      </div>
    </div>
    <div class="title-txt m-t-32px">在职人员（{{ tableData.length }} 人）</div>
    <div class="table-wrapper m-b-40px" ref="tableWrapperRef">
      <div class="table-content m-t-16px">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
        >
          <ps-column :table-headers="tableSetting">
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowPhoto(row)" type="primary"> 查看 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div> -->
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
    <el-drawer
      v-model="dialogConfig.dialogFormVisible"
      title="资质公示"
      direction="rtl"
      class="ps-drawer"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div>
        <el-form ref="dialogFormRef" label-width="140px">
          <el-form-item label="资质名称">{{ dialogConfig.dialogForm.qualification_type_alias }}</el-form-item>
          <div v-if="dialogConfig.dialogForm.qualification_type === 'YY'">
            <el-form-item label="统一社会信用代码">{{ dialogConfig.dialogForm.extra.creditCode }}</el-form-item>
            <el-form-item label="名称">{{ dialogConfig.dialogForm.extra.name }}</el-form-item>
            <el-form-item label="类型">{{ dialogConfig.dialogForm.extra.type }}</el-form-item>
            <el-form-item label="经营范围">{{ dialogConfig.dialogForm.extra.businessScope }}</el-form-item>
            <el-form-item label="注册日期">{{
              formateText(dialogConfig.dialogForm.extra.registrationDate)
            }}</el-form-item>
          </div>
          <div v-if="dialogConfig.dialogForm.qualification_type === 'CY'">
            <el-form-item label="单位名称">{{ dialogConfig.dialogForm.extra.name }}</el-form-item>
            <el-form-item label="地址">{{ dialogConfig.dialogForm.extra.address }}</el-form-item>
            <el-form-item label="类型">{{ dialogConfig.dialogForm.extra.type }}</el-form-item>
            <el-form-item label="备注">{{ dialogConfig.dialogForm.extra.remark }}</el-form-item>
            <el-form-item label="有效期限">{{
              formateExpirationDate(dialogConfig.dialogForm.extra.expirationDate)
            }}</el-form-item>
          </div>
          <div v-if="dialogConfig.dialogForm.qualification_type === 'SP'">
            <el-form-item label="经营者名称">{{ dialogConfig.dialogForm.extra.name }}</el-form-item>
            <el-form-item label="统一社会信用代码">{{ dialogConfig.dialogForm.extra.creditCode }}</el-form-item>
            <el-form-item label="法定代表人">{{ dialogConfig.dialogForm.extra.legalRepresentative }}</el-form-item>
            <el-form-item label="住所">{{ dialogConfig.dialogForm.extra.address }}</el-form-item>
            <el-form-item label="经营场所">{{ dialogConfig.dialogForm.extra.businessPremises }}</el-form-item>
            <el-form-item label="主体业态">{{ dialogConfig.dialogForm.extra.mainFormOfBusiness }}</el-form-item>
            <el-form-item label="经营项目">{{ dialogConfig.dialogForm.extra.businessProject }}</el-form-item>
            <el-form-item label="许可证编号">{{ dialogConfig.dialogForm.extra.licenseNumber }}</el-form-item>
            <el-form-item label="举报电话">{{ dialogConfig.dialogForm.extra.tipOffTelephone }}</el-form-item>
            <el-form-item label="发证机关">{{ dialogConfig.dialogForm.extra.issuingAuthority }}</el-form-item>
            <el-form-item label="有效期限">{{
              formateExpirationDate(dialogConfig.dialogForm.extra.expirationDate)
            }}</el-form-item>
          </div>
          <div v-if="dialogConfig.dialogForm.qualification_type === 'WS'">
            <el-form-item label="单位名称">{{ dialogConfig.dialogForm.extra.name }}</el-form-item>
            <el-form-item label="负责人">{{ dialogConfig.dialogForm.extra.personInCharge }}</el-form-item>
            <el-form-item label="地址">{{ dialogConfig.dialogForm.extra.address }}</el-form-item>
            <el-form-item label="许可项目">{{ dialogConfig.dialogForm.extra.permittedItem }}</el-form-item>
            <el-form-item label="发证机关">{{ dialogConfig.dialogForm.extra.issuingAuthority }}</el-form-item>
            <el-form-item label="有效期限">{{
              formateExpirationDate(dialogConfig.dialogForm.extra.expirationDate)
            }}</el-form-item>
          </div>
          <div v-if="dialogConfig.dialogForm.qualification_type === 'XF'">
            <el-form-item label="场所名称">{{ dialogConfig.dialogForm.extra.placeName }}</el-form-item>
            <el-form-item label="地址">{{ dialogConfig.dialogForm.extra.address }}</el-form-item>
            <el-form-item label="场所所在建筑名称">{{ dialogConfig.dialogForm.extra.buildingName }}</el-form-item>
            <el-form-item label="场所建筑面积">{{ dialogConfig.dialogForm.extra.floorArea }}</el-form-item>
            <el-form-item label="消防安全责任人">{{
              dialogConfig.dialogForm.extra.personResponsibleForSafety
            }}</el-form-item>
            <el-form-item label="使用性质">{{ dialogConfig.dialogForm.extra.natureOfUse }}</el-form-item>
          </div>
          <div v-if="dialogConfig.dialogForm.qualification_type === 'SW'">
            <el-form-item label="纳税人名称">{{ dialogConfig.dialogForm.extra.taxpayerName }}</el-form-item>
            <el-form-item label="法定代表人 (负责人)">{{
              dialogConfig.dialogForm.extra.legalRepresentative
            }}</el-form-item>
            <el-form-item label="地址">{{ dialogConfig.dialogForm.extra.address }}</el-form-item>
            <el-form-item label="登记注册类型">{{ dialogConfig.dialogForm.extra.typeOfRegistration }}</el-form-item>
            <el-form-item label="经营范围">{{ dialogConfig.dialogForm.extra.businessScope }}</el-form-item>
            <el-form-item label="扣缴义务">{{ dialogConfig.dialogForm.extra.withholdingObligation }}</el-form-item>
          </div>
          <el-form-item label="附件">
            <div class="img-box">
              <img
                :src="dialogConfig.dialogForm.image"
                @click="handlerShowPhoto({ img: dialogConfig.dialogForm.image })"
                class="cursor-pointer"
              />
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="drawer-footer m-t-20px">
        <!-- <el-button @click="closeDialog">取消</el-button> -->
        <el-button type="primary" @click="closeDialog"> 关闭 </el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue"
import { getImgByName } from "@/hooks/useTheme"
import IcTestZhiZhao from "@/assets/test/ic_test_zhizhao.png"
import { cloneDeep } from "lodash"
import { TABLE_SETTING_ON_JOB } from "../constants"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { ElMessage } from "element-plus"
import to from "await-to-js"
import { apiSupervisionCanteenInfo, apiSupervisionCanteenQualification } from "@/api/supervision/index"
import icJiangkang from "@/assets/images/ic_jiangkang.png"
import zhanghua from "@/assets/test/zhanghua.png"
import yanghai from "@/assets/test/yanghai.png"
import chenting from "@/assets/test/chenting.png"

const props = defineProps({
  selectTree: {
    type: Object,
    required: false,
    default: ""
  }
})
console.log("props.selectTree", props.selectTree)

const canteenInfo = ref<any>({})

// 安全等级图标
const levelIcon = ref("level-green-1")
// 头部图标
const imgProject = getImgByName("ic_baseinfo_top")
// 图片列表
const imgList = ref<Array<any>>([
  {
    img: IcTestZhiZhao,
    name: "工商营业执照"
  },
  {
    img: IcTestZhiZhao,
    name: "餐饮服务许可证"
  },
  {
    img: IcTestZhiZhao,
    name: "食品经营许可证"
  },
  {
    img: IcTestZhiZhao,
    name: "卫生许可证"
  },
  {
    img: IcTestZhiZhao,
    name: "消防安全证书"
  },
  {
    img: IcTestZhiZhao,
    name: "税务登记证"
  }
])
// 头像列表
const imgHeadList = ref<Array<any>>([
  {
    img: zhanghua,
    name: "张华",
    mobile: "13800138045",
    unit: "食品安全责任人"
  },
  {
    img: yanghai,
    name: "杨海",
    mobile: "13587250982",
    unit: "食品安全管理员"
  },
  {
    img: chenting,
    name: "陈婷",
    mobile: "13525679087",
    unit: "所在辖区监管员"
  }
])
// 表格
const tableData = ref([
  {
    name: "樊少秦",
    phone: "15801424487",
    job: "厨师长",
    valid_time: "",
    img: icJiangkang
  },
  {
    name: "张浩田",
    phone: "13727165494",
    job: "打荷",
    valid_time: "",
    img: icJiangkang
  },
  {
    name: "王月娥",
    phone: "13465289636",
    job: "服务员",
    valid_time: "",
    img: icJiangkang
  },
  {
    name: "田月",
    phone: "13745692456",
    job: "服务员",
    valid_time: "",
    img: icJiangkang
  },
  {
    name: "李秀梅",
    phone: "15869894412",
    job: "仓管",
    valid_time: "",
    img: icJiangkang
  }
])
const loading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_ON_JOB)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
const dialogConfig = reactive({
  dialogFormVisible: false,
  dialogForm: ref<any>({})
})

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// 食堂信息
const getCanteenInfo = async () => {
  loading.value = true
  const [err, res] = await to(
    apiSupervisionCanteenInfo({
      id: props.selectTree.id,
      org_id: props.selectTree.id
    })
  )
  loading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    canteenInfo.value = res.data
  } else {
    ElMessage.error(res.msg)
  }
}

// 资质信息
const getCanteenQualification = async () => {
  loading.value = true
  const [err, res] = await to(
    apiSupervisionCanteenQualification({
      id: props.selectTree.id,
      org_id: props.selectTree.id
    })
  )
  loading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    imgList.value = res.data
  } else {
    ElMessage.error(res.msg)
  }
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  // getDataList()
}

// 查看照片
const handlerShowPhoto = (row: any) => {
  console.log(row)
  if (row.img) {
    imageList.value = [row.img]
  } else {
    imageList.value = [icJiangkang]
  }
  imageVisible.value = true
}
// 跳转4.0登录
const handlerLogin = () => {
  window.open("https://cashier-v4.debug.packertec.com/#/login", "_blank")
}

// 弹窗
const openDialog = (data: any) => {
  dialogConfig.dialogFormVisible = true
  dialogConfig.dialogForm = data
}
// 弹窗关闭
const closeDialog = () => {
  dialogConfig.dialogFormVisible = false
}

const formateText = (text: any) => {
  let str: any = ""
  if (text.length > 10) {
    str = text.substring(0, 10)
  } else {
    str = text
  }
  return str
}

const formateExpirationDate = (text: any) => {
  let str1: any = ""
  let str2: any = ""
  if (text.length) {
    str1 = text[0].substring(0, 10)
    str2 = text[1].substring(0, 10)
  } else {
    str1 = text[0]
    str2 = text[1]
  }
  return str1 + " ~ " + str2
}

watch(
  () => props.selectTree,
  (val) => {
    console.log(props.selectTree, "selectTree")
    getCanteenInfo()
    getCanteenQualification()
  }
)
props.selectTree.id

onMounted(() => {
  getCanteenInfo()
  getCanteenQualification()
})
</script>
<style lang="scss" scoped>
.base-info-container {
  width: 100%;
  height: 100%;
  overflow: auto;

  .title-txt {
    color: #20201f;
    font-size: 20px;
  }

  .project-bg {
    display: flex;
    align-items: center;
    background: var(--el-color-primary);
    width: 100%;
    height: 80px;
    border-radius: 8px;
    position: relative;

    .left-name {
      background: #ffffff4d;
      border-radius: 6px;
      height: 26px;
      line-height: 26px;
      text-align: center;
      padding: 0 8px;
      color: #fff;
    }

    .middle-name {
      color: #fff;
      font-size: 20px;
    }

    .project-bg-img {
      position: absolute;
      top: 3px;
      right: 10%;
    }

    .btn-right {
      position: absolute;
      right: 30px;
      border-radius: 20px !important;
    }
  }

  .level-bg {
    display: flex;
    align-items: center;
    background: #f8f8f8;
    width: 100%;
    height: 132px;
    border-radius: 10px;

    .icon-big {
      width: 100px;
      height: 100px;
    }

    .icon-common {
      width: 40px;
      height: 40px;
    }

    .level-txt {
      color: #363636;
      font-size: 16px;
    }

    .tip-txt {
      color: #737373;
      font-size: 16px;
    }

    .big-txt {
      color: #363636;
      font-size: 48px;
      display: inline-flex;
      align-items: center;
    }
  }

  .img-box {
    width: 180px;
    height: 135px;
    background: #f8f8f8;
    border-radius: 10px;
    border: 1px solid #e9e9e9;
    padding: 8px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .head-box {
    .img-box-2 {
      width: 148px;
      height: 224px;
      background: #f8f8f8;
      border-radius: 10px;
      padding: 8px;
      position: relative;

      .unit-tag {
        position: absolute;
        right: 8px;
        top: 132px;
        opacity: 80%;
        height: 24px;
        line-height: 24px;
        text-align: center;
        padding: 0 8px;
        background-color: var(--el-color-primary);
        border-top-left-radius: 14px;
        border-bottom-left-radius: 14px;
        color: #fff;
      }
    }
  }
}
.container-wrapper .table-content {
  padding: 0 !important;
}
</style>
