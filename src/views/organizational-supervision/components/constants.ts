import { graphic } from "echarts/core"

export const SERIES_ITEM = {
  type: "line",
  smooth: true,
  showSymbol: false,
  lineStyle: {
    width: 3,
    type: "solid",
    color: "#2f77ff"
  },
  areaStyle: {
    opacity: 0.5,
    color: new graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: "#2f77ff"
      },
      {
        offset: 1,
        color: "rgba(255,255,255,0)"
      }
    ])
  },
  itemStyle: {
    color: "#FFFFFF"
  },
  data: [140, 232, 101, 264, 90, 340, 250]
}

export const LINE_ECHART_OPTION = {
  tooltip: {
    show: true,
    trigger: "axis",
    axisPointer: {
      type: "line"
    }
  },
  xAxis: {
    show: true,
    type: "category",
    data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
  },
  yAxis: {
    show: true,
    type: "value"
  },
  dataset: {
    dimensions: ["product", "data1", "data2", "data3"],
    source: [
      {
        product: "Mon",
        data1: 20,
        data2: 230,
        data3: 300
      },
      {
        product: "Tue",
        data1: 200,
        data2: 130,
        data3: 230
      },
      {
        product: "Wed",
        data1: 150,
        data2: 312,
        data3: 130
      },
      {
        product: "Thu",
        data1: 80,
        data2: 268,
        data3: 10
      },
      {
        product: "Fri",
        data1: 70,
        data2: 155,
        data3: 130
      },
      {
        product: "Sat",
        data1: 110,
        data2: 117,
        data3: 130
      },
      {
        product: "Sun",
        data1: 130,
        data2: 160,
        data3: 30
      }
    ]
  },
  series: [SERIES_ITEM]
}

export const PIE_ECHART_OPTION = {
  color: ["#57C3FF", "#377AFA", "#864DFF", "#FF627E"],
  title: {
    text: "￥307882",
    subtext: "合计成本支出",
    left: "center",
    top: "43%",
    textStyle: {
      fontSize: 25,
      color: "#000"
    },
    subtextStyle: {
      fontSize: 12,
      color: "#00000099"
    },
    show: true
  },
  tooltip: {
    trigger: "item"
  },
  legend: {
    orient: "vertical",
    top: "30%",
    right: "5%",
    show: false
  },
  series: [
    {
      name: "Access From",
      type: "pie",
      radius: ["60%", "80%"],
      avoidLabelOverlap: false,
      padAngle: 2,
      itemStyle: {
        borderRadius: 1,
        borderWidth: 2
      },
      label: {
        show: false,
        position: "center"
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 20,
          fontWeight: "bold"
        }
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 3, name: "车辆黑名单" },
        { value: 6, name: "陌生车辆" },
        { value: 2, name: "人员黑名单" },
        { value: 10, name: "陌生人人员" }
        // { value: 0, name: '危险区域' }
      ]
    }
  ]
}

export const PIE1_DATA_MONTH = [
  {
    month: 1,
    income: "0",
    expenses: "5920.08",
    profit: "-5920.08",
    profitMargin: "0.00%",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 2,
    income: "100032",
    expenses: "81460",
    profit: "18572",
    profitMargin: "18.57%",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 3,
    income: "304756.98",
    expenses: "284689.04",
    profit: "20067.94",
    profitMargin: "6.58%",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 4,
    income: "296495.14",
    expenses: "289802.32",
    profit: "6692.82",
    profitMargin: "2.26%",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 5,
    income: "286602.45",
    expenses: "280146.54",
    profit: "6455.91",
    profitMargin: "2.25%",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 6,
    income: "289820.3",
    expenses: "287316.46",
    profit: "2503.84",
    profitMargin: "0.86%",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 7,
    income: "0",
    expenses: "5975.2",
    profit: "-5975.2",
    profitMargin: "0.00%",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 8,
    income: "0",
    expenses: "6290.74",
    profit: "-6290.74",
    profitMargin: "0.00%",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 9,
    income: "324878.8",
    expenses: "308692.78",
    profit: "16186.02",
    profitMargin: "4.98%",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 10,
    income: "293902.55",
    expenses: "287995.51",
    profit: "5907.04",
    profitMargin: "2.01%",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 11,
    income: "297018.88",
    expenses: "294600.16",
    profit: "2418.72",
    profitMargin: "0.81%",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 12,
    income: "286602.45 ",
    expenses: "280146.54 ",
    profit: "6455.91 ",
    profitMargin: "2.25%",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 1,
    income: "0",
    expenses: "7030.1",
    profit: "-7030.1",
    profitMargin: "0.00%",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 2,
    income: "118788",
    expenses: "96733.75",
    profit: "22054.25",
    profitMargin: "18.57%",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 3,
    income: "361898.91",
    expenses: "338068.24",
    profit: "23830.67",
    profitMargin: "6.58%",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 4,
    income: "352087.98",
    expenses: "344140.26",
    profit: "7947.73",
    profitMargin: "2.26%",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 5,
    income: "340340.41",
    expenses: "332674.02",
    profit: "7666.39",
    profitMargin: "2.25%",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 6,
    income: "344161.61",
    expenses: "341188.3",
    profit: "2973.31",
    profitMargin: "0.86%",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 7,
    income: "0",
    expenses: "7095.55",
    profit: "-7095.55",
    profitMargin: "0.00%",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 8,
    income: "0",
    expenses: "7470.25",
    profit: "-7470.25",
    profitMargin: "0.00%",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 9,
    income: "385793.58",
    expenses: "366572.68",
    profit: "19220.9",
    profitMargin: "4.98%",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 10,
    income: "349009.27",
    expenses: "341994.66",
    profit: "7014.61",
    profitMargin: "2.01%",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 11,
    income: "352709.92",
    expenses: "349837.69",
    profit: "2872.23",
    profitMargin: "0.81%",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 12,
    income: "340340.41 ",
    expenses: "332674.02 ",
    profit: "7666.39 ",
    profitMargin: "2.25%",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 1,
    income: "0",
    expenses: "5550.08",
    profit: "-5550.08",
    profitMargin: "0.00%",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 2,
    income: "93780",
    expenses: "76368.75",
    profit: "17411.25",
    profitMargin: "18.57%",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 3,
    income: "285709.67",
    expenses: "266895.98",
    profit: "18813.69",
    profitMargin: "6.58%",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 4,
    income: "277964.2",
    expenses: "271689.68",
    profit: "6274.52",
    profitMargin: "2.26%",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 5,
    income: "268689.8",
    expenses: "262637.39",
    profit: "6052.41",
    profitMargin: "2.25%",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 6,
    income: "271706.54",
    expenses: "269359.19",
    profit: "2347.35",
    profitMargin: "0.86%",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 7,
    income: "0",
    expenses: "5601.75",
    profit: "-5601.75",
    profitMargin: "0.00%",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 8,
    income: "0",
    expenses: "5897.57",
    profit: "-5897.57",
    profitMargin: "0.00%",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 9,
    income: "304573.88",
    expenses: "289399.49",
    profit: "15174.39",
    profitMargin: "4.98%",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 10,
    income: "275533.64",
    expenses: "269995.79",
    profit: "5537.85",
    profitMargin: "2.01%",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 11,
    income: "278455.2",
    expenses: "276187.65",
    profit: "2267.55",
    profitMargin: "0.81%",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 12,
    income: "268689.8",
    expenses: "262637.39",
    profit: "6052.41",
    profitMargin: "2.25%",
    school: "qiyuan",
    schoolName: "启元中学"
  }
]

export const PIE1_DATA_YEAR = [
  {
    year: 2023,
    income: "0",
    expenses: "0",
    profit: "0",
    profitMargin: "0.00%",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    year: 2023,
    income: "0",
    expenses: "0",
    profit: "0",
    profitMargin: "0.00%",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    year: 2023,
    income: "0",
    expenses: "0",
    profit: "0",
    profitMargin: "0.00%",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    year: 2024,
    income: "2480109.56 ",
    expenses: "2413035.38 ",
    profit: "67074.17 ",
    profitMargin: "0.00%",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    year: 2024,
    income: "2945130.10 ",
    expenses: "2865479.52 ",
    profit: "79650.58 ",
    profitMargin: "0.00%",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    year: 2024,
    income: "2325102.708",
    expenses: "2262220.671",
    profit: "62882.037",
    profitMargin: "0.00%",
    school: "qiyuan",
    schoolName: "启元中学"
  }
]

export const PIE2_DATA_MONTH = [
  {
    month: 1,
    totalExpenditure: "5920.08", // 合计支出
    rawMaterial: "0", // 原材料支出
    utilities: "165.08", // 水电气
    artificial: "5000.00", // 人工
    other: "755", // 其他
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 2,
    totalExpenditure: "81460",
    rawMaterial: "53262",
    utilities: "14498",
    artificial: "13700.00",
    other: "0",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 3,
    totalExpenditure: "284689.04",
    rawMaterial: "256490.04",
    utilities: "14499",
    artificial: "13700.00",
    other: "0",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 4,
    totalExpenditure: "289802.32",
    rawMaterial: "261602.32",
    utilities: "14500",
    artificial: "13700.00",
    other: "0",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 5,
    totalExpenditure: "280146.54",
    rawMaterial: "251945.54",
    utilities: "14501",
    artificial: "13700.00",
    other: "0",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 6,
    totalExpenditure: "287316.46",
    rawMaterial: "259114.46",
    utilities: "14502",
    artificial: "13700.00",
    other: "0",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 7,
    totalExpenditure: "6161.93",
    rawMaterial: "0",
    utilities: "221.93",
    artificial: "5000.00",
    other: "940",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 8,
    totalExpenditure: "6290.74",
    rawMaterial: "0",
    utilities: "217.32",
    artificial: "5000.00",
    other: "1073.41",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 9,
    totalExpenditure: "308692.78",
    rawMaterial: "280490.78",
    utilities: "14502",
    artificial: "13700.00",
    other: "0",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 10,
    totalExpenditure: "287995.51",
    rawMaterial: "259792.51",
    utilities: "14503",
    artificial: "13700.00",
    other: "0",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 11,
    totalExpenditure: "294600.16",
    rawMaterial: "266396.16",
    utilities: "14504",
    artificial: "13700.00",
    other: "0",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 12,
    totalExpenditure: "280146.54",
    rawMaterial: "251941.54",
    utilities: "14505",
    artificial: "13700.00",
    other: "0",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 1,
    totalExpenditure: "7030.1",
    rawMaterial: "0",
    utilities: "165.08",
    artificial: "5000.00",
    other: "1865.01",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 2,
    totalExpenditure: "96733.75",
    rawMaterial: "67035.75",
    utilities: "15998",
    artificial: "13700.00",
    other: "0",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 3,
    totalExpenditure: "338068.24",
    rawMaterial: "309869.24",
    utilities: "14499",
    artificial: "13700.00",
    other: "0",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 4,
    totalExpenditure: "344140.26",
    rawMaterial: "315940.26",
    utilities: "14500",
    artificial: "13700.00",
    other: "0",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 5,
    totalExpenditure: "332674.02",
    rawMaterial: "304473.02",
    utilities: "14501",
    artificial: "13700.00",
    other: "0",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 6,
    totalExpenditure: "341188.3",
    rawMaterial: "312986.3",
    utilities: "14502",
    artificial: "13700.00",
    other: "0",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 7,
    totalExpenditure: "7095.55",
    rawMaterial: "0",
    utilities: "215.2",
    artificial: "5000.00",
    other: "1880.35",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 8,
    totalExpenditure: "7470.25",
    rawMaterial: "0",
    utilities: "210.74",
    artificial: "5000.00",
    other: "2259.51",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 9,
    totalExpenditure: "366572.68",
    rawMaterial: "338373.68",
    utilities: "14499",
    artificial: "13700.00",
    other: "0",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 10,
    totalExpenditure: "341994.66",
    rawMaterial: "313794.66",
    utilities: "14500",
    artificial: "13700.00",
    other: "0",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 11,
    totalExpenditure: "349837.69",
    rawMaterial: "321636.69",
    utilities: "14501",
    artificial: "13700.00",
    other: "0",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 12,
    totalExpenditure: "332674.02",
    rawMaterial: "304472.02",
    utilities: "14502",
    artificial: "13700.00",
    other: "0",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 1,
    totalExpenditure: "5550.08",
    rawMaterial: "0",
    utilities: "170.09",
    artificial: "5000.00",
    other: "379.99",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 2,
    totalExpenditure: "76368.75",
    rawMaterial: "49170.75",
    utilities: "13498",
    artificial: "13700.00",
    other: "0",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 3,
    totalExpenditure: "266895.98",
    rawMaterial: "238696.98",
    utilities: "14499",
    artificial: "13700.00",
    other: "0",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 4,
    totalExpenditure: "271689.68",
    rawMaterial: "243489.68",
    utilities: "14500",
    artificial: "13700.00",
    other: "0",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 5,
    totalExpenditure: "262637.39",
    rawMaterial: "234436.39",
    utilities: "14501",
    artificial: "13700.00",
    other: "0",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 6,
    totalExpenditure: "269359.19",
    rawMaterial: "241157.19",
    utilities: "14502",
    artificial: "13700.00",
    other: "0",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 7,
    totalExpenditure: "5601.75",
    rawMaterial: "0",
    utilities: "235.38",
    artificial: "5000.00",
    other: "366.38",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 8,
    totalExpenditure: "5897.57",
    rawMaterial: "0",
    utilities: "230.5",
    artificial: "5000.00",
    other: "667.07",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 9,
    totalExpenditure: "289399.49",
    rawMaterial: "261194.49",
    utilities: "14505",
    artificial: "13700.00",
    other: "0",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 10,
    totalExpenditure: "269995.79",
    rawMaterial: "241789.79",
    utilities: "14506",
    artificial: "13700.00",
    other: "0",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 11,
    totalExpenditure: "276187.65",
    rawMaterial: "247980.65",
    utilities: "14507",
    artificial: "13700.00",
    other: "0",
    school: "qiyuan",
    schoolName: "启元中学"
  },
  {
    month: 12,
    totalExpenditure: "262637.39",
    rawMaterial: "234429.39",
    utilities: "14508",
    artificial: "13700.00",
    other: "0",
    school: "qiyuan",
    schoolName: "启元中学"
  }
]

export const PIE2_DATA_YEAR = [
  {
    month: 2024,
    totalExpenditure: "2413222.107",
    rawMaterial: "2141035.363",
    utilities: "131118.3323",
    artificial: "138300",
    other: "2768.4119",
    school: "huaxi",
    schoolName: "华溪实验中学"
  },
  {
    month: 2024,
    totalExpenditure: "2865479.517",
    rawMaterial: "2588581.619",
    utilities: "132593.0217",
    artificial: "138300",
    other: "6004.8761",
    school: "tianjia",
    schoolName: "田家炳实验小学"
  },
  {
    month: 2024,
    totalExpenditure: "2262220.671",
    rawMaterial: "1992345.278",
    utilities: "130161.956",
    artificial: "138300",
    other: "1413.437",
    school: "qiyuan",
    schoolName: "启元中学"
  }
]

export const MONTH_LIST = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"]

export const LINE_DATA_capitalIncome = {
  huaxi: [
    [0.0, 78914.38, 275792.51, 280746.0, 271391.96, 278337.82, 0.0, 0.0, 299046.13, 278995.65, 285393.91, 271391.96],
    [0.0, 954.61, 3336.2, 3396.12, 3282.97, 3366.99, 0.0, 0.0, 3617.49, 3374.95, 3452.35, 3282.97]
  ],
  tianjia: [
    [0.0, 89096.88, 311378.64, 316971.29, 306410.28, 314252.38, 0.0, 0.0, 337632.73, 314995.09, 322218.93, 306410.28],
    [0.0, 1145.53, 4003.44, 4075.35, 3939.56, 4040.39, 0.0, 0.0, 4340.99, 4049.94, 4142.81, 3939.56]
  ],
  qiyuan: [
    [0.0, 84005.63, 293585.57, 298858.64, 288901.12, 296295.1, 0.0, 0.0, 318339.43, 296995.37, 303806.42, 288901.12],
    [0.0, 1081.89, 3781.03, 3848.94, 3720.7, 3815.92, 0.0, 0.0, 4099.83, 3824.94, 3912.66, 3720.7]
  ]
}

export const LINE_DATA_operatingIncome = {
  huaxi: [
    [0.0, 99295.84, 303946.66, 295869.11, 285867.56, 289318.05, 0.0, 0.0, 323854.32, 293117.71, 296623.52, 286079.81],
    [0.0, 736.16, 810.32, 626.04, 734.9, 502.26, 0.0, 0.0, 1024.49, 784.84, 395.36, 522.64],
    [0.0, 100032.0, 304756.98, 296495.14, 286602.45, 289820.3, 0.0, 0.0, 324878.8, 293902.55, 297018.88, 286602.45]
  ],
  tianjia: [
    [0.0, 102398.84, 313444.99, 305115.02, 294800.92, 298359.24, 0.0, 0.0, 333974.76, 302277.63, 305893.01, 295019.81],
    [0.0, 759.17, 835.64, 645.6, 757.86, 517.95, 0.0, 0.0, 1056.5, 809.37, 407.72, 538.97],
    [0.0, 103158.0, 314280.63, 305760.62, 295558.78, 298877.19, 0.0, 0.0, 335031.27, 303087.0, 306300.72, 295558.78]
  ],
  qiyuan: [
    [0.0, 108604.83, 332441.66, 323606.84, 312667.64, 316441.62, 0.0, 0.0, 354215.66, 320597.49, 324431.98, 312899.79],
    [0.0, 805.18, 886.29, 684.73, 803.79, 549.34, 0.0, 0.0, 1120.53, 858.42, 432.43, 571.64],
    [0.0, 109410.0, 333327.94, 324291.56, 313471.43, 316990.96, 0.0, 0.0, 355336.19, 321455.91, 324864.4, 313471.43]
  ]
}
export const LINE_DATA_foodConsumption = {
  huaxi: [
    [0.0, 53262.0, 256490.04, 261602.32, 251945.54, 259114.46, 0.0, 0.0, 280490.78, 259792.51, 266396.16, 251941.54]
  ],
  tianjia: [
    [0.0, 67035.75, 309869.24, 315940.26, 304473.02, 312986.3, 0.0, 0.0, 338373.68, 313794.66, 321636.69, 304472.02]
  ],
  qiyuan: [
    [0.0, 49170.75, 238696.98, 243489.68, 234436.39, 241157.19, 0.0, 0.0, 261194.49, 241789.79, 247980.65, 234429.39]
  ]
}

export const LINE_DATA_profitAnalysis = {
  huaxi: [
    [-5920.08, 18572.0, 20067.94, 6692.82, 6455.91, 2503.84, -5975.2, -6290.74, 16186.02, 5907.04],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  ],
  tianjia: [
    [-7030.1, 22054.25, 23830.67, 7947.73, 7666.39, 2973.31, -7095.55, -7470.25, 19220.9, 7014.61],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  ],
  qiyuan: [
    [-5550.08, 17411.25, 18813.69, 6274.52, 6052.41, 2347.35, -5601.75, -5897.57, 15174.39, 5537.85],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  ]
}
