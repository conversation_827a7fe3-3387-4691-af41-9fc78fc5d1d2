<template>
  <div class="supervision-setting">
    <el-drawer
      v-model="dialogFormVisible"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div>
        <el-form :model="ruleForm" :rules="rules" ref="dialogFormRef">
          <el-form-item label="" prop="name">
            <div class="inline">经营风险</div>
            <div class="h-32px">
              <img :src="IcQuestionBlack" class="w-20px h-20px m-t-5px" />
            </div>
            <div class="inline m-l-10px">更新时间为每月</div>
            <el-select v-model="ruleForm.name" placeholder="请选择" style="width: 120px" class="m-l-5px">
              <el-option v-for="item in MONTH_DAYS" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="drawer-footer m-t-20px">
        <el-button @click="cancelDialog">取消</el-button>
        <el-button type="primary" @click="confirmDialog" v-loading="confirmLoading"> 确认 </el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, reactive } from "vue"
import type { FormInstance, FormRules } from "element-plus"
// import to from "await-to-js"
import { MONTH_DAYS } from "../constants"
import IcQuestionBlack from "@/assets/layouts/ic_question_black.png"

const props = defineProps({
  title: {
    type: String,
    default: "报表设置"
  },
  width: {
    type: String,
    default: "364px"
  },
  isShow: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: "setting"
  },
  orgId: {
    type: Number,
    default: -1
  },
  alreadyChooseIds: {
    type: Array,
    default: () => {
      return []
    }
  },
  // 选择节点数据
  organizationData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const dialogFormVisible = ref(false)
const dialogFormRef = ref<FormInstance>()
const confirmLoading = ref(false)
const ruleForm = reactive<any>({
  name: "1"
})
// 表单规则
const rules = reactive<FormRules<any>>({
  name: [{ required: true, message: "请选择", trigger: "blur" }]
})
// 弹窗确认
const confirmDialog = () => {
  if (dialogFormRef.value) {
    dialogFormRef.value.validate((valid, fields) => {
      if (valid) {
        console.log("submit!")
        saveInfo()
      } else {
        console.log("error submit!", fields)
      }
    })
  }
}
// 弹窗取消
const cancelDialog = () => {
  dialogFormVisible.value = false
}
// 弹窗关闭
const closeDialog = () => {
  emit("cancelDialog")
}

// 初始化数据
const initData = async () => {}
// 保存数据
const saveInfo = async () => {
  // const params = {
  //   name: ruleForm.name,
  //   phone: ruleForm.mobile,
  //   username: ruleForm.accountName
  // }
  confirmLoading.value = true
  // const flag = await to(saveCanteen(params))
  const flag = true
  confirmLoading.value = false
  if (flag) {
    emit("confirmDialog", ruleForm, props.type)
  }
}

watch(
  () => ({
    isShow: props.isShow
  }),
  (newValue) => {
    console.log("watch dialog", newValue)
    dialogFormVisible.value = newValue.isShow
    if (newValue.isShow) {
      initData()
    }
  }
)
defineExpose({})
</script>
<style scoped lang="scss">
.supervision-setting {
  margin: 24px;

  .drawer-footer {
    display: flex;
  }
}
</style>
