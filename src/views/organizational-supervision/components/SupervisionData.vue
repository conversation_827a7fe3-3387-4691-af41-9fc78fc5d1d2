<template>
  <div class="supervision-data">
    <div class="chart-wrap">
      <echart-card-header title="收支利润" dataType="monthYear" @change="changeRevenueExpenditureData" />
      <echart-pie
        :color-list="colorList.value"
        :data-list="option.revenueExpenditureData"
        :text="option.revenueExpenditureTotal"
        name="收支利润"
        :subtext="option.revenueExpenditureSubtext"
      />
    </div>
    <!-- <div class="chart-wrap">
      <echart-card-header title="资金收入" dataType="year" @change="changeCapitalIncomeData"></echart-card-header>
      <line-gradients
        :color-list="colorList.value"
        :data-list="option.capitalIncomeData"
        :legend-data="option.capitalIncomeLegend"
        :collect-data="option.capitalIncomeCollectData"
      />
    </div>
    <div class="chart-wrap">
      <echart-card-header title="经营收入" dataType="year" @change="changeOperatingIncomeData"></echart-card-header>
      <line-gradients
        :color-list="colorList.value"
        :data-list="option.operatingIncomeData"
        :legend-data="option.operatingIncomeLegend"
        :collect-data="option.operatingIncomeCollectData"
      />
    </div> -->
    <div class="chart-wrap">
      <echart-card-header title="经营成本" dataType="monthYear" @change="changeCapitalExpensesData" />
      <echart-pie
        :color-list="colorList.value"
        :data-list="option.capitalExpensesData"
        :text="option.capitalExpensesTotal"
        name="经营成本"
        :subtext="option.capitalExpensesSubtext"
      />
    </div>
    <div class="chart-wrap">
      <echart-card-header title="食材消耗" dataType="year" @change="changeFoodConsumptionData" />
      <line-gradients
        :color-list="colorList.value"
        :data-list="option.foodConsumptionData"
        :legend-data="option.foodConsumptionLegend"
        :collect-data="option.foodConsumptionCollectData"
      />
    </div>
    <div class="chart-wrap">
      <echart-card-header title="利润分析" dataType="year" @change="changeProfitAnalysisData" />
      <line-gradients
        :color-list="colorList.value"
        :data-list="option.profitAnalysisData"
        :legend-data="option.profitAnalysisLegend"
        :collect-data="option.profitAnalysisCollectData"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import EchartCardHeader from "@/components/EchartCard/Header.vue"
import LineGradients from "@/components/EchartCard/LineGradients.vue"
import EchartPie from "@/components/EchartCard/EchartPie.vue"
import { reactive, watch, PropType, toRefs, onMounted, ref } from "vue"
import VChart from "vue-echarts"
import { use, graphic, dataTool } from "echarts/core"
import { CanvasRenderer } from "echarts/renderers"
import { LineChart, PieChart } from "echarts/charts"
import { DatasetComponent, GridComponent, TooltipComponent, LegendComponent, TitleComponent } from "echarts/components"
import { useTheme } from "@/hooks/useTheme"
import { LINE_ECHART_OPTION, PIE_ECHART_OPTION, SERIES_ITEM } from "./constants"
import { getCssVariableValue } from "@/utils"
import { cloneDeep } from "lodash"
import { ElMessage } from "element-plus"
import to from "await-to-js"
import {
  apiSupervisionIncomeExpensesProfit,
  apiSupervisionExpenditureFunds,
  apiSupervisionIngredientExhaustion,
  apiSupervisionProfitAnalysis
} from "@/api/supervision/index"
import { parseFormat } from "@/utils/date"
import {
  PIE1_DATA_MONTH,
  PIE1_DATA_YEAR,
  PIE2_DATA_MONTH,
  PIE2_DATA_YEAR,
  LINE_DATA_capitalIncome,
  LINE_DATA_operatingIncome,
  LINE_DATA_foodConsumption,
  LINE_DATA_profitAnalysis,
  MONTH_LIST
} from "./constants"

const props = defineProps({
  channelId: {
    type: Number,
    required: false,
    default: 0
  },
  school: {
    type: Array,
    required: false,
    default: []
  }
})

console.log("channelId", props.channelId)

const currentYear = new Date().getFullYear() // 当前年
const currentMonth = new Date().getFullYear() + "-" + new Date().getMonth() + 1
const preMonth =
  new Date().getFullYear() + "-" + (new Date().getMonth() > 10 ? new Date().getMonth() : "0" + new Date().getMonth()) // 上个月
console.log("security", preMonth)

const activeThemeName = useTheme().activeThemeName

const loading = ref(false)
const colorList = reactive({
  value: [
    getCssVariableValue("--el-color-echart-1"),
    getCssVariableValue("--el-color-echart-2"),
    getCssVariableValue("--el-color-echart-3"),
    getCssVariableValue("--el-color-echart-4"),
    getCssVariableValue("--el-color-echart-5")
  ]
})

use([
  DatasetComponent,
  CanvasRenderer,
  LineChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  PieChart,
  TitleComponent
])

interface MyObject {
  [key: string]: any // 允许任意字符串作为键，值类型为 string
}
const option = reactive<MyObject>({
  // 收支利润
  revenueExpenditureType: "month",
  revenueExpenditureTime: preMonth,
  revenueExpenditureTotal: "",
  revenueExpenditureData: [
    { value: 0, name: "收入" },
    { value: 0, name: "支出" }
  ],
  revenueExpenditureSubtext: "利润（万元）",
  // 资金收入
  capitalIncomeTime: "",
  capitalIncomeTotal: 0,
  capitalIncomeData: [],
  capitalIncomeLegend: ["营业性", "非营业性"],
  capitalIncomeCollectData: [
    {
      title: "合计",
      fee: 0
    },
    {
      title: "均值",
      fee: 0
    }
  ],
  // 经营收入
  operatingIncomeTime: "",
  operatingIncomeTotal: 0,
  operatingIncomeData: [],
  operatingIncomeLegend: ["消费", "退款", "收入"],
  operatingIncomeCollectData: [
    {
      title: "合计",
      fee: 0.0
    },
    {
      title: "均值",
      fee: 0.0
    }
  ],
  // 资金支出
  capitalExpensesType: "month",
  capitalExpensesTime: preMonth,
  capitalExpensesTotal: "",
  capitalExpensesData: [
    { value: 0, name: "原材料" },
    { value: 0, name: "水电气" },
    { value: 0, name: "人工" },
    { value: 0, name: "经营-其他" },
    { value: 0, name: "非经营-其他" }
  ],
  capitalExpensesSubtext: "合计（万元）",
  // 食材消耗
  foodConsumptionTime: currentYear,
  foodConsumptionTotal: 0,
  foodConsumptionData: [],
  foodConsumptionLegend: ["消耗"],
  foodConsumptionCollectData: [
    {
      title: "合计",
      fee: 0.0
    },
    {
      title: "均值",
      fee: 0.0
    }
  ],
  // 利润分析数据
  profitAnalysisTime: currentYear,
  profitAnalysisTotal: 0,
  profitAnalysisData: [],
  profitAnalysisLegend: ["本期利润", "同期利润"],
  profitAnalysisCollectData: [
    {
      title: "合计",
      fee: 0.0
    },
    {
      title: "均值",
      fee: 0.0
    }
  ]
})

// 初始化数据
const initData = async () => {
  getIncomeExpensesProfit()
  getExpenditureFunds()
  getIngredientExhaustion()
  getProfitAnalysis()
  computeLineData(option.capitalIncomeTime, "capitalIncome", LINE_DATA_capitalIncome)
  computeLineData(option.operatingIncomeTime, "operatingIncome", LINE_DATA_operatingIncome)
  // computeLineData(option.foodConsumptionTime, "foodConsumption", LINE_DATA_foodConsumption)
  // computeLineData(option.profitAnalysisTime, "profitAnalysis", LINE_DATA_profitAnalysis)
}

// 收支利润
const getIncomeExpensesProfit = async () => {
  console.log("option.revenueExpenditureType", option.revenueExpenditureType)

  loading.value = true
  const [err, res] = await to(
    apiSupervisionIncomeExpensesProfit({
      supervision_channel_id: props.channelId,
      date_type: option.revenueExpenditureType,
      date: option.revenueExpenditureTime,
      org_id_list: props.school
    })
  )
  loading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    option.revenueExpenditureData = [
      { value: (res.data.income_num / 100).toFixed(2), name: "收入" },
      { value: (res.data.expenses_num / 100).toFixed(2), name: "支出" }
    ]
    if (res.data.profit_num >= 10000000 || res.data.profit_num <= -10000000) {
      option.revenueExpenditureTotal = formatNumberWithoutRounding(res.data.profit_num / 1000000, 2)
      option.revenueExpenditureSubtext = "利润（万元）"
    } else {
      option.revenueExpenditureTotal = formatNumberWithoutRounding(res.data.profit_num / 100, 2)
      option.revenueExpenditureSubtext = "利润（元）"
    }
  } else {
    ElMessage.error(res.msg)
  }
}
// 保留两位小数咯，别四舍五入
function formatNumberWithoutRounding(num: number, decimalPlaces: number) {
  // 将数字转换为字符串，并分割成整数部分和小数部分
  const [integerPart, decimalPart] = num.toString().split(".")

  // 如果没有小数部分或小数部分长度小于需要的位数，直接返回原数字
  if (!decimalPart || decimalPart.length < decimalPlaces) {
    return num.toFixed(decimalPlaces) // 这会添加零以达到所需的位数
  }

  // 截取小数部分的前两位
  const truncatedDecimalPart = decimalPart.slice(0, decimalPlaces)

  // 将整数部分和截取后的小数部分组合起来
  return `${integerPart}.${truncatedDecimalPart}`
}

// 资金支出
const getExpenditureFunds = async () => {
  loading.value = true
  const [err, res] = await to(
    apiSupervisionExpenditureFunds({
      supervision_channel_id: props.channelId,
      date_type: option.capitalExpensesType,
      date: option.capitalExpensesTime,
      org_id_list: props.school
    })
  )
  loading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    let raw_material_cost = res.data.raw_material_cost ? (res.data.raw_material_cost / 100).toFixed(2) : 0
    let utilities = res.data.utilities ? (res.data.utilities / 100).toFixed(2) : 0
    let labor_cost = res.data.labor_cost ? (res.data.labor_cost / 100).toFixed(2) : 0
    // let other_costs = res.data.other_costs ? (res.data.other_costs / 100).toFixed(2) : 0
    let operatingOtherCosts = res.data.operating_other_costs ? (res.data.operating_other_costs / 100).toFixed(2) : 0
    let nonOperatingOtherCosts = res.data.non_operating_other_costs
      ? (res.data.non_operating_other_costs / 100).toFixed(2)
      : 0
    option.capitalExpensesData = [
      { value: raw_material_cost, name: "原材料" },
      { value: utilities, name: "水电气" },
      { value: labor_cost, name: "人工" },
      { value: operatingOtherCosts, name: "经营-其他" },
      { value: nonOperatingOtherCosts, name: "非经营-其他" }
    ]
    if (res.data.total_price >= 10000000 || res.data.total_price <= -1000000) {
      option.capitalExpensesTotal = formatNumberWithoutRounding(res.data.total_price / 1000000, 2)
      option.capitalExpensesSubtext = "合计（万元）"
    } else {
      option.capitalExpensesTotal = (res.data.total_price / 100).toFixed(2)
      option.capitalExpensesSubtext = "合计（元）"
    }
  } else {
    ElMessage.error(res.msg)
  }
}

// 食材消耗
const getIngredientExhaustion = async () => {
  loading.value = true
  const [err, res] = await to(
    apiSupervisionIngredientExhaustion({
      supervision_channel_id: props.channelId,
      date_type: "year",
      year_date: option.foodConsumptionTime,
      org_id_list: props.school
    })
  )
  loading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    let data: any = []
    let total = 0
    // 要改成有数据的年份喔
    let average = 0
    res.data.map((item: any) => {
      total += item.price
      data.push((item.price / 100).toFixed(2))
      if (item.price > 0) {
        average++
      }
    })
    option.foodConsumptionData = [data]

    // if (currentYear === option.foodConsumptionTime) {
    //   average = new Date().getMonth() + 1
    // }

    option.foodConsumptionCollectData = [
      {
        title: "合计",
        fee: (total / 100).toFixed(2)
      },
      {
        title: "均值",
        fee: average > 0 ? (total / 100 / average).toFixed(2) : 0
      }
    ]
    console.log("average", average, option)
  } else {
    ElMessage.error(res.msg)
  }
}

// 利润分析
const getProfitAnalysis = async () => {
  loading.value = true
  const [err, res] = await to(
    apiSupervisionProfitAnalysis({
      supervision_channel_id: props.channelId,
      date_type: "year",
      year_date: option.profitAnalysisTime,
      org_id_list: props.school
    })
  )
  loading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    let data1: any = []
    let data2: any = []
    let data = res.data || {}
    let totalProfit = data.total_profit || 0
    let averageProfit = data.average_profit || 0
    let resultList = data.list || []
    if (resultList && resultList.length > 0) {
      resultList.map((item: any) => {
        data1.push((item.profit / 100).toFixed(2))
        data2.push((item.last_profit / 100).toFixed(2))
      })
      option.profitAnalysisData = [data1, data2]
      let average = 12
      if (currentYear === option.profitAnalysisTime) {
        average = new Date().getMonth() + 1
      }
      console.log("average", average, currentYear, option.foodConsumptionTime)
      option.profitAnalysisCollectData = [
        {
          title: "合计",
          fee: (totalProfit / 100).toFixed(2)
        },
        {
          title: "均值",
          fee: (averageProfit / 100).toFixed(2)
        }
      ]
    }
  } else {
    ElMessage.error(res.msg)
  }
}

const changeRevenueExpenditureData = (e: any) => {
  option.revenueExpenditureType = e.type
  option.revenueExpenditureTime = e.time.value
  getIncomeExpensesProfit()
}

const changeCapitalExpensesData = (e: any) => {
  option.capitalExpensesType = e.type
  option.capitalExpensesTime = e.time.value
  getExpenditureFunds()
}

const changeCapitalIncomeData = (e: any) => {
  option.capitalIncomeTime = e.time.value
  computeLineData(e.time.value, "capitalIncome", LINE_DATA_capitalIncome)
}

const changeOperatingIncomeData = (e: any) => {
  option.operatingIncomeTime = e.time.value
  console.log(option.operatingIncomeTime, "option.operatingIncomeTime")
  computeLineData(e.time.value, "operatingIncome", LINE_DATA_operatingIncome)
}

const changeFoodConsumptionData = (e: any) => {
  option.foodConsumptionTime = e.time.value
  getIngredientExhaustion()
}
const changeProfitAnalysisData = (e: any) => {
  option.profitAnalysisTime = e.time.value
  getProfitAnalysis()
}

const computeLineData = (time: any, key: string, linedata: any) => {
  if (time === 2023) {
    option[key + "Data"] = []
    option[key + "Legend"].map((item: any, index: any) => {
      option[key + "Data"].push([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])
    })
    option[key + "CollectData"] = [
      {
        title: "合计",
        fee: 0
      },
      {
        title: "均值",
        fee: 0
      }
    ]
  } else {
    let schoolList: any = []
    let total: any = 0
    if (props.school.length) {
      schoolList = props.school
    } else {
      schoolList = Object.keys(linedata)
    }
    let resultlist: any = []
    option[key + "Legend"].map((item: any, index: any) => {
      let datainfo: any = []
      schoolList.map((schoolitem: any, schoolindex: any) => {
        if (linedata[schoolitem] && linedata[schoolitem][index]) {
          datainfo.push(linedata[schoolitem][index])
        }
      })
      let result = new Array(MONTH_LIST.length).fill(0)
      // 遍历每个数组，并将相同索引的值累加到 result 中
      datainfo.forEach((arr: any) => {
        arr.forEach((value: any, index: any) => {
          result[index] += Number(value)
        })
      })
      result = result.map((item) => {
        total += item
        return item.toFixed(2)
      })
      resultlist.push(result)
    })
    console.log("resultlist", resultlist)
    option[key + "Data"] = resultlist
    option[key + "CollectData"] = [
      {
        title: "合计",
        fee: total.toFixed(2)
      },
      {
        title: "均值",
        fee: (total / 12).toFixed(2)
      }
    ]
  }
}

watch(
  () => props.school,
  (newVal) => {
    initData()
  }
)
watch(
  () => props.channelId,
  (newValue) => {
    initData()
  }
)
watch(
  () => activeThemeName.value,
  (newVal) => {
    colorList.value = [
      getCssVariableValue("--el-color-echart-1"),
      getCssVariableValue("--el-color-echart-2"),
      getCssVariableValue("--el-color-echart-3"),
      getCssVariableValue("--el-color-echart-4"),
      getCssVariableValue("--el-color-echart-5")
    ]
    initData()
  }
)

onMounted(() => {
  initData()
})

// watch(
//   () => props.chartConfig.option.chartOpts.dataset,
//   () => {
//     option.value = props.chartConfig.option
//   }
// )
</script>

<style scoped lang="scss">
.supervision-data {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  .chart-wrap {
    background: #f4f6fc;
    width: 646px;
    height: 364px;
    border-radius: 10px;
    padding: 10px 20px;
    margin-bottom: 20px;
  }
  .chart-wrap:nth-child(2n-1) {
    margin-right: 20px;
  }
}
</style>
