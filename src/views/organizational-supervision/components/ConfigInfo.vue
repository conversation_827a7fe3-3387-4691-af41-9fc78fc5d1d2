<template>
  <div class="food-safety-container container-wrapper">
    <div class="ps-flex row-between title-txt m-t-32px">
      <div />
      <el-button type="primary" v-if="!isEdit" @click="handlerEdit">编辑</el-button>
      <el-button type="primary" @click="handlerSave" v-loading="confirmLoading" v-if="isEdit">保存</el-button>
    </div>
    <div>
      <el-form ref="dialogFormRef" label-width="140px" :model="dialogForm" :rules="dialogFormRules">
        <div class="form-box">
          <el-form-item label="利润率预警：" prop="surplus_val">
            <div class="ps-flex">
              <span>盈余阙值</span>
              <el-input class="el-width-100" v-model="dialogForm.surplus_val" :disabled="!isEdit" />
              <span>%</span>
              <span class="tips m-l-10px">超过该值时触发预警</span>
            </div>
          </el-form-item>
          <el-form-item label="" prop="loss_val">
            <div class="ps-flex">
              <span>亏损阙值</span>
              <el-input class="el-width-100" v-model="dialogForm.loss_val" :disabled="!isEdit" />
              <span>%</span>
              <span class="tips m-l-10px">超过该值时触发预警</span>
            </div>
          </el-form-item>
          <el-form-item label="预警周期：">
            <div class="ps-flex">
              <span>每月预警时间</span>
              <el-select class="el-width-100" v-model="dialogForm.time" placeholder="请选择" :disabled="!isEdit">
                <el-option v-for="(item, index) in 31" :key="index" :label="`${item}日`" :value="item" />
              </el-select>
              <span class="tips m-l-10px">选择当月日期预警上月的数据</span>
            </div>
          </el-form-item>
          <el-form-item label="同步操作：" v-if="isEdit">
            <div class="ps-flex">
              <el-select
                class="el-width-180"
                v-model="dialogForm.rateOrg"
                placeholder="请选择要同步的组织"
                :disabled="dialogForm.checkSchool"
                multiple
              >
                <el-option v-for="(item, index) in orgsList" :key="index" :label="item.name" :value="item.id" />
              </el-select>
              <div class="m-l-10px">
                <el-checkbox
                  v-model="dialogForm.checkSchool"
                  @change="selectAllOrgHandle(dialogForm.checkSchool, 'checkSchool')"
                  >同步所有学校</el-checkbox
                >
              </div>
            </div>
          </el-form-item>
        </div>
        <div class="form-box">
          <el-form-item label="原材料支出预警：" prop="threshold">
            <div class="ps-flex">
              <span>阙值</span>
              <el-input class="el-width-100" v-model="dialogForm.threshold" :disabled="!isEdit" />
              <span>%</span>
              <span class="tips m-l-10px">低于该值时触发预警</span>
            </div>
          </el-form-item>
          <el-form-item label="预警周期：">
            <div class="ps-flex">
              <span>每月预警时间</span>
              <el-select class="el-width-100" v-model="dialogForm.timeRaw" placeholder="请选择" :disabled="!isEdit">
                <el-option v-for="(item, index) in 31" :key="index" :label="`${item}日`" :value="item" />
              </el-select>
              <span class="tips m-l-10px">选择当月日期预警上月的数据</span>
            </div>
          </el-form-item>
          <el-form-item label="同步操作：" v-if="isEdit">
            <div class="ps-flex">
              <el-select
                class="el-width-180"
                v-model="dialogForm.rawOrg"
                placeholder="请选择要同步的组织"
                :disabled="dialogForm.checkRawOrg"
                multiple
              >
                <el-option v-for="(item, index) in orgsList" :key="index" :label="item.name" :value="item.id" />
              </el-select>
              <div class="m-l-10px">
                <el-checkbox
                  v-model="dialogForm.checkRawOrg"
                  @change="selectAllOrgHandle(dialogForm.checkRawOrg, 'checkRawOrg')"
                  >同步所有学校</el-checkbox
                >
              </div>
            </div>
          </el-form-item>
        </div>
        <!-- <div class="form-box mb-20px">
          <el-form-item class="form-item" label="采购质量预警：">
            <div v-for="(item, index) in dialogForm.stats_list" :key="index" class="flex flex-align-center m-b-10px">
              <el-select class="el-width-100" v-model="item.name" :disabled="!isEdit">
                <el-option
                  v-for="(item, index) in materialAttributeList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-select class="el-width-100" v-model="item.decider" :disabled="!isEdit">
                <el-option key="lt" label="<" value="lt" />
                <el-option key="lte" label="≤" value="lte" />
                <el-option key="gte" label="≥" value="gte" />
                <el-option key="gt" label=">" value="gt" />
              </el-select>
              <span>阙值</span>
              <el-input class="el-width-100" v-model="item.value" :disabled="!isEdit" />
              <span>%</span>
              <div class="ps-flex m-l-20px">
                <el-icon :size="20" @click="delTimeRange(index)" v-if="dialogForm.stats_list.length > 1"
                  ><RemoveFilled
                /></el-icon>
                <el-icon :size="20" @click="addTimeRange()" v-if="dialogForm.stats_list.length - 1 === index"
                  ><CirclePlusFilled
                /></el-icon>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="预警周期：">
            <div class="ps-flex">
              <span>每月预警时间</span>
              <el-select
                class="el-width-100"
                v-model="dialogForm.timePurchase"
                placeholder="请选择"
                :disabled="!isEdit"
              >
                <el-option v-for="(item, index) in 31" :key="index" :label="`${item}日`" :value="item" />
              </el-select>
              <span class="tips m-l-10px">选择当月日期预警上月的数据</span>
            </div>
          </el-form-item>
          <el-form-item label="同步操作：" v-if="isEdit">
            <div class="ps-flex">
              <el-select
                class="el-width-180"
                v-model="dialogForm.purchaseOrg"
                placeholder="请选择要同步的组织"
                :disabled="dialogForm.checkPurchase"
                multiple
              >
                <el-option v-for="(item, index) in orgsList" :key="index" :label="item.name" :value="item.id" />
              </el-select>
              <div class="m-l-10px">
                <el-checkbox
                  v-model="dialogForm.checkPurchase"
                  @change="selectAllOrgHandle(dialogForm.checkPurchase, 'checkPurchase')"
                  >同步所有学校</el-checkbox
                >
              </div>
            </div>
          </el-form-item>
        </div> -->
      </el-form>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue"
import {
  apiBackgroundFundSupervisionOrganizationSupervisionSettingInfoModifyPost,
  apiBackgroundFundSupervisionOrganizationSupervisionSettingInfoDetailPost,
  apiBackgroundFundSupervisionOrganizationSupervisionGetMaterialsAttributePost
} from "@/api/warning"
import to from "await-to-js"
import { ElMessage } from "element-plus"
import { validateTwoDecimal } from "@/utils/validate-form"
import { cloneDeep } from "lodash"

const props = defineProps({
  selectTree: {
    type: Object,
    required: false,
    default: () => {}
  },
  treeList: {
    type: Array<any>,
    default: () => []
  }
})
// 保存loading
const confirmLoading = ref(false)
// 表单
const dialogFormRef = ref()

const dialogForm = reactive({
  surplus_val: "3",
  loss_val: "-3",
  time: 1,
  rateOrg: [] as any,
  checkSchool: false,
  threshold: "70",
  timeRaw: 1,
  rawOrg: [] as any,
  purchaseOrg: [] as any,
  checkRawOrg: false,
  stats_list: [
    {
      name: "",
      decider: "",
      value: ""
    }
  ],
  timePurchase: 1,
  checkPurchase: false
})
// 表单校验
const dialogFormRules = reactive({
  surplus_val: [
    {
      required: false,
      message: "请输入",
      trigger: "blur"
    },
    {
      validator: validateTwoDecimal,
      trigger: "blur"
    }
  ],
  loss_val: [
    {
      required: false,
      message: "请输入",
      trigger: "blur"
    },
    {
      validator: validateTwoDecimal,
      trigger: "blur"
    }
  ],
  threshold: [
    {
      required: false,
      message: "请输入",
      trigger: "blur"
    },
    {
      validator: validateTwoDecimal,
      trigger: "blur"
    }
  ]
})
// 组织列表
const orgsList = ref<Array<any>>()
const isEdit = ref(false)

const delTimeRange = (index: any) => {
  dialogForm.stats_list.splice(index, 1)
}
const addTimeRange = () => {
  dialogForm.stats_list.push({
    name: "",
    decider: "",
    value: ""
  })
}

// 获取物资属性
const materialAttributeList = ref<any>([])
const getMaterialAttributeList = async () => {
  const [err, res]: [any, any] = await to(
    apiBackgroundFundSupervisionOrganizationSupervisionGetMaterialsAttributePost({
      org_id: props.selectTree.id
    })
  )
  if (err) {
    ElMessage.error(err.msg)
    return
  }
  if (res && res.code === 0) {
    materialAttributeList.value = res.data.map((item: any) => {
      let obj = {
        label: item,
        value: item
      }
      return obj
    })
  } else {
    ElMessage.error(res.msg)
  }
}

// 保存
const handlerSave = async () => {
  console.log("handlerSave", dialogForm)
  if (dialogFormRef.value) {
    dialogFormRef.value.validate((valid: any, fields: any) => {
      if (valid) {
        console.log("submit!")
        saveDataBySetting()
      } else {
        console.log("error submit!", fields)
      }
    })
  }
}
// 保存设置
const saveDataBySetting = async () => {
  confirmLoading.value = true
  let params = {
    org_id: props.selectTree.id,
    profit_rate: {
      surplus_val: parseFloat(dialogForm.surplus_val),
      loss_val: parseFloat(dialogForm.loss_val),
      date: dialogForm.time === 31 ? 0 : dialogForm.time
    },
    profit_rate_org_list: dialogForm.rateOrg,
    raw_materials: {
      threshold: parseFloat(dialogForm.threshold),
      date: dialogForm.timeRaw === 31 ? 0 : dialogForm.timeRaw
    },
    raw_materials_org_list: dialogForm.rawOrg,
    materials_stats: {
      date: dialogForm.timePurchase === 31 ? 0 : dialogForm.timePurchase,
      stats_list: cloneDeep(dialogForm.stats_list)
    },
    materials_stats_org_list: dialogForm.purchaseOrg
  }
  const [err, res] = await to(apiBackgroundFundSupervisionOrganizationSupervisionSettingInfoModifyPost(params))
  confirmLoading.value = false
  if (err) {
    return
  }

  if (res && res.code === 0) {
    ElMessage.success("保存成功")
    isEdit.value = false
    getSettingInfo()
  } else {
    ElMessage.error(res.msg || "保存失败")
  }
}
// 获取父级的绑定组织
const getParantBindOrgs = (id: any, list: Array<any>): Array<any> => {
  console.log("getParantBindOrgs", id, list)
  if (list && list.length > 0) {
    for (let index = 0; index < list.length; index++) {
      const element: any = list[index]
      const bindedOrgInfo = element.binded_org_info || []
      const childRenList = element.children_list
      if (bindedOrgInfo && bindedOrgInfo.length > 0) {
        const findItem = bindedOrgInfo.find((item: any) => item.org_id === id)
        if (findItem) {
          return bindedOrgInfo
        }
      }
      if (childRenList && childRenList.length > 0) {
        let resultOrgs = getParantBindOrgs(id, childRenList)
        if (resultOrgs && resultOrgs.length > 0) {
          return resultOrgs
        }
      }
    }
  }
  return []
}
// 获取预警配置信息
const getSettingInfo = async () => {
  confirmLoading.value = true
  let params: any = {
    org_id: props.selectTree.id
  }
  const [err, res] = await to(apiBackgroundFundSupervisionOrganizationSupervisionSettingInfoDetailPost(params))
  confirmLoading.value = false
  if (err) {
    return
  }

  if (res && res.code === 0) {
    let data: any = res.data || {}
    console.log("getSettingInfo", data)
    let profitRate = data.profit_rate || {}
    let rawMaterials = data.raw_materials || {}
    let materialsStats = data.materials_stats || {}
    dialogForm.surplus_val = profitRate.surplus_val || ""
    dialogForm.loss_val = profitRate.loss_val || ""
    dialogForm.time = profitRate.date === 0 ? 31 : profitRate.date
    dialogForm.threshold = rawMaterials.threshold || ""
    dialogForm.timeRaw = rawMaterials.date === 0 ? 31 : rawMaterials.date
    dialogForm.timePurchase = materialsStats.date === 0 ? 31 : materialsStats.date
    dialogForm.stats_list = cloneDeep(materialsStats.stats_list || [])
    console.log("dialogForm", dialogForm)
  } else {
    ElMessage.error(res.msg || "获取配置信息失败")
  }
}
const handlerEdit = () => {
  isEdit.value = true
}

// 全选操作
const selectAllOrgHandle = (status: boolean, type: string) => {
  const newArr = [] as any
  if (status) {
    switch (type) {
      case "checkSchool":
        dialogForm.rateOrg = orgsList.value?.map((item: any) => {
          return item.org_id
        })
        break
      case "checkRawOrg":
        dialogForm.rawOrg = orgsList.value?.map((item: any) => {
          return item.org_id
        })
        break
      case "checkPurchase":
        dialogForm.purchaseOrg = orgsList.value?.map((item: any) => {
          return item.org_id
        })
        break
    }
  } else {
    switch (type) {
      case "checkSchool":
        dialogForm.rateOrg = cloneDeep(newArr)
        break
      case "checkRawOrg":
        dialogForm.rawOrg = cloneDeep(newArr)
        break
      case "checkPurchase":
        dialogForm.purchaseOrg = cloneDeep(newArr)
        break
    }
  }
}

watch(
  () => props.selectTree,
  (val) => {
    console.log("selectTree configInfo", val)
  }
)
onMounted(() => {
  getMaterialAttributeList()
  if (props.treeList) {
    let list = getParantBindOrgs(props.selectTree.id, props.treeList)
    console.log("selectTree configInfo 222", list)
    if (list && Array.isArray(list)) {
      orgsList.value = list.filter((item: any) => {
        return item.id !== props.selectTree.id
      })
    }
  }
  getSettingInfo()
})
</script>
<style lang="scss" scoped>
.title-txt {
  color: #20201f;
  font-size: 20px;
}

.tips {
  color: #b9b9b9;
}

.el-width-100 {
  width: 100px !important;
  margin: 0 10px;
}
.el-width-180 {
  width: 180px !important;
  margin: 0 10px;
}

.form-box {
  background: #f4f6fc;
  padding: 20px;
  border-radius: 10px;
  margin-top: 20px;
  width: 800px;
}

.form-item .el-form-item__content {
  display: block;
}
</style>
