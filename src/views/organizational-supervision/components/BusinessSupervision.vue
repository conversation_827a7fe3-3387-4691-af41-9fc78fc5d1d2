<template>
  <div class="container-wrapper purchase-record">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="">
          <el-tooltip placement="top-start">
            <template #content> {{ tipTxt }} </template>
            <img :src="IcQuestionBlack" class="w-32px h-32px" />
          </el-tooltip>
        </div>
        <div class="table-button">
          <el-button type="primary" @click="handlerMulti">更新数据</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowDetail(row)" type="primary"> 详情 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { SEARCH_FORM_SETTING_BUSINESS_MONITOR, TABLE_SETTING_BUSINESS_MONITOR } from "../constants"
import { apiCardGroupListPost } from "@/api/user"
import { ElMessage } from "element-plus"
import IcQuestionBlack from "@/assets/layouts/ic_question_black.png"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(100, 40, psTableRef, 70)
// table数据
const tableData = ref([{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_BUSINESS_MONITOR))
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_BUSINESS_MONITOR)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

const tipTxt =
  "食堂利润不得超过5%；自营食堂直接成本(原辅材料成本)不得低于伙食费标准的65%，承包、托管经营食堂直接成本(原辅材料成本)不得低于学生伙食费标准的60%。红色为超标预警，灰色为预警忽略。"

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "feature") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.feature = data[key].value
      }
    }
  }
  return params
}
// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiCardGroupListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
// 详情
const handlerShowDetail = (row: any) => {
  console.log(row)
}
// 批量下发
const handlerMulti = () => {
  console.log("批量下发")
}

onMounted(() => {
  // getDataList()
})
</script>
<style lang="scss" scoped>
.purchase-record {
  .search-form-wrapper {
    box-shadow: none !important;
    min-height: 40px !important;
    :deep(.el-button) {
      margin-bottom: 0 !important;
    }
  }
  :deep(.el-form-item) {
    margin-bottom: 0 !important;
  }
  .el-button {
    margin-bottom: 0 !important;
  }
}
</style>
