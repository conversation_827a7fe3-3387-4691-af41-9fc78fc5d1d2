<template>
  <div class="container-wrapper storage-record">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header ps-flex col-center">
        结算总金额￥{{ totalData.settlement_price_total }}， 待结算总金额￥{{ totalData.not_settlement_price_total }}
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="loading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowDetail(row)" type="primary"> 结算明细 </el-button>
              <el-button plain link size="small" @click="handlerShowDetail(row)" type="primary"> 供应商 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { SEARCH_FORM_SETTING_SUPPLIER_INFO, TABLE_SETTING_SUPPLIER_INFO } from "../constants"
import { apiSupervisionVendorManageList, apiSupervisionVendorManageTotal } from "@/api/supervision/index"
import { ElMessage } from "element-plus"
import { useRouter } from "vue-router"
const router = useRouter()

const props = defineProps({
  selectTree: {
    type: Object,
    required: false,
    default: ""
  }
})

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(20, 40, psTableRef, 80)
// table数据
const tableData = ref([])
const totalData = ref<any>({})
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_SUPPLIER_INFO))
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_SUPPLIER_INFO)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}
// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getVendorManageList()
  getVendorManageTotal()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getVendorManageList()
  getVendorManageTotal()
}

// 供应商
const getVendorManageList = async () => {
  loading.value = true
  const [err, res] = await to(
    apiSupervisionVendorManageList({
      ...formatQueryParams(searchFormSetting.value),
      org_id: props.selectTree.id,
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  loading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
  } else {
    ElMessage.error(res.msg)
  }
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getVendorManageList()
  getVendorManageTotal()
}

// 供应商合计
const getVendorManageTotal = async () => {
  loading.value = true
  const [err, res] = await to(
    apiSupervisionVendorManageTotal({
      ...formatQueryParams(searchFormSetting.value),
      org_id: props.selectTree.id
    })
  )
  loading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    totalData.value = res.data
    console.log("totalData", totalData.value)
  } else {
    ElMessage.error(res.msg)
  }
}

// 详情
const handlerShowDetail = (row: any) => {
  console.log(row)
  router.push({ path: "food_safety_traceability", query: { id: row.id } })
}

onMounted(() => {
  getVendorManageList()
  getVendorManageTotal()
})
</script>
<style lang="scss" scoped>
.storage-record {
  .search-form-wrapper {
    box-shadow: none !important;
    min-height: 40px !important;
  }
}
</style>
