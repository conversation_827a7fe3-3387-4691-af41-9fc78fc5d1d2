<template>
  <div class="container-wrapper storage-record">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="table-button">
          <el-button type="primary" @click="goToExport">导出</el-button>
          <el-button type="primary" @click="gotoPrint">打印</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableWarnRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #content="{ row }">
              {{ row.warn_detail }}
              <span class="red-text" v-if="searchFormSetting.classify.value === 'business_warn'">
                （{{ row.warn_val }}%）
              </span>
              <span class="red-text" v-if="searchFormSetting.classify.value === 'material_risk'">
                （ {{ row.materials_name }}）
              </span>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted, watch } from "vue"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { apiSupervisionRiskWarningList, apiSupervisionRiskWarningListExport } from "@/api/supervision/index"
import { SEARCH_FORM_SETTING_RISK_WARNING } from "../constants"
import { ElMessage } from "element-plus"
import { useRouter } from "vue-router"
import { exportHandle } from "@/utils/exportExcel"
import { setLocalStorage } from "@/utils/storage"

const props = defineProps({
  selectTree: {
    type: Object,
    required: false,
    default: () => {}
  },
  treeList: {
    type: Array<any>,
    default: () => []
  }
})

const router = useRouter()
// 父级channelId
const supervisionChannelId = ref("")

const psTableWarnRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(90, 40, psTableWarnRef, 90)
// table数据
const tableData = ref([])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_RISK_WARNING))
const tableLoading = ref(false)
const tableSetting = [
  {
    label: "预警时间",
    prop: "create_time"
  },
  {
    label: "预警指标",
    prop: "classify"
  },
  {
    label: "预警类别",
    prop: "warn_type"
  },
  {
    label: "预警内容",
    prop: "warn_detail",
    slot: "content"
  }
]
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}

// 搜索
const changeSearch = (e?: any, type?: any) => {
  console.log("type", type)
  if (type && typeof type !== "string") {
    switch (searchFormSetting.value.classify.value) {
      case "business_warn":
        searchFormSetting.value.warn_type.label = "类型"
        searchFormSetting.value.warn_type.dataList = [
          {
            label: "全部",
            value: ""
          },
          {
            label: "利润率盈余",
            value: "surplus"
          },
          {
            label: "利润率亏损",
            value: "loss"
          },
          {
            label: "原材料占比",
            value: "raw_material_percentage"
          }
        ] as Array<any>
        break
      case "material_risk":
        searchFormSetting.value.warn_type.label = "操作类型"
        searchFormSetting.value.warn_type.dataList = [
          {
            label: "采购入库",
            value: "PURCHASE_ENTRY",
            key: "PURCHASE_ENTRY"
          },
          {
            label: "调拨入库",
            value: "BORROW_ENTRY",
            key: "BORROW_ENTRY"
          },
          {
            label: "其他入库",
            value: "OTHER_ENTRY",
            key: "OTHER_ENTRY"
          },
          {
            label: "损耗出库",
            value: "EXPEND_EXIT",
            key: "EXPEND_EXIT"
          },
          {
            label: "调拨出库",
            value: "BORROW_EXIT",
            key: "BORROW_EXIT"
          },
          {
            label: "退货出库",
            value: "REFUND_EXIT",
            key: "REFUND_EXIT"
          },
          {
            label: "其他出库",
            value: "OTHER_EXIT",
            key: "OTHER_EXIT"
          },
          {
            label: "领料出库",
            value: "RECEIVE_EXIT",
            key: "RECEIVE_EXIT"
          },
          {
            label: "采购下单",
            value: "ORDER_PURCHASE",
            key: "ORDER_PURCHASE"
          }
        ] as Array<any>
        break
      case "document_contract_alerts":
        searchFormSetting.value.warn_type.label = "证件类型"
        searchFormSetting.value.warn_type.dataList = [
          {
            label: "全部",
            value: "全部"
          },
          {
            label: "餐饮服务许可证",
            value: "catering_service_license"
          },
          {
            label: "食品经营许可证",
            value: "food_business_license"
          },
          {
            label: "食品生产许可证",
            value: "food_production_license"
          },
          {
            label: "卫生许可证",
            value: "hygienic_license"
          },
          {
            label: "健康证",
            value: "healthy_license"
          },
          {
            label: "供应商入围合同",
            value: "supplier_shortlisted_contract"
          }
        ] as Array<any>
        break
    }
    searchFormSetting.value.warn_type.value = ""
  }
  pageConfig.currentPage = 1
  getRiskWarningList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getRiskWarningList()
}

// 风险预警
const getRiskWarningList = async () => {
  console.log("getRiskWarningList", props.treeList)
  supervisionChannelId.value = getParantBindSuperVision(props.selectTree.id, props.treeList)
  console.log("supervisionChannelId.value", supervisionChannelId.value)

  tableLoading.value = true
  const [err, res] = await to(
    apiSupervisionRiskWarningList({
      ...formatQueryParams(searchFormSetting.value),
      org_id: props.selectTree.id,
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize,
      supervision_channel_id: supervisionChannelId.value
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getRiskWarningList()
}

// 详情
const handlerShowDetail = (row: any) => {
  console.log(row)
  router.push({ path: "food_safety_traceability", query: { id: row.id } })
}

// 导出
const goToExport = () => {
  const option = {
    type: "RiskWarning",
    api: apiSupervisionRiskWarningListExport,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      org_id: props.selectTree.id,
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    }
  }
  exportHandle(option)
}

const gotoPrint = () => {
  let tabbleSetting = cloneDeep(tableSetting)
  tabbleSetting = tabbleSetting.filter((item: any) => item.prop !== "img")
  setLocalStorage("print_setting", tabbleSetting)
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: "RiskWarning",
      print_title: "风险预警",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiSupervisionRiskWarningList", // 请求的api
      params: JSON.stringify({
        ...formatQueryParams(searchFormSetting.value),
        org_id: props.selectTree.id,
        page: 1,
        page_size: 9999
      })
    }
  })
  window.open(href, "_blank")
}
// 获取父级的绑定渠道Id
const getParantBindSuperVision = (id: any, list: Array<any>): any => {
  if (list && list.length > 0) {
    for (let index = 0; index < list.length; index++) {
      const element: any = list[index]
      const bindedOrgInfo = element.binded_org_info || []
      const childRenList = element.children_list
      if (bindedOrgInfo && bindedOrgInfo.length > 0) {
        const findItem = bindedOrgInfo.find((item: any) => item.org_id === id)
        if (findItem) {
          return element.id
        }
      }
      if (childRenList && childRenList.length > 0) {
        let resultSurpervison = getParantBindSuperVision(id, childRenList)
        if (resultSurpervison) {
          return resultSurpervison
        }
      }
    }
  }
  return ""
}

onMounted(() => {
  getRiskWarningList()
})
</script>
<style lang="scss" scoped>
.storage-record {
  .search-form-wrapper {
    box-shadow: none !important;
    min-height: 40px !important;
  }
  .red-text {
    color: red;
  }
}
.container-wrapper .table-wrapper {
  box-shadow: none;
  -webkit-box-shadow: none;
}
</style>
