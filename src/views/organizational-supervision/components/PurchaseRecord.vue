<template>
  <div class="container-wrapper purchase-record">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="header">
        <div class="m-l-20px m-b-24px">采购总金额：¥{{ totalAmount }}</div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="handlerShowDetail(row)" type="primary"> 详情 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!--弹窗-->
    <purchase-and-income-detail-dialog
      :is-show="isShowDetail"
      :title="dialogTitle"
      :type="dialogType"
      @confirm-dialog="handlerCloseDetail"
    />
  </div>
</template>
<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { SEARCH_FORM_SETTING_PURCHASE_RECORD, TABLE_SETTING_PURCHASE_RECORD } from "../constants"
import { apiBackgroundFundSupervisionSupervisionDataPurchaseInfoListPost } from "@/api/supervision"
import { ElMessage } from "element-plus"
import PurchaseAndIncomeDetailDialog from "./PurchaseAndIncomeDetailDialog.vue"

const props = defineProps({
  selectTree: {
    type: Object,
    default: () => {}
  }
})

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 40, psTableRef, 70)
// table数据
const tableData = ref([
  {
    pay_time: "2024-11-12",
    order_num: "*********************",
    price: "2365.2",
    account_name: "副食库",
    create_time: "2024-11-12 10:28:26",
    creater_name: "莫特俊"
  }
])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTING_PURCHASE_RECORD))
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_PURCHASE_RECORD)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
const totalAmount = ref(2356.2)
// 弹窗
const isShowDetail = ref(false)
const dialogTitle = ref("采购单详情")
const dialogType = ref("purchase")
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}
// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionSupervisionDataPurchaseInfoListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize,
      // org_id: props.selectTree.id
      org_id: 2121
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
// 详情
const handlerShowDetail = (row: any) => {
  console.log(row)
  isShowDetail.value = true
}
// 关闭详情
const handlerCloseDetail = () => {
  isShowDetail.value = false
}

onMounted(() => {
  getDataList()
})
</script>
<style lang="scss" scoped>
.purchase-record {
  .search-form-wrapper {
    box-shadow: none !important;
    min-height: 40px !important;
    :deep(.el-button) {
      margin-bottom: 0 !important;
    }
  }
  :deep(.el-form-item) {
    margin-bottom: 0 !important;
  }
  .el-button {
    margin-bottom: 0 !important;
  }
}
</style>
