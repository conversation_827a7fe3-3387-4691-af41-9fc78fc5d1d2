<template>
  <div class="ai-detail container-wrapper">
    <el-drawer
      v-model="dialogFormVisible"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div>
        <div class="flex">
          <div class="tag-title">预警时间</div>
          <div class="tag-content">{{ ruleForm.warning_time || "" }}</div>
        </div>
        <div class="flex" v-if="type === 'detail'">
          <div class="tag-title">所属组织</div>
          <div class="tag-content">{{ ruleForm.organization || "" }}</div>
        </div>
        <div class="flex" v-if="type === 'detail'">
          <div class="tag-title">所属渠道</div>
          <div class="tag-content">{{ ruleForm.origin_channel || "" }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">预警类型</div>
          <div class="tag-content">{{ ruleForm.type || "" }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">预警信息</div>
          <div class="tag-content">{{ ruleForm.channel || "" }}</div>
        </div>
        <div class="flex" v-if="type === 'detail'">
          <div class="tag-title">状态</div>
          <div class="tag-content">{{ ruleForm.status || "" }}</div>
        </div>
        <div class="flex" v-if="type === 'detail'">
          <div class="tag-title">下发时间</div>
          <div class="tag-content">{{ ruleForm.time || "" }}</div>
        </div>
        <div class="flex" v-if="type === 'detail'">
          <div class="tag-title">操作员</div>
          <div class="tag-content">{{ ruleForm.operator_name || "" }}</div>
        </div>
        <div class="flex" v-if="type === 'detail'">
          <div class="tag-title">核实要求</div>
          <div class="tag-content">{{ ruleForm.request || "" }}</div>
        </div>
        <div class="flex" v-if="type === 'detail'">
          <div class="tag-title border-bottom">核实时间</div>
          <div class="tag-content border-bottom">{{ ruleForm.verify_time || "" }}</div>
        </div>
        <div class="flex" v-if="type === 'detail'">
          <div class="tag-title border-bottom">核实信息</div>
          <div class="tag-content border-bottom">{{ ruleForm.verify_info || "" }}</div>
        </div>
        <div class="flex" v-if="type === 'detail'">
          <div class="tag-title border-bottom">忽略时间</div>
          <div class="tag-content border-bottom">{{ ruleForm.ignore_time || "" }}</div>
        </div>
        <div class="flex" v-if="type === 'detail'">
          <div class="tag-title border-bottom">忽略原因</div>
          <div class="tag-content border-bottom">{{ ruleForm.ignore_reason || "" }}</div>
        </div>
      </div>
      <!--核实要求-->
      <div class="table-content" v-if="type === 'issued' || type === 'ignore'">
        <div class="m-t-20px m-b-10px">
          <span class="color-[#FF5656] text-size-16px">*</span>{{ type === "issued" ? "核实要求：" : "忽略原因：" }}
        </div>
        <el-form label-position="left" class="m-t-20px" ref="ruleFormRef" :rules="rules" :model="ruleForm">
          <el-form-item prop="remark">
            <el-input
              type="textarea"
              v-model="ruleForm.remark"
              :rows="6"
              placeholder="请输入"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="dialog-footer m-t-20px">
        <el-button type="primary" @click="closeDialog" v-loading="confirmLoading" v-if="type === 'detail'">
          关闭
        </el-button>
        <el-button
          type="primary"
          @click="closeDialog"
          v-loading="confirmLoading"
          v-if="type === 'issued' || type === 'ignore'"
          class="ps-origin-btn-light"
        >
          取消
        </el-button>
        <el-button type="primary" @click="confirmDialog" v-if="type === 'issued' || type === 'ignore'">保存</el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue"
import type { TAiForm } from "../index.d"

const props = defineProps({
  title: {
    type: String,
    default: "预警详情"
  },
  width: {
    type: String,
    default: "768px"
  },
  isShow: {
    type: Boolean,
    default: false
  },
  type: {
    //  detail  详情  issued  下发  ignore  忽略
    type: String,
    default: "detail"
  },
  orgId: {
    type: Number,
    default: -1
  },
  dialogData: {
    type: Object,
    default: () => {
      return {}
    }
  },
  // 选择节点数据
  organizationData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const dialogFormVisible = ref(false)
const confirmLoading = ref(false)
const ruleForm = reactive<TAiForm>({
  warning_time: "2023-05-01",
  organization: "严格当月的原材料支出情况，限3天上报具体数据",
  type: "采购预警",
  origin_channel: "教育局监管",
  channel: "溢价物资12种，最高溢价30%；折价物资1种，最低折价20%",
  status: "已核实",
  time: "2024-10-17- 16:03:02",
  operator_name: "上山居士（ssjs001）",
  request: "严格当月的原材料支出情况，限3天上报具体数据",
  verify_time: "2024-10-17 16:04:04",
  verify_info: "数据统计有误",
  ignore_reason: "数据统计有误",
  ignore_time: "2024年10月18日 14:16:27",
  remark: ""
})
const ruleFormRef = ref()
// 规则验证
const rules = reactive({
  remark: [
    {
      required: true,
      message: "请输入备注",
      trigger: "blur"
    }
  ]
})

// 弹窗确认
const confirmDialog = () => {
  dialogFormVisible.value = false
  emit("confirmDialog")
}

// 弹窗关闭
const closeDialog = () => {
  dialogFormVisible.value = false
  if (ruleFormRef.value) {
    ruleFormRef.value.clearValidate()
  }
  emit("cancelDialog")
}

watch(
  () => ({
    isShow: props.isShow
  }),
  (newValue) => {
    console.log("watch dialog", newValue)
    dialogFormVisible.value = newValue.isShow
    if (newValue.isShow) {
      // initData()
      console.log("show ", ruleForm)
    }
  }
)
defineExpose({})
</script>
<style lang="scss" scoped>
.container-wrapper .table-content {
  padding: 0 !important;
}
.ai-detail {
  .tag-title {
    width: 120px;
    min-width: 120px;
    height: 40px;
    line-height: 40px;
    background: #f4f6fc;
    font-size: 14px;
    padding-left: 20px;
    border-top: 1px solid #e9ecf1;
    border-left: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .tag-content {
    min-width: 880px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    padding-left: 20px;
    border-top: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .border-bottom {
    border-bottom: 1px solid #e9ecf1;
  }
}
</style>
