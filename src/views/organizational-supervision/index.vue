<template>
  <div class="flex">
    <div class="left">
      <orgs-tree @treeClick="handlerThreeChange" />
    </div>
    <div class="right" ref="rightRef">
      <!--menu部分-->
      <div class="flex justify-between" v-if="!isSupervision">
        <!--右侧顶部菜单-->
        <div class="title flex">
          <div
            v-for="(item, index) in menuList"
            :key="index"
            class="flex items-center"
            v-permission="[`${item.permission}`]"
          >
            <div
              :class="['menu-tag', currentMenu === item.value ? 'active' : 'in-active']"
              @click="handlerMenuChange(item.value)"
            >
              {{ item.name }}
            </div>
            <div class="line-gray" v-if="index > 0 && index < menuList.length - 1" />
          </div>
        </div>
        <!--提示-->
        <div>
          <el-tooltip placement="top">
            <template #content> 监管需获取源数据，查询速度较慢，请耐心等待。 </template>
            <img :src="IcQuestionBlack" class="w-32px h-32px" />
          </el-tooltip>
        </div>
      </div>
      <!--内容部分 内容部分的最大高度应该是右边的高度减去顶部菜单的高度-->
      <el-scrollbar :height="isSupervision ? rightHeight : rightHeight - 60">
        <div>
          <base-info :select-tree="selectedNode" v-if="currentMenu === 'base_info' && isSupervision === false" />
          <food-safety :select-tree="selectedNode" v-if="currentMenu === 'food_safety'" />
          <!-- 数据问题，用了v-show -->
          <supervision-info :select-tree="selectedNode" v-if="currentMenu === 'base_info' && isSupervision === true" />
          <supervision-data :select-tree="selectedNode" v-if="currentMenu === 'supervision_data'" />
          <purchase-record :select-tree="selectedNode" v-if="currentMenu === 'purchase_record'" />
          <storage-record :select-tree="selectedNode" v-if="currentMenu === 'storage_record'" />
          <sample-record :select-tree="selectedNode" v-if="currentMenu === 'sample_record'" />
          <supplier-information :select-tree="selectedNode" v-if="currentMenu === 'supplier_information'" />
          <supplier-total :select-tree="selectedNode" v-if="currentMenu === 'supplier_total'" />
          <ai-warning :select-tree="selectedNode" v-if="currentMenu === 'AI_warning'" />
          <risk-warning :select-tree="selectedNode" v-if="currentMenu === 'risk_warning'" :tree-list="treeNodeList" />
          <business-supervision :select-tree="selectedNode" v-if="currentMenu === 'business_supervision'" />
          <kitchen-fire :select-tree="selectedNode" v-if="currentMenu === 'kitchen_fire'" />
          <building v-if="currentMenu === 'weekly_recipe'" />
          <config-info :select-tree="selectedNode" :tree-list="treeNodeList" v-if="currentMenu === 'config_info'" />
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import OrgsTree from "./components/OrgsTree.vue"
import BaseInfo from "./components/BaseInfo.vue"
import FoodSafety from "./components/FoodSafety.vue"
import SupervisionData from "./components/SupervisionData.vue"
import PurchaseRecord from "./components/PurchaseRecord.vue"
import StorageRecord from "./components/StorageRecord.vue"
import SampleRecord from "./components/SampleRecord.vue"
import SupplierInformation from "./components/SupplierInformation.vue"
import SupplierTotal from "./components/SupplierTotal.vue"
import SupervisionInfo from "./components/SupervisionInfo.vue"
import AiWarning from "./components/AiWarning.vue"
import RiskWarning from "./components/RiskWarning.vue"
import BusinessSupervision from "./components/BusinessSupervision.vue"
import KitchenFire from "./components/KitchenFire.vue"
import ConfigInfo from "./components/ConfigInfo.vue"
import IcQuestionBlack from "@/assets/layouts/ic_question_black.png"
import { SUPERVISION_MENU_LIST } from "./constants"
import { cloneDeep } from "lodash"
import { ref, watch } from "vue"
import useScreenHook from "@/hooks/useScreen"
import building from "../error-page/building.vue"

// 菜单列表
const menuList = cloneDeep(SUPERVISION_MENU_LIST)
// 当前选中菜单
const currentMenu = ref("base_info")
// 是否是监管
const isSupervision = ref(true)
// 当前选中的节点
const selectedNode = ref()
// 树形数据
const treeNodeList = ref<Array<any>>()
// 右侧高度
const rightRef = ref()
const rightHeight = useScreenHook(rightRef, 20).maxHeight
const handlerMenuChange = (value: string) => {
  console.log(value)
  currentMenu.value = value
}
// 树节点点击事件
const handlerThreeChange = (value: any, treeList: Array<any>) => {
  console.log(value, "handlerThreeChange")
  selectedNode.value = value
  currentMenu.value = "base_info"
  isSupervision.value = value.isJian
  treeNodeList.value = cloneDeep(treeList)
}

watch(
  () => rightHeight.value,
  (newValue) => {
    console.log("newValue", newValue)
  }
)
</script>
<style lang="scss" scoped>
.right {
  width: 100%;
  flex: 1;
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;
  padding: 24px;
  margin-right: 24px;

  .title {
    height: 40px;
    background: #f4f6fc;
    padding: 4px;

    .menu-tag {
      padding: 0 12px;
      font-size: 16px;
      height: 32px;
      line-height: 32px;
      cursor: pointer;
    }

    .menu-tag:hover {
      background: #ffffff;
      color: var(--el-color-primary);
      border-radius: 4px;
    }

    .active {
      background: #ffffff;
      color: var(--el-color-primary);
      border-radius: 4px;
    }

    .in-avtive {
      background: transparent;
      color: #363636;
    }

    .line-gray {
      width: 1px;
      height: 16px;
      background: #e9ecf1;
    }
  }
}
</style>
