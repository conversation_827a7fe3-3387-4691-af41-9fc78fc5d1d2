<template>
  <div class="appropriation-record container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #content="{ row }">
              <el-tooltip class="box-item" effect="dark" placement="top">
                <template #content> {{ row.apply_content }} </template>
                {{ formateText(row.apply_content) }}
              </el-tooltip>
            </template>
            <template #remark="{ row }">
              <el-tooltip class="box-item" effect="dark" placement="top">
                <template #content> {{ row.apply_remark }} </template>
                {{ formateText(row.apply_remark) }}
              </el-tooltip>
            </template>
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="openDrawer(row, 'detail')" type="primary"> 详情 </el-button>
              <el-button
                v-show="row.appropriation_status === 'appropriation_pending'"
                plain
                link
                size="small"
                @click="openDrawer(row, 'appropriation')"
                type="primary"
                v-permission="['background_fund_supervision.supervision_appropriation.supervision_appropriation']"
              >
                拨款
              </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <el-drawer
      v-model="recordDrawerShow"
      title="拨款记录"
      :direction="'rtl'"
      :show-close="false"
      class="ps-drawer"
      size="40%"
    >
      <div v-if="drawerType === 'detail'">
        <div class="apply-info info-item">
          <div class="title">申请信息</div>
          <div class="m-l-20px">
            <div class="apply-info-item">
              <div class="user-title">申请人</div>
              <div>{{ selectInfo.operator }}</div>
            </div>
            <div class="apply-info-item">
              <div class="user-title">申请来源</div>
              <div>{{ selectInfo.apply_source_alias }}</div>
            </div>
            <div class="apply-info-item">
              <div class="user-title">申请内容</div>
              <div>{{ selectInfo.apply_content }}</div>
            </div>
            <div class="apply-info-item">
              <div class="user-title">申请金额</div>
              <div>￥{{ formatPrice(selectInfo.apply_fee) }}</div>
            </div>
          </div>
        </div>
        <div class="info-item">
          <div class="title">单据信息</div>
          <div class="m-l-20px">
            <div>{{ selectInfo.appropriation_no }}</div>
          </div>
        </div>
        <div class="apply-info info-item">
          <div class="title">单据凭证</div>
          <div class="m-l-20px flex flex-items-start">
            <div v-for="(item, index) in selectInfo.image_json" :key="index">
              <el-image class="w-auto h-100" :src="item.url" :fit="'contain'" />
            </div>
          </div>
        </div>
        <div class="apply-info info-item">
          <div class="title">收款信息</div>
          <div class="m-l-20px">
            <div class="apply-info-item">
              <div class="user-title">收款人</div>
              <div>{{ selectInfo.account_person }}</div>
            </div>
            <div class="apply-info-item">
              <div class="user-title">收款账号</div>
              <div>{{ selectInfo.account_number }}</div>
            </div>
            <div class="apply-info-item">
              <div class="user-title">收款银行</div>
              <div>{{ selectInfo.account_bank }}</div>
            </div>
          </div>
        </div>
        <div class="info-item">
          <div class="title">申请备注</div>
          <div class="m-l-20px">
            <el-input type="textarea" disabled v-model="selectInfo.apply_remark" maxlength="20" />
          </div>
        </div>
        <div class="apply-info info-item">
          <div class="title">结算凭证</div>
          <div class="m-l-20px flex flex-items-start">
            <div v-for="(item, index) in selectInfo.settlement_json" :key="index">
              <el-image class="w-auto h-100" :src="item.url" :fit="'contain'" />
            </div>
          </div>
        </div>
      </div>
      <div v-if="drawerType === 'appropriation'">
        <div class="apply-info info-item">
          <div class="m-l-20px">
            <div class="apply-info-item">
              <div class="user-title">收款人</div>
              <div>{{ selectInfo.account_person }}</div>
            </div>
            <div class="apply-info-item">
              <div class="user-title">拨款金额</div>
              <div>{{ formatPrice(selectInfo.apply_fee) }}</div>
            </div>
            <div class="apply-info-item">
              <div class="user-title">收款账号</div>
              <div>{{ selectInfo.account_number }}</div>
            </div>
            <div class="apply-info-item">
              <div class="user-title">收款银行</div>
              <div>{{ selectInfo.account_bank }}</div>
            </div>
          </div>
        </div>
        <el-form ref="dialogFormRef" label-width="60px">
          <el-form-item label="拨款人">
            <el-select
              class="el-width-350"
              size="large"
              v-model="dialogForm.appropriationName"
              placeholder="请选择"
              @change="changeAppropriationName"
            >
              <el-option v-for="(item, index) in personList" :key="index" :label="item.name" :value="item.name" />
            </el-select>
          </el-form-item>
          <el-form-item label="验证码">
            <!-- <div>
              <el-input class="el-width-100" v-model="dialogForm.appropriationCode" />
              <el-button type="primary" @click="sendAppropriationName"> 获取验证码 </el-button>
            </div> -->
            <verify-message-code
              :sendAuthCode="sendAuthCode"
              :disabled="sendCodeDisabled"
              @reset="resetHandle"
              @getPhoneCode="sendAppropriationName"
              v-model="dialogForm.appropriationCode"
              @input="inputCodeChange"
            />
          </el-form-item>
          <span class="code-tips" v-if="dialogForm.appropriationName"
            >将发送验证码至{{ formatePhone(dialogForm.appropriationPhone) }}，请注意查收</span
          >
        </el-form>
      </div>
      <div class="dialog-footer m-t-20px">
        <el-button v-if="drawerType === 'appropriation'" @click="recordDrawerShow = false"> 取消 </el-button>
        <el-button type="primary" :disabled="isLoading" @click="confirmAppropriation"> 确定 </el-button>
      </div>
    </el-drawer>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted, toRefs } from "vue"
import { SEARCH_FORM_SETTING_RECORD, TABLE_SETTING_RECORD } from "./constants"
import {
  apiAppropriationList,
  apiAppropriationGetSetting,
  apiSupervisionAppropriation,
  apiSupervisionAppropriationSendCode
} from "@/api/financial/index"
import { ElMessage } from "element-plus"
import IcQuestionBlack from "@/assets/layouts/ic_question_black.png"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@//components/PsColumn/index.vue"
import { exportHandle } from "@/utils/exportExcel"
import { useRouter } from "vue-router"
import VerifyMessageCode from "@/components/VerifyMessageCode/index.vue"
import { moneyThousandFormat, divide } from "@/utils/index"
const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)
const router = useRouter()

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// table数据
const tableData = ref([])
const loading = ref(false)
// const searchFormSetting = ref(SEARCH_FORM_SETTING_RECORD)
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_RECORD)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10,
  searchFormSetting: cloneDeep(SEARCH_FORM_SETTING_RECORD)
})

let { searchFormSetting } = toRefs(pageConfig)
// 收入总金额
const incomeTotal = ref()
// 支出总金额
const outlineTotal = ref()
// 导出
const importType = "FoodSafetyTraceabilityExport"

// 抽屉
const recordDrawerShow = ref(false)
const drawerType = ref()
const selectInfo = ref<any>({})
const personList = ref<Array<any>>([])
const isLoading = ref(false)

const dialogForm = reactive({
  appropriationName: "",
  appropriationCode: "",
  appropriationPhone: ""
})

// 验证码
const sendAuthCode = ref(true)
const sendCodeDisabled = ref(true)

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime" && data[key].value !== "all") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}
// 搜索
const changeSearch = async () => {
  console.log("changeSearch")
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  changeSearch()
}

// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiAppropriationList({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 导出
const goToExport = () => {
  console.log("goToExport")
  const option = {
    type: importType,
    api: apiAppropriationList,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    }
  }
  exportHandle(option)
}

const gotoPrint = () => {
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: "ert",
      print_title: "食堂月核算报表",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiBackgroundReportCenterDataReportPersonMealListPost", // 请求的api
      table_setting: JSON.stringify(tableSetting),
      params: JSON.stringify({
        page: 1,
        page_size: 10
      })
    }
  })
  window.open(href, "_blank")
}

const openDrawer = (e: any, type: any) => {
  selectInfo.value = e
  drawerType.value = type
  dialogForm.appropriationName = ""
  dialogForm.appropriationCode = ""
  recordDrawerShow.value = true
  if (type === "appropriation") getAppropriationSetting()
}

// 获取配置
const getAppropriationSetting = async () => {
  isLoading.value = true
  const [err, res]: any[] = await to(apiAppropriationGetSetting())
  isLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    personList.value = res.data.person_info ? JSON.parse(res.data.person_info) : []
  } else {
    ElMessage.error(res.msg || "获取失败")
  }
}

const confirmAppropriation = () => {
  recordDrawerShow.value = false
  if (drawerType.value === "appropriation") {
    getSupervisionAppropriation()
  } else {
    recordDrawerShow.value = false
  }
}

// 拨款
const getSupervisionAppropriation = async () => {
  isLoading.value = true
  const [err, res]: any[] = await to(
    apiSupervisionAppropriation({
      id: selectInfo.value.id,
      appropriation_fee: selectInfo.value.appropriation_fee,
      account_person: selectInfo.value.account_person,
      account_number: selectInfo.value.account_number,
      account_bank: selectInfo.value.account_bank,
      code: dialogForm.appropriationCode,
      phone: dialogForm.appropriationPhone
    })
  )
  isLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    recordDrawerShow.value = false
    getDataList()
  } else {
    ElMessage.error(res.msg || "获取失败")
  }
}

const changeAppropriationName = (e: any) => {
  personList.value.map((item) => {
    if (item.name === e) {
      dialogForm.appropriationPhone = item.phone
    }
  })
  if (dialogForm.appropriationPhone && dialogForm.appropriationPhone.length >= 11) {
    sendCodeDisabled.value = false
  }
}

// 重置验证码倒计时
const resetHandle = () => {
  sendAuthCode.value = true
  sendCodeDisabled.value = false
}
// 输入验证码监听
const inputCodeChange = (value: any) => {
  dialogForm.appropriationCode = value
}
const sendAppropriationName = async (text: any) => {
  isLoading.value = true
  const [err, res]: any[] = await to(
    apiSupervisionAppropriationSendCode({
      phone: dialogForm.appropriationPhone
    })
  )
  isLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    sendAuthCode.value = false
    sendCodeDisabled.value = true
    ElMessage.success("发送成功")
  } else {
    ElMessage.error(res.msg || "获取失败")
  }
}

const formateText = (text: any) => {
  let str: any
  if (text.length > 15) {
    str = text.substring(0, 15) + "…"
  } else {
    str = text
  }
  return str
}

const formatePhone = (phone: any) => {
  return phone.substring(0, 3) + "****" + phone.substring(7, 11)
}

// 格式化价格
const formatPrice = (val: any) => {
  let price = divide(val)
  return price ? price : "0.00"
}

onMounted(() => {
  getDataList()
})
</script>
<style lang="scss">
.appropriation-record {
  padding: 0 20px;

  .el-popper {
    max-width: 300px;
  }

  .apply-info {
    font-size: 16px;
    .apply-info-item {
      width: 80%;
      display: flex;
      font-size: 14px;
      border-right: 1px solid #e6e8eb;
      border-bottom: 1px solid #e6e8eb;

      .user-title {
        width: 50%;
        border-right: 1px solid #e6e8eb;
        border-left: 1px solid #e6e8eb;
      }

      div {
        padding: 15px;
      }
    }
    .apply-info-item:first-child {
      border-top: 1px solid #e6e8eb;
    }
  }

  .title {
    font-size: 18px;
    margin: 15px 0 10px;
  }

  .info-item {
    margin-bottom: 20px;
  }
  .code-tips {
    color: #b6b6b6;
    margin-left: 20px;
  }
  .el-width-350 {
    width: 350px;
  }
}
</style>
