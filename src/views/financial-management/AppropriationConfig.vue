<template>
  <div class="appropriation-config container-wrapper" :style="{ height: height + 'px' }">
    <el-form :model="dialogForm" ref="dialogFormRef" label-width="230px">
      <el-form-item label="拨款审批：">
        <div class="ps-flex">
          <el-select
            class="el-width-250"
            v-model="dialogForm.approveOrgs"
            placeholder="选择需审批的监管组织"
            :disabled="dialogForm.allApproveOrgs"
            multiple
          >
            <el-option v-for="(item, index) in selectData" :key="index" :label="item.name" :value="item.id" />
          </el-select>
          <div class="m-l-10px">
            <el-checkbox v-model="dialogForm.allApproveOrgs">全选</el-checkbox>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="执行拨款：">
        <div class="ps-flex">
          <el-select
            class="el-width-250"
            v-model="dialogForm.appropriationOrgs"
            placeholder="选择需执行拨款的监管组织"
            :disabled="dialogForm.allAppropriationOrgs"
            multiple
          >
            <el-option v-for="(item, index) in selectData" :key="index" :label="item.name" :value="item.id" />
          </el-select>
          <div class="m-l-10px">
            <el-checkbox v-model="dialogForm.allAppropriationOrgs">全选</el-checkbox>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="拨款人信息：">
        <el-button type="primary" @click="openPerson()">添加/编辑</el-button>
        <div class="tips m-l-20px">说明：手机号必须已在行里添加白名单，否则无法接收短信</div>
      </el-form-item>
      <el-form-item label="">
        <el-table
          ref="tableRef"
          class="table-person"
          :data="dialogForm.personList"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="name" label="姓名" align="center" />
          <el-table-column prop="phone" label="手机号" align="center" />
          <el-table-column label="操作" align="center">
            <template #default="{ row }">
              <el-button plain link size="small" type="primary" @click="delPersonList()"> 删除 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="modifyAppropriationSetting()"> 保存 </el-button>
      </el-form-item>
    </el-form>
    <el-drawer
      v-model="personDrawerShow"
      title="添加拨款人信息"
      :direction="'rtl'"
      :show-close="false"
      class="ps-drawer"
      size="40%"
    >
      <el-form :model="dialogForm" ref="personFormRef" :rules="dialogFormRules" label-width="110px">
        <el-form-item label="姓名" prop="name">
          <el-input class="el-width-250" v-model="dialogForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input class="el-width-250" v-model="dialogForm.phone" placeholder="接收拨款验证码的手机号" />
        </el-form-item>
        <el-form-item label="所属拨款银行">
          <el-select class="el-width-250" v-model="dialogForm.bank">
            <el-option key="nonghang" label="中国农业银行" value="dongrou" />
          </el-select>
        </el-form-item>
        <div class="tips m-l-20px">说明：手机号必须已在行里添加白名单，否则无法接收短信</div>
      </el-form>
      <div class="dialog-footer m-t-20px">
        <el-button @click="personDrawerShow = false"> 取消 </el-button>
        <el-button type="primary" @click="savePerson"> 保存 </el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue"
import useScreenHook from "@/hooks/useScreen"
import { apiAppropriationGetSetting, apiAppropriationModifySetting } from "@/api/financial"
import to from "await-to-js"
import { cloneDeep } from "lodash"
import { ElMessage, FormInstance, FormRules } from "element-plus"
import { validatePhone } from "@/utils/validate-form"
import { apiBackgroundFundSupervisionSupervisionChannelChannelOrgsInofPost } from "@/api/user"

// 高度计算
const rightRef = ref()
const height = useScreenHook(rightRef, 20).maxHeight

// 加载中
const isLoading = ref(false)

// 组织列表
const selectData = ref<Array<any>>([])
const allOrgsData = ref<Array<any>>([]) // 全选的数据

// 表单
const personFormRef = ref<FormInstance>()
const dialogForm = reactive({
  approveOrgs: [],
  allApproveOrgs: false,
  appropriationOrgs: [],
  allAppropriationOrgs: false,
  personList: ref<Array<any>>([]),
  name: "",
  phone: "",
  bank: ""
})
// 规则
const dialogFormRules = reactive<FormRules<any>>({
  name: [
    {
      required: true,
      message: "请输入姓名",
      trigger: "blur"
    }
  ],
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    {
      required: true,
      validator: validatePhone,
      trigger: "blur"
    }
  ]
})

// 抽屉
const personDrawerShow = ref(false)

// 获取配置
const getAppropriationSetting = async () => {
  isLoading.value = true
  console.log("organvalue")

  isLoading.value = true
  const [err, res]: any[] = await to(apiAppropriationGetSetting())
  isLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    dialogForm.approveOrgs = res.data.need_approve_orgs ? JSON.parse(res.data.need_approve_orgs) : []
    dialogForm.appropriationOrgs = res.data.need_appropriation_orgs ? JSON.parse(res.data.need_appropriation_orgs) : []
    dialogForm.personList = res.data.person_info ? JSON.parse(res.data.person_info) : []
  } else {
    ElMessage.error(res.msg || "获取失败")
  }
}

// 修改配置
const modifyAppropriationSetting = async () => {
  isLoading.value = true
  console.log("organvalue")
  // if (!dialogForm.personList.length) return ElMessage.error("请填写拨款人信息")
  isLoading.value = true
  const [err, res]: any[] = await to(
    apiAppropriationModifySetting({
      need_approve_orgs: dialogForm.allApproveOrgs ? allOrgsData.value : dialogForm.approveOrgs,
      need_appropriation_orgs: dialogForm.allAppropriationOrgs ? allOrgsData.value : dialogForm.appropriationOrgs,
      person_info: dialogForm.personList
    })
  )
  isLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    getAppropriationSetting()
    ElMessage.success(res.msg)
  } else {
    ElMessage.error(res.msg || "获取失败")
  }
}

// 获取数据
const getOrgsData = async () => {
  isLoading.value = true
  console.log("organvalue")

  isLoading.value = true
  const [err, res]: any[] = await to(apiBackgroundFundSupervisionSupervisionChannelChannelOrgsInofPost({}))
  isLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    let data: any = res.data || {}
    let results: Array<any> = data.results || []
    selectData.value = cloneDeep(results)
    allOrgsData.value = results.map((item: any) => {
      return item.id
    })
  } else {
    ElMessage.error(res.msg || "获取失败")
  }
}

const openPerson = () => {
  personDrawerShow.value = true
  dialogForm.name = dialogForm.personList.length ? dialogForm.personList[0].name : ""
  dialogForm.phone = dialogForm.personList.length ? dialogForm.personList[0].phone : ""
}

const delPersonList = () => {
  dialogForm.personList = []
}

const savePerson = (e: any) => {
  if (!personFormRef.value) return
  personFormRef.value.validate((valid, fields) => {
    if (valid) {
      dialogForm.personList = [
        {
          name: dialogForm.name,
          phone: dialogForm.phone
        }
      ]
      personDrawerShow.value = false
    } else {
      console.log("error submit!", fields)
    }
  })
}

onMounted(() => {
  // 初始化数据
  getAppropriationSetting()
  getOrgsData()
})
</script>
<style lang="scss" scoped>
.appropriation-config {
  background-color: #fff;
  border-radius: 16px;
  padding: 24px;
  margin: 0 24px;
  .el-width-250 {
    width: 250px !important;
    margin: 0 10px;
  }
  .tips {
    color: #b9b9b9;
  }
  .table-person {
    width: 600px;
  }
}
</style>
