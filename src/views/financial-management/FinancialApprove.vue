<template>
  <div class="container-wrapper pl-20px pr-20px financial-approve">
    <el-radio-group v-model="data.tableType" @change="changeTableType" class="m-b-20px">
      <el-radio-button
        label="待审批"
        value="pending"
        v-permission="['background_fund_supervision.supervision_finance_approve.pending_list']"
      />
      <el-radio-button
        label="已同意"
        value="agree"
        v-permission="['background_fund_supervision.supervision_finance_approve.agree_list']"
      />
      <el-radio-button
        label="已拒绝"
        value="reject"
        v-permission="['background_fund_supervision.supervision_finance_approve.reject_list']"
      />
      <el-radio-button
        label="已撤销"
        value="revoke"
        v-permission="['background_fund_supervision.supervision_finance_approve.revoke_list']"
      />
    </el-radio-group>
    <div ref="searchRef">
      <search-form
        :form-setting="data.searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="data.tableSetting">
            <template #content="{ row }">
              <el-tooltip class="box-item" effect="dark" placement="top">
                <template #content> {{ row.apply_content }} </template>
                {{ formateText(row.apply_content) }}
              </el-tooltip>
            </template>
            <template #remark="{ row }">
              <el-tooltip class="box-item" effect="dark" placement="top">
                <template #content> {{ row.apply_remark }} </template>
                {{ formateText(row.apply_remark) }}
              </el-tooltip>
            </template>
            <template #operationNew="{ row }">
              <el-button
                v-if="data.tableType === 'pending'"
                plain
                link
                size="small"
                @click="gotoApprove(row, 'pending')"
                type="primary"
              >
                审批
              </el-button>
              <el-button plain link size="small" @click="gotoApprove(row, 'datail')" type="primary"> 详情 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <el-drawer
      v-model="approveDrawerShow"
      title="审批"
      :direction="'rtl'"
      :show-close="false"
      class="ps-drawer"
      size="40%"
      @close="closeDrawer"
    >
      <div class="apply-info info-item">
        <div class="title">用户信息</div>
        <div class="m-l-20px">
          <div class="apply-info-item">
            <div class="user-title">申请人</div>
            <div>{{ selectInfo.operator }}</div>
          </div>
          <div class="apply-info-item">
            <div class="user-title">申请来源</div>
            <div>{{ selectInfo.apply_source_alias }}</div>
          </div>
          <div class="apply-info-item">
            <div class="user-title">申请内容</div>
            <div>{{ selectInfo.apply_content }}</div>
          </div>
          <div class="apply-info-item">
            <div class="user-title">申请金额</div>
            <div>￥{{ formatPrice(selectInfo.apply_fee) }}</div>
          </div>
        </div>
      </div>
      <div class="info-item">
        <div class="title">单据信息</div>
        <div class="m-l-20px">
          <div>{{ selectInfo.approve_no }}</div>
        </div>
      </div>
      <div class="apply-info info-item">
        <div class="title">凭证/附件</div>
        <div class="m-l-20px flex flex-items-start flex-wrap">
          <div v-for="(item, index) in selectInfo.image_json" :key="index">
            <el-image class="h-100px m-r-10px" :src="item.url" :fit="'contain'" />
          </div>
        </div>
      </div>
      <div class="apply-info info-item">
        <div class="title">收款信息</div>
        <div class="m-l-20px">
          <div class="apply-info-item">
            <div class="user-title">供应商名称</div>
            <div>{{ selectInfo.supplier_manage_name }}</div>
          </div>
          <div class="apply-info-item">
            <div class="user-title">收款人</div>
            <div>{{ selectInfo.account_person }}</div>
          </div>
          <div class="apply-info-item">
            <div class="user-title">收款账号</div>
            <div>{{ selectInfo.account_number }}</div>
          </div>
          <div class="apply-info-item">
            <div class="user-title">收款银行</div>
            <div>{{ selectInfo.account_bank }}</div>
          </div>
        </div>
      </div>
      <div class="apply-info info-item">
        <div class="title">审批流程</div>
        <div class="step-list m-t-40">
          <el-timeline class="p-t-10 m-l-5" v-if="approveAccountInfo && approveAccountInfo.length > 0">
            <el-timeline-item
              :icon="item.icon"
              :color="item.color"
              :size="'large'"
              v-for="(item, index) in approveAccountInfo"
              :key="index"
              :timestamp="item.status_alias"
              :placement="'top'"
            >
              <div
                v-for="(itemIn, indexIn) in item.data || []"
                :key="indexIn"
                :class="[approveMethod === 'and_approve' && index !== 0 ? 'bg-grey' : '', 'm-b-10']"
              >
                <!--这里做个区别，会签和其他两个区别显示-->
                <div v-if="approveMethod !== 'and_approve'" class="flex flex-col">
                  <div class="w-350 flex flex-items-center flex-justify-between" :class="indexIn > 0 ? 'm-t-10' : ''">
                    <div>{{ itemIn?.operator || "--" }}</div>
                    <div class="w-150 flex flex-items-center flex-justify-between" v-if="itemIn?.status !== 'PENDING'">
                      <div v-if="itemIn?.status !== 'PENDING'">{{ itemIn?.timestamp || "--" }}</div>
                      <el-icon :color="itemIn?.color || '#909399'" size="18px"
                        ><CircleCheckFilled v-if="itemIn?.icon === 'el-icon-success'" />
                        <CircleCloseFilled v-if="itemIn?.icon === 'el-icon-error'"
                      /></el-icon>
                      <!-- <i :class="itemIn?.icon" :style="{ color: itemIn?.color || '#909399', fontSize: '18px' }" /> -->
                    </div>
                  </div>
                  <div v-if="index > 0 && item.status !== 'REVOKE' && itemIn?.reason" style="color: #000">
                    审批意见：{{ itemIn.reason }}
                  </div>
                </div>
                <div v-else>
                  <div v-for="(childItem, childIndex) in itemIn || []" :key="childIndex" class="flex flex-col">
                    <div
                      class="w-350 flex flex-items-center flex-justify-between"
                      :class="childIndex > 0 ? 'm-t-10' : ''"
                    >
                      <div>{{ childItem?.operator || "--" }}</div>
                      <div
                        class="w-150 flex flex-items-center flex-justify-between"
                        v-if="childItem?.status !== 'PENDING'"
                      >
                        <div v-if="childItem?.status !== 'PENDING'">{{ childItem?.timestamp || "--" }}</div>
                        <el-icon :color="childItem?.color || '#909399'" size="18px"
                          ><CircleCheckFilled v-if="childItem?.icon === 'el-icon-success'" />
                          <CircleCloseFilled v-if="childItem?.icon === 'el-icon-error'"
                        /></el-icon>
                        <!-- <i
                          :class="[childItem?.icon || '', 'icon']"
                          :style="{ color: childItem?.color || '#909399', fontSize: '18px' }"
                        /> -->
                      </div>
                    </div>
                    <div v-if="index > 0 && childItem?.status !== 'REVOKE' && childItem?.reason" style="color: #000">
                      审批意见：{{ childItem.reason }}
                    </div>
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
      <div class="info-item" v-if="drawerType === 'pending'">
        <div class="title"><span class="c-red" v-if="data.isRemark">*</span>审批意见</div>
        <div class="m-l-20px">
          <el-form ref="approveFormRef" :model="data" label-position="top">
            <el-form-item
              prop="remark"
              :rules="[{ required: data.isRemark, message: '请填写审批意见', trigger: 'blur' }]"
            >
              <el-input type="textarea" v-model="data.remark" maxlength="200" show-word-limit />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="dialog-footer m-t-20px">
        <el-button @click="approveDrawerShow = false">
          {{ drawerType === "pending" ? "取消" : "关闭" }}
        </el-button>
        <el-button v-if="drawerType === 'pending'" type="primary" @click="handleApprove('agree')"> 同意 </el-button>
        <el-button v-if="drawerType === 'pending'" type="primary" @click="handleApprove('reject')"> 拒绝 </el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted, toRefs } from "vue"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { ElMessage } from "element-plus"
import { divide } from "@/utils/index"
import { MoreFilled, CloseBold, Check, CircleCheckFilled, CircleCloseFilled } from "@element-plus/icons-vue"

import { apiBackgroundFundSupervisionSupervisionFinanceApproveApproveAccountsPost } from "@/api/financial/index"
import {
  SEARCH_FORM_SETTING_PENDING,
  TABLE_SETTING_PENDING,
  TABLE_SETTING_AGREE,
  TABLE_SETTING_REJECT,
  TABLE_SETTING_REVOLKE
} from "./constants"
import {
  apiApprovePendingList,
  apiApproveAgreeList,
  apiApproveRejectList,
  apiApproveRevokeList,
  apiApproveAgreeApprove,
  apiApproveRejectApprove,
  apiApproveGetApproveRule
} from "@/api/financial/index"

// const props = defineProps({
//   selectTree: {
//     type: Object,
//     default: () => {}
//   }
// })

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(20, 40, psTableRef, 80)
// table数据
const data = reactive({
  searchFormSetting: cloneDeep(SEARCH_FORM_SETTING_PENDING),
  tableType: "pending",
  tableSetting: cloneDeep(TABLE_SETTING_PENDING),
  selectInfo: ref<any>({}),
  approveAccountInfo: ref<Array<any>>([]),
  approveMethod: "",
  drawerType: "",
  remark: "",
  isRemark: false
})

let { selectInfo, approveAccountInfo, drawerType, approveMethod, isRemark } = toRefs(data)

// const recordList = [
//   {
//     name: "签到 09:52",
//     isError: true,
//     isBeLate: true,
//     isLeaveEarly: true
//   },
//   {
//     name: "签退 18:00",
//     isLoseEfficacy: true
//   },
//   {
//     name: "签退 18:52"
//   }
// ]

const tableData = ref([])
const loading = ref(false)
// const searchFormSetting = cloneDeep(SEARCH_FORM_SETTING_PENDING)
const tableLoading = ref(false)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

// 抽屉
const approveDrawerShow = ref(false)

// 添加表单 ref
const approveFormRef = ref()

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "" && data[key].value !== "all") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}
// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getApprovePendingList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getApprovePendingList()
}

// 列表
const getApprovePendingList = async () => {
  loading.value = true
  let api: any
  if (data.tableType === "pending") {
    api = apiApprovePendingList
  } else if (data.tableType === "agree") {
    api = apiApproveAgreeList
  } else if (data.tableType === "reject") {
    api = apiApproveRejectList
  } else if (data.tableType === "revoke") {
    api = apiApproveRevokeList
  }
  const [err, res]: any[] = await to(
    api({
      ...formatQueryParams(data.searchFormSetting),
      date_type: "apply"
    })
  )
  loading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results.map((item: any) => {
      if (data.tableType === "pending") {
        item.approve_status = "pending"
      } else if (data.tableType === "agree") {
        item.approve_status = "agree"
      } else if (data.tableType === "reject") {
        item.approve_status = "reject"
      } else if (data.tableType === "revoke") {
        item.approve_status = "revoke"
      }
      return item
    })
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg || "获取失败")
  }
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  // getDataList()
  getApprovePendingList()
}

const changeTableType = (e: any) => {
  data.tableType = e
  if (e === "pending") {
    data.tableSetting = cloneDeep(TABLE_SETTING_PENDING)
    data.searchFormSetting = cloneDeep(SEARCH_FORM_SETTING_PENDING)
  } else if (e === "agree") {
    data.tableSetting = cloneDeep(TABLE_SETTING_AGREE)
    data.searchFormSetting = cloneDeep(SEARCH_FORM_SETTING_PENDING)
  } else if (e === "reject") {
    data.tableSetting = cloneDeep(TABLE_SETTING_REJECT)
    data.searchFormSetting = cloneDeep(SEARCH_FORM_SETTING_PENDING)
  } else if (e === "revoke") {
    data.tableSetting = cloneDeep(TABLE_SETTING_REVOLKE)
    data.searchFormSetting = cloneDeep(SEARCH_FORM_SETTING_PENDING)
  }
  getApprovePendingList()
}

const gotoApprove = async (data: any, type: any) => {
  if (type === "pending") {
    await getApproveRule()
  }
  // 这里看不懂，有需要找何甘荣
  approveMethod.value = data.approve_method || ""
  let approveInfo = [
    {
      icon: Check,
      color: "#14ce84",
      status_alias: "提交申请",
      status: "pending",
      data: [
        {
          icon: Check,
          color: "#14ce84",
          status_alias: "提交申请",
          status: "pending",
          account_id: "",
          timestamp: data.apply_time || "",
          operator: data.operator || ""
        }
      ]
    }
  ]
  selectInfo.value = data || {}
  let newStatus: any = []
  let result: any = await getApprovalAccounts(data.appropriation_id)
  console.log("getApprovalAccounts", result, approveAccountInfo.value)
  if (!result || result.length === 0) {
    data.remark = ""
    drawerType.value = type
    approveDrawerShow.value = true
    return
  }
  approveAccountInfo.value = approveInfo
  // 先判断一下是不是或签，或签的话全部账号显示在一个流程里就可以了
  switch (approveMethod.value) {
    case "one_by_one_approve": {
      // 依次审批还是拿回approve_account_info组成数组显示吧
      // 先循环res.data
      result.forEach((item: any) => {
        let obj = {
          icon: Check,
          color: "#ff9b45",
          status_alias: "待审批",
          status: "pending",
          data: [] as any[]
        }
        let statusList: any[] = []
        if (item.approve_account_info && item.approve_account_info.length) {
          // 将这个审批账号里的全部账号拉出来组成目标数据丢尽obj.data
          item.approve_account_info.forEach((itemIn: any) => {
            let childStatus = itemIn.approve_status === "PENDING" || itemIn.approve_status === "AGREE"
            console.log("childStatus", childStatus)
            let child = {
              icon: childStatus ? "el-icon-success" : "el-icon-error",
              color: switchColor(itemIn.approve_status),
              status_alias: itemIn.approve_status_alias,
              status: itemIn.approve_status,
              account_id: itemIn.account_id,
              timestamp: itemIn.approve_time,
              operator: `${itemIn.account_name}`,
              reason: itemIn.approve_reason
            }
            statusList.push(itemIn.approve_status)
            obj.data.push(child)
          })
          let agreeFlag = statusList.some((item) => item === "AGREE")
          let rejectFlag = statusList.some((item) => item === "REJECT")
          // 把上传的obj根据里面的内容重新赋值一下
          obj.icon = agreeFlag ? Check : rejectFlag ? CloseBold : MoreFilled
          obj.color = agreeFlag ? "#14ce84" : rejectFlag ? "#fd594e" : "#ff9b45"
          obj.status_alias = agreeFlag ? "审批通过" : rejectFlag ? "拒绝审批" : "待审批"
          obj.status = agreeFlag ? "AGREE" : rejectFlag ? "REJECT" : "PENDING"
        }
        newStatus.push(obj)
      })
      break
    }
    case "and_approve": {
      // 如果是会签，将每个审批账号做成一个数组塞到data里面
      let obj = {
        icon:
          result[0].approve_status === "agree"
            ? Check
            : result[0].approve_status === "pending"
              ? MoreFilled
              : CloseBold,
        color: switchColor(result[0].approve_status),
        status_alias: result[0].approve_status_alias,
        status: result[0].approve_status,
        data: [] as any[]
      }
      if (result[0].approve_account_info && result[0].approve_account_info.length) {
        result[0].approve_account_info.forEach((item: any) => {
          if (item.length) {
            let arr: any[] = []
            item.forEach((itemIn: any) => {
              let childStatus = itemIn.approve_status === "PENDING" || itemIn.approve_status === "AGREE"
              let child = {
                icon: childStatus ? "el-icon-success" : "el-icon-error",
                color: switchColor(itemIn.approve_status),
                status_alias: itemIn.approve_status_alias,
                status: itemIn.approve_status,
                account_id: itemIn.account_id,
                timestamp: itemIn.approve_time,
                operator: `${itemIn.account_name}`,
                reason: itemIn.approve_reason
              }
              arr.push(child)
            })
            obj.data.push(arr)
          }
        })
        newStatus.push(obj)
      }
      break
    }
    case "or_approve": {
      // 如果是或签，将所有账号放在一个流程内
      let obj = {
        icon:
          result[0].approve_status === "agree"
            ? Check
            : result[0].approve_status === "pending"
              ? MoreFilled
              : CloseBold,
        color: switchColor(result[0].approve_status),
        status_alias: result[0].approve_status_alias,
        status: result[0].approve_status,
        data: [] as any[]
      }
      if (result[0].approve_account_info && result[0].approve_account_info.length) {
        result[0].approve_account_info.forEach((item: any) => {
          if (item.length) {
            item.forEach((itemIn: any) => {
              let childStatus = itemIn.approve_status === "PENDING" || itemIn.approve_status === "AGREE"
              let child = {
                icon: childStatus ? "el-icon-success" : "el-icon-error",
                color: switchColor(itemIn.approve_status),
                status_alias: itemIn.approve_status_alias,
                status: itemIn.approve_status,
                account_id: itemIn.account_id,
                timestamp: itemIn.approve_time,
                operator: `${itemIn.account_name}`,
                reason: itemIn.approve_reason
              }
              obj.data.push(child)
            })
          }
        })
        newStatus.push(obj)
      }
      break
    }
  }
  addRejectStatus(result, newStatus)
  // 如果这时没撤回，再往里塞待拨款状态节点
  if (selectInfo.value.appropriation_status !== "revoked" && selectInfo.value.appropriation_status !== "pending") {
    setAppropriationProcess(newStatus)
  }
  if (approveMethod.value !== "and_approve") {
    approveAccountInfo.value.push(...newStatus)
  } else {
    let obj = cloneDeep(approveAccountInfo.value[0])
    obj.data = [[obj.data[0]]]
    approveAccountInfo.value = [obj, ...newStatus]
  }
  console.log(approveAccountInfo.value, "approveAccountInfo.value")
  data.remark = ""
  approveDrawerShow.value = true
  drawerType.value = type
}
const addRejectStatus = (data: any, statusArr: any) => {
  // 处理状态
  if (data[0].approve_status === "revoke") {
    let obj = {
      icon: "el-icon-error",
      color: "#909399",
      status_alias: "撤销申请",
      status: "REVOKE",
      timestamp: data[0].apply_time,
      operator: `${data[0].operator}`
    }
    let status: any = {
      icon: "CloseBold",
      color: "#909399",
      status_alias: "撤销申请",
      status: "REVOKE",
      data: []
    }
    // 用历史操作处理旧数据
    let record: any = []
    if (data[0].approve_record && data[0].approve_record.record && data[0].approve_record.record.length) {
      record = cloneDeep(data[0].approve_record.record)
    }
    // 如果是撤销的，直接塞
    switch (data[0].approve_method) {
      case "one_by_one_approve": {
        // 先把最后一个干掉
        statusArr.pop()
        statusArr.forEach((item: any) => {
          let approvalStatusArr: any = []
          item.data.forEach((itemIn: any) => {
            let obj = record.filter((recordItem: any) => recordItem.account_id === itemIn.account_id)
            if (obj.length) {
              // 如果有就改
              let childStatus = obj[0].status === "PENDING" || obj[0].status === "AGREE"
              itemIn.icon = childStatus ? "el-icon-success" : "el-icon-error"
              itemIn.color = switchColor(obj[0].status)
              itemIn.status_alias = obj[0].content
              itemIn.status = obj[0].status
              itemIn.timestamp = obj[0].time
            } else {
              // 没有就置空
              itemIn.icon = ""
              itemIn.timestamp = ""
            }
            approvalStatusArr.push(itemIn.status)
          })
          // 根据statusArr里的状态去判断
          let flag = approvalStatusArr.some((item: any) => item === "REJECT")
          // 审批账号里面的改好了，轮到该审批账号本身的状态了
          item.icon = flag ? "CloseBold" : "Check"
          item.color = flag ? switchColor("") : switchColor("AGREE")
          item.status_alias = flag ? "" : "审批通过"
          item.status = flag ? "" : "AGREE"
        })
        // 依次审批的话因为最后一个审批账号没审就撤销了，把最后那个干掉
        status.data = [{ ...obj }]
        statusArr.push(status)
        break
      }
      case "and_approve": {
        statusArr[0].data.forEach((item: any) => {
          item.forEach((itemIn: any) => {
            let obj = record.filter((recordItem: any) => recordItem.account_id === itemIn.account_id)
            if (obj.length) {
              // 如果有就改
              itemIn.icon = obj[0].status === "AGREE" ? "el-icon-success" : "el-icon-error"
              itemIn.color = switchColor(obj[0].status)
              itemIn.status_alias = obj[0].content
              itemIn.status = obj[0].status
              itemIn.timestamp = obj[0].time
            } else {
              // 没有就置空
              itemIn.icon = ""
              itemIn.timestamp = ""
            }
          })
        })
        // 审批账号里面的改好了，轮到该审批账号本身的状态了
        statusArr[0].icon = "MoreFilled"
        statusArr[0].color = switchColor("PENDING")
        statusArr[0].status_alias = "待审批"
        statusArr[0].status = "PENDING"
        status.data = [[{ ...obj }]]
        statusArr.push(status)
        break
      }
      case "or_approve": {
        // 先把最后一个干掉
        statusArr.pop()
        status.data = [{ ...obj }]
        statusArr.push(status)
        break
      }
    }
  }
}

// 设置待拨款流程
const setAppropriationProcess = (arr: any) => {
  console.log("看看待拨款的情况", arr, selectInfo.value)
  if (selectInfo.value.zj_approve_status === "agree") {
    let obj = {
      icon: "el-icon-check",
      color: "#14ce84",
      status_alias: "待拨款",
      status: "agree",
      data:
        selectInfo.value.approve_method === "and_approve"
          ? [
              [
                {
                  icon: "el-icon-success",
                  color: "#14ce84",
                  status_alias: "",
                  status: "agree",
                  account_id: "",
                  timestamp: "",
                  operator: ``
                }
              ]
            ]
          : [
              {
                icon: "el-icon-success",
                color: "#14ce84",
                status_alias: "",
                status: "agree",
                account_id: "",
                timestamp: "",
                operator: ``
              }
            ]
    }
    arr.push(obj)
    if (selectInfo.value.zj_appropriation_status === "appropriated") {
      // 如果是已拨款，再插入已拨款的状态
      let obj = {
        icon: "el-icon-check",
        color: "#14ce84",
        status_alias: "已拨款",
        status: "agree",
        data:
          selectInfo.value.approve_method === "and_approve"
            ? [
                [
                  {
                    icon: "el-icon-success",
                    color: "#14ce84",
                    status_alias: "",
                    status: "agree",
                    account_id: "",
                    timestamp: selectInfo.value.appropriation_time
                  }
                ]
              ]
            : [
                {
                  icon: "el-icon-success",
                  color: "#14ce84",
                  status_alias: "",
                  status: "agree",
                  account_id: "",
                  timestamp: selectInfo.value.appropriation_time
                }
              ]
      }
      arr.push(obj)
    }
  }
}

const switchColor = (status: any) => {
  let color = ""
  switch (status) {
    case "PENDING":
      color = "#ff9b45"
      break
    case "AGREE":
      color = "#14ce84"
      break
    case "REJECT":
      color = "#fd594e"
      break
    case "pending":
      color = "#ff9b45"
      break
    case "agree":
      color = "#14ce84"
      break
    case "reject":
      color = "#fd594e"
      break
    default:
      color = "#909399"
  }
  return color
}

// 修改审批处理函数
const handleApprove = async (type: "agree" | "reject") => {
  if (!approveFormRef.value) return

  try {
    await approveFormRef.value.validate()
    // 表单验证通过，调用原有的审批函数
    agreeOrRejectApprove(type)
  } catch (error) {
    // 表单验证失败
    console.error("表单验证失败:", error)
    return false
  }
}

// 修改 getApproveRule 函数，在设置 isRemark 后重置表单验证
const getApproveRule = (): Promise<boolean> => {
  return new Promise((resolve) => {
    apiApproveGetApproveRule({})
      .then((res: any) => {
        if (res && res.code === 0) {
          isRemark.value = res.data || false
          // 重置表单验证
          if (approveFormRef.value) {
            approveFormRef.value.clearValidate()
          }
          resolve(res.data)
        }
      })
      .catch((err) => {
        console.log(err)
        resolve(false)
      })
  })
}

// 移除原有的验证逻辑
const agreeOrRejectApprove = async (e: any) => {
  loading.value = true
  let api: any
  if (e === "agree") {
    api = apiApproveAgreeApprove
  } else if (e === "reject") {
    api = apiApproveRejectApprove
  }
  let params: any = {
    id: data.selectInfo.id
  }
  if (data.remark) params.reason = data.remark
  const [err, res]: any[] = await to(api(params))
  loading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    ElMessage.success("成功")
    approveDrawerShow.value = false
    getApprovePendingList()
  } else {
    ElMessage.error(res.msg || "失败")
  }
}

// 格式化价格
const formatPrice = (val: any) => {
  let price = divide(val)
  return price ? price : "0.00"
}

const formateText = (text: any) => {
  let str: any
  if (text.length > 15) {
    str = text.substring(0, 15) + "…"
  } else {
    str = text
  }
  return str
}
// 获取审批流程状态
const getApprovalAccounts = async (id: number) => {
  return new Promise((resolve) => {
    apiBackgroundFundSupervisionSupervisionFinanceApproveApproveAccountsPost({ id: id })
      .then((res: any) => {
        if (res && res.code === 0) {
          console.log(res.data)
          resolve(res.data)
        } else {
          resolve([])
        }
      })
      .catch((err) => {
        console.log(err)
        resolve([])
      })
  })
}
// 关闭弹窗清除数据
const closeDrawer = () => {
  console.log("关闭了")
  data.remark = ""
}

onMounted(() => {
  getApprovePendingList()
})
</script>

<style lang="scss" scoped>
.financial-approve {
  .title {
    font-size: 18px;
    margin: 15px 0 10px;
  }
  .apply-info {
    font-size: 16px;
    .apply-info-item {
      width: 80%;
      display: flex;
      font-size: 14px;
      border-right: 1px solid #e6e8eb;
      border-bottom: 1px solid #e6e8eb;

      .user-title {
        width: 50%;
        border-right: 1px solid #e6e8eb;
        border-left: 1px solid #e6e8eb;
      }

      div {
        padding: 15px;
      }
    }
    .apply-info-item:first-child {
      border-top: 1px solid #e6e8eb;
    }
  }
  .bg-grey {
    padding: 10px 20px;
    border: 1px solid #e7e9ef;
    border-radius: 4px;
  }
  .flex flex-items-center flex-justify-between {
    display: flex;
    justify-content: space-between;
  }
  .step-list {
    img {
      width: 30px;
      height: 30px;
    }
    .step-item {
      .w-400px {
        width: 400px;
      }
      position: relative;
      .step-item-title::before {
        content: "";
        display: block;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        position: absolute;
        left: -30px;
        top: 8px;
        background-color: #c2c5c8;
      }
      .step-item-leave-line {
        position: absolute;
        top: 30px;
        bottom: -30px;
        left: -25px;
        width: 1px;
        background-color: #e4e4e4;
      }
      .step-item-line {
        position: absolute;
        top: 40px;
        bottom: -38px;
        left: 14px;
        width: 1px;
        background-color: #e4e4e4;
      }
    }
  }
}
</style>
