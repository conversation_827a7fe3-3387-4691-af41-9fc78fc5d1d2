import { lang } from "@/setting/systemSetting"

<template>
  <div class="agreement-box">
    <!-- <div class="title-box">
      <div class="title">{{ current.agreement_type_alias }}</div>
      <div class="update-time">
        [更新时间：{{ parseTime(current.update_time ? current.update_time : current.create_time, "{y}年{m}月{d}日") }}]
      </div>
    </div> -->
    <div class="content" v-dompurify-html="content" />
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from "vue"
import { unescapeHTML, parseTime } from "@/utils"
import to from "await-to-js"
import { ElMessage } from "element-plus"
import { apiBackgroundAdminAgreementAgreementDetailByTypePost } from "@/api/login"
import { useRoute } from "vue-router"
const route = useRoute()
const type = ref()
const current = ref({
  agreement_type_alias: "",
  update_time: "",
  create_time: ""
})
const content = ref("")

const getAgreementDetails = async (type: string) => {
  const [err, res]: any[] = await to(
    apiBackgroundAdminAgreementAgreementDetailByTypePost({
      agreement_type: type
    })
  )
  if (err) {
    return
  }
  if (res && res.code === 0) {
    current.value = res.data.results
    if (res.data.results.content) {
      content.value = unescapeHTML(res.data.results.content)
    }
  } else {
    ElMessage.error(res.msg)
  }
}
onMounted(() => {
  console.log("onMounted", route.query)
  type.value = route.query.type
  getAgreementDetails(type.value)
})
</script>

<style lang="scss" scope>
.agreement-box {
  background-color: white;

  .title {
    padding: 40px 0 10px;
    font-size: 20px;
    letter-spacing: 5px;
    font-weight: 600;
  }

  .content {
    padding: 20px 40px;
    user-select: none;
  }
  .title-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}
</style>
