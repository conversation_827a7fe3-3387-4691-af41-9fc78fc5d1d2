<template>
  <div>
    <div v-for="(item, index) in list" :key="index" class="">
      <div :style="{ height: getHeight(index) }">{{ item }}</div>
      <div v-if="index < list.length - 1" class="hor-line-black m-b-10 m-t-10" />
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  list: {
    type: Array<any>,
    default: () => []
  },
  dataItem: {
    type: Object,
    default: () => {}
  }
})
// 获取高度
const getHeight = (keyIndex: number) => {
  let foodList = props.dataItem.food_record_list || []
  if (keyIndex < foodList.length) {
    let tagItem = foodList[keyIndex]
    let remark = tagItem.remark
    let length = remark ? Math.ceil(remark.length / 10) : 1
    if (length < 1) {
      length = 1
    }
    console.log("getHeight", length, remark, Math.ceil(remark.length / 10))
    return `${length * 15}px`
  }
  return "15px"
}
</script>

<style scoped>
.m-b-10 {
  margin-bottom: 10px;
}

.m-t-10 {
  margin-top: 10px;
}

.hor-line-black {
  border-bottom: 1px dashed black;
}
</style>
