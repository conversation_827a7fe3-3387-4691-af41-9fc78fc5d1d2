import { onBeforeMount, onMounted, onBeforeUnmount, Ref } from "vue"
import { divide } from "@/utils/index"
import { LocationQueryValue } from "vue-router"
import { getNameByType, getMealTypeName, getPersonNameByList } from "../canteen-management/utils"

// 最好把格式化这种的单一逻辑抽出来
const useFormatDataHook = (params?: any) => {
  // 格式化数据
  const formatDataByType = (printType: Ref<LocationQueryValue | LocationQueryValue[]>, data: any, key: string): any => {
    if (!printType.value) {
      return ""
    }
    switch (printType.value) {
      // 留样记录格式化
      case "SupervisionCanteenSampleRecord":
        return formatCanteenSampleRecord(data, key)
      // 晨检汇总
      case "SupervisionCanteenSafetyCheckCollect":
      case "SupervisionCanteenSafetyCheckDetail":
        return formatCanteenCheckCollect(data, key)
      // 格式化财务报表
      case "CanteenMonthReport":
      case "IncomeExpenseStatistics":
      case "IncomeExpenseSummary":
      case "CapitalflowDetails":
        return formatCanteenMonthReport(data, key)
      // 格式化有效生物防制
      case "PestControlExport":
        return formatPestControl(data, key)
      //  格式化民主监督
      case "DemocraticSupervisionCanteenExport":
        return formatDemocraticSupervisionCanteen(data, key)
      // 格式化供应商汇总表
      case "SupplierTotalExport":
        return formatSupplierTotal(data, key)
      // 格式化供应关系
      case "SupplyRelationshipExport":
        return formatSupplyRelationship(data, key)
      // 格式化库存台账
      case "InventoryBalanceExport":
        return formatnventoryBalance(data, key, params)
      // 格式化资金收入日报表 格式化资金收入月报表
      case "DailyReportIncomeExport":
      case "MonthReportIncomeExport":
        return formatDailyReportIncomeExport(data, key)
      case "ApplicationOrderExport":
        return formatApplicationOrder(data, key)
      // 陪餐管理
      case "MealManagementExport":
        return formatMealManagement(data, key, params)
      // 问卷调查
      case "AuditRecords":
        return formatAuditRecords(data, key)
      default:
        return data[key]
    }
  }

  /** 在组件挂载前添加窗口大小变化事件监听器 */
  onBeforeMount(() => {})

  /** 在组件挂载后根据窗口大小判断设备类型并调整布局 */
  onMounted(() => {})

  /** 在组件卸载前移除窗口大小变化事件监听器 */
  onBeforeUnmount(() => {})

  return {
    formatDataByType
  }
}

// 留样记录格式化数据
const formatCanteenSampleRecord = (data: any, key: string) => {
  switch (key) {
    case "sample_entry_user":
      return getNameByList(data.sample_entry_user)
    case "sample_exit_user":
      return getNameByList(data.sample_exit_user)
    case "entry_cupboard":
      return data.entry_cupboard ? "是" : "否"
    case "temperature":
      return data.temperature ? data.temperature + "°C" : ""
    case "food_weight":
      return data.food_weight ? data.food_weight + "g" : ""
    case "not_entry_reason":
      return data.entry_cupboard ? "" : data.not_entry_reason || ""
    default:
      return data[key]
  }
}
// 格式化晨检汇总数据

const formatCanteenCheckCollect = (data: any, key: string) => {
  switch (key) {
    case "check_status":
      return data.check_status ? "已晨检" : "未晨检"
    case "temperature":
      return data.temperature ? data.temperature + "°C" : ""
    default:
      return data[key]
  }
}
// 格式化财务报表
const formatCanteenMonthReport = (data: any, key: string) => {
  switch (key) {
    case "revenue":
    case "expend":
    case "last_remainder":
    case "accept":
    case "remainder":
    case "use":
    case "weg_fee":
    case "cost":
    case "account_fee":
    case "consume_price_total":
    case "refund_price_total":
    case "profit":
    case "price":
    case "in_price":
    case "non_in_price":
    case "out_price":
    case "non_out_price":
      return data[key] ? "¥" + divide(data[key]) : "¥0"
    default:
      return data[key]
  }
}
// 格式化有效生物防制
const formatPestControl = (data: any, key: string) => {
  switch (key) {
    case "prevent_area":
      return data[key] ? divide(data[key]) + "㎡" : "--"
    case "prevent_duration":
      return data[key] ? divide(data[key]) + "h" : "--"
    default:
      return data[key]
  }
}

const formatDemocraticSupervisionCanteen = (data: any, key: string) => {
  switch (key) {
    case "operator_name":
      return getOperatorName(data)
    default:
      return data[key]
  }
}
// 格式化供应商汇总表
const formatSupplierTotal = (data: any, key: string) => {
  switch (key) {
    case "total_fee":
      return data[key] ? "¥" + divide(data[key]) : "--"
    default:
      return data[key]
  }
}
// 格式化供应商关系表
const formatSupplyRelationship = (data: any, key: string) => {
  switch (key) {
    case "total_settled_fee":
    case "total_unliquidated_fee":
      return data[key] ? "¥" + divide(data[key]) : "--"
    default:
      return data[key]
  }
}
// 获取账号名称与账号
const getOperatorName = (row: any) => {
  let operatorMemberName = row.operator_member_name || ""
  let operatorName = row.operator_name || ""
  if (!operatorMemberName && !operatorName) {
    return ""
  }
  return (operatorMemberName ? operatorMemberName : "--") + "(" + (operatorName ? operatorName : "--") + ")"
}
// 获取列表名字
const getNameByList = (list: any) => {
  console.log("getNameByList", list)
  if (!list || list.length === 0) {
    return ""
  }
  if (typeof list === "object") {
    let newList = []
    for (let key in list) {
      newList.push(list[key].name)
    }
    return newList.join("、")
  } else if (Array.isArray(list) && list.length > 0) {
    let newList = list.map((item: any) => item.name)
    return newList.join("、")
  }
}
// 格式化数据
const formatnventoryBalance = (data: any, key: string, params: any) => {
  console.log("formatnventoryBalance", key, params)
  if (key === "date" && params) {
    let paramsJson = JSON.parse(params)
    let startDate = paramsJson.start_date ? paramsJson.start_date : ""
    let endDate = paramsJson.end_date ? paramsJson.end_date : ""
    return `${startDate}至${endDate}`
  } else {
    return data[key]
  }
}
// 格式化资金收入日报表
const formatDailyReportIncomeExport = (data: any, key: string) => {
  switch (key) {
    case "date_str":
    case "org_name":
      return data[key]
    default:
      return data[key] ? "¥" + divide(data[key]) : "¥0"
  }
}
// 格式化申请单
const formatApplicationOrder = (data: any, key: string) => {
  switch (key) {
    case "settle_fee":
      return data[key] ? "¥" + divide(data[key]) : "--"
    default:
      return data[key]
  }
}
// 格式化数据
const formatMealManagement = (data: any, key: string, params: any) => {
  console.log("formatMealManagement", key, params)
  const foodRecordList: any[] = data.food_record_list || []
  const setMealRecordList: any[] = data.set_meal_record_list || []
  const totalFoodRecordList = foodRecordList.concat(setMealRecordList)
  switch (key) {
    case "room_clean_type":
      return (
        getNameByType("room_clean_type", data.room_clean_type) +
        "、" +
        getNameByType("room_attitude_type", data.room_attitude_type)
      )
    case "area_clean_type":
      return (
        getNameByType("area_clean_type", data.area_clean_type) +
        "、" +
        getNameByType("area_waste_type", data.area_waste_type)
      )
    case "oa_clean_type":
      return (
        getNameByType("oa_clean_type", data.oa_clean_type) +
        "、" +
        getNameByType("oa_operate_type", data.oa_operate_type)
      )
    case "tda_clean_type":
      return (
        getNameByType("tda_clean_type", data.tda_clean_type) +
        "、" +
        getNameByType("tda_disinfection_type", data.tda_disinfection_type)
      )
    case "operation_type":
      return getNameByType("operation_type", data.operation_type)
    case "person":
      return getPersonNameByList(data.person_record_list)
    case "meal_type":
      return getMealTypeName(data.meal_type)
    case "food_name":
      return getNameByFoodList("name", totalFoodRecordList)
    case "gg_excellent_type_alias":
      return getNameByFoodList("gg_excellent_type_alias", totalFoodRecordList)
    case "zl_excellent_type_alias":
      return getNameByFoodList("zl_excellent_type_alias", totalFoodRecordList)
    case "quantity_type_alias":
      return getNameByFoodList("quantity_type_alias", totalFoodRecordList)
    case "price_type_alias":
      return getNameByFoodList("price_type_alias", totalFoodRecordList)
    case "food_remark":
      return getNameByFoodList("remark", totalFoodRecordList)
    default:
      return data[key]
  }
}
const getNameByFoodList = (type: string, list: any) => {
  if (!list || list.length === 0) {
    return ""
  }
  switch (type) {
    case "name":
      // 返回html 格式 菜品名称，每个菜品占据一行
      return list.map((item: any) => item.name + "  ¥" + divide(item.price || 0))
    case "gg_excellent_type_alias":
      return list.map((item: any) => item.gg_excellent_type_alias)
    default:
    case "zl_excellent_type_alias":
      return list.map((item: any) => item.zl_excellent_type_alias)
    case "quantity_type_alias":
      return list.map((item: any) => item.quantity_type_alias)
    case "price_type_alias":
      return list.map((item: any) => item.price_type_alias)
    case "remark":
      return list.map((item: any) => item.remark)
  }
}

const formatAuditRecords = (data: any, key: string) => {
  if (key === "enable") {
    return data[key] ? "已发布" : "未发布"
  } else {
    return data[key]
  }
}

export default useFormatDataHook
