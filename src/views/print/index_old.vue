<template>
  <div id="print" class="print-container">
    <div class="print-table">
      <div class="print-title">{{ route.query.print_title }}</div>
      <ps-table :tableData="tableData" ref="psTableRef" v-loading="tableLoading">
        <ps-column :table-headers="tableSetting" />
      </ps-table>
    </div>
    <div class="print-btn noprint">
      <el-button type="primary" size="large" :icon="Printer" circle @click="toPrint" class="right-btn" />
      <!-- <el-button type="primary" size="large" :icon="Printer" circle v-print="printObj" class="right-btn" /> -->
      <!-- <el-button type="primary" @click="toPrint">打印</el-button> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from "vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { useRouter, useRoute } from "vue-router"
import { Printer } from "@element-plus/icons-vue"
import { getLocalStorage } from "@/utils/storage"
import { cloneDeep } from "lodash"
const router = useRouter()
const route = useRoute()

const tableData = ref([])
const tableSetting = JSON.parse(route.query.table_setting as string)
const tableLoading = ref(false)
const printObj = {
  id: "print", // 这里是要打印元素的ID
  popTitle: "&nbsp", // 打印的标题
  extraCss: "", // 打印可引入外部的一个 css 文件
  extraHead: "" // 打印头部文字
}

const toPrint = () => {
  window.print()
}
// 获取本地数据
const getLocalData = () => {
  const list = getLocalStorage("print_data") || []
  console.log("getLocalData", list)
  if (list && list.length > 0) {
    tableData.value = cloneDeep(list)
  }
}
onMounted(() => {
  getLocalData()
})
</script>
<style lang="scss">
.print-container {
  position: relative;
}
.print-table {
  margin: 20px auto;
  .print-title {
    font-size: 26px;
    text-align: center;
    font-weight: bold;
    margin-bottom: 20px;
  }
}
.print-btn {
  width: 100%;
  position: sticky;
  z-index: 99;
  bottom: 20px;
  .right-btn {
    position: absolute;
    right: 20px;
    bottom: 0;
  }
}
@media print {
  html {
    font-size: 1920px !important;
    margin: 0px;
    height: auto;
  }
  body {
    font-size: 1920px !important;
    margin: 5mm 10mm;
    height: auto;
  }
  @page {
    size: A4 landscape;
    margin: 0;
    padding: 0;
  }
  .noprint {
    display: none;
  }
  #print {
    table {
      table-layout: auto !important;
    }
    .el-table {
      width: 100% !important;
      border-collapse: collapse;
    }
    tr {
      page-break-inside: avoid !important;
    }
    thead {
      display: table-header-group !important;
      page-break-before: auto !important;
    }
    .el-table thead th {
      position: -webkit-sticky; /* Safari */
      position: sticky;
      top: 0;
      background-color: #f0f0f0;
    }

    .el-table th.gutter {
      display: table-cell !important;
    }

    .el-table td {
      page-break-inside: avoid !important;
      break-inside: avoid !important;
    }
    // .el-table--group,
    // .el-table--border {
    //   border: none !important;
    // }
    .el-table__header-wrapper .el-table__header {
      width: 100% !important;
    }
    .el-table__row {
      page-break-inside: avoid !important;
      page-break-after: auto !important;
    }
    .el-table__header-wrapper {
      display: table-header-group !important;
    }
    .el-table__body-wrapper {
      display: table-row-group !important;
    }

    // .el-table .cell.el-tooltip {
    //   text-overflow: clip;
    //   white-space: normal;
    //   min-width: none;
    //   width: auto !important;
    //   border: none !important;
    // }

    .el-table__header-wrapper,
    .el-table__body-wrapper,
    .el-table__footer-wrapper {
      page-break-inside: avoid !important;
    }
    // .el-table__body td {
    //   page-break-inside: avoid !important;
    //   vertical-align: top !important;
    // }
    // .el-table td,
    // .el-table th,
    // .el-table tr {
    //   // border: 1px solid #000 !important;
    // }
    // .el-table th[colspan],
    // .el-table td[colspan] {
    //   width: auto;
    // }

    // .el-table th[rowspan],
    // .el-table td[rowspan] {
    //   height: auto;
    // }
    // .el-table__body {
    //   border-collapse: collapse;
    // }
    // .el-table--group::after,
    // .el-table--border::after,
    .el-table::before {
      display: none;
    }
  }
}
</style>
