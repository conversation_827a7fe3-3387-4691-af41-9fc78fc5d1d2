<script lang="ts" setup>
import { reactive, ref, onMounted, watchEffect, onUnmounted } from "vue"
import { useRouter } from "vue-router"
import { useUserStore } from "@/store/modules/user"
import { useAccountStore } from "@/store/modules/account"
import { type FormInstance, type FormRules, ElMessage } from "element-plus"
import { getLoginCodeApi, apiBackgroundGetSmsVerifyCodePost, apiBackgroundVerificationCodePost } from "@/api/login"
import { type LoginRequestData } from "@/api/login/types/login"
import { useFocus } from "./hooks/useFocus"
import { healthyRoutes } from "@/router/modules"
import VerifyCode from "@/components/VerifyCode/index.vue"
import VerifyMessageCode from "@/components/VerifyMessageCode/index.vue"
import { Base64 } from "js-base64"
import to from "await-to-js"
import { cloneDeep } from "lodash"
import { getImgByName } from "@/hooks/useTheme"
import DoubleFactor from "@/components/DoubleFactor/index.vue"
import { usePermissionStoreHook } from "@/store/modules/permission"
const router = useRouter()
const userStore = useUserStore()
const useAccount = useAccountStore()
const permissionStore = usePermissionStoreHook()
const { handleBlur, handleFocus } = useFocus()

const imgBg = getImgByName("login_bg")
const imgMain = getImgByName("login_main")
console.log("imgBg", imgBg, "imgMain", imgMain)

// 登录类型
const loginType = ref("account")

// 验证码
const sendAuthCode = ref(true)
const sendCodeDisabled = ref(true)
const isShowVerify = ref(false)
const answerList = ref<any[]>([])
const answer = ref("")
const verifyCode = ref()
const isShowLoading = ref(false)

/** 登录表单元素的引用 */
const loginFormRef = ref<FormInstance | null>(null)

/** 登录按钮 Loading */
const loading = ref(false)
/** 验证码图片 URL */
const codeUrl = ref("")
/** 登录表单数据 */
const loginFormData: LoginRequestData = reactive({
  username: useAccount.getUsername || "",
  password: useAccount.getPassWord || "",
  verify_code: "",
  mode: "account",
  phone: "",
  smsCode: ""
})
// 是否显示双因子认证
const isShowDoubleFactor = ref(false)
const backUserInfo = ref()

/** 登录表单校验规则 */
const loginFormRules: FormRules = {
  username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 8, max: 16, message: "长度在 8 到 16 个字符", trigger: "blur" }
  ],
  verify_code: [{ required: true, message: "请输入验证码", trigger: "blur" }]
}
/** 登录逻辑 */
const handleLogin = () => {
  loginFormRef.value?.validate((valid: boolean, fields) => {
    if (valid) {
      loading.value = true
      isShowLoading.value = true
      userStore
        .login(cloneDeep(loginFormData))
        .then((data: any) => {
          isShowLoading.value = false
          let isDoubleFactor = data.is_double_factor || false
          if (isDoubleFactor) {
            // 需要双因子认证
            backUserInfo.value = data
            isShowDoubleFactor.value = true
          } else {
            toRoutePage()
          }
        })
        .catch(() => {
          createCode()
          loginFormData.password = ""
        })
        .finally(() => {
          isShowLoading.value = false
          loading.value = false
        })
    } else {
      console.error("表单校验不通过", fields)
    }
  })
}
// 跳转动态路由得第一个路由
const toRoutePage = () => {
  let list = permissionStore.getDyNamicRoutes
  console.log("list", list)
  if (list && list.length > 0) {
    let children = list[0].children || []
    if (children && children.length > 0) {
      router.push({ name: children[0].name })
    } else {
      ElMessage.error("没有设置路由")
    }
  } else {
    ElMessage.error("没有设置路由")
  }
}
/** 创建验证码 */
const createCode = () => {
  // 先清空验证码的输入
  loginFormData.verify_code = ""
  // 获取验证码
  codeUrl.value = ""
  getLoginCodeApi().then((res) => {
    codeUrl.value = res.data
  })
}

/** 初始化验证码 */
createCode()

// 忘记密码相关
const showDrawer = ref(false)
const forgetPasswordForm = ref({
  phone: "",
  verify_code: "",
  newPassword: "",
  confirmPassword: ""
})

// 控制记住我功能
const rememberMe = ref(false)
watchEffect(() => {
  if (
    loginFormData.username === useAccount.getUsername &&
    loginFormData.password === useAccount.getPassWord &&
    useAccount.$state.username &&
    useAccount.$state.password
  ) {
    rememberMe.value = true
  } else {
    rememberMe.value = false
  }
})
const remember = () => {
  if (rememberMe.value) {
    useAccount.setAccountInfo({ username: loginFormData.username || "", password: loginFormData.password || "" })
  } else {
    useAccount.resetAccountInfo()
  }
}
// 获取图形验证码
const getVerCode = async (flag: boolean) => {
  if (!loginFormData.phone || !/^1[3456789]\d{9}$/.test(loginFormData.phone)) {
    return
  }
  const [err, res] = await to(
    apiBackgroundGetSmsVerifyCodePost({
      phone: loginFormData.phone
    })
  )
  if (err) {
    return
  }
  if (res && res.code === 0) {
    const data = res.data || {}
    answer.value = data.key || ""
    loginFormData.code = data.key || ""
    const keys = Base64.decode(data.key) ? JSON.parse(Base64.decode(data.key)) : ""
    console.log("getLoginVerifyCode", keys)
    answerList.value = []
    if (keys && typeof keys === "object") {
      for (const keyName in keys) {
        answerList.value.push(keys[keyName])
      }
    }
    if (verifyCode.value) {
      verifyCode.value.setAnswerList(answerList.value)
      if (flag) {
        verifyCode.value.reset()
      }
    }
    isShowVerify.value = true
    console.log("result", answerList.value)
  } else {
    ElMessage.error(res.msg)
  }
}

// 验证成功
const verifySuccess = (value: any) => {
  console.log("verifySuccess", value)
  isShowVerify.value = false
  getPhoneCode()
}
// 刷新验证码
const verifyRefresh = () => {
  getVerCode(true)
}
// 重置验证码倒计时
const resetHandle = () => {
  sendAuthCode.value = true
  sendCodeDisabled.value = false
}
const handlerPhoneChange = (e: any) => {
  if (e && e.length >= 11) {
    sendCodeDisabled.value = false
  }
}
// 获取手机验证码
const getPhoneCode = async () => {
  const choices = 0
  const params = {
    phone: loginFormData.phone,
    choices: choices,
    code: answer.value
  }
  console.log("getPhoneCode", params)
  const [err, res] = await to(apiBackgroundVerificationCodePost(params))
  if (err) {
    return
  }
  if (res.code === 0) {
    sendAuthCode.value = false
    sendCodeDisabled.value = true
    ElMessage.success("发送成功")
  } else {
    ElMessage.error(res.msg)
  }
}
// 输入验证码监听
const inputCodeChange = (value: any) => {
  loginFormData.verify_code = value
}
// 切换登录方式
const changeLoginType = (value: any) => {
  loginFormData.mode = value
}
// 初始化数据
const initData = () => {
  const userName = useAccount.getUsername
  const password = useAccount.getPassWord
  if (userName && password) {
    loginFormData.username = userName
    loginFormData.password = password
  }
}
// 跳转协议
const handleGoToAgreement = (type: string) => {
  window.open(location.origin + "/#/agreement?type=" + type, "_blank")
}
// 确认双因子
const confirmDoubleFactor = () => {
  console.log("confirmDoubleFactor")
  toRoutePage()
}

onMounted(() => {
  setSessionStorage("CHECKDOUBLEFACTOR", "")
  if (userStore) {
    userStore.resetToken()
  }
  initData()
})
onUnmounted(() => {
  console.log("登录页销毁")
})

import useGlobalMessageReminder from "@/hooks/useGlobalMessageReminder"
import { setSessionStorage } from "@/utils/storage"
const { onlyCloseDialog } = useGlobalMessageReminder()
onlyCloseDialog()
</script>

<template>
  <div :class="['login-page', 'loginBg']" :style="{ backgroundImage: `url(${imgBg})` }">
    <div class="login-page-box">
      <div class="login-title" />
      <div class="tip-txt" />
      <img class="login-main-img" :src="imgMain" />
      <!-- 登录Form -->
      <div class="login-form">
        <div class="verify-pop">
          <verify-code
            ref="verifyCode"
            :visible="isShowVerify"
            @success="verifySuccess"
            :is-number="true"
            @refresh="verifyRefresh"
          />
        </div>
        <div class="login-form-title">
          <h2>欢迎来到资金监管平台</h2>
        </div>
        <div class="login-form-content">
          <el-tabs v-model="loginType" class="demo-tabs" @tab-change="changeLoginType">
            <el-tab-pane label="账号登录" name="account" />
            <el-tab-pane label="手机号登录" name="sms" />
          </el-tabs>
          <el-form ref="loginFormRef" :model="loginFormData" :rules="loginFormRules" @keyup.enter="handleLogin">
            <el-form-item prop="username" v-if="loginType === 'account'">
              <el-input
                v-model.trim="loginFormData.username"
                placeholder="用户名"
                type="text"
                prefix-icon="User"
                size="large"
              />
            </el-form-item>
            <el-form-item prop="password" v-if="loginType === 'account'">
              <el-input
                v-model.trim="loginFormData.password"
                placeholder="密码"
                type="password"
                prefix-icon="Lock"
                size="large"
                show-password
                @blur="handleBlur"
                @focus="handleFocus"
              />
            </el-form-item>
            <el-form-item prop="verify_code" v-if="loginType === 'account'">
              <el-input
                v-model.trim="loginFormData.verify_code"
                placeholder="验证码"
                type="text"
                prefix-icon="Key"
                maxlength="7"
                size="large"
              >
                <template #append>
                  <el-image :src="codeUrl" @click="createCode" draggable="false">
                    <template #placeholder>
                      <el-icon>
                        <Picture />
                      </el-icon>
                    </template>
                    <template #error>
                      <el-icon>
                        <Loading />
                      </el-icon>
                    </template>
                  </el-image>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="phone" v-if="loginType === 'sms'">
              <el-input
                v-model.trim="loginFormData.phone"
                placeholder="请输入手机号"
                type="text"
                prefix-icon="Iphone"
                @input="handlerPhoneChange"
                size="large"
              />
            </el-form-item>
            <el-form-item prop="verify_code" v-if="loginType === 'sms'">
              <div>
                <verify-message-code
                  :sendAuthCode="sendAuthCode"
                  :disabled="sendCodeDisabled"
                  @reset="resetHandle"
                  @getPhoneCode="getVerCode"
                  v-model="loginFormData.verify_code"
                  @input="inputCodeChange"
                />
              </div>
            </el-form-item>
            <el-form-item>
              <div class="controls">
                <el-checkbox
                  label="记住密码"
                  v-model="rememberMe"
                  @change="remember"
                  v-show="loginType === 'account'"
                />
                <span @click="showDrawer = true">忘记密码？</span>
              </div>
            </el-form-item>
            <el-form-item>
              <div class="login-form-button" @click.prevent="handleLogin" v-loading="isShowLoading">登录</div>
            </el-form-item>
            <!-- <el-button :loading="loading" type="primary" size="large" @click.prevent="handleLogin">登 录</el-button> -->
          </el-form>
        </div>
        <div class="login-form-bottom">
          <span class="login-form-bottom-up cursor-pointer"
            >登录即代表你已同意<span @click="handleGoToAgreement('FS')">《监管平台服务协议》</span
            ><span @click="handleGoToAgreement('FSD')">《监管平台免责声明》</span></span
          >
        </div>
      </div>
    </div>
    <div class="login-page-drawer">
      <el-drawer v-model="showDrawer" direction="rtl" size="30%">
        <template #title>
          <span>忘记密码</span>
        </template>
        <template #default>
          <el-form :model="forgetPasswordForm" label-width="auto" :label-position="'top'" :size="'large'">
            <el-form-item prop="phone" label="手机号">
              <el-input v-model="forgetPasswordForm.phone" placeholder="请输入手机号码" />
            </el-form-item>
            <el-form-item prop="verify_code" label="手机验证码">
              <el-input
                v-model.trim="forgetPasswordForm.verify_code"
                placeholder="请输入验证码"
                type="text"
                tabindex="3"
                maxlength="7"
              >
                <template #append>
                  <el-image :src="codeUrl" @click="createCode" draggable="false">
                    <template #placeholder>
                      <el-icon>
                        <Picture />
                      </el-icon>
                    </template>
                    <template #error>
                      <el-icon>
                        <Loading />
                      </el-icon>
                    </template>
                  </el-image>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="newPassword" label="新密码">
              <el-input v-model="forgetPasswordForm.newPassword" placeholder="请输入新的密码" />
            </el-form-item>
            <el-form-item prop="confirmPassword" label="确认密码">
              <el-input v-model="forgetPasswordForm.confirmPassword" placeholder="请再次确认" />
            </el-form-item>
            <el-form-item>
              <div class="login-page-drawer-button">提交</div>
            </el-form-item>
          </el-form>
        </template>
      </el-drawer>
    </div>
    <!--双因子认证 -->
    <double-factor v-model:isShow="isShowDoubleFactor" :user-info="backUserInfo" @doubleConfirm="confirmDoubleFactor" />
  </div>
</template>

<style lang="scss" scoped>
.login-page {
  min-height: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position-y: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  &-box {
    width: 1330px;
    height: 650px;
    position: relative;

    .login-title {
      position: absolute;
      top: 30px;
      left: 10px;
      width: 573px;
      height: 113px;
      background-image: url("@/assets/login/ic_title_head_txt.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .tip-txt {
      position: absolute;
      top: 163px;
      left: 10px;
      width: 590px;
      height: 43px;
      background-image: url("@/assets/login/ic_title_head_tip_txt.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .login-main-img {
      position: absolute;
      top: 169px;
      left: 65px;
      width: 459px;
      height: 409px;
    }

    .login-form {
      position: absolute;
      top: 30px;
      right: 70px;
      width: 440px;
      min-height: 420px;
      border-radius: 20px;
      background-color: #fff;
      padding: 30px 50px;
      margin: 0 auto;

      &-content {
        :deep(.el-input-group__append) {
          padding: 0;
          overflow: hidden;

          .el-image {
            width: 100px;
            height: 40px;
            border-left: 0px;
            user-select: none;
            cursor: pointer;
            text-align: center;
          }
        }

        .controls {
          width: 100%;
          display: flex;
          justify-content: space-between;

          & span {
            font-size: 14px;
            color: #767676;
            font-weight: 400;
          }
        }
      }

      &-button {
        cursor: pointer;
        height: 48px;
        width: 100%;
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: var(--el-color-primary);
        color: #fff;
      }

      &-bottom {
        margin-top: 10px;
        height: 60px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        font-weight: 400;
        color: #767676;

        & span {
          & span {
            color: var(--el-color-primary);
          }
        }
      }

      .verify-code {
        width: 200px;
      }

      .captcha {
        margin-left: 20px;
        width: 120px;
      }
    }
  }

  &-drawer {
    &-button {
      cursor: pointer;
      height: 48px;
      width: 100%;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: var(--el-color-primary);
      color: #fff;
    }
  }
}

.verify-pop {
  position: absolute;
  top: 187px;
  left: 60px;
}
</style>
