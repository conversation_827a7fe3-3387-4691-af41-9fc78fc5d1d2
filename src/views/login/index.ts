// 身份证正则
export const IDRe18 = /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/

export const IDre15 = /^([1-6][1-9]|50)\d{4}\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}$/

export const UserAccount = /^[a-zA-Z0-9]{8,20}$/

export const PhoneNumber = /^(13[0-9]|14[********]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/

export const WorkingYears = /^-?\d+$/

export type UploadFiles = UploadFile[]

export type UploadUserFile = Omit<UploadFile, "status" | "uid"> & Partial<Pick<UploadFile, "status" | "uid">>

export type UploadStatus = "ready" | "uploading" | "success" | "fail"

export type Awaitable<T> = Promise<T> | T

export type Mutable<T> = { -readonly [P in keyof T]: T[P] }

export interface UploadFile {
  name: string
  percentage?: number
  status: UploadStatus
  size?: number
  response?: unknown
  uid: number
  url?: string
  raw?: UploadRawFile
}

export interface UploadProgressEvent extends ProgressEvent {
  percent: number
}

export interface UploadRawFile extends File {
  uid: number
}

export interface UploadRequestOptions {
  action: string
  method: string
  data: Record<string, string | Blob | [string | Blob, string]>
  filename: string
  file: File
  headers: Headers | Record<string, string | number | null | undefined>
  onError: (evt: any) => void
  onProgress: (evt: UploadProgressEvent) => void
  onSuccess: (response: any) => void
  withCredentials: boolean
}

export type ServiceTime = {
  startTime: string
  endTime: string
}

export type Gender = "MAN" | "WOMEN" | "OTHER"

export type SignInBaseForm = {
  username: string
  password: string
  phone: string
  repeatPassword: string
}

export type SignInInfoForm = {
  imageUrl: string
  realName: string
  gender: Gender
  idCardUrl: string
  idCardNO: string
  phone: string
  workUnit: string
  address: string
  nutritionistType: string
  expertise: string[]
  otherName: string
  qualificationsUrl: UploadUserFile[]
  qualificationsNO: string
  workingYears: number | undefined
  serviceTime: ServiceTime
  introduction: string
}

export interface SignInParamsTypes {
  id?: number
  username?: string
  images_url?: string[]
  name?: string
  gender?: Gender
  id_card?: string
  id_card_url?: string[]
  phone?: string
  work_unit?: string
  address?: string
  nutritionist_type?: string
  expertise?: string[]
  other_name?: string
  qualifications_url?: string[]
  qualification_num?: string
  working_years?: number | undefined
  introduction?: string
  start_time?: string
  end_time?: string
  password?: string
}
