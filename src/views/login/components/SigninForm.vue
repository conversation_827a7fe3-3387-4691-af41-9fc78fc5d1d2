<template>
  <div class="signin-form">
    <el-scrollbar height="620px">
      <div class="signin-form-title">
        <h2>注册</h2>
      </div>
      <div class="signin-form-box">
        <div class="signin-form-box-tip">第一步：完善账号信息</div>
        <el-form class="signin-form-box-content" label-width="90px" label-position="left">
          <el-form-item label="登录账号">
            <el-input placeholder="请输入8-20位数字或字母组成的账号" />
          </el-form-item>
          <el-form-item label="登录密码">
            <el-input placeholder="请输入8-20位数字或字母组成的密码" />
          </el-form-item>
          <el-form-item label="重复密码">
            <el-input placeholder="请再次输入密码" />
          </el-form-item>
        </el-form>
        <div class="signin-form-box-tip">第二步：完善营养师信息</div>
        <el-form class="signin-form-box-content" label-width="90px" label-position="left">
          <el-form-item label="个人照片">
            <el-upload
              class="avatar-uploader"
              action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
              :show-file-list="false"
            >
              <img v-if="imageUrl" :src="imageUrl" class="avatar" />
              <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            </el-upload>
          </el-form-item>
          <el-form-item label="真实姓名">
            <el-input placeholder="请输入真实姓名" />
          </el-form-item>
          <el-form-item label="性别">
            <el-radio-group v-model="gender" class="ml-4">
              <el-radio value="man">男</el-radio>
              <el-radio value="woman">女</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="身份证号码">
            <el-input placeholder="请输入身份证号码" />
          </el-form-item>
          <el-form-item label="手机号码">
            <el-input placeholder="请输入11位手机号码" />
          </el-form-item>
          <el-form-item label="工作单位">
            <el-input placeholder="请输入公司名称" />
          </el-form-item>
          <el-form-item label="联系地址">
            <el-input placeholder="请输入联系地址" />
          </el-form-item>
          <el-form-item label="职业等级">
            <el-radio-group v-model="level" class="ml-4">
              <el-radio value="1">公共营养师</el-radio>
              <el-radio value="2">注册营养师</el-radio>
              <el-radio value="3">临床营养师</el-radio>
              <el-radio value="4">健康管理师</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="擅长领域">
            <el-checkbox-group v-model="checkList">
              <el-checkbox label="体重管理" value="1" />
              <el-checkbox label="慢病营养" value="2" />
              <el-checkbox label="孕期健康" value="3" />
              <el-checkbox label="儿童营养" value="4" />
              <el-checkbox label="食谱制作" value="5" />
              <el-checkbox label="运动营养" value="6" />
              <el-checkbox label="食物营养" value="7" />
              <el-checkbox label="营养分析" value="8" />
              <el-checkbox label="其他" value="9" />
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="从业资质">
            <el-upload
              class="avatar-uploader"
              action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
              :show-file-list="false"
            >
              <img v-if="imageUrl" :src="imageUrl" class="avatar" />
              <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            </el-upload>
          </el-form-item>
          <el-form-item label="从业年限">
            <el-input placeholder="请输入">
              <template #suffix>
                <span>年</span>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="服务时间">
            <el-date-picker
              v-model="dateTime"
              type="daterange"
              unlink-panels
              range-separator="To"
              start-placeholder="Start date"
              end-placeholder="End date"
              :shortcuts="shortcuts"
              size="default"
            />
          </el-form-item>
          <el-form-item label="个人简介">
            <el-input type="textarea" />
          </el-form-item>
        </el-form>
        <div class="signin-form-box-button">登录</div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { Plus } from "@element-plus/icons-vue"

const imageUrl = ref("")

const gender = ref("man")

const level = ref("1")

const checkList = ref([])

const shortcuts = [
  {
    text: "Last week",
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: "Last month",
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: "Last 3 months",
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]

const dateTime = ref("")
</script>

<style lang="scss" scoped>
* {
  // outline: 1px solid red;
}
.signin-form {
  padding: 20px 40px;
  border-radius: 20px;
  background-color: #fff;
  width: 672px;
  &-box {
    &-tip {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 20px;
    }
    &-button {
      width: 70%;
      margin: 0 auto;
      height: 48px;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #0dc195;
      color: #fff;
    }
  }
}
.scrollbar-demo-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  margin: 10px;
  text-align: center;
  border-radius: 4px;
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}
</style>
