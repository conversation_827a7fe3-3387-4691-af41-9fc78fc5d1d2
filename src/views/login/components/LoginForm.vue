<template>
  <div class="login-form">
    <div class="login-form-title">
      <h2>欢迎使用登录系统</h2>
    </div>
    <el-form class="login-form-content">
      <el-form-item>
        <el-input placeholder="请输入用户名">
          <template #prefix>
            <img src="@/assets/login/user.png" width="14px" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-input placeholder="请输入密码">
          <template #prefix>
            <img src="@/assets/login/password.png" width="14px" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-input placeholder="请输入验证码" class="verify-code">
          <template #prefix>
            <img src="@/assets/login/verify_code.png" width="14px" />
          </template>
        </el-input>
        <div class="captcha">
          此处是验证码
          <!-- <el-image :src="captchaBase64">
            <template #error>
              <div class="image-slot">
                <i-ep-picture />
              </div>
            </template>
          </el-image> -->
        </div>
      </el-form-item>
      <el-form-item>
        <div class="controls">
          <el-checkbox label="记住我" />
          <span v-show="false">忘记密码？</span>
        </div>
      </el-form-item>
      <el-form-item>
        <div class="login-form-button">登录</div>
      </el-form-item>
    </el-form>
    <div class="login-form-bottom">
      <span class="login-form-bottom-up">登录即代表你已同意<span>《信息保护协议》</span></span>
      <span class="login-form-bottom-down">还没账号？<span @click="goToSignin">去注册</span></span>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["checkSignin"])
const goToSignin = () => {
  emit("checkSignin")
}
</script>

<style lang="scss" scoped>
* {
  // outline: 1px solid red;
}
.login-form {
  width: 440px;
  height: 468px;
  border-radius: 20px;
  background-color: #fff;
  padding: 40px 50px;
  margin: 0 auto;
  &-content {
    .controls {
      width: 100%;
      display: flex;
      justify-content: space-between;
      & span {
        font-size: 14px;
        color: #767676;
        font-weight: 400;
      }
    }
  }
  &-button {
    height: 48px;
    width: 100%;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #0dc195;
    color: #fff;
  }
  &-bottom {
    margin-top: 10px;
    height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    font-weight: 400;
    color: #767676;
    & span {
      & span {
        color: #0dc195;
      }
    }
  }
  .verify-code {
    width: 200px;
  }
  .captcha {
    margin-left: 20px;
    width: 120px;
  }
}
</style>
