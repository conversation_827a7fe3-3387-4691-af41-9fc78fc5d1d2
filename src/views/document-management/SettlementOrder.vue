<template>
  <div class="container-wrapper pl-20px pr-20px">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="table-button">
          <el-button type="primary" @click="goToExport">导出</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #settle_time="{ row }">
              <div>{{ row.settle_status === "settled" ? row.settle_time : "--" }}</div>
            </template>
            <template #operation="{ row }">
              <el-button plain link size="small" type="primary" @click="showDetail(row)"> 详情 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>

    <!-- 详情 -->
    <el-drawer
      v-model="detailDrawerShow"
      title="详情"
      :direction="'rtl'"
      :show-close="false"
      class="ps-drawer"
      size="40%"
      ref="detailDrawer"
      id="detailDrawer"
    >
      <div class="top-btn">
        <el-button type="primary" v-print="printObj" class="no-print"> 打印 </el-button>
        <!-- <el-button @click="goToExport(formData.id)" type="primary" plain class="no-print"> 导出 </el-button> -->
      </div>
      <el-form :model="formData" label-width="auto" label-position="top">
        <el-form-item>
          <span>组织名称：</span>
          {{ formData.organization_name }}
        </el-form-item>
        <!-- <el-form-item>
          <span>所属仓库：</span>
          {{ formData.stash }}
        </el-form-item> -->
        <el-form-item>
          <span>单据编号：</span>
          {{ formData.trade_no }}
        </el-form-item>
        <el-form-item>
          <span>收货时间：</span>
          {{ formData.update_time }}
        </el-form-item>
        <el-form-item>
          <span>创建时间：</span>
          {{ formData.create_time }}
        </el-form-item>
        <el-form-item label="关联收货单据">
          <div class="flex flex-col">
            <div v-for="(item, index) in formData.delivery_info_nos" :key="index" class="flex flex-items-center">
              <span>{{ item.trade_no }}</span>
              <el-button
                plain
                link
                size="small"
                type="primary"
                class="ml-10px no-print"
                @click="handlerCopy(item.trade_no)"
              >
                复制
              </el-button>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <span>结算金额：</span>
          {{ divide(formData.settle_fee) }}
        </el-form-item>
        <el-form-item>
          <span>审批人：</span>
          {{ formData.approver }}
        </el-form-item>
        <el-form-item>
          <span>审批时间：</span>
          {{ formData.approval_time }}
        </el-form-item>
        <el-form-item>
          <div class="flex flex-items-start">
            <span>单据凭证：</span>
            <div
              v-for="(item, index) in formData.extra"
              :key="index"
              @click="handlerShowPhoto(item)"
              class="cursor-pointer"
            >
              <el-image class="w-auto h-100" :src="item" :fit="'contain'" />
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="dialog-footer m-t-20px">
        <el-button @click="detailDrawerShow = false" class="no-print"> 关闭 </el-button>
      </div>
    </el-drawer>
    <!-- 图片预览-->
    <image-preview-dialog v-model="imageVisible" :imgs="imageList" :currentIndex="0" :title="dialogTitle" />
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from "lodash"
import { ref, reactive, onMounted } from "vue"
import { SEARCH_FORM_SETTLEMENT_ORDER, TABLE_SETTING_SETTLEMENT_ORDER } from "./constants"
import useTableHeightHook from "@/hooks/useTableHeight"
import {
  apiFinalStatementList,
  apiBackgroundFundSupervisionSupervisionDataFinalStatementListExportPost
} from "@/api/document/index"
import to from "await-to-js"
import { ElMessage } from "element-plus"
import { divide } from "@/utils"
import useClipboard from "vue-clipboard3"
import { exportHandle } from "@/utils/exportExcel"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// table数据
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_SETTLEMENT_ORDER))
const loading = ref(false)
const tableData = ref<any>([])
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_SETTLEMENT_ORDER)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])
const dialogTitle = ref("图片预览")

const detailDrawerShow = ref(false)
const formData = ref<any>({})
// 复制
const { toClipboard } = useClipboard()
// 打印
const printObj = {
  id: "detailDrawer", // 这里是要打印元素的ID
  popTitle: "&nbsp", // 打印的标题
  extraCss: "", // 打印可引入外部的一个 css 文件
  extraHead: "" // 打印头部文字
}
// 导出
const printType = "SettlementOrderDetailExport"

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (
      (typeof data[key].value === "string" && data[key].value !== "") ||
      (Array.isArray(data[key].value) && data[key].value.length > 0)
    ) {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}

// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiFinalStatementList({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    if (pageConfig.currentPage === 1) {
      tableData.value = []
    }
    return
  }
  if (res && res.code === 0) {
    let data: any = res.data || {}
    let results: any[] = data.results || []
    tableData.value = results || []
    pageConfig.total = data.count
  } else {
    if (pageConfig.currentPage === 1) {
      tableData.value = []
    }
    ElMessage.error(res.msg)
  }
}

onMounted(() => {
  getDataList()
})

// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 改变页面
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

const showDetail = (data: any) => {
  formData.value = data
  detailDrawerShow.value = true
}

// 复制链接
const handlerCopy = async (order: string) => {
  // navigator.clipboard.writeText(shareUrl.value)
  try {
    await toClipboard(order)
    ElMessage.success("复制成功")
  } catch (error) {
    ElMessage.error("复制失败！")
  }
}
// 查看照片
const handlerShowPhoto = (row: any) => {
  console.log(row)
  const img = row ? [row] : ""
  dialogTitle.value = "单据凭证"
  if (!img) {
    return ElMessage.error("图片不存在")
  }
  imageList.value = cloneDeep(img)
  imageVisible.value = true
}
// 导出
const goToExport = (id: any) => {
  console.log("goToExport", id)
  const option = {
    type: printType,
    api: apiBackgroundFundSupervisionSupervisionDataFinalStatementListExportPost,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    }
  }
  exportHandle(option)
}
</script>

<style scoped lang="scss">
.ps-drawer {
  .top-btn {
    position: absolute;
    top: 40px;
    right: 20px;
  }
}
</style>
<style lang="scss">
@media print {
  html {
    font-size: 1920px !important;
    margin: 0px;
    height: auto;
  }
  body {
    font-size: 1920px !important;
    margin: 5mm 10mm;
    height: auto;
  }
  @page {
    size: A4 portrait;
    margin: 0;
    padding: 0;
  }
  .no-print {
    display: none;
  }
  #detailDrawer {
    width: 100%;
  }
  .ps-drawer {
    width: 100%;
    padding: 40px;
  }
  .el-drawer {
    width: 100% !important;
    height: 100% !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 10000 !important;
  }
}
</style>
