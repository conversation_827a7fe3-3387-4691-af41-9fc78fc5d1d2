import { getPreDate } from "@/utils/date"
import head1 from "./img/head1.png"
import head2 from "./img/head2.png"
import head3 from "./img/head3.png"
import car1 from "./img/car1.png"
import car2 from "./img/car2.png"
import card1 from "./img/card1.png"
import card2 from "./img/card2.png"
import document from "./img/document.png"
import document1 from "./img/document1.png"
import document2 from "./img/document2.png"
import document3 from "./img/document3.png"
import document4 from "./img/document4.png"
import document5 from "./img/document5.png"
import head4 from "./img/head4.png"
import head5 from "./img/head5.png"
import head6 from "./img/head6.png"
import head7 from "./img/head7.png"

export const defaultImg = "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg"

export const SEARCH_FORM_PUCHASE_ORDER = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "采购日期",
    labelWidth: "80px",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  trade_no: {
    type: "input",
    label: "单据编号",
    labelWidth: "80px",
    value: "" as string,
    placeholder: "请输入单据编号",
    clearable: true
  },
  org_id: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: "",
    placeholder: "请选择",
    multiple: false,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  }
}
export const TABLE_SETTING_PUCHASE_ORDER = [
  {
    label: "采购日期",
    prop: "purchase_time"
  },
  {
    label: "监管组织",
    prop: "organization_name"
  },
  {
    label: "所属仓库",
    prop: "warehouse_name"
  },
  {
    label: "单据编号",
    prop: "trade_no"
  },
  {
    label: "采购金额",
    prop: "detail_total_fee",
    type: "price"
  },
  {
    label: "创建时间",
    prop: "create_time"
  },
  {
    label: "经手人",
    prop: "account_name"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operation"
  }
]

export const SEARCH_FORM_INBOUND_AND_OUTBOUND_ORDERS = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "操作日期",
    labelWidth: "80px",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  trade_no: {
    type: "input",
    label: "单据编号",
    labelWidth: "80px",
    value: "" as string,
    placeholder: "请输入单据编号",
    clearable: true
  },
  org_id: {
    type: "supervisionOrgs",
    label: "组织名称",
    value: "",
    placeholder: "请选择",
    multiple: false,
    clearable: true,
    collapseTags: true
  },
  inventory_type: {
    type: "select",
    label: "操作类型",
    value: "" as string,
    clearable: false,
    dataList: [
      {
        label: "全部",
        value: ""
      },
      {
        label: "领料出库",
        value: "RECEIVE_EXIT"
      },
      {
        label: "调拨出库",
        value: "BORROW_EXIT"
      },
      {
        label: "损耗出库",
        value: "EXPEND_EXIT"
      },
      {
        label: "退货出库",
        value: "REFUND_EXIT"
      },
      {
        label: "其他出库",
        value: "OTHER_EXIT"
      },
      {
        label: "采购入库",
        value: "PURCHASE_ENTRY"
      },
      {
        label: "调拨入库",
        value: "BORROW_ENTRY"
      },
      {
        label: "其他入库",
        value: "OTHER_ENTRY"
      }
    ] as Array<any>,
    placeholder: "请选择操作类型"
  }
}
export const TABLE_SETTING_INBOUND_AND_OUTBOUND_ORDERS = [
  {
    label: "操作日期",
    prop: "create_time"
  },
  {
    label: "组织名称",
    prop: "organization_name"
  },
  {
    label: "所属仓库",
    prop: "warehouse_name"
  },
  {
    label: "操作类型",
    prop: "inventory_entry_type"
  },
  {
    label: "单据编号",
    prop: "trade_no"
  },
  {
    label: "合计金额",
    prop: "totalPrice"
  },
  {
    label: "创建时间",
    prop: "create_time"
  },
  {
    label: "经手人",
    prop: "account_name"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operation"
  }
]
export const SEARCH_FORM_DELIVERY_ORDER = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "配送日期",
    labelWidth: "80px",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  trade_no: {
    type: "input",
    label: "单据编号",
    labelWidth: "80px",
    value: "" as string,
    placeholder: "请输入单据编号",
    clearable: true
  },
  org_id: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: "",
    placeholder: "请选择",
    multiple: false,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  }
}
export const TABLE_SETTING_DELIVERY_ORDER = [
  {
    label: "配送日期",
    prop: "delivery_date"
  },
  {
    label: "送达时间",
    prop: "arrival_date"
  },
  {
    label: "监管组织",
    prop: "organization_name"
  },
  {
    label: "所属仓库",
    prop: "warehouse_name"
  },
  {
    label: "单据编号",
    prop: "trade_no"
  },
  {
    label: "创建时间",
    prop: "create_time"
  },
  {
    label: "供应商",
    prop: "supplier_manage_name"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operation"
  }
]
export const SEARCH_FORM_RECEIPT_ORDER = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "收货时间",
    labelWidth: "80px",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  trade_no: {
    type: "input",
    label: "收货单号",
    labelWidth: "80px",
    value: "" as string,
    placeholder: "请输入单据编号",
    clearable: true
  },
  org_id: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: "",
    placeholder: "请选择",
    multiple: false,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  },
  statement_trade_no: {
    type: "input",
    label: "结算单号",
    labelWidth: "80px",
    value: "" as string,
    placeholder: "请输入单据编号",
    clearable: true
  }
}
export const TABLE_SETTING_RECEIPT_ORDER = [
  {
    label: "收货时间",
    prop: "create_time"
  },
  {
    label: "监管组织",
    prop: "organization_name"
  },
  {
    label: "所属仓库",
    prop: "warehouse_name"
  },
  {
    label: "收货单号",
    prop: "trade_no"
  },
  {
    label: "创建时间",
    prop: "create_time"
  },
  {
    label: "供应商",
    prop: "supplier_manage_name"
  },
  {
    label: "核验人",
    prop: "account_name"
  },
  {
    label: "关联结算单",
    prop: "null"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operation"
  }
]
// 结算状态
export const DIC_SETTLE_STATUS = [
  {
    label: "全部",
    value: "全部"
  },
  {
    label: "未结算",
    value: "unliquidated"
  },
  {
    label: "结算中",
    value: "settling"
  },
  {
    label: "已结算",
    value: "settled"
  }
]

export const DIC_APPROVAL_STATUS = [
  {
    label: "全部",
    value: "全部"
  },
  {
    label: "审批中",
    value: "approving"
  },
  {
    label: "同意",
    value: "agree"
  },
  {
    label: "拒绝",
    value: "rejected"
  }
]

export const SEARCH_FORM_SETTLEMENT_ORDER = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "结算时间",
    labelWidth: "80px",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  trade_no: {
    type: "input",
    label: "单据编号",
    labelWidth: "80px",
    value: "" as string,
    placeholder: "请输入单据编号",
    clearable: true
  },
  org_ids: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  }
}
export const TABLE_SETTING_SETTLEMENT_ORDER = [
  {
    label: "结算时间",
    prop: "settle_time",
    slot: "settle_time"
  },
  {
    label: "监管组织",
    prop: "organization_name"
  },
  // {
  //   label: "所属仓库",
  //   prop: "approver"
  // },
  {
    label: "单据编号",
    prop: "trade_no"
  },
  {
    label: "结算金额",
    prop: "settle_fee",
    type: "price"
  },
  {
    label: "创建时间",
    prop: "create_time"
  },
  {
    label: "供应商",
    prop: "supplier_manage_name"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operation"
  }
]
// 申请单筛选
export const SEARCH_FORM_APPLICATION_ORDER = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "提交时间",
    labelWidth: "80px",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  trade_no: {
    type: "input",
    label: "单据编号",
    labelWidth: "80px",
    value: "" as string,
    placeholder: "请输入单据编号",
    clearable: true
  },
  org_ids: {
    type: "supervisionOrgs",
    label: "所属组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true
  },
  supplier_manage_name: {
    type: "input",
    label: "所属供应商",
    value: "",
    placeholder: "请输入",
    clearable: true
  },
  approval_status: {
    type: "select",
    label: "审批状态",
    value: "全部",
    placeholder: "请选择",
    dataList: DIC_APPROVAL_STATUS,
    multiple: false,
    clearable: true
  },
  settle_status: {
    type: "select",
    label: "结算状态",
    value: "全部",
    placeholder: "请选择",
    dataList: DIC_SETTLE_STATUS,
    multiple: false,
    clearable: true
  }
}
// 申请单筛选
export const TABLE_SETTING_APPLICATION_ORDER = [
  {
    label: "提交时间",
    prop: "create_time"
  },
  {
    label: "申请结算单号",
    prop: "trade_no"
  },
  {
    label: "所属组织",
    prop: "organization_name"
  },
  {
    label: "所属供应商",
    prop: "supplier_manage_name"
  },
  {
    label: "结算金额",
    prop: "settle_fee",
    type: "price"
  },
  {
    label: "审批状态",
    prop: "approval_status_alias"
  },
  {
    label: "结算状态",
    prop: "settle_status_alias"
  }
]

export const PUCHASE_ORDER_TABLE_DATA = [
  {
    date: "2024-11-22",
    name: "华溪实验中学",
    stash: "第一食堂冷链库",
    no: "CG241122200000987323",
    price: "￥2,000.00",
    createTime: "2024年11月22日20:00:00",
    handlers: "华田富",
    documenter: "林政",
    auditor: "林爱婷、张小艾",
    info: [
      {
        name: "黑猪肉",
        number: "40",
        specification: "300~500g",
        purchasePrice: "￥8.00/斤",
        totalCount: "￥320.00",
        shelfLife: "90天",
        supplier: "立恒食品批发有限责任公司"
      },
      {
        name: "琵琶腿",
        number: "30",
        specification: "500g/包",
        purchasePrice: "￥35.00/包",
        totalCount: "￥1,050.00",
        shelfLife: "180天",
        supplier: "立恒食品批发有限责任公司"
      },
      {
        name: "鲜牛肉",
        number: "10",
        specification: "500g~1.2kg",
        purchasePrice: "￥12.00/斤",
        totalCount: "￥120.00",
        shelfLife: "3天",
        supplier: "喀什牛羊肉冷链供应公司"
      },
      {
        name: "嫩羊肉",
        number: "15",
        specification: "200~500g",
        purchasePrice: "￥10.00/斤",
        totalCount: "￥150.00",
        shelfLife: "3天",
        supplier: "喀什牛羊肉冷链供应公司"
      },
      {
        name: "空心菜",
        number: "12",
        specification: "中等捆重量",
        purchasePrice: "￥3.50/斤",
        totalCount: "￥42.00",
        shelfLife: "2天",
        supplier: "宏利鲜果批发（雅宝店）"
      },
      {
        name: "苦瓜",
        number: "10",
        specification: "20~30厘米",
        purchasePrice: "￥5.50/斤",
        totalCount: "￥55.00",
        shelfLife: "5天",
        supplier: "宏利鲜果批发（雅宝店）"
      },
      {
        name: "小米辣",
        number: "13",
        specification: "5~10g",
        purchasePrice: "￥1.00/斤",
        totalCount: "￥13.00",
        shelfLife: "3天",
        supplier: "立恒食品批发有限责任公司"
      },
      {
        name: "金龙鱼食用油",
        number: "10",
        specification: "大包装（10升）",
        purchasePrice: "￥25.00/桶",
        totalCount: "￥250.00",
        shelfLife: "365天",
        supplier: "立恒食品批发有限责任公司"
      }
    ]
  },
  {
    date: "2024-11-22",
    name: "田家炳实验小学",
    stash: "一食堂仓库",
    no: "CG241122210000987324",
    price: "￥800.00",
    createTime: "2024年11月22日21:00:00",
    handlers: "许旭城",
    documenter: "石项",
    auditor: "周秋利",
    info: [
      {
        name: "黑猪肉",
        number: "40",
        specification: "300~500g",
        purchasePrice: "￥8.00/斤",
        totalCount: "￥320.00",
        shelfLife: "90天",
        supplier: "立恒食品批发有限责任公司"
      },
      {
        name: "嫩羊肉",
        number: "15",
        specification: "200~500g",
        purchasePrice: "￥10.00/斤",
        totalCount: "￥150.00",
        shelfLife: "180天",
        supplier: "喀什牛羊肉冷链供应公司"
      },
      {
        name: "空心菜",
        number: "12",
        specification: "中等捆重量",
        purchasePrice: "￥3.50/斤",
        totalCount: "￥42.00",
        shelfLife: "3天",
        supplier: "宏利鲜果批发（雅宝店）"
      },
      {
        name: "鸭翅尖",
        number: "10",
        specification: "100~200g",
        purchasePrice: "￥8.80/斤",
        totalCount: "￥88.00",
        shelfLife: "3天",
        supplier: "立恒食品批发有限责任公司"
      },
      {
        name: "鲜牛肉",
        number: "10",
        specification: "500g~1.2kg",
        purchasePrice: "￥12.00/斤",
        totalCount: "￥120.00",
        shelfLife: "2天",
        supplier: "喀什牛羊肉冷链供应公司"
      },
      {
        name: "鸭肉",
        number: "8",
        specification: "500~1.2kg",
        purchasePrice: "￥10.00/斤",
        totalCount: "￥80.00",
        shelfLife: "5天",
        supplier: "立恒食品批发有限责任公司"
      }
    ]
  },
  {
    date: "2024-11-21",
    name: "启元中学",
    stash: "启元食堂仓库",
    no: "CG241121080000987373",
    price: "￥1,200.00",
    createTime: "2024年11月22日08:00:00",
    handlers: "郑晓赖",
    documenter: "陈熙磷",
    auditor: "洛玉香",
    info: [
      {
        name: "鸡胸肉",
        number: "20",
        specification: "500g/包",
        purchasePrice: "￥30.00/包",
        totalCount: "￥600.00",
        shelfLife: "90天",
        supplier: "立恒食品批发有限责任公司"
      },
      {
        name: "空心菜",
        number: "12",
        specification: "中等捆重量",
        purchasePrice: "￥5.00/斤",
        totalCount: "￥60.00",
        shelfLife: "180天",
        supplier: "宏利鲜果批发（雅宝店）"
      },
      {
        name: "苦瓜",
        number: "10",
        specification: "20~30厘米",
        purchasePrice: "￥5.50/斤",
        totalCount: "￥55.00",
        shelfLife: "3天",
        supplier: "宏利鲜果批发（雅宝店）"
      },
      {
        name: "辣椒酱",
        number: "3",
        specification: "500g/瓶",
        purchasePrice: "￥5.00/瓶",
        totalCount: "￥15.00",
        shelfLife: "3天",
        supplier: "宏利鲜果批发（雅宝店）"
      },
      {
        name: "嫩羊肉",
        number: "15",
        specification: "200~500g",
        purchasePrice: "￥10.00/斤",
        totalCount: "￥150.00",
        shelfLife: "2天",
        supplier: "喀什牛羊肉冷链供应公司"
      },
      {
        name: "鲜牛肉",
        number: "10",
        specification: "500g~1.2kg",
        purchasePrice: "￥12.00/斤",
        totalCount: "￥120.00",
        shelfLife: "5天",
        supplier: "喀什牛羊肉冷链供应公司"
      },
      {
        name: "叉烧包",
        number: "10",
        specification: "1.2kg/包",
        purchasePrice: "￥20.00/包",
        totalCount: "￥200.00",
        shelfLife: "3天",
        supplier: "立恒食品批发有限责任公司"
      }
    ]
  }
]

export const INBOUND_AND_OUT_BOUND_ORDER_TABLE_DATA = [
  {
    date: "2024年11月22日",
    name: "华溪实验中学",
    stash: "第一食堂冷链库",
    handleType: "领料出库",
    no: "CK241122200000987323",
    totalPrice: "￥2,000.00",
    createTime: "2024年11月22日20:00:00",
    handlers: "华田富",
    faceInfo: head1,
    documenter: "林政",
    auditor: "林爱婷、张小艾",
    handleDate: "2024-11-22",
    info: [
      {
        name: "冷冻猪肉",
        number: "40",
        specification: "300~500g",
        purchasePrice: "￥8.00/斤",
        totalCount: "￥320.00",
        shelfLife: "90天",
        supplier: "立恒食品批发有限 责任公司"
      },
      {
        name: "琵琶腿",
        number: "30",
        specification: "500g/包",
        purchasePrice: "￥35.00/包",
        totalCount: "￥1,050.00",
        shelfLife: "180天",
        supplier: "立恒食品批发有限 责任公司"
      },
      {
        name: "鲜牛肉",
        number: "10",
        specification: "500g~1.2kg",
        purchasePrice: "￥12.00/斤",
        totalCount: "￥120.00",
        shelfLife: "3天",
        supplier: "喀什牛羊肉冷链供 应公司"
      },
      {
        name: "嫩羊肉",
        number: "15",
        specification: "200~500g",
        purchasePrice: "￥10.00/斤",
        totalCount: "￥150.00",
        shelfLife: "3天",
        supplier: "喀什牛羊肉冷链供 应公司"
      },
      {
        name: "空心菜",
        number: "12",
        specification: "中等捆重量",
        purchasePrice: "￥3.50/斤",
        totalCount: "￥42.00",
        shelfLife: "2天",
        supplier: "宏利鲜果批发（雅 宝店）"
      },
      {
        name: "苦瓜",
        number: "10",
        specification: "20~30厘米",
        purchasePrice: "￥5.50/斤",
        totalCount: "￥55.00",
        shelfLife: "5天",
        supplier: "宏利鲜果批发（雅 宝店）"
      },
      {
        name: "小米辣",
        number: "13",
        specification: "5~10g",
        purchasePrice: "￥1.00/斤",
        totalCount: "￥13.00",
        shelfLife: "3天",
        supplier: "立恒食品批发有限 责任公司"
      },
      {
        name: "金龙鱼食用油",
        number: "10",
        specification: "大包装(10升)",
        purchasePrice: "￥25.00/桶",
        totalCount: "￥250.00",
        shelfLife: "365天",
        supplier: "立恒食品批发有限 责任公司"
      }
    ]
  },
  {
    date: "2024年11月22日",
    name: "田家炳实验小学",
    stash: "食堂仓库",
    handleType: "采购入库",
    no: "RK241122210000987324",
    totalPrice: "￥800.00",
    createTime: "2024年11月22日21:00:00",
    handlers: "许旭城",
    faceInfo: head2,
    documenter: "石项",
    auditor: "周秋利",
    handleDate: "2024-11-22",
    info: [
      {
        name: "冷冻猪肉",
        number: "40",
        specification: "300~500g",
        purchasePrice: "￥8.00/斤",
        totalCount: "￥320.00",
        shelfLife: "90天",
        supplier: "立恒食品批发有限 责任公司"
      },
      {
        name: "鲜牛肉",
        number: "10",
        specification: "500g~1.2kg",
        purchasePrice: "￥12.00/斤",
        totalCount: "￥120.00",
        shelfLife: "180天",
        supplier: "喀什牛羊肉冷链供 应公司"
      },
      {
        name: "嫩羊肉",
        number: "11",
        specification: "200~500g",
        purchasePrice: "￥10.00/斤",
        totalCount: "￥110.00",
        shelfLife: "3天",
        supplier: "喀什牛羊肉冷链供 应公司"
      },
      {
        name: "金龙鱼食用油",
        number: "10",
        specification: "大包装(10升)",
        purchasePrice: "￥25.00/桶",
        totalCount: "￥250.00",
        shelfLife: "3天",
        supplier: "立恒食品批发有限 责任公司"
      }
    ]
  },
  {
    date: "2024年11月21日",
    name: "启元中学",
    stash: "启元食堂仓库",
    handleType: "领料出库",
    no: "CK241121080000987373",
    totalPrice: "￥1,000.00",
    createTime: "2024年11月22日08:00:00",
    handlers: "郑晓赖",
    faceInfo: head3,
    documenter: "陈熙磷",
    auditor: "洛玉香",
    handleDate: "2024-11-22",
    info: [
      {
        name: "冷冻猪肉",
        number: "40",
        specification: "300~500g",
        purchasePrice: "￥8.00/斤",
        totalCount: "￥320.00",
        shelfLife: "90天",
        supplier: "立恒食品批发有限 责任公司"
      },
      {
        name: "鲜牛肉",
        number: "10",
        specification: "500g~1.2kg",
        purchasePrice: "￥12.00/斤",
        totalCount: "￥120.00",
        shelfLife: "180天",
        supplier: "喀什牛羊肉冷链供 应公司"
      },
      {
        name: "嫩羊肉",
        number: "15",
        specification: "200~500g",
        purchasePrice: "￥10.00/斤",
        totalCount: "￥150.00",
        shelfLife: "3天",
        supplier: "喀什牛羊肉冷链供 应公司"
      },
      {
        name: "苦瓜",
        number: "10",
        specification: "20~30厘米",
        purchasePrice: "￥5.50/斤",
        totalCount: "￥55.00",
        shelfLife: "3天",
        supplier: "宏利鲜果批发(雅 宝店)"
      },
      {
        name: "金龙鱼食用油",
        number: "10",
        specification: "大包装(10升）",
        purchasePrice: "￥25.00/桶",
        totalCount: "￥250.00",
        shelfLife: "2天",
        supplier: "立恒食品批发有限 责任公司"
      },
      {
        name: "罗非鱼",
        number: "10",
        specification: "500g",
        purchasePrice: "￥10.5/斤",
        totalCount: "￥105.00",
        shelfLife: "5天",
        supplier: "立恒食品批发有限 责任公司"
      }
    ]
  }
]

export const DELIVERY_ORDER_TABLE_DATA = [
  {
    deliveryDate: "11/22/24",
    deliveryTime: "11/22/24 6:00",
    name: "华溪实验中学",
    stash: "第一食堂冷链库",
    no: "PS241122060010987323",
    createTime: "2024年11月22日06:00:10",
    supplier: "立恒食品批发有限责任公司",
    receivingParty: "华溪实验中学",
    address: "华溪路72号，华溪实验中学第一食堂",
    consignee: "华富贵",
    contactNumber: "华富贵13785659988",
    driverName: "郑越进",
    contactInfo: "13653232215",
    certificateInfo: [
      {
        img: card1,
        name: "驾驶证件"
      },
      {
        img: card2,
        name: "健康证件"
      }
    ],
    distributionMode: "常温配送",
    carType: "轻型厢式货车",
    carNo: "粤A·OU955",
    carImg: [car1, car2],
    document: [document],
    info: [
      {
        name: "冷冻猪肉",
        number: "40斤",
        specification: "300~500g",
        purchasePrice: "￥8.00/斤",
        totalCount: "￥320.00",
        supplier: "90天"
      },
      {
        name: "琵琶腿",
        number: "30包",
        specification: "500g/包",
        purchasePrice: "￥35.00/包",
        totalCount: "￥1,050.00",
        supplier: "180天"
      },
      {
        name: "鲜牛肉",
        number: "10",
        specification: "500g~1.2kg",
        purchasePrice: "￥12.00/斤",
        totalCount: "￥120.00",
        supplier: "3天"
      },
      {
        name: "嫩羊肉",
        number: "15",
        specification: "200~500g",
        purchasePrice: "￥10.00/斤",
        totalCount: "￥150.00",
        supplier: "3天"
      },
      {
        name: "空心菜",
        number: "12",
        specification: "中等捆重量",
        purchasePrice: "￥3.50/斤",
        totalCount: "￥42.00",
        supplier: "2天"
      },
      {
        name: "苦瓜",
        number: "10",
        specification: "20~30厘米",
        purchasePrice: "￥5.50/斤",
        totalCount: "￥55.00",
        supplier: "5天"
      },
      {
        name: "小米辣",
        number: "13",
        specification: "5~10g",
        purchasePrice: "￥1.00/斤",
        totalCount: "￥13.00",
        supplier: "3天"
      },
      {
        name: "金龙鱼食用油",
        number: "10",
        specification: "大包装 (10升)",
        purchasePrice: "￥25.00/桶",
        totalCount: "￥250.00",
        supplier: "365天"
      }
    ]
  },
  {
    deliveryDate: "11/22/24",
    deliveryTime: "11/22/24 6:28",
    name: "田家炳实验小学",
    stash: "一食堂仓库",
    no: "PS241122062844987324",
    createTime: "2024年11月22日06:28:44",
    supplier: "立恒食品批发有限责任公司",
    receivingParty: "田家炳实验小学",
    address: "田家镇新华街11号, 一食堂仓库",
    consignee: "张子荣",
    contactNumber: "张子荣 13574584452",
    driverName: "冯玉祥",
    contactInfo: "13745876695",
    certificateInfo: [
      {
        img: card1,
        name: "驾驶证件"
      },
      {
        img: card2,
        name: "健康证件"
      }
    ],
    distributionMode: "常温配送",
    carType: "轻型厢式货车",
    carNo: "粤G-TF515",
    carImg: [car1, car2],
    document: [document],
    info: [
      {
        name: "冷冻猪肉",
        number: "80斤",
        specification: "300~500g",
        purchasePrice: "￥8.00/斤",
        totalCount: "￥640.00",
        supplier: "90天"
      },
      {
        name: "冷冻鸡翅",
        number: "20包",
        specification: "500g/包",
        purchasePrice: "￥10.00/包",
        totalCount: "￥200.00",
        supplier: "120天"
      },
      {
        name: "冷冻鸡胗",
        number: "20",
        specification: "500g~1.2kg",
        purchasePrice: "￥12.00/斤",
        totalCount: "￥240.00",
        supplier: "120天"
      },
      {
        name: "冷冻鸡腿",
        number: "30",
        specification: "200~500g",
        purchasePrice: "￥10.00/斤",
        totalCount: "￥300.00",
        supplier: "120天"
      },
      {
        name: "大白菜",
        number: "24",
        specification: "200-300g",
        purchasePrice: "￥4.50/斤",
        totalCount: "￥108.00",
        supplier: "2天"
      },
      {
        name: "南瓜",
        number: "20",
        specification: "1kg~ 1.2kg",
        purchasePrice: "￥5.50/斤",
        totalCount: "￥110.00",
        supplier: "5天"
      },
      {
        name: "龙口粉丝",
        number: "26",
        specification: "250g",
        purchasePrice: "￥0.8/斤",
        totalCount: "￥20.80",
        supplier: "120天"
      },
      {
        name: "金龙鱼食用油",
        number: "10",
        specification: "大包装 (10升)",
        purchasePrice: "￥25.00/桶",
        totalCount: "￥250.00",
        supplier: "365天"
      }
    ]
  }
]

export const RECEIPT_ORDER_TABLE_DATA = [
  {
    receivingTime: "2024-11-22 06:00:10",
    name: "华溪实验中学",
    stash: "第一食堂冷链库",
    no: "SH241122060010987323",
    createTime: "2024年11月22日06:00:10",
    supplier: "立恒食品批发有限责任公司",
    auditor: "张国庆、李秋梅",
    receivingParty: "华溪实验中学",
    address: "华溪路72号，华溪实验中学第一食堂",
    consignee: "华富贵",
    contactNumber: "13785659988",
    driverName: "郑越进",
    contactInfo: "13653232215",
    certificateInfo: [
      {
        img: card1,
        name: "驾驶证件"
      },
      {
        img: card2,
        name: "健康证件"
      }
    ],
    distributionMode: "常温配送",
    carType: "轻型厢式货车",
    carNo: "粤A·OU955",
    carImg: [car1, car2],
    document: [document],
    certificateInfo1: [
      {
        img: document1,
        name: "营业执照"
      },
      {
        img: document2,
        name: "食品生产许可证"
      },
      {
        img: document3,
        name: "食品经营许可证"
      }
    ],
    certificateInfo2: [document4, document5],
    personnelInfo: [
      {
        label: "学校食品安全员",
        name: "张国庆",
        img: head4
      },
      {
        label: "食堂管理员",
        name: "李秋梅",
        img: head5
      }
    ],
    info: [
      {
        name: "冷冻猪肉",
        number: "80斤",
        specification: "300~500g",
        purchasePrice: "￥8.00/斤",
        totalCount: "￥640.00",
        supplier: "90天",
        file: [document1, document2]
      },
      {
        name: "冷冻鸡翅",
        number: "20包",
        specification: "500g/包",
        purchasePrice: "￥10.00/包",
        totalCount: "￥200.00",
        supplier: "120天",
        file: []
      },
      {
        name: "冷冻鸡胗",
        number: "20",
        specification: "500g~1.2kg",
        purchasePrice: "￥12.00/斤",
        totalCount: "￥240.00",
        supplier: "120天",
        file: []
      },
      {
        name: "冷冻鸡腿",
        number: "30",
        specification: "200~500g",
        purchasePrice: "￥10.00/斤",
        totalCount: "￥300.00",
        supplier: "120天",
        file: []
      },
      {
        name: "大白菜",
        number: "24",
        specification: "200-300g",
        purchasePrice: "￥4.50/斤",
        totalCount: "￥108.00",
        supplier: "2天",
        file: []
      },
      {
        name: "南瓜",
        number: "20",
        specification: "1kg~1.2kg",
        purchasePrice: "￥5.50/斤",
        totalCount: "￥110.00",
        supplier: "5天",
        file: []
      },
      {
        name: "龙口粉丝",
        number: "26",
        specification: "250g",
        purchasePrice: "￥0.8/斤",
        totalCount: "￥20.80",
        supplier: "120天",
        file: []
      },
      {
        name: "金龙鱼食用油",
        number: "10",
        specification: "大包装(10升)",
        purchasePrice: "￥25.00/桶",
        totalCount: "￥250.00",
        supplier: "365天",
        file: []
      }
    ]
  },
  {
    receivingTime: "2024-11-22 06:00:10",
    name: "田家炳实验小学",
    stash: "一食堂仓库",
    no: "SH241122062844987324",
    createTime: "2024年11月22日06:28:44",
    supplier: "立恒食品批发有限责任公司",
    auditor: "李艺哆、曾向宇",
    receivingParty: "田家炳实验小学",
    address: "田家镇新华街11号，一食堂仓库",
    consignee: "张子荣",
    contactNumber: "13574584452",
    driverName: "冯玉祥",
    contactInfo: "13745876695",
    certificateInfo: [
      {
        img: card1,
        name: "驾驶证件"
      },
      {
        img: card2,
        name: "健康证件"
      }
    ],
    distributionMode: "常温配送",
    carType: "轻型厢式货车",
    carNo: "粤G·TF515",
    carImg: [car1, car2],
    document: [document],
    certificateInfo1: [
      {
        img: document1,
        name: "营业执照"
      },
      {
        img: document2,
        name: "食品生产许可证"
      },
      {
        img: document3,
        name: "食品经营许可证"
      }
    ],
    certificateInfo2: [document4, document5],
    personnelInfo: [
      {
        label: "学校食品安全员",
        name: "韦国忠",
        img: head6
      },
      {
        label: "食堂管理员",
        name: "黄汐源",
        img: head7
      }
    ],
    info: [
      {
        name: "冷冻猪肉",
        number: "40",
        specification: "300~500g",
        purchasePrice: "￥8.00/斤",
        totalCount: "￥320.00",
        supplier: "90天",
        file: [document1, document2]
      },
      {
        name: "琵琶腿",
        number: "30",
        specification: "500g/包",
        purchasePrice: "￥35.00/包",
        totalCount: "￥1,050.00",
        supplier: "180天",
        file: [document1, document2]
      },
      {
        name: "鲜牛肉",
        number: "10",
        specification: "500g~1.2kg",
        purchasePrice: "￥12.00/斤",
        totalCount: "￥120.00",
        supplier: "3天",
        file: [document1, document2]
      },
      {
        name: "嫩羊肉",
        number: "15",
        specification: "200~500g",
        purchasePrice: "￥10.00/斤",
        totalCount: "￥150.00",
        supplier: "3天",
        file: []
      },
      {
        name: "空心菜",
        number: "12",
        specification: "中等捆重量",
        purchasePrice: "￥3.50/斤",
        totalCount: "￥42.00",
        supplier: "2天",
        file: []
      },
      {
        name: "苦瓜",
        number: "10",
        specification: "20~30厘米",
        purchasePrice: "￥5.50/斤",
        totalCount: "￥55.00",
        supplier: "5天",
        file: []
      },
      {
        name: "小米辣",
        number: "13",
        specification: "5~10g",
        purchasePrice: "￥1.00/斤",
        totalCount: "￥13.00",
        supplier: "3天",
        file: []
      },
      {
        name: "金龙鱼食用油",
        number: "10",
        specification: "大包装(10升)",
        purchasePrice: "￥25.00/桶",
        totalCount: "￥250.00",
        supplier: "365天",
        file: []
      }
    ]
  }
]

export const SETTLEMENT_ORDER_TABLE_DATA = [
  {
    time: "2024-11-22 06:00:10",
    name: "华溪实验中学",
    stash: "第一食堂冷链库",
    no: "JS241122060010987323",
    price: "￥1500.00",
    createTime: "2024年11月22日06:00:10",
    supplier: "立恒食品批发有限责任公司",
    receivingTime: "2024-11-22 06:00:10",
    associateReceiptDocuments: ["SH241122060010987323", "SH241122062844987324"],
    approver: "张小艾",
    approvalTime: "2024年11月22日 06:05:10",
    document: [document]
  },
  {
    time: "2024-11-22 06:28:44",
    name: "田家炳实验小学",
    stash: "一食堂仓库",
    no: "JS241122062844987324",
    price: "￥1899.00",
    createTime: "2024年11月22日06:28:44",
    supplier: "立恒食品批发有限责任公司",
    receivingTime: "2024-11-22 06:28:44",
    associateReceiptDocuments: ["SH241122060010987323", "SH241122062844987324"],
    approver: "李明伟",
    approvalTime: "2024年11月22日 06:30:52",
    document: [document]
  }
]
