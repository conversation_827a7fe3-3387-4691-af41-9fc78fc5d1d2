<template>
  <div class="container-wrapper pl-20px pr-20px">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper pt-20px" ref="tableWrapperRef">
      <!-- <div class="flex flex-justify-start flex-item-center mb-10px pr-20px pl-20px">
        <div class="flex flex-item-center">
          <div>采购总金额：￥100000.00</div>
        </div>
      </div> -->
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operation="{ row }">
              <el-button plain link size="small" type="primary" @click="showDetail(row)"> 详情 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>

    <!-- 详情 -->
    <el-drawer
      v-model="detailDrawerShow"
      title="详情"
      :direction="'rtl'"
      :show-close="false"
      class="ps-drawer"
      size="40%"
    >
      <el-form :model="formData" label-width="auto" label-position="top">
        <el-form-item>
          <span>组织名称：</span>
          {{ formData.organization_name }}
        </el-form-item>
        <el-form-item>
          <span>所属仓库：</span>
          {{ formData.warehouse_name }}
        </el-form-item>
        <el-form-item>
          <span>单据编号：</span>
          {{ formData.trade_no }}
        </el-form-item>
        <el-form-item>
          <span>经手人/制单人：</span>
          {{ formData.account_name }}
        </el-form-item>
        <!-- <el-form-item>
          <span>制单人：</span>
          {{ formData.documenter }}
        </el-form-item> -->
        <el-form-item>
          <span>审核人：</span>
          {{ formData.approve_account_name }}
        </el-form-item>
        <el-form-item>
          <span>采购日期：</span>
          {{ formData.purchase_time }}
        </el-form-item>
        <el-form-item>
          <span>创建时间：</span>
          {{ formData.create_time }}
        </el-form-item>
        <el-form-item label="物资信息：">
          <ps-table
            :tableData="formData.materials_detail"
            ref="psTableRef"
            :show-pagination="false"
            style="width: 100%"
          >
            <ps-column :table-headers="formTableSetting" />
          </ps-table>
        </el-form-item>
      </el-form>
      <div class="dialog-footer m-t-20px">
        <el-button @click="detailDrawerShow = false"> 关闭 </el-button>
        <!-- <el-button type="primary" @click="detailDrawerShow = false"> 打印 </el-button> -->
        <!-- <el-button type="primary" @click="detailDrawerShow = false"> 导出 </el-button> -->
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from "lodash"
import { ref, reactive, onMounted } from "vue"
import { SEARCH_FORM_PUCHASE_ORDER, TABLE_SETTING_PUCHASE_ORDER, PUCHASE_ORDER_TABLE_DATA } from "./constants"
import useTableHeightHook from "@/hooks/useTableHeight"
import { apiChannelPurchaseInfo, apiPurchaseDetailList } from "@/api/document/index"
import to from "await-to-js"
import { ElMessage } from "element-plus"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// table数据
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_PUCHASE_ORDER))
const loading = ref(false)
const tableData = ref<any>([])
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_PUCHASE_ORDER)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

const detailDrawerShow = ref(false)
const formData = ref<any>({})

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
        params.start_time = data[key].value[0]
        params.end_time = data[key].value[1]
      }
    }
  }
  return params
}

// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiChannelPurchaseInfo({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 详情
const getDetailList = async (data: any) => {
  tableLoading.value = true
  const [err, res] = await to(
    apiPurchaseDetailList({
      id: data.id
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    formData.value = res.data
  } else {
    ElMessage.error(res.msg)
  }
}

onMounted(() => {
  getDataList()
})

// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 改变页面
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 抽屉
const formTableSetting = [
  {
    label: "物资名称",
    prop: "materials_name"
  },
  {
    label: "采购数量",
    prop: "count"
  },
  {
    label: "规格",
    prop: "material_specification_record"
  },
  {
    label: "采购价",
    prop: "ref_unit_price",
    type: "price"
  },
  {
    label: "合计金额",
    prop: "total",
    type: "price"
  },
  // {
  //   label: "保质期",
  //   prop: "shelfLife"
  // },
  {
    label: "供应商",
    prop: "supplier_manage_name"
  }
]
const showDetail = (data: any) => {
  detailDrawerShow.value = true
  getDetailList(data)
  // formData.value = data
}
</script>

<style scoped></style>
