<template>
  <div class="container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper pt-20px" ref="tableWrapperRef">
      <div class="flex flex-justify-between flex-item-center mb-10px pr-20px pl-20px">
        <div class="flex flex-item-center">
          <!-- <div class="mr-10px">期末库存总额：{{ "¥ " + dateTotalEndFee }}</div> -->
        </div>
        <div class="table-button">
          <!-- <el-button type="primary" @click="gotoPrint">打印</el-button>
          <el-button type="primary" @click="goToExport">导出</el-button> -->
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #dataRange="{ row }">
              <div class="flex flex-wrap">
                {{ getDateRange() }}
              </div>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from "lodash"
import { ref, reactive, onMounted } from "vue"
import { SEARCH_FORM_INVENTORY_BALANCE, TABLE_SETTING_INVENTORY_BALANCE } from "./constants"
import useTableHeightHook from "@/hooks/useTableHeight"
import {
  apiBackgroundFundSupervisionSupervisionDataInventoryBalanceListPost,
  apiBackgroundFundSupervisionSupervisionDataInventoryBalanceListExportPost
} from "@/api/document"
import to from "await-to-js"
import { ElMessage } from "element-plus"
import { exportHandle } from "@/utils/exportExcel"
import { setLocalStorage } from "@/utils/storage"
import { useRouter } from "vue-router"
import { divide } from "@/utils/index"
// 路由
const router = useRouter()
// 表格
const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef, 45)
// 总额
const dateTotalEndFee = ref(0)
// table数据
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_INVENTORY_BALANCE))
const loading = ref(false)
const tableData = ref<any>([])
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_INVENTORY_BALANCE))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 导出
const printType = "InventoryBalanceExport"
// 格式化从参数
const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value && data[key].value !== "" && data[key].value !== "全部" && data[key].value.length > 0) {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}
// 获取数据
const getDataList = async () => {
  tableLoading.value = true
  const [err, res]: any[] = await to(
    apiBackgroundFundSupervisionSupervisionDataInventoryBalanceListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    let results = data.results || []
    let collect = data.collect || {}
    dateTotalEndFee.value = collect.date_total_end_fee ? divide(collect.date_total_end_fee) : 0
    tableData.value = cloneDeep(results)
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 改变页面
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}
// 导出
const goToExport = () => {
  console.log("goToExport")
  const option = {
    type: printType,
    api: apiBackgroundFundSupervisionSupervisionDataInventoryBalanceListExportPost,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    }
  }
  exportHandle(option)
}

// 打印
const gotoPrint = () => {
  let tabbleSetting = cloneDeep(tableSetting.value)
  tabbleSetting = tabbleSetting.filter((item: any) => item.prop !== "imgs")
  setLocalStorage("print_setting", tabbleSetting)
  // let collectList = [
  //   {
  //     label: "期末库存总额：￥",
  //     value: dateTotalEndFee.value
  //   }
  // ]
  // setLocalStorage("collect" + printType, collectList)
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: printType,
      print_title: "库存余额表",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiBackgroundFundSupervisionSupervisionDataInventoryBalanceListPost",
      params: JSON.stringify({
        ...formatQueryParams(searchFormSetting.value),
        page: 1,
        page_size: 9999
      }),
      isShowCollect: 0
    }
  })
  window.open(href, "_blank")
}
// 获取时间段
const getDateRange = () => {
  if (searchFormSetting.value.selecttime) {
    let startDate = searchFormSetting.value.selecttime.value[0] ? searchFormSetting.value.selecttime.value[0] : ""
    let endDate = searchFormSetting.value.selecttime.value[1] ? searchFormSetting.value.selecttime.value[1] : ""
    return `${startDate}至${endDate}`
  }
  return ""
}

onMounted(() => {
  getDataList()
})
</script>

<style scoped></style>
