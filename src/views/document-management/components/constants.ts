import { getPreDate } from "@/utils/date"
import { TYPE_INVENTORY } from "../../organizational-supervision/constants"
import { cloneDeep } from "lodash"

export const SEARCH_FORM_INVENTORY_BALANCE = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "结算时间",
    labelWidth: "80px",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  org_ids: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    isShowChildOrs: true
  },
  material_name: {
    type: "input",
    label: "物资名称",
    labelWidth: "80px",
    value: "" as string,
    placeholder: "请输入物资名称",
    clearable: true
  }
}
export const TABLE_SETTING_INVENTORY_BALANCE = [
  {
    label: "日期",
    prop: "date",
    slot: "dataRange"
  },
  {
    label: "监管组织",
    prop: "organization_name"
  },
  {
    label: "所属仓库",
    prop: "warehouse_name"
  },
  {
    label: "物资名称",
    prop: "materials_name"
  },
  {
    label: "最小单位",
    prop: "limit_unit_name"
  },
  // {
  //   label: "规格",
  //   prop: "material_spec"
  // },
  // {
  //   label: "单价",
  //   prop: "unit_price",
  //   type: "price"
  // },
  {
    label: "期初库存数量",
    prop: "date_start_count"
  },
  {
    label: "本期入库数量",
    prop: "entry_count"
  },
  {
    label: "本期出库数量",
    prop: "exit_count"
  },
  {
    label: "期末库存数量",
    prop: "date_end_count"
  }
  // {
  //   label: "期末库存金额",
  //   prop: "date_end_fee",
  //   type: "price"
  // }
]
export const SEARCH_FORM_INVENTORY_CHANGE = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "结算时间",
    labelWidth: "80px",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  org_id: {
    type: "supervisionOrgs",
    label: "组织名称",
    value: "",
    placeholder: "请选择",
    multiple: false,
    clearable: true,
    collapseTags: true
  },
  material_name: {
    type: "input",
    label: "物资名称",
    labelWidth: "80px",
    value: "" as string,
    placeholder: "请输入物资名称",
    clearable: true
  },
  inventory_type: {
    type: "select",
    label: "操作类型",
    value: "全部",
    dataList: cloneDeep(TYPE_INVENTORY),
    placeholder: "请选择操作类型",
    multiple: false,
    clearable: true
  }
}
export const TABLE_SETTING_INVENTORY_CHANGE = [
  {
    label: "日期",
    prop: "creat_time"
  },
  {
    label: "所属学校",
    prop: "org_name"
  },
  {
    label: "所属仓库",
    prop: "source_warehouse_name"
  },
  {
    label: "物资名称",
    prop: "material_name"
  },
  {
    label: "操作类型",
    prop: "inventory_type"
  },
  {
    label: "单价",
    prop: "unit_price"
  },
  {
    label: "最小单位",
    prop: "limit_count_record"
  },
  {
    label: "规格",
    prop: "material_specification_record"
  },
  {
    label: "数量",
    prop: "count"
  },
  {
    label: "合计金额",
    prop: "total"
  },
  {
    label: "当前库存数量",
    prop: "stock_num"
  },
  {
    label: "出入库图片",
    prop: "imgs",
    slot: "imgs"
  }
]
