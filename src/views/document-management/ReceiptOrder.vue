<template>
  <div class="receipt-order container-wrapper pl-20px pr-20px">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper pt-20px" ref="tableWrapperRef">
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operation="{ row }">
              <el-button plain link size="small" type="primary" @click="showDetail(row)"> 详情 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>

    <!-- 详情 -->
    <el-drawer
      v-model="detailDrawerShow"
      title="详情"
      :direction="'rtl'"
      :show-close="false"
      class="ps-drawer"
      size="700"
    >
      <el-form :model="formData" label-width="auto" label-position="top">
        <div class="divider">
          <h3>订单信息</h3>
          <el-form-item>
            <span>组织信息：</span>
            {{ formData.organization_name }}
          </el-form-item>
          <el-form-item>
            <span>所属仓库：</span>
            {{ formData.warehouse_name }}
          </el-form-item>
          <el-form-item>
            <span>单据编号：</span>
            {{ formData.trade_no }}
          </el-form-item>
          <el-form-item>
            <span>收货时间：</span>
            {{ formData.create_time }}
          </el-form-item>
          <el-form-item>
            <span>创建时间：</span>
            {{ formData.create_time }}
          </el-form-item>
        </div>
        <div class="divider">
          <h3>配送信息</h3>
          <el-form-item>
            <span>收货方名称：</span>
            {{ formData.contact_name }}
          </el-form-item>
          <el-form-item>
            <span>收货地址：</span>
            {{ formData.address }}
          </el-form-item>
          <el-form-item>
            <span>收货人：</span>
            {{ formData.contact_name }}
          </el-form-item>
          <el-form-item>
            <span>联系电话：</span>
            {{ formData.phone }}
          </el-form-item>
          <div v-for="(driver, index) in formData.driver_info" :key="index" class="bg-f2 m-b-10">
            <el-form-item>
              <span>司机姓名：</span>
              {{ driver.name }}
            </el-form-item>
            <el-form-item>
              <span>联系方式：</span>
              {{ driver.phone }}
            </el-form-item>
            <el-form-item>
              <div class="img-box-wrap">
                <div class="title">证件信息：</div>
                <div class="img-wrap">
                  <div v-for="(item, index) in driver.driving_licence" :key="index" class="car-img">
                    <div class="flex flex-col flex-items-center flex-justify-between mr-10px">
                      <el-image class="w-auto h-100" :src="item" :fit="'contain'" />
                      <span>驾驶证件</span>
                    </div>
                  </div>
                  <div v-for="(item, index) in driver.health_certificate" :key="index" class="car-img">
                    <div class="flex flex-col flex-items-center flex-justify-between mr-10px">
                      <el-image class="w-auto h-100" :src="item" :fit="'contain'" />
                      <span>健康证</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </div>
          <el-form-item>
            <span>配送温度：</span>
            {{ formData.delivery_temperature }}℃
          </el-form-item>
          <div v-for="(vehicle, index) in formData.vehicle_info" :key="index" class="bg-f2 m-b-10">
            <el-form-item>
              <span>车辆类型：</span>
              {{ vehicle.car_type_alias }}
            </el-form-item>
            <el-form-item>
              <span>车牌号：</span>
              {{ vehicle.plate_number }}
            </el-form-item>
            <el-form-item>
              <div class="img-box-wrap">
                <div class="title">车辆图片：</div>
                <div class="img-wrap">
                  <div v-for="(item, index) in vehicle.car_img" :key="index" class="car-img">
                    <el-image class="mr-10px w-auto h-100" :src="item" :fit="'contain'" />
                  </div>
                </div>
              </div>
            </el-form-item>
          </div>
        </div>
        <div class="divider">
          <h3>资质信息</h3>
          <el-form-item>
            <div class="img-box-wrap">
              <div class="title">证件信息：</div>
              <div class="img-wrap">
                <div v-for="(item, index) in formData.certification_info" :key="index">
                  <div class="flex flex-col flex-items-center flex-justify-between mr-10px">
                    <el-image class="w-auto w-100" :src="item.imgUrl" :fit="'contain'" />
                    <span>{{ item.name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item>
            <div class="flex flex-items-start" v-if="formData.certificateInfo2">
              <span>其他证件信息：</span>
              <div v-for="(item, index) in formData.certificateInfo2" :key="index">
                <div class="flex flex-col flex-items-center flex-justify-between">
                  <el-image class="w-auto h-100 mr-10px" :src="item" :fit="'contain'" />
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
        <div class="divider">
          <h3>核验人员信息</h3>
          <div class="flex">
            <div v-for="(item, index) in formData.check_accounts" :key="index" class="mr-10px">
              <el-form-item>
                <span>{{ item.role_name }}：</span>
                {{ item.username }}
              </el-form-item>
              <el-form-item>
                <div class="flex flex-items-start">
                  <span>收货实况人脸：</span>
                  <el-image class="w-auto h-100" :src="item.face_url" :fit="'contain'" />
                </div>
              </el-form-item>
            </div>
          </div>
        </div>
        <div>
          <h3>物资核验信息</h3>
          <ps-table :tableData="formData.ingredient_data" ref="psTableRef" :show-pagination="false" style="width: 100%">
            <ps-column :table-headers="formTableSetting">
              <template #validDate="{ row }"> {{ row.start_valid_date }}-{{ row.end_valid_date }} </template>
              <template #file="{ row }">
                <div class="flex" v-for="(item, index) in row.file" :key="index">
                  <el-image class="w-auto h-100 mr-10px" :src="item" :fit="'contain'" />
                </div>
              </template>
            </ps-column>
          </ps-table>
        </div>
      </el-form>
      <div class="dialog-footer m-t-20px">
        <el-button @click="detailDrawerShow = false"> 关闭 </el-button>
        <!-- <el-button @click="detailDrawerShow = false"> 打印 </el-button> -->
        <!-- <el-button @click="detailDrawerShow = false"> 导出 </el-button> -->
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from "lodash"
import { ref, reactive, onMounted } from "vue"
import { SEARCH_FORM_RECEIPT_ORDER, TABLE_SETTING_RECEIPT_ORDER, RECEIPT_ORDER_TABLE_DATA } from "./constants"
import useTableHeightHook from "@/hooks/useTableHeight"
import { apiReceivingNoteList, apiReceivingNoteDetailList } from "@/api/document/index"
import to from "await-to-js"
import { ElMessage } from "element-plus"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// table数据
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_RECEIPT_ORDER))
const loading = ref(false)
const tableData = ref<any>([])
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_RECEIPT_ORDER)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

const detailDrawerShow = ref(false)
const formData = ref<any>({})

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}

// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiReceivingNoteList({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results.map((item: any) => {
      let arr: any = []
      item.check_accounts.map((acc: any) => {
        arr.push(acc.username)
      })
      item.account_name = arr.join("、")
      return item
    })
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 详情
const getDetailList = async (data: any) => {
  tableLoading.value = true
  const [err, res] = await to(
    apiReceivingNoteDetailList({
      id: data.id
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    formData.value = res.data
  } else {
    ElMessage.error(res.msg)
  }
}

onMounted(() => {
  getDataList()
})

// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 改变页面
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

const formTableSetting = [
  {
    label: "物资名称",
    prop: "materials_name",
    align: "center"
  },
  {
    label: "收货数量",
    prop: "receive_count",
    align: "center"
  },
  {
    label: "规格",
    prop: "specification_record"
  },
  {
    label: "单价",
    prop: "unit_price",
    prefix: "￥",
    align: "center",
    type: "price"
  },
  {
    label: "合计金额",
    prop: "total_price",
    prefix: "￥",
    type: "price",
    align: "center"
  },
  {
    label: "保质期",
    prop: "valid_date",
    align: "center",
    slot: "validDate"
  }
]
const showDetail = (data: any) => {
  detailDrawerShow.value = true
  getDetailList(data)
}
</script>

<style lang="scss" scoped>
.receipt-order {
  .divider {
    border-bottom: 1px solid #dcdfe6;
  }
  .bg-f2 {
    padding: 20px;
    border-radius: 10px;
    background-color: #f2f2f2;
  }
  .img-box-wrap {
    display: flex;
    .title {
      flex-shrink: 0;
      width: 80px;
    }
    .img-wrap {
      display: flex;
      flex-wrap: wrap;
    }
  }
  // .car-img{
  //   height: 150px;
  //   el-image{
  //     height: 100%;
  //   }
  // }
}
</style>
