<template>
  <div class="container-wrapper pl-20px pr-20px">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="flex items-center" />
        <div class="table-button">
          <!--v-permission="['background_fund_supervision.channel_canteen_management.pest_control_record_export']"-->
          <el-button type="primary" @click="goToExport">导出</el-button>
          <el-button type="primary" class="ps-origin-btn-light" @click="gotoPrint">打印</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting" />
        </ps-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from "lodash"
import { ref, reactive, onMounted } from "vue"
import { SEARCH_FORM_APPLICATION_ORDER, TABLE_SETTING_APPLICATION_ORDER } from "./constants"
import useTableHeightHook from "@/hooks/useTableHeight"
import {
  apiBackgroundFundSupervisionSupervisionDataApplicationFormListPost,
  apiBackgroundFundSupervisionSupervisionDataApplicationFormListExportPost
} from "@/api/document/index"
import { apiBackgroundFundSupervisionAppropriationSupplierManageListPost } from "@/api/supplier/index"
import to from "await-to-js"
import { ElMessage } from "element-plus"
import { exportHandle } from "@/utils/exportExcel"
import { setLocalStorage } from "@/utils/storage"
import { useRouter } from "vue-router"
// 路由
const router = useRouter()

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// table数据
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_APPLICATION_ORDER))
const loading = ref(false)
const tableData = ref<any>([])
const tableLoading = ref(false)
const tableSetting = ref(cloneDeep(TABLE_SETTING_APPLICATION_ORDER))
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
const supplierList = ref<Array<any>>() // 供应商列表

// 导出
const printType = "ApplicationOrderExport"

const formatQueryParams = (data: any) => {
  console.log("formatQueryParams", data)
  const params: Record<string, any> = {}
  for (const key in data) {
    if (
      (data[key].value &&
        typeof data[key].value === "string" &&
        data[key].value !== "" &&
        data[key].value !== "全部") ||
      (typeof data[key].value === "number" && data[key].value !== -1) ||
      (Array.isArray(data[key].value) && data[key].value.length > 0)
    ) {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
        params.start_time = data[key].value[0]
        params.end_time = data[key].value[1]
      }
    }
  }
  return params
}

// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionSupervisionDataApplicationFormListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

onMounted(() => {
  getDataList()
  getSupervisionsList()
})

// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 改变页面
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}
// 导出
const goToExport = () => {
  console.log("goToExport")
  const option = {
    type: printType,
    api: apiBackgroundFundSupervisionSupervisionDataApplicationFormListExportPost,
    params: {
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    }
  }
  exportHandle(option)
}
// 打印
const gotoPrint = () => {
  let tabbleSetting = cloneDeep(tableSetting.value)
  tabbleSetting = tabbleSetting.filter((item: any) => item.prop !== "imgs")
  setLocalStorage("print_setting", tabbleSetting)
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: printType,
      print_title: "申请单",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiBackgroundFundSupervisionSupervisionDataApplicationFormListPost",
      params: JSON.stringify({
        ...formatQueryParams(searchFormSetting.value),
        page: 1,
        page_size: 9999
      })
    }
  })
  window.open(href, "_blank")
}

// 获取供应商列表
const getSupervisionsList = async () => {
  const [err, res]: any[] = await to(apiBackgroundFundSupervisionAppropriationSupplierManageListPost({ only_enter: 0 }))
  if (err) {
    ElMessage.error(err.message)
    return
  }
  if (res && res.code === 0) {
    let data = res.data || {}
    let results = data.results || []
    supplierList.value = results || []
    // searchFormSetting.value.supplier_manage_id.dataList = cloneDeep(results)
  } else {
    ElMessage.error(res.msg)
  }
}
</script>

<style scoped></style>
