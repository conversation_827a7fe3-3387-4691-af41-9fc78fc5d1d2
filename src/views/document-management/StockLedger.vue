<template>
  <div class="supplier-approval-container">
    <el-radio-group v-model="tabPosition">
      <el-radio-button
        value="InventoryBalance"
        v-permission="['background_fund_supervision.supervision_data.inventory_balance_list']"
        >库存余额表</el-radio-button
      >
      <el-radio-button value="InventoryChange" v-permission="['no_permissions']">库存变动记录</el-radio-button>
    </el-radio-group>
    <div class="m-t-20px">
      <inventory-balance v-if="tabPosition === 'InventoryBalance'" />
      <!-- <inventory-change v-if="tabPosition === 'InventoryChange'" /> -->
    </div>
  </div>
</template>
<script lang="ts" setup>
import { InventoryBalance, InventoryChange } from "./components/index"
import { ref } from "vue"

const tabPosition = ref("InventoryBalance")
</script>
<style lang="scss" scoped>
.supplier-approval-container {
  padding: 0 20px;
}
</style>
