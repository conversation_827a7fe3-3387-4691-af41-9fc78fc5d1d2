<template>
  <div class="container-wrapper pl-20px pr-20px">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper pt-20px" ref="tableWrapperRef">
      <div class="flex flex-justify-start flex-item-center mb-10px pr-20px pl-20px">
        <div class="flex flex-item-center">
          <div class="mr-10px">出库总金额：￥3000.00</div>
          <div>入库总金额：￥800.00</div>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operation="{ row }">
              <el-button plain link size="small" type="primary" @click="showDetail(row)"> 详情 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>

    <!-- 详情 -->
    <el-drawer
      v-model="detailDrawerShow"
      title="详情"
      :direction="'rtl'"
      :show-close="false"
      class="ps-drawer"
      size="40%"
    >
      <el-form :model="formData" label-width="auto" label-position="top">
        <el-form-item>
          <span>所属学校：</span>
          {{ formData.organization_name }}
        </el-form-item>
        <el-form-item>
          <span>所属仓库：</span>
          {{ formData.warehouse_name }}
        </el-form-item>
        <el-form-item>
          <span>单据编号：</span>
          {{ formData.trade_no }}
        </el-form-item>
        <el-form-item>
          <span>经手人：</span>
          {{ formData.account_name }}
        </el-form-item>
        <el-form-item>
          <div class="flex flex-items-start">
            <span>人脸信息：</span>
            <el-image class="w-auto h-100" :src="formData.faceInfo" :fit="'contain'" />
          </div>
        </el-form-item>
        <el-form-item>
          <span>制单人：</span>
          {{ formData.documenter }}
        </el-form-item>
        <el-form-item>
          <span>审核人：</span>
          {{ formData.exit_account_name }}
        </el-form-item>
        <el-form-item>
          <span>操作日期：</span>
          {{ formData.create_time }}
        </el-form-item>
        <el-form-item>
          <span>创建时间：</span>
          {{ formData.create_time }}
        </el-form-item>
        <el-form-item label="物资信息：">
          <ps-table :tableData="formData.info" ref="psTableRef" :show-pagination="false" style="width: 100%">
            <ps-column :table-headers="formTableSetting" />
          </ps-table>
        </el-form-item>
      </el-form>
      <div class="dialog-footer m-t-20px">
        <el-button @click="detailDrawerShow = false"> 关闭 </el-button>
        <el-button @click="detailDrawerShow = false"> 打印 </el-button>
        <el-button @click="detailDrawerShow = false"> 导出 </el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from "lodash"
import { ref, reactive, onMounted } from "vue"
import {
  SEARCH_FORM_INBOUND_AND_OUTBOUND_ORDERS,
  TABLE_SETTING_INBOUND_AND_OUTBOUND_ORDERS,
  INBOUND_AND_OUT_BOUND_ORDER_TABLE_DATA
} from "./constants"
import useTableHeightHook from "@/hooks/useTableHeight"
import { apiInoutInfoList } from "@/api/document/index"
import to from "await-to-js"
import { ElMessage } from "element-plus"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef)

// table数据
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_INBOUND_AND_OUTBOUND_ORDERS))
const loading = ref(false)
const tableData = ref<any>([])
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_INBOUND_AND_OUTBOUND_ORDERS)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

const detailDrawerShow = ref(false)
const formData = ref<any>({})

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}

// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiInoutInfoList({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

onMounted(() => {
  getDataList()
})

// 搜索
const changeSearch = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 改变页面
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

const formTableSetting = [
  {
    label: "物资名称",
    prop: "name"
  },
  {
    label: "出库数量",
    prop: "number"
  },
  {
    label: "规格",
    prop: "specification"
  },
  {
    label: "成本价",
    prop: "purchasePrice"
  },
  {
    label: "合计金额",
    prop: "totalCount"
  },
  {
    label: "保质期",
    prop: "shelfLife"
  },
  {
    label: "供应商",
    prop: "supplier"
  }
]
const showDetail = (data: any) => {
  formData.value = data
  detailDrawerShow.value = true
}
</script>

<style scoped></style>
