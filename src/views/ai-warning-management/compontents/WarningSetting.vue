<template>
  <div class="warning-setting container-wrapper" v-loading="isLoading">
    <el-button @click="handlerSetting('edit')" type="primary" class="right-btn mb-10px" v-if="!isEdit">
      编辑
    </el-button>
    <div class="title">
      <span>预警配置</span>
      <span class="ps-red-txt m-l-10px">预警事项数量达到阈值后，进行提醒</span>
    </div>
    <div>
      <div class="setting-tag m-l-66px m-t-10px" v-for="(item, index) in warnSetting" :key="index">
        <div class="flex items-center">
          <div class="w-130px text-right">{{ item.name }}</div>
          <el-input
            v-model="item.num"
            type="number"
            :min="1"
            :max="9999"
            style="width: 100px"
            class="m-l-8px"
            :disabled="!isEdit"
          />
          <div class="m-l-8px">条</div>
        </div>
        <div class="flex items-center">
          <div class="w-130px text-right">通知类型：</div>
          <el-checkbox class="m-l-8px" v-model="item.notice" :disabled="!isEdit">站内通知</el-checkbox>
        </div>
      </div>
    </div>
    <div class="m-t-20px" v-if="isEdit">
      <el-button class="ps-origin-btn-light" @click="handlerSetting('cancle')" type="primary"> 取消 </el-button>
      <el-button @click="handlerSetting('save')" type="primary"> 保存 </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue"
import { cloneDeep } from "lodash"
import {
  apiBackgroundFundSupervisionWarnDispositionWarnSettingEditPost,
  apiBackgroundFundSupervisionWarnDispositionWarnSettingDetailPost
} from "@/api/warning"
import to from "await-to-js"
import { ElMessage } from "element-plus"

type settingForm = {
  key?: string
  name: string
  num: string | number
  notice: boolean
}
interface warnSettingType {
  all_warn_thresholds: settingForm
  rate_warn_num: settingForm
  raw_material_warn_num: settingForm
  material_risk_warn_num: settingForm
  // procurement_warn_num: settingForm
  documents_warn_num: settingForm
  contract_warn_num: settingForm
}
class WarnSetting implements warnSettingType {
  [key: string]: settingForm

  all_warn_thresholds: settingForm
  rate_warn_num: settingForm
  raw_material_warn_num: settingForm
  material_risk_warn_num: settingForm
  // procurement_warn_num: settingForm
  documents_warn_num: settingForm
  contract_warn_num: settingForm

  constructor() {
    this.all_warn_thresholds = { key: "", name: "", num: "", notice: false }
    this.rate_warn_num = { key: "", name: "", num: "", notice: false }
    this.raw_material_warn_num = { key: "", name: "", num: "", notice: false }
    this.material_risk_warn_num = { key: "", name: "", num: "", notice: false }
    // this.procurement_warn_num = { key: "", name: "", num: "", notice: false }
    this.documents_warn_num = { key: "", name: "", num: "", notice: false }
    this.contract_warn_num = { key: "", name: "", num: "", notice: false }
  }
}

const isEdit = ref(false)

// 预警设置
const warnSetting = ref<Array<settingForm>>([
  {
    // 总预警
    key: "all_warn_thresholds",
    name: "总预警数量阈值：",
    num: "0",
    notice: false
  },
  {
    // 利润率
    key: "rate_warn_num",
    name: "利润率预警数：",
    num: "0",
    notice: false
  },
  {
    // 原材料支出
    key: "raw_material_warn_num",
    name: "原材料支出预警数：",
    num: "0",
    notice: false
  },
  {
    // 原材料风险
    key: "material_risk_warn_num",
    name: "物资风险预警数：",
    num: "0",
    notice: false
  },
  // {
  //   // 采购质量预警数
  //   key: "procurement_warn_num",
  //   name: "采购质量预警数：",
  //   num: "0",
  //   notice: false
  // },
  {
    // 证件风险预警
    key: "documents_warn_num",
    name: "证件风险预警数：",
    num: "0",
    notice: false
  },
  {
    // 合同风险预警
    key: "contract_warn_num",
    name: "合同风险预警数：",
    num: "0",
    notice: false
  }
])
// 设置
const handlerSetting = (type: string) => {
  switch (type) {
    case "edit":
      isEdit.value = true
      break
    case "save":
      saveSetting()
      isEdit.value = false
      break
    case "cancle":
      isEdit.value = false
      break
  }
}

// 获取设置
const isLoading = ref(false)
const getSetting = async () => {
  isLoading.value = true
  const [err, res]: [any, any] = await to(apiBackgroundFundSupervisionWarnDispositionWarnSettingDetailPost({}))
  if (err) {
    return ElMessage.error(err.msg)
  }
  if (res.code === 0) {
    warnSetting.value.forEach((item: settingForm) => {
      if (item.key) {
        item.num = res.data[item.key].num.toString()
        item.notice = res.data[item.key].notice
      }
    })
    isLoading.value = false
  } else {
    ElMessage.error(res.msg)
  }
}
onMounted(() => {
  getSetting()
})

// 保存设置
const saveSetting = async () => {
  let params = new WarnSetting()
  warnSetting.value.forEach((item: settingForm) => {
    if (item.key) {
      params[item.key] = cloneDeep(item)
    }
  })
  const [err, res]: [any, any] = await to(
    apiBackgroundFundSupervisionWarnDispositionWarnSettingEditPost({
      warn_setting: params
    })
  )
  if (err) {
    return ElMessage.error(err.msg)
  }
  if (res.code === 0) {
    ElMessage.success("保存成功")
    getSetting()
  } else {
    ElMessage.error(res.msg)
  }
}
</script>
<style lang="scss" scoped>
.warning-setting {
  padding: 24px;
  background-color: #fff;
  .title {
    position: relative;
    .right-btn {
      position: absolute;
      right: 0;
      bottom: 0;
    }
  }
  .setting-tag {
    background: var(--el-color-primary-light-3);
    padding: 10px;
  }
  .el-popper {
    max-width: 300px;
  }
}
</style>
