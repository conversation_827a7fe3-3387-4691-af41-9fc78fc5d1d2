<template>
  <div class="purchase-detail container-wrapper">
    <el-drawer
      v-model="dialogFormVisible"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div>
        <div class="flex">
          <div class="tag-title">所属组织</div>
          <div class="tag-content">{{ ruleForm.organization }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">所属仓库</div>
          <div class="tag-content">{{ ruleForm.warehouse }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">操作类型</div>
          <div class="tag-content">{{ ruleForm.type }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">物资名称</div>
          <div class="tag-content">{{ ruleForm.name }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">预警情况</div>
          <div class="tag-content">{{ ruleForm.warning_status }}</div>
        </div>
      </div>
      <!--物资信息-->
      <div class="table-content">
        <div class="m-t-20px m-b-10px">处理结果：</div>
        <div class="flex">
          <div class="tag-title">是否处理</div>
          <div class="tag-content">{{ ruleForm.isHandler ? "是" : "否" }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">操作人</div>
          <div class="tag-content">{{ ruleForm.opertor }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">处理说明</div>
          <div class="tag-content">{{ ruleForm.remark }}</div>
        </div>
        <div class="flex">
          <div class="tag-title">附件</div>
          <div class="tag-content">
            <img :src="ruleForm.imgs" alt="" class="w-200px h-100px" />
          </div>
        </div>
      </div>
      <!--拒绝弹窗-->
      <el-dialog v-model="dialogRejectVisible" width="400" title="提示">
        <div>
          <div>确认驳回改处理结果？</div>
          <div class="flex m-t-20px">
            <el-form :rules="rules" :model="ruleForm" ref="ruleFormRef" label-position="top">
              <el-form-item label="驳回原因：" prop="newRemark">
                <el-input
                  v-model="ruleForm.newRemark"
                  placeholder="请输入驳回原因"
                  type="textarea"
                  maxlength="100"
                  show-word-limit
                  :rows="6"
                  clearable
                  style="width: 370px"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>
        <template #footer>
          <div class="">
            <el-button @click="dialogRejectVisible = false">取消</el-button>
            <el-button type="primary" @click="handlerRejectConfirm"> 确认 </el-button>
          </div>
        </template>
      </el-dialog>
      <div class="dialog-footer m-t-20px">
        <el-button type="primary" class="ps-origin-btn-plain" @click="closeDialog" v-loading="confirmLoading">
          取消
        </el-button>
        <el-button type="primary" @click="confirmDialog" v-loading="confirmLoading"> 已处理 </el-button>
        <el-button type="primary" class="ps-red-btn" @click="rejectDialog" v-loading="confirmLoading"> 驳回 </el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue"
import type { MaterialForm } from "../index.d"
import { confirm } from "@/utils/message"

const props = defineProps({
  title: {
    type: String,
    default: "详情"
  },
  width: {
    type: String,
    default: "448px"
  },
  isShow: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: "detail"
  },
  orgId: {
    type: Number,
    default: -1
  },
  alreadyChooseIds: {
    type: Array,
    default: () => {
      return []
    }
  },
  // 选择节点数据
  organizationData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const ruleFormRef = ref()
const dialogFormVisible = ref(false)
const confirmLoading = ref(false)
const ruleForm = reactive<MaterialForm>({
  organization: "田家实验中学",
  warehouse: "食堂仓库",
  type: "领用出库",
  name: "水蜜桃",
  warning_status: "检测到高风险物资入库",
  isHandler: true,
  opertor: "张三",
  remark: "针对本次反馈的问题，这边已经发现问题所在，并保证相同问题不会再发生",
  imgs: "https://www.uviewui.com/index/banner_1920x1080.png",
  newRemark: ""
})
// 规则
const rules = reactive({
  newRemark: [
    {
      required: true,
      message: "请输入驳回原因",
      trigger: "blur"
    }
  ]
})
// 驳回弹窗
const dialogRejectVisible = ref(false)

// 弹窗确认
const confirmDialog = () => {
  confirm({ content: "确定修改为已处理状态？" }, () => {
    emit("confirmDialog")
  })
}

// 弹窗关闭
const closeDialog = () => {
  emit("cancelDialog")
}

// 驳回弹窗
const rejectDialog = () => {
  dialogRejectVisible.value = true
}

const handlerRejectConfirm = () => {
  // 确认驳回
  console.log("ruleForm", ruleForm)
  if (ruleFormRef.value) {
    ruleFormRef.value.validate((valid: any) => {
      if (valid) {
        // 确认驳回
        console.log("ruleForm", ruleForm)
        dialogRejectVisible.value = false
        confirmDialog()
      } else {
        return false
      }
    })
  }
}

watch(
  () => ({
    isShow: props.isShow
  }),
  (newValue) => {
    console.log("watch dialog", newValue)
    dialogFormVisible.value = newValue.isShow
    if (newValue.isShow) {
      // initData()
    }
  }
)
defineExpose({})
</script>
<style lang="scss" scoped>
.container-wrapper .table-content {
  padding: 0 !important;
}
.purchase-detail {
  .tag-title {
    width: 120px;
    min-width: 120px;
    background: #f4f6fc;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-left: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .tag-content {
    min-width: 300px;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .border-bottom {
    border-bottom: 1px solid #e9ecf1;
  }
}
</style>
