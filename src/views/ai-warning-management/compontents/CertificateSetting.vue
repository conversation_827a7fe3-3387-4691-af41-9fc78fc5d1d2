<template>
  <div class="operate-warning container-wrapper">
    <div ref="searchRef" />
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="ps-flex col-center">
          <div class="flex m-l-10px">
            <div>已选：{{ alreadyProcess }}</div>
            <div class="m-l-20px">
              未选：<span class="color-[#FF5656]">{{ noProcess }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="false"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operationNew="{ row }">
              <el-switch v-model="row.enable" @change="changeStatus(row)" />
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted, watch } from "vue"
import { TABLE_SETTING_CERTIFICATION_SETTING } from "../constants"
import { apiCardGroupListPost } from "@/api/user"
import { ElMessage } from "element-plus"
import useTableHeightHook from "@/hooks/useTableHeight"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { useRouter } from "vue-router"
import icTestZhizhao from "@/assets/test/ic_test_zhizhao.png"
// import { certificateSettingData } from "../data"
import { getTestData } from "@/utils/index"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef, 52)
const router = useRouter()

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])
// 已处理未处理
const alreadyProcess = ref(0)
const noProcess = ref(0)

// table数据
const tableData = ref([])
const loading = ref(false)
// const searchFormSetting = ref(SEARCH_FORM_CERTIFICATE_WARNING)
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_CERTIFICATION_SETTING)

// 获取数据
import {
  apiBackgroundFundSupervisionWarnDispositionContractWarnListPost,
  apiBackgroundFundSupervisionWarnDispositionContractWarnEditPost
} from "@/api/warning"
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(apiBackgroundFundSupervisionWarnDispositionContractWarnListPost())
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = cloneDeep(res.data.data || [])
  } else {
    ElMessage.error(res.msg)
  }
}
onMounted(() => {
  getDataList()
})

// 改变状态
const changeStatus = async (row: any) => {
  const [err, res] = await to(
    apiBackgroundFundSupervisionWarnDispositionContractWarnEditPost({
      id: row.id,
      enable: row.enable
    })
  )
  if (err) {
    return
  }
  if (res && res.code === 0) {
    ElMessage.success("修改状态成功")
    getDataList()
  } else {
    ElMessage.error(res.msg)
  }
}

// 设置已处理未处理
const setStatus = (newlist: Array<any>) => {
  const list = newlist
  alreadyProcess.value = 0
  noProcess.value = 0
  console.log("list", list)
  list.forEach((item: any) => {
    if (item.enable) {
      alreadyProcess.value += 1
    } else {
      noProcess.value += 1
    }
  })
}

watch(
  () => tableData.value,
  (newValue) => {
    if (newValue) {
      setStatus(newValue)
    }
  },
  { deep: true }
)
</script>
<style lang="scss">
.operate-warning {
  .el-popper {
    max-width: 300px;
  }
}
</style>
