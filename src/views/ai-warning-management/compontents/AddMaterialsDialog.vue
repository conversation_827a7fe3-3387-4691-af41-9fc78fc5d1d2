<template>
  <div class="ai-detail container-wrapper">
    <el-drawer
      v-model="dialogFormVisible"
      :title="title"
      direction="rtl"
      class="ps-drawer"
      :size="width"
      :destroy-on-close="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <div>
        <el-form label-position="left" class="m-t-20px" ref="ruleFormRef" :rules="rules" :model="ruleForm">
          <el-form-item label="物资类型" prop="material_type">
            <el-select v-model="ruleForm.material_type" placeholder="请选择物资类型">
              <el-option v-for="item in TYPE_MATERIALS" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="物资名称" prop="name">
            <el-input type="text" v-model="ruleForm.name" placeholder="请输入物资名称" maxlength="20" />
          </el-form-item>
          <el-form-item label="预警类型" prop="warn_type">
            <el-radio-group v-model="ruleForm.warn_type">
              <el-radio label="high_risk">高风险</el-radio>
              <el-radio label="disable">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <div class="dialog-footer m-t-20px">
        <el-button type="primary" @click="closeDialog" class="ps-origin-btn-light"> 取消 </el-button>
        <el-button type="primary" @click="confirmDialog" v-loading="confirmLoading">保存</el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue"
import { TYPE_MATERIALS } from "../constants"

const props = defineProps({
  title: {
    type: String,
    default: "添加预警物资"
  },
  width: {
    type: String,
    default: "468px"
  },
  isShow: {
    type: Boolean,
    default: false
  },
  type: {
    //  detail  详情  add  添加  edit  忽略
    type: String,
    default: "add"
  },
  orgId: {
    type: Number,
    default: -1
  },
  dialogData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const emit = defineEmits([
  "confirmDialog", // 弹窗确认
  "cancelDialog" // 弹窗确认
])
const dialogFormVisible = ref(false)
const confirmLoading = ref(false)
const ruleForm = reactive<any>({
  material_type: "",
  name: "",
  warn_type: "high_risk"
})
const ruleFormRef = ref()
// 规则验证
const rules = reactive({
  material_type: [
    {
      required: true,
      message: "请选择物资类型",
      trigger: "change"
    }
  ],
  name: [
    {
      required: true,
      message: "请输入物资名称",
      trigger: "blur"
    }
  ],
  warn_type: [
    {
      required: true,
      message: "请选择预警类型",
      trigger: "change"
    }
  ]
})

// 弹窗确认
const confirmDialog = () => {
  if (ruleFormRef.value) {
    ruleFormRef.value.validate(async (valid: any) => {
      if (valid) {
        emit("confirmDialog", ruleForm, props.type)
        dialogFormVisible.value = false
      }
    })
  }
}

// 弹窗关闭
const closeDialog = () => {
  dialogFormVisible.value = false
  ruleForm.material_type = ""
  ruleForm.name = ""
  ruleForm.warn_type = "high_risk"
  if (ruleFormRef.value) {
    ruleFormRef.value.clearValidate()
  }
  emit("cancelDialog")
}

// 设置弹窗数据
const setDialogData = (data: any) => {
  console.log("setDialogData", data)
  ruleForm.material_type = data.material_type
  ruleForm.name = data.name
  ruleForm.warn_type = data.warn_type
}

watch(
  () => ({
    isShow: props.isShow
  }),
  (newValue) => {
    console.log("watch dialog", newValue)
    dialogFormVisible.value = newValue.isShow
    if (newValue.isShow) {
      // initData()
      console.log("show ", ruleForm)
    }
  }
)
defineExpose({ setDialogData })
</script>

<style lang="scss" scoped>
.container-wrapper .table-content {
  padding: 0 !important;
}
.ai-detail {
  .tag-title {
    width: 120px;
    min-width: 120px;
    height: 40px;
    line-height: 40px;
    background: #f4f6fc;
    font-size: 14px;
    padding-left: 20px;
    border-top: 1px solid #e9ecf1;
    border-left: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .tag-content {
    min-width: 880px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    padding-left: 20px;
    border-top: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }
  .border-bottom {
    border-bottom: 1px solid #e9ecf1;
  }
}
</style>
