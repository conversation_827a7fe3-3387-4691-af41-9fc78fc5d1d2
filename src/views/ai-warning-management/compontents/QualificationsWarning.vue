<template>
  <div class="operate-warning container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="ps-flex col-center">
          <el-tooltip effect="dark" placement="top-start" content="经营类预警可前往监管中心进行配置">
            <img :src="IcQuestionBlack" class="w-32px h-32px" />
          </el-tooltip>
          <!-- <div class="flex m-l-10px">
            <div>已处理：8</div>
            <div class="m-l-20px">待处理：<span class="color-[#FF5656]">4</span></div>
          </div> -->
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operationNew="">
              <el-button plain link size="small" @click="gotoDetail" type="primary"> 查看数据 </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import { SEARCH_FORM_QUALIFICATION_WARNING, TABLE_SETTING_QUALIFICATION_WARNING } from "../constants"
import { apiBackgroundFundSupervisionWarnManageWarningMessageListPost } from "@/api/warning"
import { ElMessage } from "element-plus"
import IcQuestionBlack from "@/assets/layouts/ic_question_black.png"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { useRouter } from "vue-router"
import { qualificationsWarningData } from "../data"
import { getTestData, getSearchTestData } from "@/utils/index"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef, 52)
const router = useRouter()

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// table数据
const tableData = ref([])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_QUALIFICATION_WARNING))
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_QUALIFICATION_WARNING)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value && value !== "全部") {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}
// 搜索
const changeSearch = async () => {
  const params = formatQueryParams(searchFormSetting.value)
  console.log("changeSearch", params)
  const searchList: any[] = []
  if (params.organization_name && params.organization_name !== "全部") {
    searchList.push({ key: "organization_name", content: params.organization_name })
  }
  if (searchList.length > 0) {
    const list: any[] = await getSearchTestData(
      tableData,
      qualificationsWarningData,
      searchList,
      "",
      tableLoading,
      pageConfig.currentPage
    )
    pageConfig.total = list?.length || 0
  } else {
    getTestData(tableData, qualificationsWarningData, tableLoading, pageConfig.currentPage)
    pageConfig.total = qualificationsWarningData?.length || 0
  }
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getTestData(tableData, qualificationsWarningData, tableLoading, pageConfig.currentPage)
  pageConfig.total = qualificationsWarningData?.length || 0
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  changeSearch()
}

// 跳转详情
const gotoDetail = () => {
  router.push({ path: "/business_report/income_expense_statistics" })
}
onMounted(() => {
  getTestData(tableData, qualificationsWarningData, tableLoading, pageConfig.currentPage)
  pageConfig.total = qualificationsWarningData?.length || 0
  getDataList()
})
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionWarnManageWarningMessageListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize,
      classify: "procurement_quality_warn"
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
const gotoPrint = () => {
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: "ert",
      print_title: "收支统计表",
      result_key: "result", // 返回的数据处理的data keys
      api: "apiBackgroundReportCenterDataReportPersonMealListPost", // 请求的api
      table_setting: JSON.stringify(tableSetting),
      params: JSON.stringify({
        page: 1,
        page_size: 10
      })
    }
  })
  window.open(href, "_blank")
}
</script>
<style lang="scss">
.operate-warning {
  .el-popper {
    max-width: 300px;
  }
}
</style>
