<template>
  <div class="operate-warning container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="ps-flex col-center">
          <el-tooltip effect="dark" placement="top-start" content="经营类预警可前往监管中心进行配置">
            <img :src="IcQuestionBlack" class="w-32px h-32px" />
          </el-tooltip>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #warnText="{ row }">
              <div v-if="row.file_type_alias === '健康证'">
                {{ row.warn_text }}<span class="color-[#ff5656]">（ {{ row.account_name }} ）</span>
              </div>
              <div v-else>{{ row.warn_text }}</div>
            </template>
            <template #timeRange="{ row }">
              <div v-if="row.start_time">{{ row.start_time }} ~ {{ row.end_time }}</div>
              <div v-else>{{ row.end_time }}</div>
            </template>
            <template #image="{ row }">
              <el-button plain link size="small" @click="handlerShowPhoto(row.image_json)" type="primary">
                查看
              </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import { SEARCH_FORM_CERTIFICATE_WARNING, TABLE_SETTING_CERTIFICATE_WARNING } from "../constants"
import { apiBackgroundFundSupervisionWarnDispositionDocContWarnListPost } from "@/api/warning"
import { ElMessage } from "element-plus"
import IcQuestionBlack from "@/assets/layouts/ic_question_black.png"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef, 52)
// const router = useRouter()

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// table数据
const tableData = ref([])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_CERTIFICATE_WARNING))
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_CERTIFICATE_WARNING)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 已处理未处理
const alreadyProcess = ref()
const noProcess = ref("0")

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value && value !== "全部") {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}
// 搜索
const changeSearch = async () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

onMounted(() => {
  getDataList()
})
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionWarnDispositionDocContWarnListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}

// 查看照片
const handlerShowPhoto = (row: any) => {
  let imgUrl = "" as string
  if (row.length) {
    imgUrl = row[0].img
  } else {
    imgUrl = row.img
  }
  if (imgUrl) {
    imageList.value = [imgUrl]
    imageVisible.value = true
  } else {
    ElMessage.error("暂无图片")
  }
}
</script>
<style lang="scss">
.operate-warning {
  .el-popper {
    max-width: 300px;
  }
}
</style>
