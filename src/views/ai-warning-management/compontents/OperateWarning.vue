<template>
  <div class="operate-warning container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="ps-flex col-center">
          <el-tooltip effect="dark" placement="top-start" content="经营类预警可前往监管中心进行配置">
            <img :src="IcQuestionBlack" class="w-32px h-32px" />
          </el-tooltip>
          <!-- <div class="flex m-l-10px">
            <div>待核实：8</div>
            <div class="m-l-20px">核实中：4</div>
            <div class="m-l-20px">已核实：4</div>
            <div class="m-l-20px">已忽略：2</div>
          </div> -->
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #accountType="{ row }">
              <span>{{ row.account_type }}</span>
              <span class="color-[#ff5656]">{{ row.val ? "（" + row.val + "%）" : "" }}</span>
            </template>
            <template #warnDetail="{ row }">
              <span
                >{{ row.warn_detail }}
                <span class="color-[#FF5656]" v-if="row.warn_val">{{ "(" + row.warn_val + "%)" }} </span></span
              >
            </template>
            <template #operationNew="{ row }">
              <el-button plain link size="small" @click="gotoDetail(row)" type="primary" :disabled="isAllowGo">
                查看数据
              </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!-- 图片预览-->
    <image-preview v-model="imageVisible" :imgs="imageList" />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted, computed } from "vue"
import { SEARCH_FORM_OPERATE_WARNING, TABLE_SETTING_OPERATE_WARNING } from "../constants"
import { apiBackgroundFundSupervisionWarnManageWarningMessageListPost } from "@/api/warning"
import { ElMessage } from "element-plus"
import IcQuestionBlack from "@/assets/layouts/ic_question_black.png"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import { useRouter } from "vue-router"
import useChannelTreeDataHook from "@/hooks/useChannelTreeData"
import { useUserStore } from "@/store/modules/user"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef, 52)
const router = useRouter()
const useChannelTree = useChannelTreeDataHook()
const userStore = useUserStore()
const isAllowGo = computed(() => {
  const flag = !userStore.getRoles.includes("background_fund_supervision.business_report.income_statistics_list")
  console.log("flag", !flag)
  return flag
})

// 图片预览
const imageVisible = ref(false)
const imageList = ref<any[]>([])

// table数据
const tableData = ref([])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_OPERATE_WARNING))
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_OPERATE_WARNING)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "" && data[key].value !== null && data[key].value !== "全部") {
      if (key === "warn_type_list") {
        params[key] = [data[key].value]
      } else if (key !== "selecttime") {
        const value = data[key].value
        if (value && value !== "全部") {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}
// 搜索
const changeSearch = async () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  changeSearch()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 跳转详情
const gotoDetail = (row: any) => {
  let warnTime = row.warn_time || ""
  let orgsId = row.org_id || ""
  router.push({
    path: "/business_report/income_expense_statistics",
    query: {
      type: "operate",
      warnTime,
      orgsId
    }
  })
}
onMounted(() => {
  getDataList()
})
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  let channelId = null
  if (searchFormSetting.value.org_id.value) {
    const treeData = useChannelTree.treeData
    channelId = useChannelTree.getParantBindSuperVision(searchFormSetting.value.org_id.value, treeData.value)
  } else {
    channelId = userStore.userInfo.supervision_channel_id
  }

  console.log("channelId", channelId)
  const [err, res] = await to(
    apiBackgroundFundSupervisionWarnManageWarningMessageListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize,
      classify: "business_warn",
      channel_id: channelId
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
// const gotoPrint = () => {
//   const { href } = router.resolve({
//     path: "/print",
//     query: {
//       print_type: "ert",
//       print_title: "经营收支统计表",
//       result_key: "result", // 返回的数据处理的data keys
//       api: "apiBackgroundReportCenterDataReportPersonMealListPost", // 请求的api
//       table_setting: JSON.stringify(tableSetting),
//       params: JSON.stringify({
//         page: 1,
//         page_size: 10
//       })
//     }
//   })
//   window.open(href, "_blank")
// }
</script>
<style lang="scss">
.operate-warning {
  .el-popper {
    max-width: 300px;
  }
}
</style>
