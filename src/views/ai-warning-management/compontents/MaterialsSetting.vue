<template>
  <div class="materials-setting container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="ps-flex col-center" />
        <div class="table-button">
          <el-button type="primary" @click="handlerEdit({}, 'add')">添加预警物资</el-button>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting">
            <template #operationNew="{ row }">
              <el-button plain link @click="handlerEdit(row, 'edit')" type="primary"> 编辑 </el-button>
              <el-button plain link @click="handlerEdit(row, 'delete')" color="#ff5656" type="primary">
                删除
              </el-button>
            </template>
          </ps-column>
        </ps-table>
      </div>
    </div>
    <!--弹窗-->
    <add-materials-dialog
      ref="addMaterialsDialogRef"
      :is-show="isShowMaterialsDialog"
      :title="dialogTitle"
      :type="dialogType"
      @cancel-dialog="handlerClose"
      @confirm-dialog="handlerConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import { SEARCH_FORM_MATERIALS_SETTING, TABLE_SETTING_MATERIALS_SETTING } from "../constants"
import { ElMessage } from "element-plus"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"
import AddMaterialsDialog from "./AddMaterialsDialog.vue"
import { confirm } from "@/utils/message"
const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef, 52)

// table数据
const tableData = ref()
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_MATERIALS_SETTING))
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_MATERIALS_SETTING)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 弹窗数据
const addMaterialsDialogRef = ref()
const isShowMaterialsDialog = ref(false)
const dialogTitle = ref("添加预警物资") // 弹窗标题
const dialogType = ref("add") // 弹窗类型

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "" && data[key].value !== "全部") {
      const value = data[key].value
      if (value) {
        params[key] = data[key].value
      }
    }
  }
  return params
}

// 获取列表
import { apiBackgroundFundSupervisionWarnDispositionMaterialWarnListPost } from "@/api/warning"
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionWarnDispositionMaterialWarnListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
onMounted(() => {
  getDataList()
})

// 搜索
const changeSearch = async () => {
  pageConfig.currentPage = 1
  getDataList()
}

// 重置
const handlerReset = () => {
  changeSearch()
}

// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

// 弹窗取消
const handlerClose = () => {
  isShowMaterialsDialog.value = false
}

// 弹窗确认
import {
  apiBackgroundFundSupervisionWarnDispositionMaterialWarnAddPost,
  apiBackgroundFundSupervisionWarnDispositionMaterialWarnEditPost,
  apiBackgroundFundSupervisionWarnDispositionMaterialWarnDeletePost
} from "@/api/warning"
const handlerConfirm = (data: any, type: string) => {
  console.log("handlerConfirm", cloneDeep(data), type)
  if (type === "add") {
    addDataList(data)
  } else {
    let obj = {
      id: selectId,
      ...data
    }
    editDataList(obj)
  }
}
const addDataList = async (data: any) => {
  const [err, res]: [any, any] = await to(apiBackgroundFundSupervisionWarnDispositionMaterialWarnAddPost({ ...data }))
  if (err) {
    ElMessage.error(err.msg)
    return
  }
  if (res && res.code === 0) {
    ElMessage.success("新增成功")
    getDataList()
  } else {
    ElMessage.error(res.msg)
    return
  }
}
const editDataList = async (data: any) => {
  const [err, res]: [any, any] = await to(apiBackgroundFundSupervisionWarnDispositionMaterialWarnEditPost({ ...data }))
  if (err) {
    ElMessage.error(err.msg)
    return
  }
  if (res && res.code === 0) {
    ElMessage.success("操作成功")
    getDataList()
  } else {
    ElMessage.error(res.msg)
    return
  }
}
const deleteDataList = async (id: number) => {
  const [err, res]: [any, any] = await to(
    apiBackgroundFundSupervisionWarnDispositionMaterialWarnDeletePost({
      id: id
    })
  )
  if (err) {
    ElMessage.error(err.msg)
    return
  }
  if (res && res.code === 0) {
    ElMessage.success("删除成功")
    getDataList()
  } else {
    ElMessage.error(res.msg)
    return
  }
}

// 编辑等操作
let selectId = 0
const handlerEdit = (row: any, type: string) => {
  // handlerShowPhoto(row, "edit")
  console.log("handlerEdit", row, type)
  switch (type) {
    case "add":
      dialogTitle.value = "添加预警物资"
      dialogType.value = "add"
      isShowMaterialsDialog.value = true
      break
    case "edit":
      dialogTitle.value = "编辑预警物资"
      dialogType.value = "edit"
      selectId = row.id
      if (addMaterialsDialogRef.value) {
        addMaterialsDialogRef.value.setDialogData(row)
      }
      isShowMaterialsDialog.value = true
      break
    case "delete":
      confirm({ content: "确定删除该预警物资？" }, () => {
        deleteDataList(row.id)
      })
      break
    default:
      break
  }
}
</script>
<style lang="scss">
.materials-setting {
  .el-popper {
    max-width: 300px;
  }
}
</style>
