<template>
  <div class="operate-warning container-wrapper">
    <div ref="searchRef">
      <search-form
        :form-setting="searchFormSetting"
        @change-search="changeSearch"
        @reset="handlerReset"
        v-loading="loading"
        search-mode="normal"
      />
    </div>
    <div class="table-wrapper" ref="tableWrapperRef">
      <div class="table-header">
        <div class="ps-flex col-center">
          <el-tooltip effect="dark" placement="top-start" content="物资风险预警可前往预警配置功能进行配置">
            <img :src="IcQuestionBlack" class="w-32px h-32px" />
          </el-tooltip>
        </div>
      </div>
      <div class="table-content">
        <ps-table
          :tableData="tableData"
          ref="psTableRef"
          v-loading="tableLoading"
          :show-pagination="true"
          @pagination-change="handleCurrentChange"
          :pageConfig="pageConfig"
          :max-height="maxHeight"
        >
          <ps-column :table-headers="tableSetting" />
        </ps-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js"
import cloneDeep from "lodash/cloneDeep"
import { reactive, ref, onMounted } from "vue"
import { SEARCH_FORM_INCOME_AND_OUTGOING, TABLE_SETTING_INCOME_AND_OUTGOING } from "../constants"
import { apiBackgroundFundSupervisionWarnDispositionMaterialRiskListPost } from "@/api/warning"
import { ElMessage } from "element-plus"
import IcQuestionBlack from "@/assets/layouts/ic_question_black.png"
import useTableHeightHook from "@/hooks/useTableHeight"
import SearchForm from "@/components/SearchForm/index.vue"
import PsTable from "@/components/PsTable/index.vue"
import PsColumn from "@/components/PsColumn/index.vue"

const psTableRef = ref()
const { searchRef, tableWrapperRef, maxHeight } = useTableHeightHook(72, 64, psTableRef, 52)

// table数据
const tableData = ref([])
const loading = ref(false)
const searchFormSetting = ref(cloneDeep(SEARCH_FORM_INCOME_AND_OUTGOING))
const tableLoading = ref(false)
const tableSetting = cloneDeep(TABLE_SETTING_INCOME_AND_OUTGOING)
const pageConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10
})
// 已处理未处理
const alreadyProcess = ref("0")
const noProcess = ref("0")

const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value && value !== "全部" && value.length > 0) {
          params[key] = data[key].value
        }
      } else if (data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}
// 搜索
const changeSearch = async (model: any, type: any, itemType: string) => {
  if (itemType === "channel_org_id") {
    searchFormSetting.value.org_id.value = []
    searchFormSetting.value.org_id.orgsId = type
  }
  pageConfig.currentPage = 1
  getDataList()
}
// 重置
const handlerReset = () => {
  pageConfig.currentPage = 1
  getDataList()
}
// 页码改变
const handleCurrentChange = (page: number, pageSize: number) => {
  pageConfig.currentPage = page
  pageConfig.pageSize = pageSize
  getDataList()
}

onMounted(() => {
  getDataList()
})
// 获取列表
const getDataList = async () => {
  tableLoading.value = true
  const [err, res] = await to(
    apiBackgroundFundSupervisionWarnDispositionMaterialRiskListPost({
      ...formatQueryParams(searchFormSetting.value),
      page: pageConfig.currentPage,
      page_size: pageConfig.pageSize
    })
  )
  tableLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    tableData.value = res.data.results
    pageConfig.total = res.data.count
  } else {
    ElMessage.error(res.msg)
  }
}
</script>
<style lang="scss">
.operate-warning {
  .el-popper {
    max-width: 300px;
  }
}
</style>
