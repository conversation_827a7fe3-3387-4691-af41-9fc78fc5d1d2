import { getPreDate } from "@/utils/date"
import { TYPE_WARNING, TYPE_INVENTORY } from "@/views/organizational-supervision/constants"
import dayjs from "dayjs"

export const TYPE_MATERIALS = [
  {
    value: "cereal",
    label: "谷物及制品"
  },
  {
    value: "potato",
    label: "薯类、淀粉及制品"
  },
  {
    value: "vegetable",
    label: "蔬菜类及制品"
  },
  {
    value: "fruit",
    label: "水果类及制品"
  },
  {
    value: "meat",
    label: "畜肉类及制品、禽肉类及制品"
  },
  {
    value: "shrimp",
    label: "鱼虾蟹贝壳"
  },
  {
    value: "egg",
    label: "蛋类及制品"
  },
  {
    value: "milk",
    label: "乳类及制品"
  },
  {
    value: "beans",
    label: "干豆类及制品"
  },
  {
    value: "nut",
    label: "坚果、种子类"
  },
  {
    value: "flavouring",
    label: "调味品类"
  },
  {
    value: "grease",
    label: "油脂类"
  }
]

export const TYPE_ORGANIZATION = [
  {
    value: "全部",
    label: "全部"
  },
  {
    value: "田家炳实验小学",
    label: "田家炳实验小学"
  },
  {
    value: "华溪实验中学",
    label: "华溪实验中学"
  },

  {
    value: "启元中学",
    label: "启元中学"
  }
]
// 证件类型
export const TYPE_CERTIFICATE = [
  {
    value: "全部",
    label: "全部"
  },
  {
    value: "营业执照",
    label: "营业执照"
  },
  {
    value: "餐饮服务许可证",
    label: "餐饮服务许可证"
  },
  {
    value: "食品经营许可证",
    label: "食品经营许可证"
  },
  {
    value: "卫生许可证",
    label: "卫生许可证"
  },
  {
    value: "消费安全证书",
    label: "消费安全证书"
  },
  {
    value: "税务登记证",
    label: "税务登记证"
  },
  {
    value: "健康证",
    label: "健康证明"
  },
  {
    value: "合同",
    label: "合同"
  }
]

// 经营预警搜索表单设置
export const SEARCH_FORM_OPERATE_WARNING = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "预警时间",
    value: [getPreDate(30, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  org_id: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: "",
    placeholder: "请选择",
    isShowChildOrs: true,
    multiple: false,
    clearable: true,
    collapseTags: true
  },
  warn_type_list: {
    type: "select",
    label: "类型",
    value: "",
    dataList: TYPE_WARNING,
    placeholder: "请选择",
    multiple: false,
    clearable: true,
    collapseTags: true
  }
}
//经营预警表格设置
export const TABLE_SETTING_OPERATE_WARNING = [
  {
    label: "预警时间",
    prop: "create_time",
    "show-overflow-tooltip": true,
    align: "center"
  },
  {
    label: "监管组织",
    prop: "org_name",
    align: "center"
  },
  {
    label: "预警类型",
    prop: "warn_type",
    align: "center"
  },
  {
    label: "预警内容",
    prop: "warn_detail",
    slot: "warnDetail",
    align: "center"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operationNew",
    align: "center",
    width: "180px"
  }
]

// 原材料风险表单设置
export const SEARCH_FORM_INCOME_AND_OUTGOING = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "预警时间",
    value: [getPreDate(30, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  channel_org_id: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: [] as Array<any>,
    placeholder: "请选择",
    isShowChildOrs: true,
    multiple: true,
    clearable: true,
    collapseTags: true
  },
  org_id: {
    type: "canteenOrgs",
    label: "所属组织",
    value: [],
    placeholder: "请选择",
    multiple: true,
    clearable: true,
    collapseTags: true,
    orgsId: []
  },
  name: {
    type: "input",
    label: "物资名称",
    value: "",
    placeholder: "请输入物资名称",
    clearable: true,
    maxlength: 20
  },
  drp_type: {
    type: "select",
    label: "操作类型",
    value: "",
    dataList: TYPE_INVENTORY,
    placeholder: "请选择",
    multiple: false,
    clearable: true,
    collapseTags: true
  }
}
// 原材料风险表格设置
export const TABLE_SETTING_INCOME_AND_OUTGOING = [
  {
    label: "预警时间",
    prop: "create_time",
    align: "center"
  },
  {
    label: "监管组织",
    prop: "bind_org_name",
    align: "center"
  },
  {
    label: "所属组织",
    prop: "organization_name",
    align: "center"
  },
  {
    label: "所属仓库",
    prop: "warehouse_name",
    align: "center"
  },
  {
    label: "类型",
    prop: "drp_type_alias",
    align: "center"
  },
  {
    label: "物资名称",
    prop: "materials_name",
    align: "center"
  },
  {
    label: "所属供应商",
    prop: "supplier_manage_name",
    align: "center"
  },
  {
    label: "预警情况",
    prop: "warn_text",
    width: "220px",
    align: "center"
  },
  {
    label: "经手人",
    prop: "operator",
    align: "center"
  },
  {
    label: "关联单据",
    prop: "trade_no",
    width: "220px",
    align: "center"
  }
]

// 采购质量预警表单设置
export const SEARCH_FORM_QUALIFICATION_WARNING = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: true,
    label: "预警时间",
    value: [getPreDate(7, "YYYY-MM-DD"), getPreDate(0, "YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  organization_name: {
    type: "select",
    label: "组织名称",
    value: "全部",
    dataList: TYPE_ORGANIZATION,
    placeholder: "请选择组织",
    multiple: false,
    clearable: true,
    collapseTags: true
  },
  handler_status: {
    type: "select",
    label: "处理状态",
    value: "全部",
    dataList: [
      {
        label: "全部",
        value: "全部"
      },
      {
        label: "待处理",
        value: "待处理"
      },
      {
        label: "已处理",
        value: "已处理"
      }
    ],
    placeholder: "请选择",
    clearable: false
  }
}
// 采购质量预警表格设置
export const TABLE_SETTING_QUALIFICATION_WARNING = [
  {
    label: "预警时间",
    prop: "warn_time",
    "show-overflow-tooltip": true,
    align: "center"
  },
  {
    label: "组织名称",
    prop: "organization_name",
    align: "center"
  },
  {
    label: "类型",
    prop: "operation_type",
    align: "center"
  },
  {
    label: "预警情况",
    prop: "warning_status",
    "show-overflow-tooltip": true,
    align: "center"
  }
]

// 证件/合同表单设置
export const SEARCH_FORM_CERTIFICATE_WARNING = {
  selecttime: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "预警时间",
    value: [dayjs().startOf("year").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
    placeholder: "请选择",
    dataList: [] as Array<any>
  },
  org_type: {
    type: "select",
    label: "组织属性",
    value: "全部",
    dataList: [
      {
        label: "全部",
        value: "全部"
      },
      {
        label: "学校",
        value: "school"
      },
      {
        label: "供应商",
        value: "supplier"
      }
    ],
    placeholder: "请选择",
    clearable: false
  },
  org_id: {
    type: "supervisionOrgs",
    label: "监管组织",
    value: "",
    placeholder: "请选择",
    isShowChildOrs: true,
    multiple: false,
    clearable: true,
    collapseTags: true
  },
  file_type: {
    type: "select",
    label: "证件类型",
    value: "全部",
    dataList: [
      {
        label: "全部",
        value: "全部"
      },
      {
        label: "餐饮服务许可证",
        value: "catering_service_license"
      },
      {
        label: "食品经营许可证",
        value: "food_business_license"
      },
      {
        label: "食品生产许可证",
        value: "food_production_license"
      },
      {
        label: "卫生许可证",
        value: "hygienic_license"
      },
      {
        label: "健康证",
        value: "healthy_license"
      },
      {
        label: "供应商入围合同",
        value: "supplier_shortlisted_contract"
      }
    ],
    placeholder: "请选择",
    clearable: false
  }
}
// 证件/合同预警表格设置
export const TABLE_SETTING_CERTIFICATE_WARNING = [
  {
    label: "预警时间",
    prop: "create_time",
    align: "center"
  },
  {
    label: "组织属性",
    prop: "org_type_alias",
    align: "center"
  },
  {
    label: "监管组织",
    prop: "channel_org_name",
    align: "center"
  },
  {
    label: "所属组织",
    prop: "org_name",
    align: "center"
  },
  {
    label: "类型",
    prop: "file_type_alias",
    align: "center"
  },
  {
    label: "预警情况",
    prop: "warn_text",
    slot: "warnText",
    align: "center"
  },
  {
    label: "有效期",
    prop: "start_time",
    slot: "timeRange",
    align: "center"
  },
  {
    label: "附件",
    prop: "image_json",
    slot: "image",
    align: "center"
  }
]

// 物质预警表单设置
export const SEARCH_FORM_MATERIALS_SETTING = {
  name: {
    type: "input",
    label: "物资名称",
    value: "",
    placeholder: "请输入物资名称",
    clearable: true
  },
  warn_type: {
    type: "select",
    label: "预警类型",
    value: "全部",
    dataList: [
      {
        label: "全部",
        value: "全部"
      },
      {
        label: "禁用物资",
        value: "disable"
      },
      {
        label: "高风险物资",
        value: "high_risk"
      }
    ],
    placeholder: "请选择",
    clearable: false
  }
}
// 物质预警表格设置
export const TABLE_SETTING_MATERIALS_SETTING = [
  {
    label: "物资分类",
    prop: "material_type_alias",
    align: "center"
  },
  {
    label: "物资名称",
    prop: "name",
    align: "center"
  },
  {
    label: "预警类型",
    prop: "warn_type_alias",
    align: "center"
  },
  {
    label: "操作时间",
    prop: "create_time",
    align: "center"
  },
  {
    label: "操作人",
    prop: "operator",
    align: "center"
  },
  {
    label: "操作",
    prop: "operation",
    slot: "operationNew",
    width: "180px",
    align: "center"
  }
]

// 证件/合同预警设置表格设置
export const TABLE_SETTING_CERTIFICATION_SETTING = [
  {
    label: "组织属性",
    prop: "org_attribute_alias"
  },
  {
    label: "预警类型",
    prop: "warn_type_alias"
  },
  {
    label: "预警情况",
    prop: "warn_circumstance_alias"
  },

  {
    label: "启用状态",
    prop: "enable",
    slot: "operationNew",
    width: "180px"
  }
]
