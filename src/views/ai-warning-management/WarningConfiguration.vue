<template>
  <div class="warning-config-container">
    <el-radio-group v-model="tabPosition">
      <el-radio-button
        value="warn_setting"
        v-permission="['background_fund_supervision.warn_disposition.warn_setting_edit']"
        >预警设置</el-radio-button
      >
      <el-radio-button
        value="materials_setting"
        v-permission="['background_fund_supervision.warn_disposition.material_warn_list']"
        >物资预警</el-radio-button
      >
      <el-radio-button
        value="certificate_setting"
        v-permission="['background_fund_supervision.warn_disposition.contract_warn_list']"
        >证件/合同预警</el-radio-button
      >
    </el-radio-group>
    <div class="m-t-20px">
      <warning-setting v-if="tabPosition === 'warn_setting'" />
      <materials-setting v-else-if="tabPosition === 'materials_setting'" />
      <certificate-setting v-else />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from "vue"
import MaterialsSetting from "./compontents/MaterialsSetting.vue"
import CertificateSetting from "./compontents/CertificateSetting.vue"
import WarningSetting from "./compontents/WarningSetting.vue"

// 权限判断
import { useUserStoreHook } from "@/store/modules/user"
const { roles } = useUserStoreHook()
const tabPosition = ref("")
const checkoutPermission = () => {
  // roles.forEach((item: any) => {
  //   if (["background_fund_supervision.warn_disposition.warn_setting_edit"].includes(item)) {
  //     tabPosition.value = "warn_setting"
  //   } else if (["background_fund_supervision.warn_disposition.material_warn_list"].includes(item)) {
  //     tabPosition.value = "materials_setting"
  //   } else if (["background_fund_supervision.warn_disposition.contract_warn_list"].includes(item)) {
  //     tabPosition.value = "certificate_setting"
  //   } else {
  //     tabPosition.value = ""
  //   }
  // })
  if (roles.includes("background_fund_supervision.warn_disposition.warn_setting_edit")) {
    tabPosition.value = "warn_setting"
  } else if (roles.includes("background_fund_supervision.warn_disposition.material_warn_list")) {
    tabPosition.value = "materials_setting"
  } else if (roles.includes("background_fund_supervision.warn_disposition.contract_warn_list")) {
    tabPosition.value = "certificate_setting"
  } else {
    tabPosition.value = ""
  }
}
onMounted(() => {
  checkoutPermission()
})
</script>
<style lang="scss" scoped>
.warning-config-container {
  padding: 0 20px;
}
</style>
