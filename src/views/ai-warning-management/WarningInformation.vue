<template>
  <div class="warning-information-container">
    <el-radio-group v-model="tabPosition">
      <el-radio-button value="operate">经营预警</el-radio-button>
      <el-radio-button value="incomeAndOutGoing">物资风险</el-radio-button>
      <!-- <el-radio-button value="qualifications">采购质量预警</el-radio-button> -->
      <el-radio-button value="certificate">证件/合同预警</el-radio-button>
    </el-radio-group>
    <div class="m-t-20px">
      <operate-warning v-if="tabPosition === 'operate'" />
      <income-and-out-going-warning v-if="tabPosition === 'incomeAndOutGoing'" />
      <!-- <qualifications-warning v-if="tabPosition === 'qualifications'" /> -->
      <certificate-warning v-if="tabPosition === 'certificate'" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, watch } from "vue"
import OperateWarning from "./compontents/OperateWarning.vue"
import IncomeAndOutGoingWarning from "./compontents/IncomeAndOutGoingWarning.vue"
// import QualificationsWarning from "./compontents/QualificationsWarning.vue"
import CertificateWarning from "./compontents/CertificateWarning.vue"

import { useRoute } from "vue-router"
const route = useRoute()
const tabPosition = ref<any>("operate")
onMounted(() => {
  if (route.query.type) {
    switch (route.query.type) {
      case "business": {
        tabPosition.value = "operate"
        break
      }
      case "doc_cont": {
        tabPosition.value = "certificate"
        break
      }
      case "materials": {
        tabPosition.value = "incomeAndOutGoing"
        break
      }
    }
  }
})
watch(
  () => route.query.type,
  (newVal) => {
    if (newVal) {
      switch (newVal) {
        case "business": {
          tabPosition.value = "operate"
          break
        }
        case "doc_cont": {
          tabPosition.value = "certificate"
          break
        }
        case "materials": {
          tabPosition.value = "incomeAndOutGoing"
          break
        }
      }
    }
  }
)
</script>
<style lang="scss" scoped>
.warning-information-container {
  padding: 0 20px;
}
</style>
