// 超管横向一级路由  这里保持跟4.0 getList 字段一致，后续要做动态路由
export const ROUTE_SUPER = [
  {
    index: "1",
    is_menu: true,
    key: "consulting_management",
    level: 0,
    perm_type: 2,
    verbose_name: "咨询管理",
    svgIconName: "consulting-management",
    activeMenu: "",
    children: [
      {
        index: "1-1",
        is_menu: true,
        key: "order_management",
        level: 1,
        perm_type: 2,
        parent: "1",
        verbose_name: "订单管理",
        children: [
          {
            index: "1-1-1",
            is_menu: true,
            key: "consultation_order",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "咨询订单"
          },
          {
            index: "1-1-2",
            is_menu: true,
            key: "refund_request",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "退款申请"
          },
          {
            index: "1-1-3",
            is_menu: true,
            key: "evaluation_management",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "评价管理"
          }
        ]
      }
    ]
  },
  // {
  //   index: "2",
  //   is_menu: true,
  //   key: "data_statistics",
  //   level: 0,
  //   perm_type: 2,
  //   verbose_name: "数据统计",
  //   svgIconName: "data-collect",
  //   activeMenu: "",
  //   children: [
  //     {
  //       index: "2-1",
  //       is_menu: true,
  //       key: "business_data",
  //       level: 1,
  //       parent: "2",
  //       perm_type: 2,
  //       verbose_name: "经营数据",
  //       children: [
  //         {
  //           index: "2-1-1",
  //           is_menu: true,
  //           key: "data_summary",
  //           level: 2,
  //           parent: "2-1",
  //           perm_type: 2,
  //           verbose_name: "数据汇总"
  //         }
  //       ]
  //     },
  //     {
  //       index: "2-2",
  //       is_menu: true,
  //       key: "account_reconciliation_management",
  //       level: 1,
  //       parent: "2",
  //       perm_type: 2,
  //       verbose_name: "对账管理",
  //       children: [
  //         {
  //           index: "2-2-1",
  //           is_menu: true,
  //           key: "reconciliation_list",
  //           level: 2,
  //           parent: "2-1",
  //           perm_type: 2,
  //           verbose_name: "对账列表"
  //         }
  //       ]
  //     }
  //   ]
  // },
  {
    index: "3",
    is_menu: true,
    key: "nutritionist_management",
    level: 0,
    perm_type: 2,
    verbose_name: "营养师管理",
    svgIconName: "health-manage",
    activeMenu: "",
    children: [
      {
        index: "3-1",
        is_menu: true,
        key: "nutritionist_management",
        level: 1,
        parent: "3",
        perm_type: 2,
        verbose_name: "营养师管理",
        children: [
          {
            index: "3-1-1",
            is_menu: true,
            key: "nutritionists_List",
            level: 2,
            parent: "3-1",
            perm_type: 2,
            verbose_name: "营养师列表"
          },
          {
            index: "3-1-2",
            is_menu: true,
            key: "registration_approval",
            level: 2,
            parent: "3-1",
            perm_type: 2,
            verbose_name: "注册审批"
          },
          {
            index: "3-1-3",
            is_menu: true,
            key: "service_configuration",
            level: 2,
            parent: "3-1",
            perm_type: 2,
            verbose_name: "服务配置"
          }
        ]
      },
      {
        index: "3-2",
        is_menu: true,
        key: "convenient_tools",
        level: 1,
        parent: "3",
        perm_type: 2,
        verbose_name: "便捷工具",
        children: [
          {
            index: "3-2-1",
            is_menu: true,
            key: "quick_reply",
            level: 2,
            parent: "3-2",
            perm_type: 2,
            verbose_name: "快捷回复"
          },
          {
            index: "3-2-2",
            is_menu: true,
            key: "rapid_evaluation",
            level: 2,
            parent: "3-2",
            perm_type: 2,
            verbose_name: "快速评价"
          },
          {
            index: "3-2-3",
            is_menu: true,
            key: "common_problem",
            level: 2,
            parent: "3-2",
            perm_type: 2,
            verbose_name: "常用问题"
          }
        ]
      }
    ]
  },
  {
    index: "4",
    is_menu: true,
    key: "system_configuration",
    level: 0,
    perm_type: 2,
    verbose_name: "系统配置",
    svgIconName: "system-setting",
    activeMenu: "",
    children: [
      {
        index: "4-1",
        is_menu: true,
        key: "mini_program_configuration",
        level: 1,
        parent: "4",
        perm_type: 2,
        verbose_name: "小程序配置",
        children: [
          // {
          //   index: "4-1-1",
          //   is_menu: true,
          //   key: "promotion_image",
          //   level: 2,
          //   parent: "4-1",
          //   perm_type: 2,
          //   verbose_name: "推广图"
          // },
          {
            index: "4-1-2",
            is_menu: true,
            key: "id_card_authentication",
            level: 2,
            parent: "4-2",
            perm_type: 2,
            verbose_name: "实名认证"
          },
          {
            index: "4-1-3",
            is_menu: true,
            key: "ai_reply_record",
            level: 2,
            parent: "4-2",
            perm_type: 2,
            verbose_name: "实名认证"
          },
          {
            index: "4-1-4",
            is_menu: true,
            key: "basic_config",
            level: 2,
            parent: "4-2",
            perm_type: 2,
            verbose_name: "基础配置"
          }
        ]
      }
      //     {
      //       index: "4-2",
      //       is_menu: true,
      //       key: "points_management",
      //       level: 1,
      //       parent: "4",
      //       perm_type: 2,
      //       verbose_name: "积分管理",
      //       children: [
      //         {
      //           index: "4-2-1",
      //           is_menu: true,
      //           key: "points_order",
      //           level: 2,
      //           parent: "4-2",
      //           perm_type: 2,
      //           verbose_name: "积分订单"
      //         },
      //         {
      //           index: "4-2-2",
      //           is_menu: true,
      //           key: "integral_products",
      //           level: 2,
      //           parent: "4-2",
      //           perm_type: 2,
      //           verbose_name: "积分商品"
      //         },
      //         {
      //           index: "4-2-3",
      //           is_menu: true,
      //           key: "integral_task",
      //           level: 2,
      //           parent: "4-2",
      //           perm_type: 2,
      //           verbose_name: "积分任务"
      //         }
      //       ]
      //     }
    ]
  },
  {
    index: "5",
    is_menu: true,
    key: "disseminate_admin",
    level: 0,
    perm_type: 2,
    verbose_name: "推广管理",
    svgIconName: "system-setting",
    activeMenu: "",
    children: [
      {
        index: "5-1",
        is_menu: true,
        key: "disseminate_admin",
        level: 1,
        parent: "5",
        perm_type: 2,
        verbose_name: "推广管理",
        children: [
          {
            index: "5-1-1",
            is_menu: true,
            key: "channel_accounting",
            level: 2,
            parent: "5-1",
            perm_type: 2,
            verbose_name: "渠道核算"
          },
          {
            index: "5-1-2",
            is_menu: true,
            key: "members_accounting",
            level: 2,
            parent: "5-1",
            perm_type: 2,
            verbose_name: "成员核算"
          },
          {
            index: "5-1-3",
            is_menu: true,
            key: "channel_admin",
            level: 2,
            parent: "5-1",
            perm_type: 2,
            verbose_name: "渠道管理"
          },
          {
            index: "5-1-4",
            is_menu: true,
            key: "members_admin",
            level: 2,
            parent: "5-1",
            perm_type: 2,
            verbose_name: "成员管理"
          }
        ]
      }
    ]
  }
]

// 横向一级路由  这里保持跟4.0 getList 字段一致，后续要做动态路由
export const ROUTE_HEALTHY = [
  {
    index: "1",
    is_menu: true,
    key: "supervision_center",
    level: 0,
    perm_type: 2,
    verbose_name: "监管中心",
    svgIconName: "consulting-management",
    activeMenu: "",
    children: [
      {
        index: "1-1",
        is_menu: true,
        key: "fund_supervision_center",
        level: 1,
        perm_type: 2,
        parent: "1",
        verbose_name: "监管中心",
        children: [
          {
            index: "1-1-1",
            is_menu: true,
            key: "background_fund_supervision.organization_supervision",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "组织监管"
          },
          {
            index: "1-1-2",
            is_menu: true,
            key: "background_fund_supervision.supervision_food_safety_source",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "食安溯源"
          },
          {
            index: "1-1-3",
            is_menu: true,
            key: "background_fund_supervision.supervision_questionnaire.list",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "问卷缉查"
          },
          {
            index: "1-1-4",
            is_menu: true,
            key: "background_fund_supervision.supervision_questionnaire.questionnaire_answer_detail",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "数据明细"
          }
        ]
      }
    ]
  },
  {
    index: "6",
    is_menu: true,
    key: "canteen_management",
    level: 0,
    perm_type: 2,
    verbose_name: "食堂管理",
    svgIconName: "consulting-management",
    activeMenu: "",
    children: [
      {
        index: "6-2",
        is_menu: true,
        key: "channel_canteen_management",
        level: 1,
        perm_type: 2,
        parent: "1",
        verbose_name: "食堂管理",
        children: [
          // {
          //   index: "6-2-1",
          //   is_menu: true,
          //   key: "kitchen_fire",
          //   level: 2,
          //   parent: "1-1",
          //   perm_type: 2,
          //   verbose_name: "名厨亮灶"
          // },
          {
            index: "6-2-2",
            is_menu: true,
            key: "background_fund_supervision.channel_canteen_management.food_reserved_sample_record",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "留样记录"
          },
          {
            index: "6-2-3",
            is_menu: true,
            key: "background_fund_supervision.channel_canteen_management.morning_check",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "晨检记录"
          },
          {
            index: "6-2-4",
            is_menu: true,
            key: "background_fund_supervision.channel_canteen_management.pest_control_record",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "有害生物防制"
          },
          {
            index: "6-2-5",
            is_menu: true,
            key: "background_fund_supervision.channel_canteen_management.democratic_feedback",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "民主监督"
          },
          {
            index: "6-2-6",
            is_menu: true,
            key: "background_fund_supervision.fund_market_inquiry.list",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "市场询价"
          },
          // {
          //   index: "6-2-7",
          //   is_menu: true,
          //   key: "weekly_recipes",
          //   level: 2,
          //   parent: "1-1",
          //   perm_type: 2,
          //   verbose_name: "每周食谱"
          // },
          // {
          //   index: "6-2-8",
          //   is_menu: true,
          //   key: "asset_statistics",
          //   level: 2,
          //   parent: "1-1",
          //   perm_type: 2,
          //   verbose_name: "资产统计"
          // },
          // {
          //   index: "6-2-9",
          //   is_menu: true,
          //   key: "debt_statistics",
          //   level: 2,
          //   parent: "1-1",
          //   perm_type: 2,
          //   verbose_name: "负债统计"
          // },
          {
            index: "6-2-10",
            is_menu: true,
            key: "background_fund_supervision.channel_canteen_management.channel_meal_accompanying_list",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "陪餐管理"
          },
          // {
          //   index: "6-2-11",
          //   is_menu: true,
          //   key: "asset_statistics_detail",
          //   level: 2,
          //   parent: "1-1",
          //   perm_type: 2,
          //   verbose_name: "资产统计详情"
          // },
          // {
          //   index: "6-2-12",
          //   is_menu: true,
          //   key: "debt_statistics_detail",
          //   level: 2,
          //   parent: "1-1",
          //   perm_type: 2,
          //   verbose_name: "负债统计详情"
          // }
          {
            index: "6-2-9",
            is_menu: true,
            key: "background_fund_supervision.supervision_ration_recipe.list",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "带量食谱"
          }
        ]
      },
      {
        index: "6-3",
        is_menu: true,
        key: "fund_supervision_asset",
        level: 1,
        perm_type: 2,
        parent: "1",
        verbose_name: "资产管理",
        children: [
          {
            index: "6-3-1",
            is_menu: true,
            key: "background_fund_supervision.asset.channel_asset_info_statistics_list",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "资产统计"
          },
          {
            index: "6-3-2",
            is_menu: true,
            key: "background_fund_supervision.asset.channel_liability_info_statistics_list",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "负债统计"
          }
        ]
      }
    ]
  },
  {
    index: "3",
    is_menu: true,
    key: "fund_supplier_center",
    level: 0,
    perm_type: 2,
    verbose_name: "供应商管理",
    svgIconName: "fund_supplier_center",
    activeMenu: "",
    children: [
      {
        index: "3-1",
        is_menu: true,
        key: "fund_supervision_supplier",
        level: 1,
        perm_type: 2,
        parent: "1",
        verbose_name: "供应商管理",
        children: [
          {
            index: "3-1-1",
            is_menu: true,
            key: "background_fund_supervision.supplier_manage",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "供应商信息"
          },
          {
            index: "3-1-2",
            is_menu: true,
            key: "background_fund_supervision.supplier_manage.supplier_manage_apply_data",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "供应商审批"
          }
        ]
      }
    ]
  },
  {
    index: "4",
    is_menu: true,
    key: "document_management",
    level: 0,
    perm_type: 2,
    verbose_name: "单据管理",
    svgIconName: "consulting-management",
    activeMenu: "",
    children: [
      {
        index: "4-1",
        is_menu: true,
        key: "fund_supervision_document",
        level: 1,
        perm_type: 2,
        parent: "1",
        verbose_name: "单据管理",
        children: [
          {
            index: "4-1-1",
            is_menu: true,
            key: "background_fund_supervision.supervision_data.purchase_info_list",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "采购单"
          },
          {
            index: "4-1-2",
            is_menu: true,
            key: "inbound_and_outbound_orders",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "出入库订单"
          },
          {
            index: "4-1-3",
            is_menu: true,
            key: "background_fund_supervision.supervision_data.vendor_delivery_list",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "配送单"
          },
          {
            index: "4-1-4",
            is_menu: true,
            key: "background_fund_supervision.supervision_data.receiving_note_list",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "收货单"
          },
          {
            index: "4-1-6",
            is_menu: true,
            key: "background_fund_supervision.supervision_data.final_statement_list",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "结算单"
          },
          {
            index: "4-1-5",
            is_menu: true,
            key: "background_fund_supervision.supervision_data.application_form_list",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "申请单"
          },
          {
            index: "4-1-7",
            is_menu: true,
            key: "background_fund_supervision.supervision_data.inventory_balance_list",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "库存台账"
          }
        ]
      },
      {
        index: "4-2",
        is_menu: true,
        key: "supervision_finance_setting",
        level: 1,
        perm_type: 2,
        parent: "1",
        verbose_name: "财务管理",
        children: [
          {
            index: "4-2-1",
            is_menu: true,
            key: "supervision_finance_approve",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "财务审批"
          },
          {
            index: "4-2-2",
            is_menu: true,
            key: "background_fund_supervision.supervision_appropriation.list",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "拨款记录"
          },
          {
            index: "4-2-3",
            is_menu: true,
            key: "background_fund_supervision.supervision_appropriation.get_setting",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "拨款设置"
          }
        ]
      }
    ]
  },
  {
    index: "5",
    is_menu: true,
    key: "ai_warn_manage",
    level: 0,
    perm_type: 2,
    verbose_name: "预警管理",
    svgIconName: "consulting-management",
    activeMenu: "",
    children: [
      {
        index: "5-1",
        is_menu: true,
        key: "ai_warn_manage",
        level: 1,
        perm_type: 2,
        parent: "1",
        verbose_name: "AI预警",
        children: [
          {
            index: "5-1-1",
            is_menu: true,
            key: "background_fund_supervision.warn_manage.warning_message_list",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "预警信息"
          },
          {
            index: "5-1-2",
            is_menu: true,
            key: "warn_disposition",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "预警配置"
          }
        ]
      }
    ]
  },
  {
    index: "2",
    is_menu: true,
    key: "data_center",
    level: 0,
    perm_type: 2,
    verbose_name: "数据中心",
    svgIconName: "notice",
    activeMenu: "",
    children: [
      {
        index: "2-1",
        is_menu: true,
        key: "business_report",
        level: 1,
        perm_type: 2,
        parent: "2",
        verbose_name: "业务报表",
        children: [
          {
            index: "2-1-1",
            is_menu: true,
            key: "background_fund_supervision.business_report.canteen_month_accounting_list",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "食堂月核算报表"
          },
          {
            index: "2-1-2",
            is_menu: true,
            key: "background_fund_supervision.business_report.income_summary_list",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "经营收支汇总表"
          },
          {
            index: "2-1-3",
            is_menu: true,
            key: "background_fund_supervision.business_report.income_statistics_list",
            level: 2,
            parent: "1-1",
            perm_type: 2,
            verbose_name: "经营收支统计表"
          }
        ]
      },
      {
        index: "2-2",
        is_menu: true,
        key: "finance_report",
        level: 1,
        parent: "1",
        perm_type: 1,
        verbose_name: "财务报表",
        children: [
          {
            index: "2-2-1",
            is_menu: true,
            key: "background_fund_supervision.finance_report.fund_water_list",
            level: 2,
            parent: "2-2",
            perm_type: 2,
            verbose_name: "资金流水明细"
          },
          {
            index: "2-2-2",
            is_menu: true,
            key: "background_fund_supervision.finance_report.fund_day_report_list",
            level: 2,
            parent: "2-2",
            perm_type: 2,
            verbose_name: "资金收入日报表"
          },
          {
            index: "2-2-3",
            is_menu: true,
            key: "background_fund_supervision.finance_report.fund_month_report_list",
            level: 2,
            parent: "2-2",
            perm_type: 2,
            verbose_name: "资金收入月报表"
          }
          // {
          //   index: "2-2-4",
          //   is_menu: true,
          //   key: "low_consumption_statistics",
          //   level: 2,
          //   parent: "2-2",
          //   perm_type: 2,
          //   verbose_name: "低消费统计"
          // },
          // {
          //   index: "2-2-5",
          //   is_menu: true,
          //   key: "funds_account_record",
          //   level: 2,
          //   parent: "2-2",
          //   perm_type: 2,
          //   verbose_name: "资金专户记录"
          // },
          // {
          //   index: "2-2-6",
          //   is_menu: true,
          //   key: "account_income_expenditure_details",
          //   level: 2,
          //   parent: "2-2",
          //   perm_type: 2,
          //   verbose_name: "专户收支明细"
          // }
        ]
      },
      {
        index: "2-3",
        is_menu: true,
        key: "big_shield",
        level: 1,
        parent: "1",
        perm_type: 1,
        verbose_name: "数据驾驶舱",
        children: [
          {
            index: "2-3-1",
            is_menu: true,
            key: "big_shield_list",
            level: 2,
            parent: "2-3",
            perm_type: 2,
            verbose_name: "大屏列表"
          }
        ]
      }
    ]
  },
  {
    index: "6",
    is_menu: true,
    key: "fund_system_management",
    level: 0,
    perm_type: 2,
    verbose_name: "系统管理",
    svgIconName: "notice",
    activeMenu: "",
    children: [
      {
        index: "6-1",
        is_menu: true,
        key: "fund_account_management",
        level: 1,
        perm_type: 2,
        parent: "2",
        verbose_name: "账号管理",
        children: [
          {
            index: "6-1-1",
            is_menu: false,
            key: "background_fund_supervision.audit_account",
            level: 2,
            parent: "6-1",
            perm_type: 2,
            verbose_name: "账号管理"
          },
          {
            index: "6-1-2",
            is_menu: false,
            key: "background_fund_supervision.channel_role",
            level: 2,
            parent: "6-1",
            perm_type: 2,
            verbose_name: "角色管理"
          }
        ]
      },
      {
        index: "6-2",
        is_menu: true,
        key: "fund_approval_config",
        level: 1,
        perm_type: 2,
        parent: "2",
        verbose_name: "审批配置",
        children: [
          {
            index: "6-2-1",
            is_menu: true,
            key: "background_fund_supervision.channel_approve_rule",
            level: 2,
            parent: "6-1",
            perm_type: 2,
            verbose_name: "审批流程"
          }
        ]
      },
      {
        index: "6-3",
        is_menu: true,
        key: "supervision_system_messages",
        level: 1,
        perm_type: 2,
        parent: "2",
        verbose_name: "通知公告",
        children: [
          {
            index: "6-3-1",
            is_menu: true,
            key: "background_fund_supervision.supervision_messages.list",
            level: 2,
            parent: "6-3",
            perm_type: 2,
            verbose_name: "系统通知"
          },
          {
            index: "6-3-2",
            is_menu: true,
            key: "background_fund_supervision.supervision_messages.notice_list",
            level: 2,
            parent: "6-3",
            perm_type: 2,
            verbose_name: "系统公告"
          }
        ]
      }
      // {
      //   index: "6-4",
      //   is_menu: true,
      //   key: "other_services",
      //   level: 1,
      //   perm_type: 2,
      //   parent: "2",
      //   verbose_name: "其他服务",
      //   children: [
      //     {
      //       index: "6-4-1",
      //       is_menu: true,
      //       key: "query_center",
      //       level: 1,
      //       parent: "6-4",
      //       perm_type: 1,
      //       verbose_name: "查询中心",
      //       children: [
      //         {
      //           index: "6-4-1-1",
      //           is_menu: true,
      //           key: "suspend_query",
      //           level: 2,
      //           parent: "2-4",
      //           perm_type: 2,
      //           verbose_name: "挂起查询"
      //         },
      //         {
      //           index: "6-4-1-2",
      //           is_menu: true,
      //           key: "suspend_details",
      //           level: 2,
      //           parent: "2-4",
      //           perm_type: 2,
      //           verbose_name: "查看详情"
      //         }
      //       ]
      //     },
      //     {
      //       index: "6-4-2",
      //       is_menu: true,
      //       key: "download_center",
      //       level: 1,
      //       parent: "6-4",
      //       perm_type: 1,
      //       verbose_name: "下载中心",
      //       children: [
      //         {
      //           index: "6-4-2-1",
      //           is_menu: true,
      //           key: "download_record",
      //           level: 2,
      //           parent: "6-4-2",
      //           perm_type: 2,
      //           verbose_name: "下载记录"
      //         }
      //       ]
      //     }
      //   ]
      // }
    ]
  }
]
