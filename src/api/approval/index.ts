import { request } from "@/utils/service"
/**
 * 审批规则添加
 *
 * @param data
 */
export function apiBackgroundFundSupervisionChannelApproveRuleAddPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/channel_approve_rule/add",
    method: "post",
    data: data
  })
}

/**
 * 审批规则修改
 *
 * @param data
 */
export function apiBackgroundFundSupervisionChannelApproveRuleModifyPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/channel_approve_rule/modify",
    method: "post",
    data: data
  })
}

/**
 * 审批规则删除
 *
 * @param data
 */
export function apiBackgroundFundSupervisionChannelApproveRuleDeletePost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/channel_approve_rule/delete",
    method: "post",
    data: data
  })
}

/**
 * 审批规则列表
 *
 * @param data
 */
export function apiBackgroundFundSupervisionChannelApproveRuleListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/channel_approve_rule/list",
    method: "post",
    data: data
  })
}

export default {
  apiBackgroundFundSupervisionChannelApproveRuleAddPost,
  apiBackgroundFundSupervisionChannelApproveRuleModifyPost,
  apiBackgroundFundSupervisionChannelApproveRuleDeletePost,
  apiBackgroundFundSupervisionChannelApproveRuleListPost
}
