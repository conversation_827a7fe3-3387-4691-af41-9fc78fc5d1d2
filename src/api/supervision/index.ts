import { request } from "@/utils/service"
/**
 * 监管渠道列表
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionChannelListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_channel/channel_list",
    method: "post",
    data: data
  })
}

/**
 * 获取当前登陆渠道的监管信息（组织监管页面）
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionDataSupervisionChannelInfoPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_data/supervision_channel_info",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 监管概览
 *
 * @param data
 */
export function apiBackgroundFundSupervisionOrganizationSupervisionRegulatoryPreviewData(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/regulatory_preview_data",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 预警概览
 *
 * @param data
 */
export function apiBackgroundFundSupervisionOrganizationSupervisionWarningOverview(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/warning_overview",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 预警明细
 *
 * @param data
 */
export function apiBackgroundFundSupervisionOrganizationSupervisionWarningDetails(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/warning_details",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 渠道组织
 *
 * @param data
 */
export function apiCanteenChannelList(data: any) {
  return request<any>({
    url: "/background_fund_supervision/big_shield/canteen_channel_list",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 收支利润
 *
 * @param data
 */
export function apiSupervisionIncomeExpensesProfit(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/income_expenses_profit",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 资金支出
 *
 * @param data
 */
export function apiSupervisionExpenditureFunds(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/expenditure_funds",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 食材消耗
 *
 * @param data
 */
export function apiSupervisionIngredientExhaustion(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/ingredient_exhaustion",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 利润分析
 *
 * @param data
 */
export function apiSupervisionProfitAnalysis(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/profit_analysis",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 食堂信息
 *
 * @param data
 */
export function apiSupervisionCanteenInfo(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/canteen_info",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 食堂信息
 *
 * @param data
 */
export function apiSupervisionCanteenQualification(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/canteen_qualification",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 食安信息
 *
 * @param data
 */
export function apiSupervisionFoodSafetyPublicity(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/food_safety_publicity",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 人员配比
 *
 * @param data
 */
export function apiSupervisionPersonnelRatio(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/personnel_ratio",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 在职人员
 *
 * @param data
 */
export function apiSupervisionJobPersonList(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/job_person_list",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 供应商管理
 *
 * @param data
 */
export function apiSupervisionVendorManageList(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/vendor_manage_list",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 供应商合计
 *
 * @param data
 */
export function apiSupervisionVendorManageTotal(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/vendor_manage_total",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 供应商汇总
 *
 * @param data
 */
export function apiSupervisionSupplySummaryTableList(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/supply_summary_table_list",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 供应汇总表导出
 *
 * @param data
 */
export function apiBackgroundFundSupervisionOrganizationSupervisionSupplySummaryTableExportPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/supply_summary_table_export",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 风险预警
 *
 * @param data
 */
export function apiSupervisionRiskWarningList(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/risk_warning_list",
    method: "post",
    data: data
  })
}

/**
 * 组织监管 风险预警
 *
 * @param data
 */
export function apiSupervisionRiskWarningListExport(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/risk_warning_list_export",
    method: "post",
    data: data
  })
}

/**
 * 菜品溯源
 *
 * @param data
 */
export function apiFoodSafetySourceFoodSourceListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_food_safety_source/food_source_list",
    method: "post",
    data: data
  })
}

/**
 * 食材溯源
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionFoodSafetySourceGetFoodIngredientSourcePost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_food_safety_source/get_food_ingredient_source",
    method: "post",
    data: data
  })
}

/**
 * 留样信息
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionFoodSafetySourceGetReservedSampleInfoPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_food_safety_source/get_reserved_sample_info",
    method: "post",
    data: data
  })
}

/**
 * 获取值班人员
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionFoodSafetySourceGetSchedulePersonPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_food_safety_source/get_schedule_person",
    method: "post",
    data: data
  })
}

/**
 * 导出
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionFoodSafetySourceExportOrderSourcePost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_food_safety_source/export_order_source",
    method: "post",
    data: data
  })
}

/**
 * 就餐溯源
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionFoodSafetySourceDineSourceListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_food_safety_source/dine_source_list",
    method: "post",
    data: data
  })
}

/**
 * 下载订单
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionFoodSafetySourceDownloadOrderPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_food_safety_source/download_order",
    method: "post",
    data: data
  })
}

/**
 * 带量食谱
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionRationRecipeListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_ration_recipe/list",
    method: "post",
    data: data
  })
}

/**
 * 查看食谱
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionRationRecipeViewRecipePost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_ration_recipe/view_recipe",
    method: "post",
    data: data
  })
}

/**
 * 带量食谱列表导出
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionRationRecipeExportRationRecipeListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_ration_recipe/export_ration_recipe_list",
    method: "post",
    data: data
  })
}

/**
 * 食谱导出
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionRationRecipeExportRecipePost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_ration_recipe/export_recipe",
    method: "post",
    data: data
  })
}

/**
 * 菜品留样记录列表
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionDataFoodReservedPost(data: any) {
  return request<any>({
    url: "background_fund_supervision/supervision_data/food_reserved",
    method: "post",
    data: data
  })
}

/**
 * 采购单列表
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionDataPurchaseInfoListPost(data: any) {
  return request<any>({
    url: "background_fund_supervision/supervision_data/purchase_info_list",
    method: "post",
    data: data
  })
}

/**
 * 业务报表
 *
 * @param data
 */
export function apiBackgroundFundSupervisionBusinessReportListPost(data: any) {
  return request<any>({
    url: "background_fund_supervision/business_report/list",
    method: "post",
    data: data
  })
}

/**
 * 业务报表导出
 *
 * @param data
 */
export function apiBackgroundFundSupervisionBusinessReportListExportPost(data: any) {
  return request<any>({
    url: "background_fund_supervision/business_report/list_export",
    method: "post",
    data: data
  })
}

/**
 * 食堂月核算表
 *
 * @param data
 */
export function apiBackgroundFundSupervisionFinanceReportCanteenMonthAccountingListPost(data: any) {
  return request<any>({
    url: "background_fund_supervision/business_report/canteen_month_accounting_list",
    method: "post",
    data: data
  })
}

/**
 * 食堂月核算表导出
 *
 * @param data
 */
export function apiBackgroundFundSupervisionFinanceReportCanteenMonthAccountingListExport(data: any) {
  return request<any>({
    url: "background_fund_supervision/business_report/canteen_month_accounting_list_export",
    method: "post",
    data: data
  })
}

/**
 * 食堂月核算表统计
 *
 * @param data
 */
export function apiBackgroundFundSupervisionFinanceReportCanteenMonthAccountingTotalPost(data: any) {
  return request<any>({
    url: "background_fund_supervision/business_report/canteen_month_accounting_total",
    method: "post",
    data: data
  })
}

/**
 * 收支统计
 *
 * @param data
 */
export function apiBackgroundFundSupervisionFinanceReportIncomeStatisticsListPost(data: any) {
  return request<any>({
    url: "background_fund_supervision/business_report/income_statistics_list",
    method: "post",
    data: data
  })
}

/**
 * 收支统计导出
 *
 * @param data
 */
export function apiBackgroundFundSupervisionFinanceReportIncomeStatisticsListExportPost(data: any) {
  return request<any>({
    url: "background_fund_supervision/business_report/income_statistics_list_export",
    method: "post",
    data: data
  })
}

/**
 * 收支统计汇总
 *
 * @param data
 */
export function apiBackgroundFundSupervisionFinanceReportIncomeStatisticsTotalPost(data: any) {
  return request<any>({
    url: "background_fund_supervision/business_report/income_statistics_total",
    method: "post",
    data: data
  })
}

/**
 * 收支汇总
 *
 * @param data
 */
export function apiBackgroundFundSupervisionFinanceReportIncomeSummaryListPost(data: any) {
  return request<any>({
    url: "background_fund_supervision/business_report/income_summary_list",
    method: "post",
    data: data
  })
}

/**
 * 收支汇总导出
 *
 * @param data
 */
export function apiBackgroundFundSupervisionFinanceReportIncomeSummaryListExportPost(data: any) {
  return request<any>({
    url: "background_fund_supervision/business_report/income_summary_list_export",
    method: "post",
    data: data
  })
}

/**
 * 收支汇总汇总
 *
 * @param data
 */
export function apiBackgroundFundSupervisionFinanceReportIncomeSummaryTotalPost(data: any) {
  return request<any>({
    url: "background_fund_supervision/business_report/income_summary_total",
    method: "post",
    data: data
  })
}

/**
 * 晨检汇总
 *
 * @param data
 */
export function apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckCollectPost(data: any) {
  return request<any>({
    url: "background_fund_supervision/channel_canteen_management/morning_check_collect",
    method: "post",
    data: data
  })
}

/**
 * 晨检汇总导出
 *
 * @param data
 */
export function apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckCollectExportPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/channel_canteen_management/morning_check_collect_export",
    method: "post",
    data: data
  })
}

/**
 * 晨检明细
 *
 * @param data
 */
export function apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckDetailPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/channel_canteen_management/morning_check_detail",
    method: "post",
    data: data
  })
}

/**
 * 晨检明细导出
 *
 * @param data
 */
export function apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckDetailExportPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/channel_canteen_management/morning_check_detail_export",
    method: "post",
    data: data
  })
}

/**
 * 获取组织物资分类
 *
 * @param data
 */
export function apiBackgroundFundSupervisionOrganizationSupervisionOrgMaterailClassificationPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/org_materail_classification",
    method: "post",
    data: data
  })
}

/**
 * 获取问卷列表
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_questionnaire/questionnaire_list",
    method: "post",
    data: data
  })
}

export function apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAccountAnswerPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_questionnaire/questionnaire_account_answer",
    method: "post",
    data: data
  })
}

export function apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAddPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_questionnaire/questionnaire_add",
    method: "post",
    data: data
  })
}

export function apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAnswerPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_questionnaire/questionnaire_answer",
    method: "post",
    data: data
  })
}

export function apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAnswerDetailPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_questionnaire/questionnaire_answer_detail",
    method: "post",
    data: data
  })
}

export function apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAnswerDetailExportPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_questionnaire/questionnaire_answer_detail_export",
    method: "post",
    data: data
  })
}

export function apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireDeletePost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_questionnaire/questionnaire_delete",
    method: "post",
    data: data
  })
}

export function apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireDetailPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_questionnaire/questionnaire_detail",
    method: "post",
    data: data
  })
}

export function apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireListExportPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_questionnaire/questionnaire_list_export",
    method: "post",
    data: data
  })
}

export function apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireModifyPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_questionnaire/questionnaire_modify",
    method: "post",
    data: data
  })
}

export function apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireModifyStatusPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_questionnaire/questionnaire_modify_status",
    method: "post",
    data: data
  })
}

export function apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireOperateLogListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_questionnaire/questionnaire_operate_log_list",
    method: "post",
    data: data
  })
}

export function apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireDetailNoLoginPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_questionnaire/questionnaire_detail_no_login",
    method: "post",
    data: data
  })
}

export function apiBackgroundFundSupervisionSupervisionQuestionnaireGetQuestionnaireSupplierPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_questionnaire/get_questionnaire_supplier",
    method: "post",
    data: data
  })
}

export default {
  apiBackgroundFundSupervisionSupervisionDataFoodReservedPost,
  apiBackgroundFundSupervisionSupervisionDataPurchaseInfoListPost,
  apiBackgroundFundSupervisionBusinessReportListPost,
  apiBackgroundFundSupervisionBusinessReportListExportPost,
  apiBackgroundFundSupervisionFinanceReportCanteenMonthAccountingListPost,
  apiBackgroundFundSupervisionFinanceReportCanteenMonthAccountingListExport,
  apiBackgroundFundSupervisionFinanceReportCanteenMonthAccountingTotalPost,
  apiBackgroundFundSupervisionFinanceReportIncomeStatisticsListPost,
  apiBackgroundFundSupervisionFinanceReportIncomeStatisticsListExportPost,
  apiBackgroundFundSupervisionFinanceReportIncomeStatisticsTotalPost,
  apiBackgroundFundSupervisionFinanceReportIncomeSummaryListPost,
  apiBackgroundFundSupervisionFinanceReportIncomeSummaryListExportPost,
  apiBackgroundFundSupervisionFinanceReportIncomeSummaryTotalPost,
  apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckCollectPost,
  apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckCollectExportPost,
  apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckDetailPost,
  apiBackgroundFundSupervisionChannelCanteenManagementMorningCheckDetailExportPost,
  apiSupervisionRiskWarningList,
  apiSupervisionSupplySummaryTableList,
  apiFoodSafetySourceFoodSourceListPost,
  apiBackgroundFundSupervisionSupervisionRationRecipeListPost,
  apiBackgroundFundSupervisionOrganizationSupervisionOrgMaterailClassificationPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireListPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAccountAnswerPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAddPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAnswerPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAnswerDetailPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireAnswerDetailExportPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireDeletePost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireDetailPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireListExportPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireModifyPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireModifyStatusPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireQuestionnaireOperateLogListPost,
  apiBackgroundFundSupervisionSupervisionQuestionnaireGetQuestionnaireSupplierPost
}
