import { request } from "@/utils/service"

// 财务拨款配置
export function apiAppropriationGetSetting() {
  return request({
    url: "/background_fund_supervision/supervision_appropriation/get_setting",
    method: "post"
  })
}

// 财务拨款配置修改
export function apiAppropriationModifySetting(params: any) {
  return request({
    url: "/background_fund_supervision/supervision_appropriation/modify_setting",
    method: "post",
    data: params
  })
}

// 当前审批规则
export function apiApproveGetApproveRule(params: any) {
  return request({
    url: "/background_fund_supervision/supervision_finance_approve/get_approve_rule",
    method: "post",
    data: params
  })
}

// 待审批
export function apiApprovePendingList(params: any) {
  return request({
    url: "/background_fund_supervision/supervision_finance_approve/pending_list",
    method: "post",
    data: params
  })
}
// 已同意
export function apiApproveAgreeList(params: any) {
  return request({
    url: "/background_fund_supervision/supervision_finance_approve/agree_list",
    method: "post",
    data: params
  })
}
// 已拒绝
export function apiApproveRejectList(params: any) {
  return request({
    url: "/background_fund_supervision/supervision_finance_approve/reject_list",
    method: "post",
    data: params
  })
}
// 已撤销
export function apiApproveRevokeList(params: any) {
  return request({
    url: "/background_fund_supervision/supervision_finance_approve/revoke_list",
    method: "post",
    data: params
  })
}
// 通过审批
export function apiApproveAgreeApprove(params: any) {
  return request({
    url: "/background_fund_supervision/supervision_finance_approve/agree_approve",
    method: "post",
    data: params
  })
}
// 拒绝审批
export function apiApproveRejectApprove(params: any) {
  return request({
    url: "/background_fund_supervision/supervision_finance_approve/reject_approve",
    method: "post",
    data: params
  })
}
// 拨款记录
export function apiAppropriationList(params: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_appropriation/list",
    method: "post",
    data: params
  })
}

// 大屏列表
export function getChannelScreen(params: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_channel_screen/get_channel_screen",
    method: "post",
    data: params
  })
}

// 拨款动作
export function apiSupervisionAppropriation(params: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_appropriation/supervision_appropriation",
    method: "post",
    data: params
  })
}

// 拨款获取验证码
export function apiSupervisionAppropriationSendCode(params: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_appropriation/supervision_appropriation_send_code",
    method: "post",
    data: params
  })
}

// 资金收支日报表
export function apiBackgroundFundSupervisionFinanceReportFundDayReportListPost(params: any) {
  return request<any>({
    url: "/background_fund_supervision/finance_report/fund_day_report_list",
    method: "post",
    data: params
  })
}
// 资金收支日报表 合计
export function apiBackgroundFundSupervisionFinanceReportFundDayReportTotalPost(params: any) {
  return request<any>({
    url: "/background_fund_supervision/finance_report/fund_day_report_total",
    method: "post",
    data: params
  })
}

// 资金收支日报表 导出
export function apiBackgroundFundSupervisionFinanceReportFundDayReportExportPost(params: any) {
  return request<any>({
    url: "/background_fund_supervision/finance_report/fund_day_report_export",
    method: "post",
    data: params
  })
}

// 资金收支月报表
export function apiBackgroundFundSupervisionFinanceReportFundMonthReportListPost(params: any) {
  return request<any>({
    url: "/background_fund_supervision/finance_report/fund_month_report_list",
    method: "post",
    data: params
  })
}
// 资金收支月报表 合计
export function apiBackgroundFundSupervisionFinanceReportFundMonthReportTotalPost(params: any) {
  return request<any>({
    url: "/background_fund_supervision/finance_report/fund_month_report_total",
    method: "post",
    data: params
  })
}

// 资金收支月报表 导出
export function apiBackgroundFundSupervisionFinanceReportFundMonthReportExportPost(params: any) {
  return request<any>({
    url: "/background_fund_supervision/finance_report/fund_month_report_export",
    method: "post",
    data: params
  })
}

// 资金流水明细
export function apiBackgroundFundSupervisionBusinessReportFundWaterListPost(params: any) {
  return request<any>({
    url: "/background_fund_supervision/finance_report/fund_water_list",
    method: "post",
    data: params
  })
}
// 资金流水明细 合计
export function apiBackgroundFundSupervisionBusinessReportFundWaterTotalPost(params: any) {
  return request<any>({
    url: "/background_fund_supervision/finance_report/fund_water_total",
    method: "post",
    data: params
  })
}

// 资金流水明细 导出
export function apiBackgroundFundSupervisionBusinessReportFundWaterExportPost(params: any) {
  return request<any>({
    url: "/background_fund_supervision/finance_report/fund_water_export",
    method: "post",
    data: params
  })
}

// 获取拨款申请的全部账
export function apiBackgroundFundSupervisionSupervisionFinanceApproveApproveAccountsPost(params: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_finance_approve/approve_accounts",
    method: "post",
    data: params
  })
}

export default {
  apiBackgroundFundSupervisionFinanceReportFundDayReportListPost,
  apiBackgroundFundSupervisionBusinessReportFundWaterListPost,
  apiBackgroundFundSupervisionFinanceReportFundMonthReportListPost
}
