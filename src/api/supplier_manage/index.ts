import { request } from "@/utils/service"

// 供应商审批数据
export function apiBackgroundFundSupervisionSupplierManageSupplierManageApplyData(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supplier_manage/supplier_manage_apply_data",
    method: "post",
    data: data
  })
}

// 供应商审批数据同意
export function apiBackgroundFundSupervisionSupplierManageSupplierManageApplyAgree(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supplier_manage/supplier_manage_apply_agree",
    method: "post",
    data: data
  })
}

// 供应商审批数据拒绝
export function apiBackgroundFundSupervisionSupplierManageSupplierManageApplyRefuse(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supplier_manage/supplier_manage_apply_refuse",
    method: "post",
    data: data
  })
}

// 审批账号流程
export function apiBackgroundFundSupervisionFinanceApproveApproveAccountsPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supplier_manage/approve_accounts",
    method: "post",
    data: data
  })
}
