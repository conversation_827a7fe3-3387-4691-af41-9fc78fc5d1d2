import { request } from "@/utils/service"

/**
 * ['基础接口']
 * background_base.tasks.export_query 异步导出任务结果查询
 * @param {ExportQueryReqSer} param
 * @returns {Object}
 */
export function apiBackgroundBaseTasksExportQueryPost(param: any) {
  return request({
    url: "/background_base/tasks/export_query",
    method: "post",
    data: param
  })
}

export default {
  apiBackgroundBaseTasksExportQueryPost
}
