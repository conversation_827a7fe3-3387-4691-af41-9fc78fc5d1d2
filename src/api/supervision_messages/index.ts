import { request } from "@/utils/service"

// 公告列表
export function apiBackgroundSupervisionSupervisionMessagesNoticeList(data: any) {
  return request<any>({
    url: "/background_supervision/supervision_messages/notice_list",
    method: "post",
    data: data
  })
}

// 添加公告
export function apiBackgroundSupervisionSupervisionMessagesNoticeAdd(data: any) {
  return request<any>({
    url: "/background_supervision/supervision_messages/notice_add",
    method: "post",
    data: data
  })
}

// 公告详情
export function apiBackgroundSupervisionSupervisionMessagesDetails(data: any) {
  return request<any>({
    url: "/background_supervision/supervision_messages/details",
    method: "post",
    data: data
  })
}

// 编辑公告
export function apiBackgroundSupervisionSupervisionMessagesNoticeModify(data: any) {
  return request<any>({
    url: "/background_supervision/supervision_messages/notice_modify",
    method: "post",
    data: data
  })
}

// 删除公告
export function apiBackgroundSupervisionSupervisionMessagesNoticeDelete(data: any) {
  return request<any>({
    url: "/background_supervision/supervision_messages/notice_delete",
    method: "post",
    data: data
  })
}

// 批量发布
export function apiBackgroundSupervisionSupervisionMessagesBulkPush(data: any) {
  return request<any>({
    url: "/background_supervision/supervision_messages/bulk_push",
    method: "post",
    data: data
  })
}

export default {
  apiBackgroundSupervisionSupervisionMessagesNoticeList,
  apiBackgroundSupervisionSupervisionMessagesNoticeAdd,
  apiBackgroundSupervisionSupervisionMessagesNoticeModify,
  apiBackgroundSupervisionSupervisionMessagesNoticeDelete,
  apiBackgroundSupervisionSupervisionMessagesDetails,
  apiBackgroundSupervisionSupervisionMessagesBulkPush
}
