import { request } from "@/utils/service"
/**
 * ['基础接口']
 * background_base.tasks.export_query 异步导出任务结果查询
 * @param {ExportQueryReqSer} param
 * @returns {Object}
 */
// 上传文件
export function apiBackgroundFileChannelUploadFile(param: any) {
  return request({
    url: "/background/file/channel_upload_file",
    method: "post",
    data: param
  })
}

/**
 * 异步导出任务结果查询
 *
 * @param file
 */
export function apiExportQueryPost(data: any) {
  return request({
    url: "/background_base/tasks/export_query",
    method: "post",
    data: data
  })
}

/**
 * 发送验证码给已绑定的手机号
 * @param param
 * @returns
 */
export function apiBackgroundVerificationCodeAutoPost(param: any) {
  return request({
    url: "/background/vendor_verification_code_auto",
    method: "post",
    data: param
  })
}

/**
 * 校验验证码
 */
export function apiBackgroundCheckVerificationCodePost(param: any) {
  return request({
    url: "/background/vendor_check_verification_code",
    method: "post",
    data: param
  })
}

/**
 * 获取用户报表设置信息
 * @param data {}
 * @returns
 */
export function getAccountPrintInfo(data?: any) {
  return request({
    url: "/background/admin/account/get_account_print_info",
    method: "post",
    data: data
  })
}

/**
 * 设置用户报表设置信息
 * @param data {}
 * @returns
 */
export function setAccountPrintInfo(data?: any) {
  return request({
    url: "/background/admin/account/set_account_print_info",
    method: "post",
    data: data
  })
}

import approval from "./approval"
import healthy from "./healthy"
import login from "./login"
import superApi from "./super"
import supervision from "./supervision"
import supervision_messages from "./supervision_messages"
import table from "./table"
import user from "./user"
import warning from "./warning"
import canteen from "./canteen"
import supplier from "./supplier"
import document from "./document"
import financial from "./financial"
import system from "./system"

export default {
  ...approval,
  ...healthy,
  ...login,
  ...superApi,
  ...supervision,
  ...supervision_messages,
  ...table,
  ...user,
  ...warning,
  ...canteen,
  ...supplier,
  ...document,
  ...financial,
  ...system
}
