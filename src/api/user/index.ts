import { request } from "@/utils/service"
/**
 * 分组列表
 *
 * @param data
 */
export function apiCardGroupListPost(data: any) {
  return request<any>({
    url: "/background_logistics_manage/user_manage/group_manage/list",
    method: "post",
    data: data
  })
}

/**
 * 部门列表
 *
 * @param data
 */
export function apiDepartmentListPost(data: any) {
  return request({
    url: "/background_logistics_manage/user_manage/department_manage/list",
    method: "post",
    data: data
  })
}
/**
 * 获取该组织的上下级组织
 * @param data
 * @returns
 */
export const apiBackgroundLogisticsManageLogisticsOrganizationParentTreeListPost = (data: any) => {
  return request({
    url: "/api/background_logistics_manage/logistics_organization/parent_tree_list",
    method: "post",
    data
  })
}
/**
 * 监管平台用户新增（资
 *
 * @param data
 */
export function apiBackgroundFundSupervisionAuditAccountChannelAddPost(data: any) {
  return request({
    url: "/background_fund_supervision/audit_account/channel_add",
    method: "post",
    data: data
  })
}

/**
 * 监管平台用户更改
 *
 * @param data
 */
export function apiBackgroundFundSupervisionAuditAccountChannelModifyPost(data: any) {
  return request({
    url: "/background_fund_supervision/audit_account/channel_modify",
    method: "post",
    data: data
  })
}

/**
 * 监管平台用户删除
 *
 * @param data
 */
export function apiBackgroundFundSupervisionAuditAccountChannelDeletePost(data: any) {
  return request({
    url: "/background_fund_supervision/audit_account/channel_delete",
    method: "post",
    data: data
  })
}
/**
 * 监管平台用户列表
 *
 * @param data
 */
export function apiBackgroundFundSupervisionAuditAccountChannelListPost(data: any) {
  return request({
    url: "/background_fund_supervision/audit_account/channel_list",
    method: "post",
    data: data
  })
}

/**
 * 监管平台用户导入
 *
 * @param data
 */
export function apiBackgroundFundSupervisionAuditAccountBatchAddPost(data: any) {
  return request({
    url: "/background_fund_supervision/audit_account/batch_add",
    method: "post",
    data: data
  })
}

/**
 * 监管平台角色新增
 *
 * @param data
 */
export function apiBackgroundFundSupervisionChannelRoleAddPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_role/add",
    method: "post",
    data: data
  })
}

/**
 * 监管平台角色删除
 *
 * @param data
 */
export function apiBackgroundFundSupervisionChannelRoleDeletePost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_role/delete",
    method: "post",
    data: data
  })
}

/**
 * 资金监管角色信息列表
 *
 * @param data
 */
export function apiBackgroundFundSupervisionChannelRoleListPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_role/list",
    method: "post",
    data: data
  })
}

/**
 * 资金监管角色信息列表导出
 *
 * @param data
 */
export function apiBackgroundFundSupervisionChannelRoleListExportPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_role/list_export",
    method: "post",
    data: data
  })
}

/**
 * 资金监管角色信息详情
 *
 * @param data
 */
export function apiBackgroundFundSupervisionChannelRoleRoleDetailPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_role/role_detail",
    method: "post",
    data: data
  })
}

/**
 * 资金监管角色信息修改
 *
 * @param data
 */
export function apiBackgroundFundSupervisionChannelRoleModifyPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_role/modify",
    method: "post",
    data: data
  })
}

/**
 * 资金监管角色信息状态修改
 *
 * @param data
 */
export function apiBackgroundFundSupervisionChannelRoleStatusChangePost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_role/status_change",
    method: "post",
    data: data
  })
}

/**
 * 资金后台操作历史列表
 *
 * @param data
 */
export function apiBackgroundFundSupervisionChannelOperationListPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_operation/list",
    method: "post",
    data: data
  })
}

/**
 * 监管平台用户列表批量编辑
 *
 * @param data
 */
export function apiBackgroundFundSupervisionAuditAccountBatchModifyPost(data: any) {
  return request({
    url: "/background_fund_supervision/audit_account/batch_modify",
    method: "post",
    data: data
  })
}

/**
 * 根据渠道Id获取组织
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionChannelChannelOrgsInofPost(data: any) {
  return request({
    url: "/background_fund_supervision/supervision_channel/channel_orgs_info",
    method: "post",
    data: data
  })
}

/**
 * 根据渠道Id获取组织(新) 添加了权限控制的组织，权限不勾选不会显示这些组织
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionChannelChannelRoleOrgsInofPost(data: any) {
  return request({
    url: "/background_fund_supervision/supervision_channel/channel_role_orgs_info",
    method: "post",
    data: data
  })
}

/**
 * 监管渠道列表
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionChannelListPost(data: any) {
  return request({
    url: "/background_fund_supervision/supervision_channel/channel_list",
    method: "post",
    data: data
  })
}

/**`
 * 所属食堂
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionChannelGetAllOrgPost(data: any) {
  return request({
    url: "/background_fund_supervision/supervision_channel/get_all_org",
    method: "post",
    data: data
  })
}

export function apiBackgroundFundSupervisionSupervisionPermissionGetChannelSupervisionPermissionPost(data?: any) {
  return request({
    url: "/background_fund_supervision/supervision_permission/get_channel_supervision_permission",
    method: "post",
    data: data
  })
}

export default {
  apiCardGroupListPost,
  apiDepartmentListPost,
  apiBackgroundLogisticsManageLogisticsOrganizationParentTreeListPost,
  apiBackgroundFundSupervisionAuditAccountChannelAddPost,
  apiBackgroundFundSupervisionAuditAccountChannelModifyPost,
  apiBackgroundFundSupervisionAuditAccountChannelDeletePost,
  apiBackgroundFundSupervisionAuditAccountChannelListPost,
  apiBackgroundFundSupervisionAuditAccountBatchAddPost,
  apiBackgroundFundSupervisionChannelRoleAddPost,
  apiBackgroundFundSupervisionChannelRoleDeletePost,
  apiBackgroundFundSupervisionChannelRoleListPost,
  apiBackgroundFundSupervisionChannelRoleListExportPost,
  apiBackgroundFundSupervisionChannelRoleModifyPost,
  apiBackgroundFundSupervisionChannelRoleStatusChangePost,
  apiBackgroundFundSupervisionChannelOperationListPost,
  apiBackgroundFundSupervisionAuditAccountBatchModifyPost,
  apiBackgroundFundSupervisionSupervisionChannelChannelOrgsInofPost,
  apiBackgroundFundSupervisionSupervisionChannelListPost
}
