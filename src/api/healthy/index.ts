import { request } from "@/utils/service"

// 获取营养师擅长领域
export function getNutritionistExpertiseList() {
  return request({
    url: "/background_nutritionist/nutritionist/expertise_list",
    method: "post"
  })
}
// 获取咨询订单列表
export function getConsultOrderList(data?: any) {
  return request({
    url: "/background_nutritionist/order/consult_order_list",
    method: "post",
    data
  })
}

// 获取资讯管理-我的评价列表
export function getMyAssessmentList(params: any) {
  return request({
    url: "/background_nutritionist/order/consult_order_evaluate_list",
    method: "post",
    data: params
  })
}

// 获取评价标签
export function getRapidEvaluationForHealthy(params: any) {
  return request({
    url: "/background_nutritionist/nutritionist/evaluate_list",
    method: "post",
    data: params
  })
}

// 获取咨询退款订单列表
export function getConsultRefundOrderList(params: any) {
  return request({
    url: "/background_nutritionist/order/consult_refund_order_list",
    method: "post",
    data: params
  })
}

// 接入接口
export function getApiUpdateConsultStatus(data?: any) {
  return request({
    url: "/background_nutritionist/order/update_consult_status",
    method: "post",
    data
  })
}

// 接入营养师列表
export function getApiSessionList(data?: any) {
  return request({
    url: "/background_nutritionist/consult/session_list",
    method: "post",
    data
  })
}

// 留言数据
export function getApiSessionMessageList(data?: any) {
  return request({
    url: "/background_nutritionist/consult/session_message_list",
    method: "post",
    data
  })
}

// 留言发送
export function getApiNutritionistSendConsultSession(data?: any) {
  return request({
    url: "/background_nutritionist/consult/nutritionist_send_consult_session",
    method: "post",
    data
  })
}

// 回复配置
export function getApiReplyTxtList(data?: any) {
  return request({
    url: "/background_nutritionist/nutritionist/reply_txt_list",
    method: "post",
    data
  })
}

// 获取账号信息
export function getAccountInfo(data?: any) {
  return request({
    url: "/background_nutritionist/account/info",
    method: "post",
    data
  })
}

// 账号信息编辑
export function editAccountInfo(data?: any) {
  return request({
    url: "/background_nutritionist/account/modify",
    method: "post",
    data
  })
}

// 账号密码修改
export function editAccountPassword(data?: any) {
  return request({
    url: "/background_nutritionist/account/modify_password",
    method: "post",
    data
  })
}

// 营养师切换在线状态
export function apiBackgroundNutritionistAccountUpdateLineStatusPost(data?: any) {
  return request({
    url: "/background_nutritionist/account/update_line_status",
    method: "post",
    data
  })
}
// 加时长
export function getApiSessionAddTime(data?: any) {
  return request({
    url: "/background_nutritionist/consult/session_add_time",
    method: "post",
    data
  })
}
// 审批
export function getApiNutritionistMessageApproval(data?: any) {
  return request({
    url: "/background_nutritionist/consult/nutritionist_message_approval",
    method: "post",
    data
  })
}

// 编辑默认在线
export function apiBackgroundNutritionistAccountUpdateDefaultOnlinePost(data?: any) {
  return request({
    url: "/background_nutritionist/account/update_default_online",
    method: "post",
    data
  })
}
// 咨询会话最新已读
export function getApiSessionReadNew(data?: any) {
  return request({
    url: "/background_nutritionist/consult/session_read_new",
    method: "post",
    data
  })
}

// 问题配置状态的控制
export function controlsQuestion(data?: any) {
  return request({
    url: "/background_nutritionist/nutritionist/enable_modify",
    method: "post",
    data
  })
}

// 问题配置保存
export function addQuestion(data?: any) {
  return request({
    url: "/background_nutritionist/nutritionist/question_add",
    method: "post",
    data
  })
}

// 问题配置删除
export function delQuestion(data?: any) {
  return request({
    url: "/background_nutritionist/nutritionist/question_delete",
    method: "post",
    data
  })
}

// 问题配置编辑
export function editQuestion(data?: any) {
  return request({
    url: "/background_nutritionist/nutritionist/question_modify",
    method: "post",
    data
  })
}

// 问题配置列表获取
export function getQuestionList(data?: any) {
  return request({
    url: "/background_nutritionist/nutritionist/question_list",
    method: "post",
    data
  })
}

// 问题配置列表获取
export function getMqttInfo() {
  return request({
    url: "/background_nutritionist/common/get_mqtt_info",
    method: "post"
  })
}

// 咨询用户信息
export function getApiConsultUserInfo(data?: any) {
  return request({
    url: "/background_nutritionist/consult/consult_user_info",
    method: "post",
    data
  })
}

// 数据汇总-获取咨询情况
export function getDataSummaryOfConsultSituation(data?: any) {
  return request({
    url: "/background_nutritionist/data_screen/consult_situation",
    method: "post",
    data
  })
}

// 数据汇总-获取收益趋势
export function getDataSummaryOfEarningsTrends(data?: any) {
  return request({
    url: "/background_nutritionist/data_screen/earnings_trends",
    method: "post",
    data
  })
}

// 数据汇总-获取评价系数
export function getDataSummaryOfEvaluateCoefficient(data?: any) {
  return request({
    url: "/background_nutritionist/data_screen/evaluate_coefficient",
    method: "post",
    data
  })
}

// 数据汇总-获取收益情况
export function getDataSummaryOfIncomeSituation(data?: any) {
  return request({
    url: "/background_nutritionist/data_screen/income_situation",
    method: "post",
    data
  })
}

export default {
  apiBackgroundNutritionistAccountUpdateLineStatusPost,
  getApiSessionList,
  getApiSessionMessageList,
  getApiNutritionistSendConsultSession,
  getApiReplyTxtList,
  getAccountInfo,
  editAccountInfo,
  editAccountPassword,
  getApiSessionAddTime,
  getApiNutritionistMessageApproval,
  apiBackgroundNutritionistAccountUpdateDefaultOnlinePost,
  getApiSessionReadNew,
  controlsQuestion,
  addQuestion,
  delQuestion,
  editQuestion,
  getQuestionList,
  getMqttInfo,
  getApiConsultUserInfo,
  getDataSummaryOfConsultSituation,
  getDataSummaryOfEarningsTrends,
  getDataSummaryOfEvaluateCoefficient,
  getDataSummaryOfIncomeSituation
}
