import { request } from "@/utils/service"

// 资产统计表
export function apiBackgroundFundSupervisionAssetChannelAssetInfoStatisticsList(data: any) {
  return request({
    url: "/background_fund_supervision/asset/channel_asset_info_statistics_list",
    method: "post",
    data: data
  })
}
// 资产统计明细表
export function apiBackgroundFundSupervisionAssetChannelAssetInfoStatisticsDetailsList(data: any) {
  return request({
    url: "/background_fund_supervision/asset/channel_asset_info_statistics_details_list",
    method: "post",
    data: data
  })
}
// 资产统计明细表 导出
export function apiBackgroundFundSupervisionAssetChannelAssetInfoStatisticsListExport(data: any) {
  return request({
    url: "/background_fund_supervision/asset/channel_asset_info_statistics_list_export",
    method: "post",
    data: data
  })
}

// 负债统计表
export function apiBackgroundFundSupervisionBusLiabilitySupLiabilityStatisticsList(data: any) {
  return request({
    url: "/background_fund_supervision/bus_liability/sup/liability_statistics_list",
    method: "post",
    data: data
  })
}
// 负债统计表导出
export function apiBackgroundFundSupervisionBusLiabilitySupLiabilityDetailExport(data: any) {
  return request({
    url: "/background_fund_supervision/bus_liability/sup/liability_detail_export",
    method: "post",
    data: data
  })
}
// 负债系统明细
export function apiBackgroundFundSupervisionBusLiabilitySupLiabilityDetailList(data: any) {
  return request({
    url: "/background_fund_supervision/bus_liability/sup/liability_detail_list",
    method: "post",
    data: data
  })
}
// 负债统计合计
export function apiBackgroundFundSupervisionBusLiabilitySupLiabilityTotal(data: any) {
  return request({
    url: "/background_fund_supervision/bus_liability/sup/liability_total",
    method: "post",
    data: data
  })
}

export default {
  apiBackgroundFundSupervisionAssetChannelAssetInfoStatisticsList,
  apiBackgroundFundSupervisionAssetChannelAssetInfoStatisticsDetailsList,
  apiBackgroundFundSupervisionAssetChannelAssetInfoStatisticsListExport,
  apiBackgroundFundSupervisionBusLiabilitySupLiabilityStatisticsList,
  apiBackgroundFundSupervisionBusLiabilitySupLiabilityDetailExport,
  apiBackgroundFundSupervisionBusLiabilitySupLiabilityDetailList,
  apiBackgroundFundSupervisionBusLiabilitySupLiabilityTotal
}
