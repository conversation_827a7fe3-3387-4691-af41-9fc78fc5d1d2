import { request } from "@/utils/service"

// 采购单数据
export function apiChannelPurchaseInfo(params: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_data/channel_purchase_info",
    method: "post",
    data: params
  })
}
// 采购单详情
export function apiPurchaseDetailList(params: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_data/purchase_detail_list",
    method: "post",
    data: params
  })
}

// 出入库
export function apiInoutInfoList(params: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_data/inout_info_list",
    method: "post",
    data: params
  })
}

// 配送单
export function apiVendorDeliveryList(params: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_data/vendor_delivery_list",
    method: "post",
    data: params
  })
}

// 配送单详情
export function apiVendorDeliveryDetailList(params: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_data/vendor_delivery_detail_list",
    method: "post",
    data: params
  })
}

// 收货单
export function apiReceivingNoteList(params: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_data/receiving_note_list",
    method: "post",
    data: params
  })
}

// 收货单详情
export function apiReceivingNoteDetailList(params: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_data/receiving_note_detail_list",
    method: "post",
    data: params
  })
}

// 结算单
export function apiFinalStatementList(params: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_data/final_statement_list",
    method: "post",
    data: params
  })
}

/**
 * 库存台账
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionDataInventoryBalanceListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_data/inventory_balance_list",
    method: "post",
    data: data
  })
}

/**
 * 库存台账导出
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionDataInventoryBalanceListExportPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_data/inventory_balance_list_export",
    method: "post",
    data: data
  })
}

/**
 * 申请单列表
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionDataApplicationFormListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_data/application_form_list",
    method: "post",
    data: data
  })
}
/**
 * 申请单列表导出
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionDataApplicationFormListExportPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_data/application_form_list_export",
    method: "post",
    data: data
  })
}

/**
 * 结算单导出
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionDataFinalStatementListExportPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_data/final_statement_list_export",
    method: "post",
    data: data
  })
}

export default {
  apiBackgroundFundSupervisionSupervisionDataInventoryBalanceListPost,
  apiBackgroundFundSupervisionSupervisionDataApplicationFormListPost
}
