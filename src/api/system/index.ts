import { request } from "@/utils/service"

export function apiBackgroundSupervisionSupervisionMessagesListPost(data: any) {
  return request({
    url: "/background_supervision/supervision_messages/list",
    method: "post",
    data: data
  })
}

export function apiBackgroundSupervisionSupervisionMessagesModifyPost(data: any) {
  return request({
    url: "/background_supervision/supervision_messages/modify",
    method: "post",
    data: data
  })
}

export function apiBackgroundSupervisionSupervisionMessagesGetQuestionnaireMsgNumPost() {
  return request({
    url: "/background_supervision/supervision_messages/get_questionnaire_msg_num",
    method: "post"
  })
}

export function apiBackgroundSupervisionSupervisionMessagesGetQuestionnaireMsgListPost(data: any) {
  return request({
    url: "/background_supervision/supervision_messages/get_questionnaire_msg_list",
    method: "post",
    data: data
  })
}

export function apiBackgroundSupervisionSupervisionMessagesBulkQuestionnaireMsgReadPost(data: any) {
  return request({
    url: "/background_supervision/supervision_messages/bulk_questionnaire_msg_read",
    method: "post",
    data: data
  })
}

export function apiBackgroundSupervisionSupervisionMessagesBulkQuestionnaireMsgCancelPopPost(data: any) {
  return request({
    url: "/background_supervision/supervision_messages/bulk_questionnaire_msg_cancel_pop",
    method: "post",
    data: data
  })
}

export default {
  apiBackgroundSupervisionSupervisionMessagesListPost,
  apiBackgroundSupervisionSupervisionMessagesModifyPost,
  apiBackgroundSupervisionSupervisionMessagesGetQuestionnaireMsgNumPost,
  apiBackgroundSupervisionSupervisionMessagesGetQuestionnaireMsgListPost,
  apiBackgroundSupervisionSupervisionMessagesBulkQuestionnaireMsgReadPost,
  apiBackgroundSupervisionSupervisionMessagesBulkQuestionnaireMsgCancelPopPost
}
