import { request } from "@/utils/service"
/**
 * 经营预警
 *
 * @param data
 */
export function apiBackgroundFundSupervisionWarnManageWarningMessageListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/warn_manage/warning_message_list",
    method: "post",
    data: data
  })
}

/**
 * 编辑预警信息
 *
 * @param data
 */
export function apiBackgroundFundSupervisionWarnManageBusinessWarnModifyPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/warn_manage/business_warn/modify",
    method: "post",
    data: data
  })
}

/**
 * 设置预警信息
 *
 * @param data
 */
export function apiBackgroundFundSupervisionOrganizationSupervisionSettingInfoModifyPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/setting_info_modify",
    method: "post",
    data: data
  })
}

/**
 * 获取到组织的预警信息
 *
 * @param data
 */
export function apiBackgroundFundSupervisionOrganizationSupervisionSettingInfoDetailPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/organization_supervision/setting_info_detail",
    method: "post",
    data: data
  })
}

/**
 * 获取物资风险
 *
 * @param data
 */
export function apiBackgroundFundSupervisionWarnDispositionMaterialRiskListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/warn_disposition/material_risk_list",
    method: "post",
    data: data
  })
}

/**
 * 获取证件/合同预警
 *
 * @param data
 */
export function apiBackgroundFundSupervisionWarnDispositionDocContWarnListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/warn_disposition/doc_cont_warn_list",
    method: "post",
    data: data
  })
}

/**
 * 获取预警配置
 *
 * @param data
 */
export function apiBackgroundFundSupervisionWarnDispositionWarnSettingDetailPost(data: any) {
  return request<any>({
    url: "background_fund_supervision/warn_disposition/warn_setting_detail",
    method: "post",
    data: data
  })
}

/**
 * 获取分类属性
 *
 * @param data
 */
export function apiBackgroundFundSupervisionOrganizationSupervisionGetMaterialsAttributePost(data: any) {
  return request<any>({
    url: "background_fund_supervision/organization_supervision/get_materials_attribute",
    method: "post",
    data: data
  })
}

/**
 * 保存预警配置
 *
 * @param data
 */
export function apiBackgroundFundSupervisionWarnDispositionWarnSettingEditPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/warn_disposition/warn_setting_edit",
    method: "post",
    data: data
  })
}

/**
 * 获取物资预警
 *
 * @param data
 */
export function apiBackgroundFundSupervisionWarnDispositionMaterialWarnListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/warn_disposition/material_warn_list",
    method: "post",
    data: data
  })
}

/**
 * 新增物资预警
 *
 * @param data
 */
export function apiBackgroundFundSupervisionWarnDispositionMaterialWarnAddPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/warn_disposition/material_warn_add",
    method: "post",
    data: data
  })
}

/**
 * 编辑物资预警
 *
 * @param data
 */
export function apiBackgroundFundSupervisionWarnDispositionMaterialWarnEditPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/warn_disposition/material_warn_edit",
    method: "post",
    data: data
  })
}

/**
 * 删除物资预警
 *
 * @param data
 */
export function apiBackgroundFundSupervisionWarnDispositionMaterialWarnDeletePost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/warn_disposition/material_warn_delete",
    method: "post",
    data: data
  })
}

/**
 * 获取证件/合同预警
 *
 * @param data
 */
export function apiBackgroundFundSupervisionWarnDispositionContractWarnListPost(data?: any) {
  return request<any>({
    url: "/background_fund_supervision/warn_disposition/contract_warn_list",
    method: "post",
    data: data
  })
}

/**
 * 修改证件/合同预警
 *
 * @param data
 */
export function apiBackgroundFundSupervisionWarnDispositionContractWarnEditPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/warn_disposition/contract_warn_edit",
    method: "post",
    data: data
  })
}

/**
 * 获取预警通知
 *
 * @param data
 */
export function apiBackgroundFundSupervisionWarnDispositionWarnNoticeTagListPost(data?: any) {
  return request<any>({
    url: "/background_fund_supervision/warn_disposition/warn_notice_tag_list",
    method: "post",
    data: data
  })
}

/**
 * 已读预警通知
 *
 * @param data
 */
export function apiBackgroundFundSupervisionWarnDispositionWarnNoticeTagReadPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/warn_disposition/warn_notice_tag_read",
    method: "post",
    data: data
  })
}

/**
 * 获取预警红点通知
 *
 * @param data
 */
export function apiBackgroundFundSupervisionWarnDispositionWarnNoticeRedListPost(data?: any) {
  return request<any>({
    url: "/background_fund_supervision/warn_disposition/warn_notice_red_list",
    method: "post",
    data: data
  })
}

/**
 * 已读预警红点通知
 *
 * @param data
 */
export function apiBackgroundFundSupervisionWarnDispositionWarnNoticeRedReadPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/warn_disposition/warn_notice_red_read",
    method: "post",
    data: data
  })
}

export default {
  apiBackgroundFundSupervisionWarnManageWarningMessageListPost,
  apiBackgroundFundSupervisionWarnManageBusinessWarnModifyPost,
  apiBackgroundFundSupervisionOrganizationSupervisionSettingInfoModifyPost,
  apiBackgroundFundSupervisionOrganizationSupervisionSettingInfoDetailPost
}
