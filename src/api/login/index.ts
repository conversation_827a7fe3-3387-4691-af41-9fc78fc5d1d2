import { request } from "@/utils/service"
import type * as Login from "./types/login"

// 注册营养师
export function registeredDietitian(params: any) {
  return request({
    url: "/background_nutritionist/account/register",
    method: "post",
    data: params
  })
}

/** 获取登录验证码 */
export function getLoginCodeApi() {
  return request<Login.LoginCodeResponseData>({
    url: "/background/get_login_verify_code",
    method: "post"
  })
}

/** 登录并返回 Token */
export function loginApi(data: Login.LoginRequestData) {
  return request<Login.LoginResponseData>({
    url: "/background/supervision_login",
    // url: "/background_nutritionist/login",
    method: "post",
    data
  })
}

/** 获取用户详情 */
export function getUserInfoApi() {
  return request<Login.UserInfoResponseData>({
    url: "users/info",
    method: "get"
  })
}

export function apiBackgroundLogout() {
  return request<any>({
    url: "/background/supervision_logout",
    method: "post"
  })
}

/** 获取菜单列表 */
export function getList(data?: any) {
  return request<any>({
    url: "background_base/menu/get_list",
    method: "post",
    data
  })
}

/** base.login.get_sms_verify_code 获取短信图形校验验证码 */
export function apiBackgroundGetSmsVerifyCodePost(data?: any) {
  return request<any>({
    url: "/background/get_sms_verify_code",
    method: "post",
    data
  })
}

/** base.login.verification_code 发送登陆验证码 */
export function apiBackgroundVerificationCodePost(data?: any) {
  return request<any>({
    url: "/background/verification_code",
    method: "post",
    data
  })
}

/** 监管平台用户配置 */
export function apiBackgroundFundSupervisionAuditAccountAccountSetPost(data?: any) {
  return request<any>({
    url: "/background_fund_supervision/audit_account/account_set",
    method: "post",
    data
  })
}

/** 监管平台用户更改 */
export function apiBackgroundFundSupervisionAuditAccountModifyPost(data?: any) {
  return request<any>({
    url: "/background_fund_supervision/audit_account/modify",
    method: "post",
    data
  })
}

/** 协议 agreement_type 协议类型 */
export function apiBackgroundAdminAgreementAgreementDetailByTypePost(data?: any) {
  return request<any>({
    url: "/background/admin/agreement/agreement_detail_by_type",
    method: "post",
    data
  })
}

export default {
  apiBackgroundLogout,
  getList,
  apiBackgroundGetSmsVerifyCodePost,
  apiBackgroundVerificationCodePost,
  apiBackgroundFundSupervisionAuditAccountAccountSetPost,
  apiBackgroundFundSupervisionAuditAccountModifyPost,
  apiBackgroundAdminAgreementAgreementDetailByTypePost,
  registeredDietitian,
  getLoginCodeApi,
  loginApi,
  getUserInfoApi
}
