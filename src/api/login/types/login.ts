export interface LoginRequestData {
  /** admin 或 editor */
  username?: "admin" | "editor" | string
  /** 密码 */
  password?: string
  /** 图形验证码 */
  verify_code?: string
  /** 模式 */
  mode: string
  /** 协议 */
  agreement_types?: string[]
  /** 项目点ID */
  company_id?: string
  /** 手机号 */
  phone?: string
  /** 短信获取的点文字校验码 */
  code?: string
  /** 短信验证码 */
  sms_code?:string
}

export type LoginCodeResponseData = ApiResponseData<string>

export type LoginResponseData = ApiResponseData<{ token: string }>

export type UserInfoResponseData = ApiResponseData<{ username: string; roles: string[] }>
