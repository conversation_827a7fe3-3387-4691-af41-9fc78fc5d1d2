import { request } from "@/utils/service"
/**
 * 供应商列表
 *
 * @param data
 */
export function apiBackgroundFundSupervisionAppropriationSupplierManageListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supplier_manage/supplier_manage_list",
    method: "post",
    data: data
  })
}
/**
 * 供应商列表
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupplierManageBackgroundSupplierManageListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supplier_manage/background_supplier_manage_list",
    method: "post",
    data: data
  })
}

/**
 * 供应商指派
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupplierManageSupplierManageAssignPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supplier_manage/supplier_manage_assign",
    method: "post",
    data: data
  })
}

/**
 * 监管渠道下面的组织数据
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupervisionChannelGetChannelOrgListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_channel/get_channel_org_list",
    method: "post",
    data: data
  })
}

/**
 * 渠道供应商结算单数据
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupplierManageSupplierFinalStatementinfoListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supplier_manage/supplier_final_statementinfo_list",
    method: "post",
    data: data
  })
}

/**
 * 供应商供应物资接口
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupplierManageSupplierManageApplyMaterialsPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supplier_manage/supplier_manage_apply_materials",
    method: "post",
    data: data
  })
}

/**
 * 供求关系导出
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupplierManageSupplyRelationshipInfoExportPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supplier_manage/supply_relationship_info_export",
    method: "post",
    data: data
  })
}

/**
 * 供求关系
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupplierManageSupplyRelationshipInfoPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supplier_manage/supply_relationship_info",
    method: "post",
    data: data
  })
}

/**
 * 结算明细 新
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupplierManageSupplierFinalStatementInfoListNewPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supplier_manage/supplier_final_statement_info_list",
    method: "post",
    data: data
  })
}

/**
 * 解除绑定
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupplierManageSupplierManageUnbindPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supplier_manage/supplier_manage_unbind",
    method: "post",
    data: data
  })
}

/**
 * 供应商绑定组织（过滤绑定列表）
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupplierGetChannelOrgBindListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supervision_channel/get_channel_org_bind_list",
    method: "post",
    data: data
  })
}

/**
 * 供应商绑定组织（过滤绑定列表）
 *
 * @param data
 */
export function apiBackgroundFundSupervisionSupplierChannelDemocraticFeedbackListPost(data: any) {
  return request<any>({
    url: "/background_fund_supervision/supplier_manage/channel_democratic_feedback_list",
    method: "post",
    data: data
  })
}

export default {
  apiBackgroundFundSupervisionAppropriationSupplierManageListPost,
  apiBackgroundFundSupervisionSupplierManageSupplierManageAssignPost,
  apiBackgroundFundSupervisionSupervisionChannelGetChannelOrgListPost,
  apiBackgroundFundSupervisionSupplierManageSupplierFinalStatementinfoListPost,
  apiBackgroundFundSupervisionSupplierManageSupplyRelationshipInfoPost
}
