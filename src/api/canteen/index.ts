import { request } from "@/utils/service"

// 留样记录
export function apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_canteen_management/food_reserved_sample_record",
    method: "post",
    data: data
  })
}

// 留样记录导出
export function apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordExportPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_canteen_management/food_reserved_sample_record_export",
    method: "post",
    data: data
  })
}

// 获取操作人员人脸信息
export function apiBackgroundFundSupervisionChannelCanteenManagementGetAccountFaceUrlPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_canteen_management/get_account_face_url",
    method: "post",
    data: data
  })
}
// 更新未入柜原因
export function apiBackgroundFundSupervisionChannelCanteenManagementModifyNotEntryReasonPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_canteen_management/modify_not_entry_reason",
    method: "post",
    data: data
  })
}

// 更新未留样原因
export function apiBackgroundFundSupervisionChannelCanteenManagementModifyNotReservedReasonPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_canteen_management/modify_not_reserved_reason",
    method: "post",
    data: data
  })
}

// 获取留样设备
export function apiBackgroundDeviceDeviceListPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_canteen_management/device_list",
    method: "post",
    data: data
  })
}

// 获取留样菜谱
export function apiBackgroundMenuTypeListPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_canteen_management/menu_list",
    method: "post",
    data: data
  })
}

// 有害生物防制记录
export function apiBackgroundFundSupervisionChannelCanteenManagementPestControlRecordPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_canteen_management/pest_control_record",
    method: "post",
    data: data
  })
}

// 有害生物防制记录导出
export function apiBackgroundFundSupervisionChannelCanteenManagementPestControlRecordExportPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_canteen_management/pest_control_record_export",
    method: "post",
    data: data
  })
}

// 资金民主反馈
export function apiBackgroundFundSupervisionChannelCanteenManagementChannelDemocraticFeedbackListPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_canteen_management/channel_democratic_feedback_list",
    method: "post",
    data: data
  })
}

// 资金民主反馈导出
export function apiBackgroundFundSupervisionChannelCanteenManagementChannelDemocraticFeedbackListExportPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_canteen_management/channel_democratic_feedback_list_export",
    method: "post",
    data: data
  })
}

// 资金陪餐记录
export function apiBackgroundFundSupervisionChannelCanteenManagementChannelMealAccompanyingListPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_canteen_management/channel_meal_accompanying_list",
    method: "post",
    data: data
  })
}

// 资金陪餐记录导出
export function apiBackgroundFundSupervisionChannelCanteenManagementChannelMealAccompanyingListExportPost(data: any) {
  return request({
    url: "/background_fund_supervision/channel_canteen_management/channel_meal_accompanying_list_export",
    method: "post",
    data: data
  })
}

// 资金市场询价
export function apiBackgroundFundSupervisionFundMarketInquiryListPost(data: any) {
  return request({
    url: "/background_fund_supervision/fund_market_inquiry/list",
    method: "post",
    data: data
  })
}

// 资金市场询价添加
export function apiBackgroundFundSupervisionFundMarketInquiryAddPost(data: any) {
  return request({
    url: "/background_fund_supervision/fund_market_inquiry/add",
    method: "post",
    data: data
  })
}

// 资金市场询价导入
export function apiBackgroundFundSupervisionFundMarketInquiryImportInquiryDetailPost(data: any) {
  return request({
    url: "/background_fund_supervision/fund_market_inquiry/import_inquiry_detail",
    method: "post",
    data: data
  })
}

// 资金市场询价删除
export function apiBackgroundFundSupervisionFundMarketInquiryDeletePost(data: any) {
  return request({
    url: "/background_fund_supervision/fund_market_inquiry/delete",
    method: "post",
    data: data
  })
}

// 资金市场询价删除
export function apiBackgroundFundSupervisionFundMarketInquiryListExportPost(data: any) {
  return request({
    url: "/background_fund_supervision/fund_market_inquiry/list_export",
    method: "post",
    data: data
  })
}

export default {
  apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordPost,
  apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordExportPost,
  apiBackgroundFundSupervisionChannelCanteenManagementGetAccountFaceUrlPost,
  apiBackgroundFundSupervisionChannelCanteenManagementModifyNotEntryReasonPost,
  apiBackgroundFundSupervisionChannelCanteenManagementModifyNotReservedReasonPost,
  apiBackgroundDeviceDeviceListPost,
  apiBackgroundMenuTypeListPost,
  apiBackgroundFundSupervisionChannelCanteenManagementPestControlRecordPost,
  apiBackgroundFundSupervisionChannelCanteenManagementPestControlRecordExportPost,
  apiBackgroundFundSupervisionChannelCanteenManagementChannelDemocraticFeedbackListPost,
  apiBackgroundFundSupervisionChannelCanteenManagementChannelDemocraticFeedbackListExportPost,
  apiBackgroundFundSupervisionChannelCanteenManagementChannelMealAccompanyingListPost,
  apiBackgroundFundSupervisionChannelCanteenManagementChannelMealAccompanyingListExportPost,
  apiBackgroundFundSupervisionFundMarketInquiryListPost,
  apiBackgroundFundSupervisionFundMarketInquiryAddPost,
  apiBackgroundFundSupervisionFundMarketInquiryImportInquiryDetailPost
}
