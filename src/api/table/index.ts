import { request } from "@/utils/service"
import type * as Table from "./types/table"

/** 增 */
export function createTableDataA<PERSON>(data: Table.CreateOrUpdateTableRequestData) {
  return request({
    url: "table",
    method: "post",
    data
  })
}

/** 删 */
export function deleteTableDataApi(id: string) {
  return request({
    url: `table/${id}`,
    method: "delete"
  })
}

/** 改 */
export function updateTableDataApi(data: Table.CreateOrUpdateTableRequestData) {
  return request({
    url: "table",
    method: "put",
    data
  })
}

/** 查 */
export function getTableDataApi(params: Table.GetTableRequestData) {
  return request<Table.GetTableResponseData>({
    url: "table",
    method: "get",
    params
  })
}

export default {
  createTableData<PERSON>pi,
  deleteTableDataApi,
  updateTableData<PERSON><PERSON>,
  getTableDataApi
}
