/** 统一处理 Cookie */

import <PERSON><PERSON><PERSON><PERSON> from "@/constants/cache-key"
import Cookies from "js-cookie"

export const getToken = () => {
  let token = Cookies.get(CacheKey.TOKEN)
  if (!token || token === undefined) {
    const user = JSON.parse(localStorage.getItem("user") ?? "{}")
    token = user.token
  }
  return token
}
export const setToken = (token: string) => {
  Cookies.set(CacheKey.TOKEN, token)
}
export const removeToken = () => {
  Cookies.remove(CacheKey.TOKEN)
}
