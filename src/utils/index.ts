import dayjs from "dayjs"
import { removeConfigLayout } from "@/utils/cache/local-storage"
import { ElMessage } from "element-plus"
import html2canvas from "html2canvas"
// 封装倒计时逻辑函数
import { computed, onUnmounted, ref, Ref } from "vue"
import { cloneDeep } from "lodash"
import router from "@/router"
import { setLocalStorage } from "./storage"
import NP from "number-precision"
/** 格式化时间 */
export const formatDateTime = (time: string | number | Date) => {
  return time ? dayjs(new Date(time)).format("YYYY-MM-DD HH:mm:ss") : "N/A"
}

/** 格式化时间 */
export const formatDateTimeByType = (time: string | number | Date, formatType?: string) => {
  return time ? dayjs(new Date(time)).format(formatType ? formatType : "YYYY-MM-DD HH:mm:ss") : "N/A"
}

/** sleep函数 */
export function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

/**
 * @description 下载文件
 * @param url 文件地址或 Blob 对象
 * @param isBlob 是否为 Blob 对象
 * @param name 下载文件的名称（当 url 为文件地址时有效）
 */
export function download(url: any, isBlob?: boolean, name?: string): void {
  const aTag = document.createElement("a")
  let blob: any = null

  if (isBlob) {
    // 处理 Blob 对象的下载
    const content = url
    blob = new Blob([content])
    aTag.href = URL.createObjectURL(blob)
  } else {
    // 处理普通文件地址的下载
    let fileName: string | null = name || ""
    if (!fileName) {
      // 从 url 中获取文件名
      fileName = typeof url === "string" ? url.substring(url.lastIndexOf("/") + 1) : ""
      console.log("fileName", fileName, typeof url)
      const index = fileName.indexOf("?")
      if (index !== -1) {
        fileName = fileName.substring(0, index)
      }
    }
    aTag.download = fileName
    aTag.href = typeof url === "string" ? url : ""
  }

  // 触发下载
  aTag.click()

  // 释放资源
  if (blob) {
    URL.revokeObjectURL(blob)
  }
}
/**
 * @description 去除空格
 * @param str 要处理的字符串或数字
 * @param type 去除空格的类型
 * @returns 处理后的字符串
 */
export const trim = (str: string | number, type: number): string => {
  const strType = typeof str

  if (strType === "number") {
    str = str.toString()
  }

  if (typeof str === "string") {
    switch (type) {
      case 1:
        return str.replace(/\s+/g, "")
      case 2:
        return str.replace(/(^\s*)|(\s*$)/g, "")
      case 3:
        return str.replace(/(^\s*)/g, "")
      case 4:
        return str.replace(/(\s*$)/g, "")
      default:
        return str
    }
  }

  return str.toString() // 处理其他类型
}

/** 用 JS 获取全局 css 变量 */
export const getCssVariableValue = (cssVariableName: string) => {
  let cssVariableValue = ""
  try {
    // 没有拿到值时，会返回空串
    cssVariableValue = getComputedStyle(document.documentElement).getPropertyValue(cssVariableName)
  } catch (error) {
    console.error(error)
  }
  return cssVariableValue
}

/** 用 JS 设置全局 CSS 变量 */
export const setCssVariableValue = (cssVariableName: string, cssVariableValue: string) => {
  try {
    document.documentElement.style.setProperty(cssVariableName, cssVariableValue)
  } catch (error) {
    console.error(error)
  }
}

/** 重置项目配置 */
export const resetConfigLayout = () => {
  removeConfigLayout()
  location.reload()
}

/**
 * * render 语言
 *  @param lang 语言标识
 *  @param set 设置项
 *  @param tag 要渲染成的标签
 */
export const renderLang = (lang: string) => {
  // @ts-ignore
  return window["$t"](lang)
}

/**
 * @description async/await 的 try_catch处理吧 参考await-to-js
 * @param {*} promise
 * @param {*} errorExt
 */
export const to = <T>(promise: Promise<T>, errorExt?: any) => {
  return promise
    .then((res) => [null, res])
    .catch((err) => {
      if (errorExt) {
        Object.assign(err, errorExt)
      }
      return [err, undefined]
    })
}
export const useCopyToClipboard = (val: string) => {
  //创建input标签
  const input = document.createElement("input")
  //将input的值设置为需要复制的内容
  input.value = val
  //添加input标签
  document.body.appendChild(input)
  //选中input标签
  input.select()
  //执行复制
  document.execCommand("copy")
  if (document.execCommand("copy")) {
    ElMessage.success("复制成功")
  } else {
    ElMessage.error("复制失败")
  }
  //移除input标签
  document.body.removeChild(input)
}
// 倒计时
export const useCountDown = () => {
  let timer: NodeJS.Timeout | any = null
  const totalTimeInSeconds = ref(0) // 以秒为单位存储总剩余时间

  // 格式化时间，确保小时正确显示
  const formatTime = computed(() => {
    const totalSeconds = totalTimeInSeconds.value
    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = Math.floor(totalSeconds % 60)

    // 格式化为两位数
    const formatNumber = (n: number) => (n < 10 ? "0" + n : n)
    let timeName = ""
    if (hours > 0 || minutes > 0 || seconds > 0) {
      timeName = `${formatNumber(hours)}:${formatNumber(minutes)}:${formatNumber(seconds)}`
    } else {
      timeName = "本次咨询已结束"
    }
    return timeName
  })

  // 开启倒计时的函数
  const start = (totalSeconds: number) => {
    clearInterval(timer)
    timer = null // 倒计时结束后重置timer
    // totalTimeInSeconds.value = 0
    // console.log(totalSeconds)
    totalTimeInSeconds.value = totalSeconds
    timer = setInterval(() => {
      if (totalTimeInSeconds.value > 0) {
        totalTimeInSeconds.value--
      } else {
        clearInterval(timer)
        timer = null // 倒计时结束后重置timer
      }
    }, 1000)
  }

  // 组件销毁时清除定时器
  onUnmounted(() => {
    timer && clearInterval(timer)
  })

  return {
    formatTime,
    start
  }
}
// 生成海报并下载
export const generatePosterAndDownload = async (ref: any, name: string) => {
  try {
    const scale = 4 // 考虑设备像素比
    // const dpi = 300
    // const a5WidthPx = (148 * dpi) / 25.4 // 转换毫米到像素
    // const a5HeightPx = (210 * dpi) / 25.4
    // console.log(a5WidthPx, a5HeightPx, 7777)
    // 使用html2canvas捕获DOM内容为canvas
    const canvas = await html2canvas(ref, {
      scale,
      useCORS: true // 允许图片跨域
    })
    // 将canvas转换为Blob对象
    const imgData = canvas.toDataURL("image/png") // 可以更改为'image/jpeg'等其他格式
    const byteString = atob(imgData.split(",")[1])
    const ia = new Uint8Array(byteString.length)
    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i)
    }
    const blob = new Blob([ia], { type: "image/png" })
    // 创建隐藏的a标签来下载
    const link = document.createElement("a")
    link.href = URL.createObjectURL(blob)
    link.download = name // 设置下载的文件名
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(link.href) // 释放URL对象
  } catch (error) {
    console.error("生成海报时出错:", error)
  }
}

// 获取静态文件路径
export const getAssetsFile = (url: string) => {
  // const path = `../${url}`
  return new URL(url, import.meta.url).href
}

// 获取文件后缀名
export function getSuffix(filename: string) {
  const pos = filename.lastIndexOf(".")
  let suffix = ""
  if (pos !== -1) {
    suffix = filename.substring(pos).toLowerCase()
  }
  return suffix
}

// 将HTML特殊字符转换成等值的实体
export function escapeHTML(str: string) {
  const escapeChars = {
    "<": "lt",
    ">": "gt",
    '"': "quot",
    "&": "amp",
    "'": "#39"
  }
  return str.replace(new RegExp("[" + Object.keys(escapeChars).join("") + "]", "g"), function (match) {
    // @ts-ignore
    return "&" + escapeChars[match] + ";"
  })
}

// 实体字符转换为等值的HTML。
export function unescapeHTML(str: string) {
  const htmlEntities = {
    nbsp: " ",
    lt: "<",
    gt: ">",
    quot: '"',
    amp: "&",
    apos: "'"
  }
  return str.replace(/&([^;]+);/g, function (match, key) {
    if (key in htmlEntities) {
      // @ts-ignore
      return htmlEntities[key]
    }
    return match
  })
}

/**
 * 日期格式化
 * @param time
 * @param format
 * @returns {string}
 */
export const parseTime = function (time: any, cFormat: string) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}"
  let date
  if (typeof time === "object") {
    date = time
  } else {
    if (typeof time === "string") {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), "/").replace(new RegExp(/T/gm), " ") // "2021-10-27T19:49:43.806066"
      }
    }

    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  // padStart兼容处理
  if (!String.prototype.padStart) {
    // eslint-disable-next-line no-extend-native
    String.prototype.padStart = function padStart(targetLength, padString) {
      targetLength = targetLength >> 0 // floor if number or convert non-number to 0;
      padString = String(typeof padString !== "undefined" ? padString : " ")
      if (this.length > targetLength) {
        return String(this)
      } else {
        targetLength = targetLength - this.length
        if (targetLength > padString.length) {
          padString += padString.repeat(targetLength / padString.length) // append to original to ensure we are longer than needed
        }
        return padString.slice(0, targetLength) + String(this)
      }
    }
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    // @ts-ignore
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value]
    }
    return value.toString().padStart(2, "0")
  })
  return time_str
}
// 格式化参数
export const formatQueryParams = (data: any) => {
  const params: Record<string, any> = {}
  for (const key in data) {
    if (data[key].value !== "") {
      if (key !== "selecttime") {
        const value = data[key].value
        if (value) {
          params[key] = data[key].value
        }
      } else if (key === "selecttime" && data[key].value && data[key].value.length > 0) {
        params.start_date = data[key].value[0]
        params.end_date = data[key].value[1]
      }
    }
  }
  return params
}

// 数据千分位格式化
export const moneyThousandFormat = (cellValue: string) => {
  if (cellValue) {
    cellValue += ""
    if (!cellValue.includes(".")) cellValue += "."
    return cellValue
      .replace(/(\d)(?=(\d{3})+\.)/g, ($0, $1) => {
        return $1 + ","
      })
      .replace(/\.$/, "")
  }
  return "0"
}
// 千分符反格式化
export const moneyThousandNotFormat = (s: string): number => {
  let str = ""
  if (s) {
    str = String(s).replace(/￥|,/g, "")
  }

  if (s && (s + "").indexOf(".") > -1 && Number(str)) {
    return Number(String(s).replace(/[^\d\.-]/g, ""))
  } else if (s && Number(str)) {
    return Number(str)
  } else {
    return Number(str)
  }
}
// 获取演示数据
export const getTestData = (
  tableData: Ref<Array<any>>,
  testData: Array<any>,
  tableLoading: Ref<boolean>,
  pageNo: number
) => {
  return new Promise((resolve) => {
    tableLoading.value = true
    setTimeout(() => {
      tableLoading.value = false
      if (testData && testData.length <= 10) {
        tableData.value = cloneDeep(testData)
      } else {
        tableData.value = cloneDeep(testData.slice((pageNo - 1) * 10, pageNo * 10))
      }
      resolve(testData)
    }, 500)
  })
}
/**
 * 查找查询数据  如果是单选 ，就searchkey 为填入要筛选的键值， searchContent 为要筛选的值，如果
 * 有多个，就searchkey 为数组，[{ key:'',content:'' }]
 * 这里筛选没有保存时间筛选，时间筛选自己弄咯
 *  */
export const getSearchTestData = (
  tableData: Ref<Array<any>>,
  testData: Array<any>,
  searchKey: string | Array<any>,
  searchContent: string,
  tableLoading: Ref<boolean>,
  pageNo: number
): Promise<Array<any>> => {
  return new Promise((resolve) => {
    tableLoading.value = true
    setTimeout(() => {
      tableLoading.value = false
      if (testData && testData.length > 0 && searchKey) {
        if (typeof searchKey === "string") {
          console.log("searchKey string ")
          const newList = testData.filter((item) => {
            return item[searchKey].indexOf(searchContent) > -1
          })
          tableData.value = cloneDeep(newList.slice((pageNo - 1) * 10, pageNo * 10))
          resolve(newList)
        } else if (searchKey instanceof Array) {
          console.log("searchKey array", searchKey)
          const newList = testData.filter((item) => {
            return searchKey.every((condition) => {
              const value = item[condition.key]
              // 这里假设content是字符串，如果需要支持更多类型，请扩展逻辑
              if (typeof value === "string") {
                return value.includes(condition.content)
              }
              // 留个口子给时间日期筛选 没有写这部分

              // 对于非字符串类型的值，可以直接比较
              return value === condition.content
            })
          })
          tableData.value = cloneDeep(newList.slice((pageNo - 1) * 10, pageNo * 10))
          resolve(newList)
        } else {
          console.log("searchKey other")
          tableData.value = cloneDeep(testData.slice((pageNo - 1) * 10, pageNo * 10))
          resolve(testData)
        }
      } else {
        tableData.value = cloneDeep(testData.slice((pageNo - 1) * 10, pageNo * 10))
        resolve(testData)
      }
    }, 500)
  })
}

// 打印
export const gotoPrint = (tableSetting: any, printData: any, title: string) => {
  setLocalStorage("print_data", printData)
  const { href } = router.resolve({
    path: "/print",
    query: {
      print_type: "ert",
      print_title: title,
      result_key: "result", // 返回的数据处理的data keys
      api: "apiBackgroundReportCenterDataReportPersonMealListPost", // 请求的api
      table_setting: JSON.stringify(tableSetting),
      params: JSON.stringify({
        page: 1,
        page_size: 10
      })
    }
  })
  window.open(href, "_blank")
}

// 获取当前路由的父级key
export function findTopLevelParentKey(treeList: any, targetKey: string) {
  // 递归方法，注意index 传的是最上传的index，就是一级列表的index
  function search(node: any, index: number) {
    // 如果当前节点有 children 并且不是空数组，则继续搜索子节点
    if (node && node.children && node.children.length > 0) {
      for (let j = 0; j < node.children.length; j++) {
        // 如果找到了目标 key，则返回当前节点的 key 作为一级父级 key
        if (node.children[j].name === targetKey) {
          // 这里记住返回的是最上层的key,index 可以让你确认在哪一层循环
          return treeList[index].key
        }
        // 继续递归搜索子节点
        const result: any = search(node.children[j], index)
        if (result) {
          return result
        }
      }
    }
    return null
  }

  // 遍历顶层节点
  if (treeList && treeList.length > 0) {
    for (let i = 0; i < treeList.length; i++) {
      if (treeList[i].name === targetKey) {
        return treeList[i].key // 如果目标 key 是顶层节点，直接返回
      }
      const result = search(treeList[i], i)
      if (result) {
        return result
      }
    }
  }

  return null // 如果没有找到目标 key，则返回 null
}

// 格式化金额/100
export const divide = (money: any) => {
  if (!money) return "0.00"
  if (isNaN(money)) return "0.00"
  if (typeof money === "number") {
    return NP.divide(money, 100).toFixed(2)
  } else if (typeof money === "string" && !isNaN(Number(money))) {
    money = Number(money)
    return NP.divide(money, 100).toFixed(2)
  } else {
    return money
  }
}

// 元转分
export const times = (money: any) => {
  if (!money) return 0
  if (isNaN(money)) return 0
  return NP.times(money, 100)
}

/**
 * @description 获取树状结构到第deep层级的组合的key-value列表
 * @param {*} treeData
 * @param {*} key
 * @param {*} deep 不传默认返回所有层级的组合
 * @returns
 */
export const getTreeDeepkeyList = function (treeData: Array<any>, key: string, childrenKey: string, deep: number) {
  const arr: any[] = []
  let deepNo = 1
  key = key || "key"
  childrenKey = childrenKey || "children"
  function getTreeDeepHandle(treeData: Array<any>, key: string, childrenKey: string, deep: number) {
    if (treeData instanceof Array) {
      treeData.forEach((treeItem) => {
        arr.push(treeItem[key])
        if ((!deep || deepNo < deep) && treeItem[childrenKey] && treeItem[childrenKey].length > 0) {
          deepNo++
          getTreeDeepHandle(treeItem[childrenKey], key, childrenKey, deep)
        }
      })
    }
    return arr
  }
  return getTreeDeepHandle(treeData, key, childrenKey, deep)
}
// 获取子级设置
export const findChildSetting = function (data: Array<any>, keys: Array<any>, keyName: string) {
  const res = []
  for (let i = 0; i < data.length; i++) {
    if (keys.includes(data[i][keyName]) && !data[i].children) {
      res.push(data[i])
    } else if (keys.includes(data[i][keyName]) && data[i].children) {
      const parent = JSON.parse(JSON.stringify(data[i]))
      parent.children = findChildSetting(data[i].children, keys, keyName)
      res.push(parent)
    }
  }
  return res
}
