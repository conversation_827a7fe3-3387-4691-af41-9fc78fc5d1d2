// 验证金额
export const validateMoney = (rule: any, value: any, callback: any) => {
  const reg = /^(?!0(\.0{1,2})?$)\d+(\.\d{1,2})?$/
  if (!reg.test(value)) {
    callback(new Error("金额格式有误"))
  } else {
    callback()
  }
}
// 验证最大金额99999.99
export const validateMaxMoney = (rule: any, value: any, callback: any) => {
  const reg = /^(?!0(\.0{1,2})?$)\d+(\.\d{1,2})?$/
  if (!reg.test(value)) {
    callback(new Error("金额格式有误"))
  } else if (Number(value) > 99999.99) {
    callback(new Error("最大支持输入到万"))
  } else {
    callback()
  }
}
// 手机号
export const validatePhone = (rule: any, value: any, callback: any) => {
  const reg = /^1[3,4,5,6,7,8,9][0-9]{9}$/
  if (!reg.test(value)) {
    callback(new Error("手机号错误"))
  } else {
    callback()
  }
}
// 姓名
export const validateName = (rule: any, value: any, callback: any) => {
  const reg = /^[a-z0-9]+$/i
  if (!reg.test(value)) {
    callback(new Error("输入有误"))
  } else {
    callback()
  }
}
// 密码
export const validatePassword = (rule: any, value: any, callback: any) => {
  const reg = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,}$/
  if (!reg.test(value)) {
    callback(new Error("输入有误"))
  } else {
    callback()
  }
}
// 使用正则表达式检查是否只包含小写字母
export const isUniqueLowercase = (rule: any, value: any, callback: any) => {
  const lowercaseRegex = /^[a-z]+$/
  if (!lowercaseRegex.test(value)) {
    callback(new Error("必须只包含小写英文字母"))
  } else {
    // 检查字符是否重复
    // const uniqueChars = new Set(value.split(""))
    // if (uniqueChars.size !== value.length) {
    //   callback(new Error("字符不得重复"))
    // } else {
    callback() // 如果没有错误，则调用回调且不带参数
    // }
  }
}
// 验证手机号码 非必填
export const validateTelphoneNoRequire = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback()
  } else {
    const regTelphone = /^1[3456789]\d{9}$/
    if (!regTelphone.test(value)) {
      callback(new Error("请输入正确手机号"))
    } else {
      callback()
    }
  }
}

// 验证密码
export const validatePass = (rule: any, value: any, callback: any) => {
  const regPass = /^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/
  // let regPass = /(^\w{6,32}$)/;
  if (value && value !== "********" && !regPass.test(value)) {
    callback(new Error("密码为数字与字母的组合，长度8到20位"))
  } else {
    callback()
  }
}

//  验证是否包含特殊字符
export const validateNameChina = (rule: any, value: any, callback: any) => {
  const reg = /^[\u4E00-\u9FA5\w-]+$/
  if (!reg.test(value)) {
    callback(new Error("格式不正确，不能包含特殊字符"))
  } else {
    callback()
  }
}
// 验证账号
export const validateAccount = (rule: any, value: any, callback: any) => {
  const reg = /^\w{5,20}$/
  if (!reg.test(value)) {
    callback(new Error("账号长度5到20位，只支持数字、大小写英文或下划线组合"))
  } else {
    callback()
  }
}

// 验证2位小数和正整数
export const validateTwoDecimal = (rule: any, value: any, callback: any) => {
  if (value) {
    const reg = /^[-+]?(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
    if (!reg.test(value)) {
      callback(new Error("请输入数字，最多保留两位小数"))
    } else {
      callback()
    }
  } else {
    callback()
  }
}
