// 判断数组是否相同
export const checkArrayEquality = function (arr1: any[], arr2: any[]): boolean {
  return arraysAreEqualUnordered(arr1 || [], arr2 || [])
}

const arraysAreEqualUnordered = (arr1: any[], arr2: any[]): boolean => {
  if (arr1.length !== arr2.length) {
    return false
  }
  const sortedArr1 = [...arr1].sort()
  const sortedArr2 = [...arr2].sort()
  for (let i = 0; i < sortedArr1.length; i++) {
    if (sortedArr1[i] !== sortedArr2[i]) {
      return false
    }
  }
  return true
}
