import { ElMessage, ElMessageBox } from "element-plus"
/**
 * 确认框
 * @param {String} title - 标题
 * @param {String} content - 内容
 * @param {String} confirmButtonText - 确认按钮名称
 * @param {Function} callback -
 * @returns
 **/
export function confirm(opts: any, callback: any) {
  return new Promise((resolve, reject) => {
    ElMessageBox.confirm(opts.content ? opts.content : "", opts.title ? opts.title : "提示", {
      dangerouslyUseHTMLString: opts.useHTML,
      distinguishCancelAndClose: true,
      closeOnClickModal: false,
      customClass: opts.customClass ? opts.customClass : "ps-confirm",
      cancelButtonClass: opts.cancel_class ? opts.cancel_class : "ps-cancel-btn",
      confirmButtonClass: opts.confirm_class ? opts.confirm_class : "ps-confirm-btn",
      confirmButtonText: opts.confirmButtonText,
      cancelButtonText: opts.cancelButtonText,
      center: opts.center !== "" ? opts.center : true,
      showCancelButton: Reflect.has(opts, "showCancelButton") ? opts.showCancelButton : true,
      showConfirmButton: Reflect.has(opts, "showConfirmButton") ? opts.showConfirmButton : true
    })
      .then((res) => {
        if (callback && res === "confirm") {
          callback(res)
        } else {
          callback(res)
        }
      })
      .catch((e) => {
        reject(e)
      })
  })
}
//  确认弹窗，可使用确认/取消前调用方法beforeCloseCallback ， 处理确认/取消前的一些异步操作
export function confirmBefore(opts: any, beforeCloseCallback: any, callback: any) {
  ElMessageBox.confirm(opts.content ? opts.content : "", opts.title ? opts.title : "提示", {
    dangerouslyUseHTMLString: opts.useHTML,
    distinguishCancelAndClose: true,
    closeOnClickModal: false,
    customClass: opts.customClass ? opts.customClass : "ps-confirm",
    cancelButtonClass: opts.cancel_class ? opts.cancel_class : "ps-cancel-btn",
    confirmButtonClass: opts.confirm_class ? opts.confirm_class : "ps-confirm-btn",
    confirmButtonText: opts.confirmButtonText,
    cancelButtonText: opts.cancelButtonText,
    center: opts.center !== "" ? opts.center : true,
    type: opts.type ? opts.type : "warning",
    showCancelButton: Reflect.has(opts, "showCancelButton") ? opts.showCancelButton : true,
    showConfirmButton: Reflect.has(opts, "showConfirmButton") ? opts.showConfirmButton : true,
    beforeClose: async (action, instance, done) => {
      beforeCloseCallback && beforeCloseCallback(action, instance, done)
    }
  })
    .then((res) => {
      console.log(6666, res)
      if (callback && res === "confirm") {
        callback(res)
      } else {
        callback(res)
      }
    })
    .catch((e) => {
      console.log(e)
    })
}
