export function mergeHandle(data: any, mergeOpts: any) {
  // mergeOpts = {
  //   useKeyList: {
  //     id: ['name']
  //   }, // 是否根據固定key進行合并, 有值則根據key為唯一性進行字段表格合并，無則不做處理
  //   mergeKeyList: ['id'] // 通用的合并字段，根據值合并
  // }
  // 貌似沒有橫向合并的需求，當前自做row的
  // let mergeType = 'row' // all 橫向和縱向，row 橫向，column 縱向
  let result: any = {}
  let useKey = mergeOpts.useKeyList && Object.keys(mergeOpts.useKeyList).length
  // [1, 1] 表示不處理数据
  // [0, 0] 表示數據往前挪一位
  // [2, 1] 表示數據 row 合并兩個其後2-1條需要返回[0, 0]
  // [3, 1] 表示數據 row 合并兩個其後3-1條需要返回[0, 0]
  // [1, 2] 表示數據 column 合并兩個其後2-1條需要返回[0, 0]

  if (useKey) {
    // 根據固定key的相同值進行表格合并
    Object.keys(mergeOpts.useKeyList).forEach((k: any) => {
      mergeOpts.useKeyList[k].forEach((key: any) => {
        result[key] = {
          row: [],
          mergeNum: 0,
          key: k
          // column: []
        }
        result = mapMergeData(result, data, key, k)
      })
    })
  }
  if (mergeOpts.mergeKeyList) {
    // 不根據相同key合并，根據字段值合并
    mergeOpts.mergeKeyList.forEach((key: any) => {
      result[key] = {
        row: [],
        mergeNum: 0
        // column: []
      }
      result = mapMergeData(result, data, key)
    })
  }
  console.log("result", result)
  return result
}
/**
 * @param {Object} result
 * @param {Array} data
 * @param {String} key
 * @param {String} k
 * @returns Object
 */
function mapMergeData(result: any, data: any, key: any, k?: any) {
  data.forEach((value: any, index: number) => {
    if (index === 0) {
      // 首條數據要特殊處理
      result[key].row.push(1)
      result[key].mergeNum = index
    } else {
      // 餘下的情況
      let isMergeK = k ? value[k] === data[index - 1][k] : !k
      let isMerge = value[key] === data[index - 1][key] && isMergeK
      if (isMerge) {
        // 如果當前的數據和前一天相同則表示需要合并
        let prevIndex = getMergeIndex(result[key].row)
        result[key].row[prevIndex] += 1
        result[key].row.push(0)
        result[key].mergeNum = index
      } else {
        result[key].row.push(1)
        result[key].mergeNum = index
      }
    }
  })
  return result
}

/**
 * @param {Array} list
 * @returns Number
 */
// 獲取前一個合并表格的index
function getMergeIndex(list: any) {
  let index = list.length - 1
  while (index > 0) {
    if (list[index]) {
      break
    }
    index--
  }
  return index
}

export function mergeRowAction(rowMergeArrs: any, val: any, rowIndex: number, columnIndex: number) {
  let _row = rowMergeArrs[val].row[rowIndex]
  let _col = _row > 0 ? 1 : 0
  return [_row, _col]
}
