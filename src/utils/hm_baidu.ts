// 百度统计
// #ifdef H5
/* eslint-disable */
var _hmt: any = _hmt || []
// 判断域名使用
if (location.host.indexOf("zjjg") > -1) {
  // 正式环境
  createBaiduHm("https://hm.baidu.com/hm.js?a49d81991020c9b019195bfa1b6a47c0")
} else {
  // 测试环境
  createBaiduHm("https://hm.baidu.com/hm.js?541ca703f55e562d6ec221aaf9efe773")
}

function createBaiduHm(src: string) {
  var hm = document.createElement("script")
  hm.src = src
  var s: any = document.getElementsByTagName("script")[0]
  s.parentNode.insertBefore(hm, s)
}
// #endif
