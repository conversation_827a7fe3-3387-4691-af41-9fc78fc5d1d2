import axios, { type AxiosInstance, type AxiosRequestConfig } from "axios"
import { useUserStoreHook } from "@/store/modules/user"
import { ElMessage, ElMessageBox } from "element-plus"
import { get, merge } from "lodash-es"
import { getToken } from "./cache/cookies"
import { getSessionStorage } from "@/utils/storage"
import { useAppStore } from "@/store/modules/app"

/** 退出登录并强制刷新页面（会重定向到登录页） */
function logout(msg: string) {
  ElMessageBox.confirm(msg || "获取失败", "提示", {
    confirmButtonText: "重新登录",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      useUserStoreHook().backToLogin()
    })
    .catch(() => {})
}

/** 创建请求实例 */
function createService() {
  // 创建一个 axios 实例命名为 service
  const service = axios.create()
  // 请求拦截
  service.interceptors.request.use(
    (config) => {
      // 判断是不是file，
      const checkedMethods = ["post", "put", "patch", "get"]
      if (config.method && checkedMethods.includes(config.method)) {
        config.data = config.data || {}
        let hasFiles = false
        for (const i in config.data) {
          if (config.data[i] instanceof Array) {
            for (const j in config.data[i]) {
              if (config.data[i][j] instanceof File || config.data[i][j] instanceof Blob) {
                hasFiles = true
                break
              }
            }
          }
          if (config.data[i] instanceof File || config.data[i] instanceof Blob) {
            hasFiles = true
            break
          }
        }
        console.log(hasFiles, 88888)
        if (hasFiles) {
          const formdata = new FormData()
          for (const i in config.data) {
            if (config.data[i] instanceof Array) {
              for (const j in config.data[i]) {
                console.log(hasFiles, 9999999)
                formdata.append(i, config.data[i][j])
              }
            } else {
              console.log(hasFiles, 1111111, config.data[i])
              formdata.append(i, config.data[i])
            }
          }
          config.data = formdata
        } else {
          // 不能序列化，后端不做兼容。。。
          // config.data = qs.stringify(config.data)
        }
      }
      return config
    },
    // 发送失败
    (error) => Promise.reject(error)
  )

  // 响应拦截（可根据具体业务作出相应的调整）
  service.interceptors.response.use(
    (response) => {
      // apiData 是 api 返回的数据
      const apiData = response.data
      // 二进制数据则直接返回
      const responseType = response.request?.responseType
      if (responseType === "blob" || responseType === "arraybuffer") return apiData
      // 这个 code 是和后端约定的业务 code
      const code = apiData.code
      // 如果没有 code, 代表这不是项目后端开发的 api
      if (code === undefined) {
        ElMessage.error("非本系统的接口")
        return Promise.reject(new Error("非本系统的接口"))
      }
      console.log("code", code, response.data)

      switch (code) {
        case 0:
        case 1:
        case 200:
          // 本系统采用 code === 0 来表示没有业务错误
          return apiData
        case 3:
        case 500001:
        case 4:
        case 5:
          ElMessage.error(apiData.msg)
          return apiData
        case 401:
          // Token 过期时
          return logout(apiData.msg)
        case -3:
          ElMessage.error(apiData.msg || "Error")
          return logout(apiData.msg)
        default:
          // 不是正确的 code
          ElMessage.error(apiData.msg || "Error")
          return Promise.reject(new Error(JSON.stringify(apiData)))
      }
    },
    (error) => {
      // status 是 HTTP 状态码
      const status = get(error, "response.status")
      switch (status) {
        case 400:
          error.message = "请求错误"
          break
        case 401:
          // Token 过期时
          logout("token过期")
          break
        case 403:
          error.message = "拒绝访问"
          break
        case 404:
          error.message = "请求地址出错"
          break
        case 408:
          error.message = "请求超时"
          break
        case 500:
          error.message = "服务器内部错误"
          break
        case 501:
          error.message = "服务未实现"
          break
        case 502:
          error.message = "网关错误"
          break
        case 503:
          error.message = "服务不可用"
          break
        case 504:
          error.message = "网关超时"
          break
        case 505:
          error.message = "HTTP 版本不受支持"
          break
        default:
          break
      }
      ElMessage.error(error.message)
      return Promise.reject(error)
    }
  )
  return service
}

/** 创建请求方法 */
function createRequest(service: AxiosInstance) {
  return function <T>(config: AxiosRequestConfig): Promise<T> {
    const useUserStore = useUserStoreHook()
    const token = useUserStore.getToken
    const environmentStr = import.meta.env.VITE_NODE_ENV
    const thisURl = import.meta.env.VITE_APP_API_URL
    let url = useAppStore().getUrl()
    const api = import.meta.env.VITE_BASE_API
    // 如果url为空，则使用api 没有配的情况
    if (!url || url === undefined || url === "undefined") {
      url = api
    } else {
      // 如果我配置了url，则使用url + api ，根据环境来，staging 使用url + api ，production/ development 使用api 开发环境自己去.env 修改 VITE_APP_API_URL 进行修改获取的后台
      url = environmentStr === "staging" ? url + api : api
    }
    console.log("token", token, "environmentStr", environmentStr, "url", url, "api", api, "thisURl", thisURl)
    const defaultConfig = {
      headers: {
        // 携带 Token
        // Authorization: token ? `Bearer ${token}` : undefined,
        // "Content-Type": "application/json",
        Token: token
      },
      timeout: 120000,
      baseURL: url,
      data: {}
    }
    // 将默认配置 defaultConfig 和传入的自定义配置 config 进行合并成为 mergeConfig
    const mergeConfig = merge(defaultConfig, config)
    return service(mergeConfig)
  }
}

/** 用于网络请求的实例 */
const service = createService()
/** 用于网络请求的方法 */
export const request = createRequest(service)
