/**
 * @description 获取setLocalStorage
 * @param {*} key
 * @returns *
 */
export const getSessionStorage = function (key: string): any {
  let value = sessionStorage.getItem(key)
  if (value) {
    try {
      value = JSON.parse(value).value
    } catch (e) {}
  }
  return value
}

/**
 * @description
 * @param {*} key
 * @param {*} value
 */
export const setSessionStorage = function (key: string, value: any) {
  let data: any = {
    value
  }
  data = JSON.stringify(data)
  sessionStorage.setItem(key, data)
}

/**
 * @description
 * @param {*} key
 * @returns
 */
export const removeSessionStorage = function (key: string) {
  sessionStorage.removeItem(key)
}

/**
 * @description 获取setLocalStorage
 * @param {*} key
 * @returns *
 */
export const getLocalStorage = function (key: string): any {
  let value = localStorage.getItem(key)
  if (value) {
    try {
      value = JSON.parse(value).value
    } catch (e) {}
  }
  return value
}

/**
 * @description
 * @param {*} key
 * @param {*} value
 */
export const setLocalStorage = function (key: string, value: any) {
  let data: any = {
    value
  }
  data = JSON.stringify(data)
  localStorage.setItem(key, data)
}

/**
 * @description
 * @param {*} key
 * @returns
 */
export const removeLocalStorage = function (key: string) {
  localStorage.removeItem(key)
}
