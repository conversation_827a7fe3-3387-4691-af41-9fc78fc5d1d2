import { confirm } from "@/utils/message"
import to from "await-to-js"
import { ElMessage } from "element-plus"
import router from "@/router"
import FileSaver from "file-saver"
import * as XLSX from "xlsx"
export function exportHandle(option: any) {
  console.log(option)

  let isExportLoading = false
  let exportQueryId: any

  const exportExcel = () => {
    console.log(66666)
    if (isExportLoading) {
      ElMessage.error("请勿重复点击！")
      return
    }
    isExportLoading = true
    confirm({ content: `确定导出？` }, (res: any) => {
      if (res === "confirm") {
        comfirmExportExcel(option)
      }
    })
  }

  const comfirmExportExcel = async (opt: any) => {
    const [err, res]: any[] = await to(opt.api(opt.params))
    const { code, msg } = res!
    if (err) {
      return
    }
    if (code === 0) {
      isExportLoading = false
      exportQueryId = res.data.query_id
      openExcelHandle()
    } else {
      isExportLoading = false
      ElMessage.error(msg)
    }
  }
  const openExcelHandle = () => {
    console.log("router", router)

    if (exportQueryId) {
      router.push({
        name: "ExportExcel",
        query: {
          type: option.type,
          query_id: exportQueryId,
          random: new Date().getTime()
        }
      })
    } else {
      ElMessage.error("获取query_id失败！")
      isExportLoading = false
    }
  }

  exportExcel()
}
/**
 * @description 下载文件
 * @param {*} json
 * @param {*} merges
 * @param {*} filename
 */
export function downloadJsonExcel(json: any, merges: any, filename: string) {
  let fileBlob = jsonToXlsxBlob(json, merges)
  if (!filename) {
    filename = uuid2(16, 16) + "-" + Date.now() + ".xlsx"
  }
  FileSaver.saveAs(fileBlob, filename)
}

/**
 * @description json数据转xlsx blob，但表
 * @param {*} json
 * @param {*} merges
 * @returns xlsxblob
 */
export function jsonToXlsxBlob(json: any, merges: any) {
  let wsname = "Sheet1"
  let wb = XLSX.utils.book_new()
  let ws = XLSX.utils.aoa_to_sheet(json)
  XLSX.utils.book_append_sheet(wb, ws, wsname)
  if (merges) ws["!merges"] = merges
  let opts: any = {
    bookType: "xlsx", // 要生成的文件类型
    bookSST: false,
    type: "binary" // 二进制格式
  }
  let wbout = XLSX.write(wb, opts) // 生成xlsx格式的数据
  let xlsxblob = new Blob([s2ab(wbout)], {
    // 生成数据流格式
    type: "application/octet-stream"
  })
  // 字符串转ArrayBuffer
  return xlsxblob
}

export function s2ab(s: string) {
  var buf = new ArrayBuffer(s.length)
  var view = new Uint8Array(buf)
  for (var i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xff
  return buf
}

/**
 * @description 生成uuid
 * @returns string
 */
export function uuid2(len: number, radix: number) {
  var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("")
  var uuid = []
  var i
  radix = radix || chars.length

  if (len) {
    // Compact form
    for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)]
  } else {
    // rfc4122, version 4 form
    var r

    // rfc4122 requires these characters
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = "-"
    uuid[14] = "4"

    // Fill in random data.  At i==19 set the high bits of clock sequence as
    // per rfc4122, sec. 4.1.5
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | (Math.random() * 16)
        uuid[i] = chars[i === 19 ? (r & 0x3) | 0x8 : r]
      }
    }
  }

  return uuid.join("")
}
