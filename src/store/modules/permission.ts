import { ref } from "vue"
import store from "@/store"
import { defineStore } from "pinia"
import { type RouteRecordRaw } from "vue-router"
import { useUserStore } from "./user"
import { getList } from "@/api/login/index"
import { ROUTE_SUPER, ROUTE_HEALTHY } from "@/constants/route-cons"
import routesAll, { defaltRoutes, getAllRoutesList } from "@/router/modules"
import cloneDeep from "lodash/cloneDeep"
import { findTopLevelParentKey } from "@/utils/index"
import router, { setRoute } from "@/router"

const hasPermission = (roles: string[], route: RouteRecordRaw) => {
  const routeRoles = route.meta?.roles
  return routeRoles ? roles.some((role) => routeRoles.includes(role)) : true
}

const filterDynamicRoutes = (routes: RouteRecordRaw[], roles: string[]) => {
  const res: RouteRecordRaw[] = []
  routes.forEach((route) => {
    const tempRoute = { ...route }
    if (hasPermission(roles, tempRoute)) {
      if (tempRoute.children) {
        tempRoute.children = filterDynamicRoutes(tempRoute.children, roles)
      }
      res.push(tempRoute)
    }
  })
  return res
}

const getPermissionRoutes = (list: any) => {
  const newList = cloneDeep(list)
  newList.forEach((item: any) => {
    const key = item.key
    const findItem = getAllRoutesList().find((routeItem) => {
      return routeItem.meta?.permission == key
    })
    if (findItem) {
      item = Object.assign(item, findItem)
    }
    if (item.children && item.children.length > 0) {
      const list = item.children
      item.children = getPermissionRoutes(list)
      const redirect = item.redirect || ""
      if (redirect && item.children && item.children.length > 0) {
        // 如果回调有数据，要重新设置
        let childrenPath = item.children[0].path || ""
        if (childrenPath && childrenPath.length > 0 && !childrenPath.startsWith("/")) {
          childrenPath = "/" + childrenPath
        }
        item.redirect = item.path + childrenPath
        if (item.level > 0) {
          try {
            router.addRoute(item)
          } catch (error) {
            console.error(error)
          }
        }
      }
    }
  })
  return newList
}

export const usePermissionStore = defineStore({
  id: "permission",
  state: (): any => ({
    routes: [],
    dynamicRoutes: [],
    allRoutes: []
  }),
  getters: {
    getRoutes(): [] {
      return this.routes && this.routes.length > 0 ? this.routes : defaltRoutes
    },
    getDyNamicRoutes(): [] {
      return this.dynamicRoutes
    },
    getAllRoutes(): [] {
      return this.allRoutes
    }
  },
  actions: {
    setRoutes(routes: any) {
      this.routes = this.routes.contat(routes)
    },
    // 设置动态路由
    async setDyNamicRoutes() {
      // 动态路由要换成getList获取吧
      // const {data} = await getList()
      const userStore = useUserStore()
      const keys: string[] = userStore.getRoles
      const data = cloneDeep(ROUTE_HEALTHY)
      // 这里要根据当前路由反馈得key 重新组成动态路由列表
      let newDataList = filterTreeByKeys(data, keys)
      console.log("newDataList", newDataList)
      this.dynamicRoutes = getPermissionRoutes(newDataList)
      console.log("newDataList this.dynamicRoutes", this.dynamicRoutes)
    },
    // 根据key值获取路由
    getDyNamicRoutesBykey(key: string) {
      const list = this.dynamicRoutes.filter((item: any) => {
        return item.key === key
      })
      return list && list[0] ? list[0].children : []
    },
    // 设置当前key路由的activeMenu
    setActiveMenuBykey(key: string, path: string) {
      this.dynamicRoutes = this.dynamicRoutes.map((item: any) => {
        if (item.key === key) {
          item.activeMenu = path
        }
        return item
      })
    },
    // 根据路由的name获取对应的parent menu
    getActiveMenuByRouteName(name: string) {
      console.log("getActiveMenuByRouteName", name)
      let result = findTopLevelParentKey(cloneDeep(this.dynamicRoutes), name)
      console.log("getActiveMenuByRouteName result", result)
      return result
    }
  }
})
// 过滤tree 里面得key
function filterTreeByKeys(tree: Array<any>, keys: string[]) {
  // 辅助函数，用于递归过滤树形列表
  function filterNode(node: any) {
    // 检查当前节点的 key 是否在 keys 列表中
    const isKeyMatched = keys.includes(node.key) || (Reflect.has(node, "type") && node.type === "no_permission")

    // 如果当前节点匹配，则保留该节点，并递归处理其子节点
    if (isKeyMatched) {
      // 创建一个新节点对象，只包含必要的属性（为了避免修改原始树）
      const newNode: any = {
        ...node,
        // 如果当前节点有子节点，则递归过滤子节点
        children: node.children ? filterTreeByKeys(node.children, keys) : undefined
      }
      return newNode
    }

    // 如果当前节点不匹配，但它有子节点，我们需要检查子节点是否匹配
    if (node.children && node.children.length > 0) {
      // 递归过滤子节点，并只保留匹配的子节点
      const filteredChildren: Array<any> = filterTreeByKeys(node.children, keys)

      // 如果过滤后的子节点数组不为空，则保留当前节点（作为容器），并更新其子节点
      if (filteredChildren.length > 0) {
        return {
          ...node,
          children: filteredChildren
        }
      }
    }

    // 如果当前节点不匹配且没有匹配的子节点，则返回 null，表示该节点应被过滤掉
    return null
  }

  // 对原始树形列表的每个节点应用过滤函数，并过滤掉 null 值
  return tree.map((node) => filterNode(node)).filter((node) => node !== null)
}

/** 在 setup 外使用 */
export function usePermissionStoreHook() {
  return usePermissionStore(store)
}
