import store from "@/store"
import { defineStore } from "pinia"

export const useTableHeightStore = defineStore("tableHeight", {
  state: () => {
    return {
      mainContainHeight: 0,
      buttonGroupHeight: 0
    }
  },
  getters: {
    getMainContainHeight(): number {
      return this.mainContainHeight
    },
    getButtonGroupHeight(): number {
      return this.buttonGroupHeight
    }
  },
  actions: {
    setButtonGroupHeight(buttonGroupHeight: number) {
      this.buttonGroupHeight = buttonGroupHeight
    },
    setMainContainHeight(mainContainHeight: number) {
      console.log("获取到mainContainHeight", mainContainHeight)
      this.mainContainHeight = mainContainHeight
    }
  }
})

/** 在 setup 外使用 */
export function useTableHeightStoreHook() {
  return useTableHeightStore(store)
}
