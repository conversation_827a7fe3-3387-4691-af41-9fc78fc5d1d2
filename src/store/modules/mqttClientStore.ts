import type { IMessage } from "@/service/mqtt/IMessage.d"
import type { ISubscriptionGrant } from "mqtt"
import { defineStore } from "pinia"

export const useMqttClientStore = defineStore({
  id: "useMqttClientStore",
  state: (): any => ({
    connected: false,
    isReconnet: false,
    receivedMessages: [],
    subscribedTopics: [],
    receivedMessagesTopic: [],
    reConnectMessage: [] // 重连消息
  }),
  getters: {
    // 获取返回信息
    getReceivedMessages(): [] {
      return this.receivedMessages
    },
    // 获取是否连接状态
    getConnect(): boolean {
      return this.connected
    },
    getReconnect(): boolean {
      return this.isReconnet
    },
    // 获取订阅主题
    getSubscribedTopics(): [] {
      return this.subscribedTopics
    },
    // 获取重连信息
    getReConnectMessage(): [] {
      return this.reConnectMessage
    }
  },
  actions: {
    // 设置连接状态
    setConnected(payload: boolean): void {
      this.connected = payload
    },
    // 设置重新连接状态
    setReConnected(payload: boolean): void {
      this.isReconnet = payload
    },
    // 添加信息
    addMessage(message: IMessage): void {
      this.receivedMessages.unshift(message)
    },
    // 添加主题
    addToSubscribedTopics(topic: ISubscriptionGrant): void {
      this.subscribedTopics.unshift(topic)
    },
    // 删除主题
    removeSubscribedTopic(topic: ISubscriptionGrant): void {
      this.subscribedTopics.splice(this.subscribedTopics.indexOf(topic), 1)
    },
    // 清空主题
    clearSubscribedTopics(): void {
      this.subscribedTopics.splice(0, this.subscribedTopics.length)
    },
    // 根据主题获取返回信息
    getReceivedMessagesByTopic(topic: string): [] {
      this.receivedMessagesTopic = this.receivedMessages.filter((item: ISubscriptionGrant) => item.topic === topic)
      return this.receivedMessagesTopic
    },
    // 重连消息
    addReconnentMessage(message: any): void {
      this.reConnectMessage.unshift(message)
    }
  }
})
