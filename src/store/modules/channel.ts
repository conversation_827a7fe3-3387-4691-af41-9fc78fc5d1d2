import store from "@/store"
import { defineStore } from "pinia"

export const useChannelStore = defineStore("channel", {
  state: () => {
    return {
      channelInfo: {}
    }
  },
  getters: {
    getChannelInfo(): Object {
      return this.channelInfo
    }
  },
  actions: {
    setChannelInfo(data?: any) {
      this.channelInfo = data
    },
    resetChannelInfo() {
      this.channelInfo = {}
    }
  },
  persist: {
    paths: ["channelInfo"],
    storage: localStorage
  }
})

/** 在 setup 外使用 */
export function useAccountStoreHook() {
  return useChannelStore(store)
}
