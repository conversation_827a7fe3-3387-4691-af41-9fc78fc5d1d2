import store from "@/store"
import { defineStore } from "pinia"
import { useTagsViewStore } from "./tags-view"
import { useSettingsStore } from "./settings"
import { removeToken, setToken } from "@/utils/cache/cookies"
import router from "@/router"
import { loginApi, apiBackgroundLogout } from "@/api/login"
import { type LoginRequestData } from "@/api/login/types/login"
import routeSettings from "@/config/route"
import { Md5 } from "ts-md5"
import { to } from "@/utils"
import { ElMessage } from "element-plus"
import { type TBreadCrumbDate, TUserInfo } from "../../../types/constant"
import CacheKey from "@/constants/cache-key"
import { usePermissionStoreHook } from "@/store/modules/permission"
import useMqttClient from "@/service/mqtt/useMqttService.hooks"
import { permission } from "@/directives/permission"
import { cloneDeep, update } from "lodash"

export const useUserStore = defineStore("user", {
  state: () => {
    return {
      token: "", // token
      isAdmin: false, // 是否是管理员
      userInfo: {} as TUserInfo, // 用户信息
      username: "", // 用户名
      statusLabel: "", // 状态
      default_online: false, // 默认在线
      headImg: "", // 头像
      roles: [] as string[], // 规则权限
      role_name: "", // 角色名
      role_id: -1, // 角色id
      activeMenu: "", // 激活菜单
      breadCrumbDate: {
        dateValue: [],
        dateType: "daterange",
        startPlaceholder: "",
        endPlaceholder: "",
        rangeSeparator: "",
        defaultTime: []
      },
      unreadCount: 5,
      downloadCount: 10,
      channelTreeData: [] as Array<any>
    }
  },
  getters: {
    getToken(): string {
      return this.token
    },
    getUserInfo(): TUserInfo {
      return this.userInfo
    },
    getUserName(): string {
      return this.username
    },
    getStatusLabel(): string {
      return this.statusLabel
    },
    getRoles(): string[] {
      return this.roles
    },
    getIsAdmin(): boolean {
      return this.isAdmin
    },
    getActiveMenu(): string {
      return this.activeMenu
    },
    getBreadCrumbDate(): TBreadCrumbDate {
      return this.breadCrumbDate as TBreadCrumbDate
    },
    // 返回面包屑日期
    getBreadCrumbDateValue(): Array<any> | null {
      return this.breadCrumbDate.dateValue
    },
    getHeadImg(): string {
      return this.headImg
    },
    getDefaultOnline(): boolean {
      return this.default_online
    },
    getUnReadCount(): number {
      return this.unreadCount
    },
    getDownloadCount(): number {
      return this.downloadCount
    },
    getLogoImg(): string {
      return this.userInfo?.logo_url || ""
    },
    getRoleName(): string {
      return this.role_name
    },
    getRoleId(): number {
      return this.role_id
    },
    getChannelTreeData(): Array<any> {
      return this.channelTreeData
    },
    getIsDoubleFactor(): boolean {
      return this.userInfo?.is_double_factor || false
    }
  },
  actions: {
    login(loginData: LoginRequestData) {
      let params = null
      if (loginData.mode && loginData.mode === "account") {
        // 手机登录
        const md5: any = new Md5()
        md5.appendAsciiStr(loginData.password)
        loginData.password = md5.end()
        params = {
          username: loginData.username,
          password: loginData.password,
          verify_code: loginData.verify_code,
          mode: loginData.mode,
          agreement_types: ["IPA"]
        }
      } else {
        // 手机短信登录
        params = {
          phone: loginData.phone,
          sms_code: loginData.verify_code,
          mode: loginData.mode,
          code: loginData.code,
          agreement_types: ["IPA"]
        }
      }
      return new Promise((resolve, reject) => {
        loginApi(params)
          .then((res: any) => {
            if (res && res.code === 0) {
              const data: any = res.data || {}
              this.setToken(data.token)
              let isDoubleFactor = data.is_double_factor || false
              if (!isDoubleFactor) {
                this.updateAllUserInfo(data)
              }
              resolve(res.data)
            } else {
              ElMessage.error("登录失败," + res.msg)
              reject(new Error("登录失败", res.msg))
            }
          })
          .catch((err: any) => {
            ElMessage.error("登录失败," + err.message)
            reject(new Error("登录失败", err.message))
          })
      })
    },
    /** 登出 */
    async logout() {
      const { disconnect } = useMqttClient()
      const [err, res] = await to(apiBackgroundLogout())
      console.log("logout", res, err)
      if (!err && res && (res.code === 0 || res.code === -3)) {
        // 登出断开mqtt
        disconnect()
        this.backToLogin()
      } else {
        const message = err?.message ? err.message : res.msg || "登出失败"
        ElMessage.error(message)
      }
    },
    /** 返回登录页 */
    backToLogin() {
      removeToken()
      this.resetUserData()
      this.resetTagsView()
      router.replace({ path: "/login" })
    },
    /** 更新状态 */
    updateStatus(status: string) {
      console.log("updateStatus", status)
      this.statusLabel = status
    },
    /** 重置 Visited Views 和 Cached Views */
    resetTagsView() {
      const tagsViewStore = useTagsViewStore()
      const settingsStore = useSettingsStore()
      if (!settingsStore.cacheTagsView) {
        tagsViewStore.delAllVisitedViews()
        tagsViewStore.delAllCachedViews()
      }
      localStorage.setItem(CacheKey.VISITED_VIEWS, "[]")
    },
    /** 重置 Token */
    resetToken() {
      removeToken()
      this.resetUserData()
    },
    /** 设置 Token */
    setToken(tokenValue: string) {
      this.token = tokenValue
      setToken(this.token)
    },
    /** 模拟角色变化 */
    async changeRoles(role: string) {
      const newToken = "token-" + role
      this.token = newToken
      setToken(newToken)
      // 用刷新页面代替重新登录
      window.location.reload()
    },
    /** 设置是否管理员 */
    setIsAdmin(flag: boolean) {
      this.isAdmin = flag
    },
    // 设置激活菜单
    setActiveMenu(menu: string) {
      this.activeMenu = menu
    },
    // 设置面包屑日期
    setBreakCrumbDate(date: {}) {
      this.breadCrumbDate = Object.assign(this.breadCrumbDate, date)
    },
    // 重置用户数据
    resetUserData() {
      console.log("resetUserData")
      this.token = ""
      this.isAdmin = false
      this.userInfo = {}
      this.username = ""
      this.statusLabel = ""
      this.roles = []
      this.activeMenu = ""
      this.role_id = -1
      this.role_name = ""
    },
    // 设置头像
    setHeadImg(img: string) {
      this.headImg = img
    },
    // 设置默认在线状态
    setDefaltOnline(status: boolean) {
      this.default_online = status
    },
    // 设置用户名
    setUserName(name: string) {
      this.username = name
    },
    // 设置用户信息
    setUserInfo(data: any) {
      this.userInfo = data
    },
    // 设置通道树数据
    setChannelTreeData(list: Array<any>) {
      this.channelTreeData = list || []
    },
    // 更新所有信息
    updateAllUserInfo(data: any) {
      const companyId = data.company_id || ""
      this.setIsAdmin(companyId === "1")
      const defaltOnline = data.defalt_online || false
      this.setDefaltOnline(defaltOnline)
      console.log("登录完成获取权限", data, routeSettings.defaultRoles)
      this.userInfo = data || {}
      this.token = data.token
      this.username = data.username
      this.statusLabel = data.line_status || ""
      this.role_id = data.role_id || -1
      this.role_name = data.role_name || ""
      this.headImg =
        data.images_url && Array.isArray(data.images_url) && data.images_url.length > 0 ? data.images_url[0] : ""
      // 验证返回的 roles 是否为一个非空数组，否则塞入一个没有任何作用的默认角色，防止路由守卫逻辑进入无限循环
      this.roles = data.role_permission?.length > 0 ? data.role_permission : routeSettings.defaultRoles
      // 登录完要设置一次路由
      const permissionStore = usePermissionStoreHook()
      permissionStore.setDyNamicRoutes()
    }
  },
  persist: {
    paths: [
      "token",
      "userInfo",
      "roles",
      "isAdmin",
      "activeMenu",
      "username",
      "statusLabel",
      "headImg",
      "default_online",
      "role_name",
      "role_id",
      "channelTreeData"
    ],
    storage: sessionStorage
  }
})

/** 在 setup 外使用 */
export function useUserStoreHook() {
  return useUserStore(store)
}
