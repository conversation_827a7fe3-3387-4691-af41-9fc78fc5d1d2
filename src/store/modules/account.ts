import store from "@/store"
import { defineStore } from "pinia"

export const useAccountStore = defineStore("account", {
  state: () => {
    return {
      username: "",
      password: ""
    }
  },
  getters: {
    getUsername(): string {
      return this.username
    },
    getPassWord(): string {
      return this.password
    }
  },
  actions: {
    setAccountInfo(data: { username: string; password: string }) {
      this.username = data.username
      this.password = data.password
    },
    resetAccountInfo() {
      this.username = ""
      this.password = ""
    }
  },
  persist: {
    paths: ["username", "password"],
    storage: localStorage
  }
})

/** 在 setup 外使用 */
export function useAccountStoreHook() {
  return useAccountStore(store)
}
