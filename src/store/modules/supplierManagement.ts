import dayjs from "dayjs"
import { defineStore } from "pinia"

type ContentType = {
  isSelect: boolean
  label: string
  isFillInInfo: boolean
  isRequiredForFillInInfo: boolean
  isUpLoadFile: boolean
  isRequiredForUpLoadFile: boolean
}
interface TemplateDataProps {
  title: string
  selectHandle: Array<string>
  content: Array<ContentType>
}

interface TableDataType {
  date: string
  name: string
  status: string
  status_alias: string
  remark: string
  detail: TableDataDetailType[]
}

interface TableDataDetailType {
  title: string
  content: DetailContentType[]
}

type DetailContentType = {
  label: string
  isUpToStandard: boolean
  UpToStandardText: string
  file: string | any[]
  abarbeitungInfo: string
  isAbarbeitung: boolean | null
  isAbarbeitungAlias: string
}

export const useSupplierManagement = defineStore("supplierManagement", {
  state: () => {
    return {
      templateData: [] as TemplateDataProps[],
      specialInspectionData: [] as TableDataType[],
      count: 0
    }
  },
  getters: {
    getTemplateData(): TemplateDataProps[] {
      return this.templateData
    },
    getSpecialInspectionData(): TableDataType[] {
      return this.specialInspectionData
    }
  },
  actions: {
    setTemplateData(data: TemplateDataProps[]) {
      this.templateData = [...data]
      this.setSpecialInspectionData(this.templateData)
    },
    setSpecialInspectionData(data: TemplateDataProps[]) {
      // 处理一下detail的内容
      const arr = data.filter((item) => item.selectHandle.length !== 0)
      // 转成处理弹窗要显示的数据
      const newArr = arr.map((item: TemplateDataProps) => {
        const tableObj: TableDataDetailType = {
          title: item.title,
          content: []
        }
        item.content.forEach((itemIn: ContentType) => {
          const obj: DetailContentType = {
            label: itemIn.label,
            isUpToStandard: true,
            UpToStandardText: "合格",
            file: "无",
            abarbeitungInfo: "无",
            isAbarbeitung: null,
            isAbarbeitungAlias: ""
          }
          tableObj.content.push(obj)
        })
        return tableObj
      })
      const obj: TableDataType = {
        date: dayjs().format("YYYY-MM-DD"),
        name: `检查项模板${this.count}`,
        status: "1",
        status_alias: "已处理",
        remark: "--",
        detail: [...newArr]
      }
      this.specialInspectionData.push(obj)
    }
  }
})
