import { defineStore } from "pinia"
import { getLocalLang, setLocalLang } from "@/utils/cache/local-storage"

// 语言
export const useLangStore = defineStore({
  id: "useLangStore",
  state: (): any => ({
    lang: "ZH"
  }),
  getters: {
    getLang(): string {
      if (!this.lang) {
        this.lang = getLocalLang()
      }
      return this.lang
    }
  },
  actions: {
    changeLang(lang: string): void {
      if (!this.lang) {
        this.lang = getLocalLang()
      }
      if (this.lang === lang) return
      this.lang = lang
      setLocalLang(this.lang)
      window.location.reload()
    }
  }
})
