import router from "@/router"
import { useUserStoreHook } from "@/store/modules/user"
import { usePermissionStoreHook } from "@/store/modules/permission"
import { ElMessage } from "element-plus"
import { setRouteChange } from "@/hooks/useRouteListener"
import { useTitle } from "@/hooks/useTitle"
import { getToken } from "@/utils/cache/cookies"
import routeSettings from "@/config/route"
import isWhiteList from "@/config/white-list"
import NProgress from "nprogress"
import "nprogress/nprogress.css"
import { RouteLocationNormalized } from "vue-router"
import { adminRoutes, healthyRoutes } from "@/router/modules"
import { getSessionStorage } from "@/utils/storage"

const { setTitle } = useTitle()
NProgress.configure({ showSpinner: false })

router.beforeEach(async (to, _from, next) => {
  console.log("beforeEach to", to, _from, router.getRoutes())

  const userStore = useUserStoreHook()
  const permissionStore = usePermissionStoreHook()
  // 检测路由参数
  checkoutRouteQuery(to, userStore, permissionStore, next)
  NProgress.start()

  const token = userStore.getToken
  const isAdmin = userStore.getIsAdmin
  const dynamicRoutes = permissionStore.getDyNamicRoutes
  const userInfo = userStore.getUserInfo
  const icCkeck = getSessionStorage("CHECKDOUBLEFACTOR")
  console.log("dynamicRoutes", dynamicRoutes, "userInfo", userInfo, "icCkeck", icCkeck)
  // 如果没有登陆
  if (!token) {
    // 如果在免登录的白名单中，则直接进入
    if (isWhiteList(to)) return next()
    // 其他没有访问权限的页面将被重定向到登录页面
    return next("/login")
  }

  // 如果已经登录，并准备进入 Login 页面，则重定向到主页
  if (to.path === "/login" && token && userInfo && Object.keys(userInfo).length > 0) {
    if (!userInfo.is_double_factor || (userInfo.is_double_factor && icCkeck === "1")) {
      console.log("isAdmin", isAdmin, healthyRoutes[0]?.path)
      return isAdmin ? next({ path: adminRoutes[0]?.path }) : next({ path: healthyRoutes[0]?.path })
    }
  }
  // 否则要重新获取权限角色
  try {
    if (dynamicRoutes.length === 0) {
      // 这里有动态添加路由，注意要经常查看路由变化
      permissionStore.setDyNamicRoutes()
      // 动态路由，第一次可能不能被匹配到，所以要重新设置路由 ，matched.length > 1 是匹配到了， 如果不匹配，则重新进入路由，就是再走一次匹配
      if (to.matched && to.matched.length > 0 && to.matched[0].path !== "/:pathMatch(.*)*") {
        next()
      } else {
        next(to.path)
      }
    }
    next()
    // 设置 replace: true, 因此导航将不会留下历史记录
    // next({ ...to, replace: true })
  } catch (err: any) {
    console.log("err", err)
    // 过程中发生任何错误，都直接重置 Token，并重定向到登录页面
    userStore.resetToken()
    ElMessage.error(err.message || "路由守卫过程发生错误")
    next("/login")
  }
})

router.afterEach((to) => {
  setRouteChange(to)
  setTitle(to.meta.title)
  NProgress.done()
})

function checkoutRouteQuery(to: RouteLocationNormalized, userStore: any, permissionStore: any, next: any) {
  // 这里要考虑免登的状态，就是人家有token过来没有走登录,如果传了companyId 过来，可以根据companyId 是否等于1 来判断是否是管理员
  const queryData = to.query || {}
  const tokenValue = queryData.token?.toString() || ""
  const companyId = queryData.companyId?.toString() || ""
  const userName = queryData.username?.toString() || ""
  const isAdminValue = companyId === "1"
  // 保存token
  if (tokenValue) {
    userStore.setToken(tokenValue)
    userStore.setIsAdmin(isAdminValue)
    // 设置顶部默认菜单是第一个，如果后续有改就要注意了
    userStore.setActiveMenu("consulting_management")
    userStore.setUserName(userName)
    permissionStore.setDyNamicRoutes()
    if (isAdminValue) {
      // 清除它的头像缓存
      userStore.setHeadImg("")
    }
  }
}
