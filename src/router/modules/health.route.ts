import { RouteRecordRaw } from "vue-router"
const Layouts = () => import("@/layouts/index.vue")

const healthRoutes: RouteRecordRaw[] = [
  // 监管中心-监管中心
  {
    path: "/regulatory_center",
    component: Layouts,
    redirect: "/regulatory_center/organizational_supervision",
    name: "RegulatoryCenter",
    meta: {
      title: "r_t_regulatory_center",
      roles: ["admin", "editor"], // 可以在根路由中设置角色
      alwaysShow: true, // 将始终显示根菜单
      level: 0, // 一级菜单，这个要加上，因为菜单根据这个层级知道你是第几层
      permission: "fund_supervision_center",
      keepAlive: false
    },
    children: [
      {
        path: "organizational_supervision",
        component: () => import("@/views/organizational-supervision/index.vue"),
        name: "OrganizationalSupervision",
        meta: {
          title: "r_t_organizational_supervision",
          level: 1,
          permission: "background_fund_supervision.organization_supervision",
          keepAlive: false
        }
      },
      {
        path: "food_safety_traceability",
        component: () => import("@/views/regulatory-center/FoodSafetyTraceability.vue"),
        name: "FoodSafetyTraceability",
        meta: {
          title: "r_t_food_safety_traceability",
          level: 1,
          permission: "background_fund_supervision.supervision_food_safety_source",
          keepAlive: false
        }
      },
      {
        path: "audit_records",
        component: () => import("@/views/regulatory-center/AuditRecords.vue"),
        name: "AuditRecords",
        meta: {
          title: "r_t_audit_records",
          level: 1,
          permission: "background_fund_supervision.supervision_questionnaire.list",
          keepAlive: false
        }
      },
      {
        path: "audit_configuration",
        component: () => import("@/views/regulatory-center/AuditConfiguration.vue"),
        name: "AuditConfiguration",
        meta: {
          hidden: true,
          title: "r_t_audit_configuration",
          level: 1,
          permission: "background_fund_supervision.supervision_questionnaire.questionnaire_answer_detail",
          keepAlive: false
        }
      }
    ]
  },
  // 监管中心-食堂管理
  {
    path: "/canteen_management",
    component: Layouts,
    redirect: "/canteen_management/sample_record",
    name: "CanteenManagement",
    meta: {
      title: "r_t_canteen_management",
      roles: ["admin", "editor"], // 可以在根路由中设置角色
      alwaysShow: true, // 将始终显示根菜单
      level: 0, // 一级菜单，这个要加上，因为菜单根据这个层级知道你是第几层
      permission: "channel_canteen_management",
      keepAlive: false
    },
    children: [
      {
        path: "kitchen_fire",
        component: () => import("@/views/canteen-management/KitchenFire.vue"),
        name: "KitchenFire",
        meta: {
          title: "r_t_kitchen_fire",
          level: 1,
          permission: "kitchen_fire",
          keepAlive: false
        }
      },
      {
        path: "sample_record",
        component: () => import("@/views/canteen-management/SampleRecord.vue"),
        name: "SampleRecord",
        meta: {
          title: "r_t_sample_record",
          level: 1,
          permission: "background_fund_supervision.channel_canteen_management.food_reserved_sample_record",
          keepAlive: false
        }
      },
      {
        path: "morning_inspection_record",
        component: () => import("@/views/canteen-management/MorningInspectionRecord.vue"),
        name: "MorningInspectionRecord",
        meta: {
          title: "r_t_morning_inspection_record",
          level: 1,
          permission: "background_fund_supervision.channel_canteen_management.morning_check",
          keepAlive: false
        }
      },
      {
        path: "pest_control",
        component: () => import("@/views/canteen-management/PestControl.vue"),
        name: "PestControl",
        meta: {
          title: "r_t_pest_control",
          level: 1,
          permission: "background_fund_supervision.channel_canteen_management.pest_control_record",
          keepAlive: false
        }
      },
      {
        path: "democratic_supervision",
        component: () => import("@/views/canteen-management/DemocraticSupervision.vue"),
        name: "DemocraticSupervision",
        meta: {
          title: "r_t_democratic_supervision",
          level: 1,
          permission: "background_fund_supervision.channel_canteen_management.democratic_feedback",
          keepAlive: false
        }
      },
      {
        path: "market_inquiry",
        component: () => import("@/views/canteen-management/MarketInquiry.vue"),
        name: "MarketInquiry",
        meta: {
          title: "r_t_market_inquiry",
          level: 1,
          permission: "background_fund_supervision.fund_market_inquiry.list",
          keepAlive: false
        }
      },
      {
        path: "weekly_recipes",
        component: () => import("@/views/canteen-management/WeeklyRecipes.vue"),
        name: "WeeklyRecipes",
        meta: {
          title: "r_t_weekly_recipes",
          level: 1,
          permission: "weekly_recipes",
          keepAlive: false
        }
      },
      {
        path: "meal_management_admin",
        component: () => import("@/views/canteen-management/MealManagement.vue"),
        name: "MealManagement",
        meta: {
          title: "r_t_meal_management",
          level: 1,
          permission: "background_fund_supervision.channel_canteen_management.channel_meal_accompanying_list",
          keepAlive: false
        }
      },
      {
        path: "ration_recipe",
        component: () => import("@/views/canteen-management/RationRecipe.vue"),
        name: "RationRecipe",
        meta: {
          title: "r_t_ration_recipe",
          level: 1,
          permission: "background_fund_supervision.supervision_ration_recipe.list",
          keepAlive: false
        }
      }
    ]
  },

  // 食堂管理-资产管理
  {
    path: "/property_management",
    component: Layouts,
    redirect: "/property_management/asset_statistics",
    name: "PropertyManagement",
    meta: {
      title: "r_t_property_management",
      roles: ["admin", "editor"], // 可以在根路由中设置角色
      alwaysShow: true, // 将始终显示根菜单
      level: 0, // 一级菜单，这个要加上，因为菜单根据这个层级知道你是第几层
      permission: "fund_supervision_asset",
      keepAlive: false
    },
    children: [
      {
        path: "asset_statistics",
        component: () => import("@/views/canteen-management/property_management/AssetStatistics.vue"),
        name: "AssetStatistics",
        meta: {
          title: "r_t_asset_statistics",
          level: 1,
          permission: "background_fund_supervision.asset.channel_asset_info_statistics_list",
          keepAlive: false
        }
      },
      {
        path: "debt_statistics",
        component: () => import("@/views/canteen-management/property_management/DebtStatistics.vue"),
        name: "DebtStatistics",
        meta: {
          title: "r_t_debt_statistics",
          level: 1,
          permission: "background_fund_supervision.asset.channel_liability_info_statistics_list",
          keepAlive: false
        }
      }
    ]
  },
  // 数据中心-财务报表
  {
    path: "/financial_statements",
    component: Layouts,
    redirect: "/financial_statements/capital_flow_details",
    name: "financial_statements",
    meta: {
      title: "r_t_financial_statements",
      roles: ["admin", "editor"], // 可以在根路由中设置角色
      alwaysShow: true, // 将始终显示根菜单
      level: 0, // 一级菜单，这个要加上，因为菜单根据这个层级知道你是第几层
      permission: "finance_report",
      keepAlive: false
    },
    children: [
      {
        path: "capital_flow_details",
        component: () => import("@/views/financial-report/CapitalflowDetails.vue"),
        name: "CapitalFlowDetails",
        meta: {
          title: "r_t_capital_flow_details",
          level: 1,
          keepAlive: false,
          permission: "background_fund_supervision.finance_report.fund_water_list"
        }
      },
      {
        path: "daily_report_income",
        component: () => import("@/views/financial-report/DailyReportIncome.vue"),
        name: "DailyReportIncome",
        meta: {
          title: "r_t_daily_report_income",
          level: 1,
          keepAlive: false,
          permission: "background_fund_supervision.finance_report.fund_day_report_list"
        }
      },
      {
        path: "month_report_income",
        component: () => import("@/views/financial-report/MonthReportIncome.vue"),
        name: "MonthReportIncome",
        meta: {
          title: "r_t_month_report_income",
          level: 1,
          keepAlive: false,
          permission: "background_fund_supervision.finance_report.fund_month_report_list"
        }
      },
      {
        path: "low_consumption_statistics",
        component: () => import("@/views/data-center/business-report/LowConsumptionStatistics.vue"),
        name: "LowConsumptionStatistics",
        meta: {
          title: "r_t_low_consumption_statistics",
          level: 1,
          keepAlive: false,
          permission: "low_consumption_statistics"
        }
      },
      {
        path: "funds_account_record",
        component: () => import("@/views/data-center/business-report/FundsAccountRecord.vue"),
        name: "FundsAccountRecord",
        meta: {
          title: "r_t_funds_account_record",
          level: 1,
          keepAlive: false,
          permission: "funds_account_record"
        }
      },
      {
        path: "account_income_expenditure_details",
        component: () => import("@/views/data-center/business-report/AccountIncomeExpenditureDetails.vue"),
        name: "AccountIncomeExpenditureDetails",
        meta: {
          title: "r_t_account_income_expenditure_details",
          level: 1,
          keepAlive: false,
          permission: "account_income_expenditure_details"
        }
      }
    ]
  },
  // 数据中心-业务报表
  {
    path: "/business_report",
    component: Layouts,
    redirect: "/business_report/canteen_monthly_report",
    name: "BusinessReport",
    meta: {
      title: "r_t_business_report",
      level: 0,
      permission: "business_report",
      keepAlive: false,
      alwaysShow: true
    },
    children: [
      {
        path: "canteen_monthly_report",
        component: () => import("@/views/financial-report/CanteenMonthReport.vue"),
        name: "CanteenMonthlyReport",
        meta: {
          title: "r_t_canteen_monthly_report",
          level: 1,
          keepAlive: false,
          permission: "background_fund_supervision.business_report.canteen_month_accounting_list"
        }
      },
      {
        path: "income_expense_summary",
        component: () => import("@/views/financial-report/IncomeExpenseSummary.vue"),
        name: "IncomeExpenseSummary",
        meta: {
          title: "r_t_income_expense_summary",
          level: 1,
          keepAlive: false,
          permission: "background_fund_supervision.business_report.income_summary_list"
        }
      },
      {
        path: "income_expense_statistics",
        component: () => import("@/views/financial-report/IncomeExpenseStatistics.vue"),
        name: "income_expense_statistics",
        meta: {
          title: "r_t_income_expense_statistics",
          level: 1,
          keepAlive: false,
          permission: "background_fund_supervision.business_report.income_statistics_list"
        }
      }
    ]
  },
  // 数据中心-数据驾驶舱
  {
    path: "/data_cockpit",
    component: Layouts,
    redirect: "/data_cockpit/large_sceen_list",
    name: "DataCockpit",
    meta: {
      title: "r_t_data_cockpit",
      roles: ["admin", "editor"], // 可以在根路由中设置角色
      alwaysShow: true, // 将始终显示根菜单
      level: 0, // 一级菜单，这个要加上，因为菜单根据这个层级知道你是第几层
      permission: "big_shield",
      keepAlive: false
    },
    children: [
      {
        path: "large_sceen_list",
        component: () => import("@/views/data-center/large-screen/LargeSceenList.vue"),
        name: "LargeSceenList",
        meta: {
          title: "r_t_large_sceen_list",
          level: 1,
          permission: "big_shield_list",
          keepAlive: false
        }
      }
    ]
  },
  // 供应商管理
  {
    path: "/supplier_management",
    component: Layouts,
    redirect: "/supplier_management/supplier_information",
    name: "SupplierManagement",
    meta: {
      title: "r_t_supplier_management",
      roles: ["admin", "editor"], // 可以在根路由中设置角色
      alwaysShow: true, // 将始终显示根菜单
      level: 0, // 一级菜单，这个要加上，因为菜单根据这个层级知道你是第几层
      permission: "fund_supervision_supplier",
      keepAlive: false
    },
    children: [
      {
        path: "supplier_information",
        component: () => import("@/views/supplier-management/SupplierInfomation.vue"),
        name: "SupplierInformation",
        meta: {
          title: "r_t_supplier_information",
          level: 1,
          permission: "background_fund_supervision.supplier_manage",
          keepAlive: false
        }
      },
      {
        path: "supplier_approval",
        component: () => import("@/views/supplier-management/SupplierApproval.vue"),
        name: "SupplierApproval",
        meta: {
          title: "r_t_supplier_approval",
          level: 1,
          permission: "background_fund_supervision.supplier_manage.supplier_manage_apply_data",
          keepAlive: false
        }
      }
    ]
  },
  // 单据管理
  {
    path: "/document_management",
    component: Layouts,
    redirect: "/document_management/purchase_order",
    name: "DocumentManagement",
    meta: {
      title: "r_t_document_management",
      roles: ["admin", "editor"], // 可以在根路由中设置角色
      alwaysShow: true, // 将始终显示根菜单
      level: 0, // 一级菜单，这个要加上，因为菜单根据这个层级知道你是第几层
      permission: "fund_supervision_document",
      keepAlive: false
    },
    children: [
      {
        path: "purchase_order",
        component: () => import("@/views/document-management/PuchaseOrder.vue"),
        name: "PurchaseOrder",
        meta: {
          title: "r_t_purchase_order",
          level: 1,
          permission: "background_fund_supervision.supervision_data.purchase_info_list",
          keepAlive: false
        }
      },
      // {
      //   path: "inbound_and_outbound_orders",
      //   component: () => import("@/views/document-management/InboundAndOutboundOrders.vue"),
      //   name: "InboundAndOutboundOrders",
      //   meta: {
      //     title: "r_t_inbound_and_outbound_orders",
      //     level: 1,
      //     permission: "inbound_and_outbound_orders",
      //     keepAlive: false
      //   }
      // },
      {
        path: "delivery_order",
        component: () => import("@/views/document-management/DeliveryOrder.vue"),
        name: "DeliveryOrder",
        meta: {
          title: "r_t_delivery_order",
          level: 1,
          permission: "background_fund_supervision.supervision_data.vendor_delivery_list",
          keepAlive: false
        }
      },
      {
        path: "receipt_order",
        component: () => import("@/views/document-management/ReceiptOrder.vue"),
        name: "ReceiptOrder",
        meta: {
          title: "r_t_receipt_order",
          level: 1,
          permission: "background_fund_supervision.supervision_data.receiving_note_list",
          keepAlive: false
        }
      },
      {
        path: "settlement_order",
        component: () => import("@/views/document-management/SettlementOrder.vue"),
        name: "SettlementOrder",
        meta: {
          title: "r_t_settlement_order",
          level: 1,
          permission: "background_fund_supervision.supervision_data.final_statement_list",
          keepAlive: false
        }
      },
      {
        path: "application_order",
        component: () => import("@/views/document-management/ApplicationOrder.vue"),
        name: "ApplicationtOrder",
        meta: {
          title: "r_t_application_order",
          level: 1,
          permission: "background_fund_supervision.supervision_data.application_form_list",
          keepAlive: false
        }
      },
      {
        path: "stock_ledger",
        component: () => import("@/views/document-management/StockLedger.vue"),
        name: "StockLedger",
        meta: {
          title: "r_t_stock_ledger",
          level: 1,
          permission: "background_fund_supervision.supervision_data.inventory_balance_list",
          keepAlive: false
        }
      }
    ]
  },
  // 财务管理
  {
    path: "/financial_management",
    component: Layouts,
    redirect: "/financial_management/financial_approve",
    name: "FinancialManagement",
    meta: {
      title: "r_t_financial_management",
      roles: ["admin", "editor"], // 可以在根路由中设置角色
      alwaysShow: true, // 将始终显示根菜单
      level: 0, // 一级菜单，这个要加上，因为菜单根据这个层级知道你是第几层
      permission: "supervision_finance_setting",
      keepAlive: false
    },
    children: [
      {
        path: "financial_approve",
        component: () => import("@/views/financial-management/FinancialApprove.vue"),
        name: "FinancialApprove",
        meta: {
          title: "r_t_financial_approve",
          level: 1,
          permission: "supervision_finance_approve",
          keepAlive: false
        }
      },
      {
        path: "financial_record",
        component: () => import("@/views/financial-management/AppropriationRecord.vue"),
        name: "AppropriationRecord",
        meta: {
          title: "r_t_financial_record",
          level: 1,
          permission: "background_fund_supervision.supervision_appropriation.list",
          keepAlive: false
        }
      },
      {
        path: "appropriation_config",
        component: () => import("@/views/financial-management/AppropriationConfig.vue"),
        name: "AppropriationConfig",
        meta: {
          title: "r_t_appropriation_config",
          level: 1,
          permission: "background_fund_supervision.supervision_appropriation.get_setting",
          keepAlive: false
        }
      }
    ]
  },
  // 预警管理
  {
    path: "/ai_warning_management",
    component: Layouts,
    redirect: "/ai_warning_management/warning_information",
    name: "AiWarningManagement",
    meta: {
      title: "r_t_ai_warning_management",
      roles: ["admin", "editor"], // 可以在根路由中设置角色
      alwaysShow: true, // 将始终显示根菜单
      level: 0, // 一级菜单，这个要加上，因为菜单根据这个层级知道你是第几层
      permission: "ai_warn_manage",
      keepAlive: false
    },
    children: [
      {
        path: "warning_information",
        component: () => import("@/views/ai-warning-management/WarningInformation.vue"),
        name: "WarningInformation",
        meta: {
          title: "r_t_ai_warning_information",
          level: 1,
          permission: "background_fund_supervision.warn_manage.warning_message_list",
          keepAlive: false
        }
      },
      {
        path: "warning_configuration",
        component: () => import("@/views/ai-warning-management/WarningConfiguration.vue"),
        name: "WarningConfiguration",
        meta: {
          title: "r_t_ai_warning_configuration",
          level: 1,
          permission: "warn_disposition",
          keepAlive: false
        }
      }
    ]
  },
  // 账号管理
  {
    path: "/account_management_main",
    component: Layouts,
    redirect: "/account_management_main/account_management",
    name: "AccountManagementMain",
    meta: {
      title: "r_t_account_management",
      roles: ["admin", "editor"], // 可以在根路由中设置角色
      alwaysShow: true, // 将始终显示根菜单
      level: 0, // 一级菜单，这个要加上，因为菜单根据这个层级知道你是第几层
      permission: "fund_account_management",
      keepAlive: false
    },
    children: [
      {
        path: "account_management",
        name: "AccountManagement",
        component: () => import("@/views/system-management/account-management-main/AccountManagement.vue"),
        meta: {
          title: "r_t_account_management",
          level: 1,
          permission: "background_fund_supervision.audit_account",
          keepAlive: false
        }
      },
      {
        path: "role_management",
        name: "RoleManagement",
        component: () => import("@/views/system-management/account-management-main/RoleManagement.vue"),
        meta: {
          title: "r_t_role_management",
          level: 1,
          permission: "background_fund_supervision.channel_role",
          keepAlive: false
        }
      }
    ]
  },
  // 审批配置
  {
    path: "/approval_configuration",
    component: Layouts,
    redirect: "/approval_configuration/approval_process",
    name: "ApprovalConfiguration",
    meta: {
      title: "r_t_approval_configuration",
      level: 1,
      permission: "fund_approval_config",
      keepAlive: false
    },
    children: [
      {
        path: "approval_process",
        component: () => import("@/views/system-management/approval-configuration/ApprovalProcess.vue"),
        name: "ApprovalProcess",
        meta: {
          title: "r_t_approval_process",
          level: 1,
          permission: "background_fund_supervision.channel_approve_rule",
          keepAlive: false
        }
      }
    ]
  },
  // 公告管理
  {
    path: "/notification_management",
    component: Layouts,
    redirect: "/notification_management/system_notification",
    name: "NotificationManagement",
    meta: {
      title: "r_t_notification_management",
      level: 1,
      permission: "supervision_system_messages",
      keepAlive: false
    },
    children: [
      {
        path: "system_notification",
        component: () => import("@/views/system-management/notice-and-announcement/NoticeAndAnnouncement.vue"),
        name: "system_notification",
        meta: {
          title: "r_t_system_notification",
          level: 1,
          permission: "background_fund_supervision.supervision_messages.list",
          keepAlive: false
        }
      },
      {
        path: "system_announcement",
        component: () => import("@/views/system-management/notification-management/systemAnnouncement.vue"),
        name: "system_announcement",
        meta: {
          title: "r_t_system_announcement",
          level: 1,
          permission: "background_fund_supervision.supervision_messages.notice_list",
          keepAlive: false
        }
      }
    ]
  },
  // 其他服务
  {
    path: "/other_services",
    component: Layouts,
    redirect: "/other_services/query_center",
    name: "OtherServices",
    meta: {
      title: "r_t_other_services",
      level: 1,
      permission: "other_services",
      keepAlive: false
    },
    children: [
      // 数据中心-查询中心
      {
        path: "query_center",
        component: () => import("@/layouts/RouterMenu.vue"),
        redirect: "/other_services/query_center/suspend_query",
        name: "QueryCenter",
        meta: {
          title: "r_t_query_center",
          roles: ["admin", "editor"], // 可以在根路由中设置角色
          alwaysShow: true, // 将始终显示根菜单
          level: 0, // 一级菜单，这个要加上，因为菜单根据这个层级知道你是第几层
          permission: "query_center",
          keepAlive: false
        },
        children: [
          {
            path: "suspend_query",
            component: () => import("@/views/data-center/query-center/SuspendQuery.vue"),
            name: "SuspendQuery",
            meta: {
              title: "r_t_suspend_query",
              level: 1,
              permission: "suspend_query",
              keepAlive: false
            }
          },
          {
            path: "suspend_details",
            component: () => import("@/views/data-center/query-center/SuspendDetails.vue"),
            name: "SuspendDetails",
            meta: {
              title: "r_t_see_details",
              level: 1,
              permission: "suspend_details",
              keepAlive: false,
              hidden: false
            }
          }
        ]
      },
      // 数据中心-下载中心
      {
        path: "download_center",
        component: () => import("@/layouts/RouterMenu.vue"),
        redirect: "/other_services/download_center/download_record",
        name: "DownloadCenter",
        meta: {
          title: "r_t_download_center",
          roles: ["admin", "editor"], // 可以在根路由中设置角色
          alwaysShow: true, // 将始终显示根菜单
          level: 0, // 一级菜单，这个要加上，因为菜单根据这个层级知道你是第几层
          permission: "download_center",
          keepAlive: false
        },
        children: [
          {
            path: "download_record",
            component: () => import("@/views/data-center/download-center/DownloadRecord.vue"),
            name: "DownloadRecord",
            meta: {
              title: "r_t_download_record",
              level: 1,
              permission: "download_record",
              keepAlive: false
            }
          }
        ]
      }
    ]
  }
]

export default healthRoutes
