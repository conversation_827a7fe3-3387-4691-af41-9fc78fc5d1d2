import { RouteRecordRaw } from "vue-router"
const Layouts = () => import("@/layouts/index.vue")

const constantRoutes: RouteRecordRaw[] = [
  {
    path: "/redirect",
    component: Layouts,
    meta: {
      hidden: true
    },
    children: [
      {
        path: ":path(.*)",
        component: () => import("@/views/redirect/index.vue")
      }
    ]
  },
  {
    path: "/403",
    component: () => import("@/views/error-page/403.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/404",
    component: () => import("@/views/error-page/404.vue"),
    meta: {
      hidden: true
    },
    alias: "/:pathMatch(.*)*"
  },
  {
    path: "/login",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: "r_t_login",
      hidden: true
    }
  },
  {
    path: "/agreement",
    component: () => import("@/views/agreement/index.vue"),
    meta: {
      title: "r_t_agreement",
      hidden: true,
      name: "agreement"
    }
  },
  {
    path: "/excel",
    component: Layouts,
    redirect: "/excel/export",
    meta: { hidden: true },
    children: [
      {
        path: "export",
        component: () => import("@/views/excel/Excel.vue"),
        name: "ExportExcel",
        meta: { title: "r_t_export_excel", noCache: true, hidden: true }
      }
    ]
  },
  {
    path: "/print",
    component: () => import("@/views/print/index.vue"),
    meta: { hidden: true }
  },
  {
    path: "/preview",
    component: () => import("@/views/survey/preview.vue"),
    meta: { hidden: true }
  },
  {
    path: "/questionnaire-detail",
    component: () => import("@/views/survey/QuestionnaireDetail.vue"),
    meta: { hidden: true }
  },
  {
    path: "/",
    component: Layouts,
    redirect: "/dashboard",
    name: "r_t_dashboard",
    meta: {
      hidden: true
    },
    children: [
      {
        path: "dashboard",
        component: () => import("@/views/dashboard/index.vue"),
        name: "r_t_dashboard",
        meta: {
          title: "r_t_dashboard",
          svgIcon: "dashboard",
          affix: true,
          level: 0
        }
      }
    ]
  }
]

export default constantRoutes
