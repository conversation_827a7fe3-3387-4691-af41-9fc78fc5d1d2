import defaltRoutes from "./default.route"
import adminRoutes from "./admin.route"
import healthyRoutes from "./health.route"
import { RouteRecordRaw } from "vue-router"
import cloneDeep from "lodash/cloneDeep"

const routes = [...adminRoutes, ...healthyRoutes, ...defaltRoutes]

export default routes

export { defaltRoutes, adminRoutes, healthyRoutes }

// 将所有路由转换成一个列表
export function getAllRoutesList(): RouteRecordRaw[] {
  const list = treeToFlat(routes)
  return list
}
// 将tree 的route 转换成 数组
function treeToFlat(data: any[]) {
  const newList = cloneDeep(data)
  const newData: any[] = []
  const callback = (item: any) => {
    ;(item.children || (item.children = [])).map((v: any) => {
      callback(v)
    })
    delete item.children
    newData.push(item)
  }
  newList.map((v) => callback(v))
  return newData
}
