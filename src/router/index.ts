import { type RouteRecordRaw, createRouter } from "vue-router"
import { history } from "./helper"
import routeList, { defaltRoutes } from "@/router/modules"
import type { App } from "vue"
const Layouts = () => import("@/layouts/index.vue")
console.log("routeList", routeList)

const router = createRouter({
  history,
  routes: defaltRoutes
})
// 路由失败原因
router.onError((error) => {
  console.log("router", error)
})

/** 重置路由 */
export function resetRouter() {
  // 注意：所有动态路由路由必须带有 Name 属性，否则可能会不能完全重置干净
  try {
    router.getRoutes().forEach((route: any) => {
      const { name, meta } = route
      if (name && meta.roles?.length) {
        router.hasRoute(name) && router.removeRoute(name)
      }
    })
  } catch {
    // 强制刷新浏览器也行，只是交互体验不是很好
    window.location.reload()
  }
}
// 注册router
export function setupRouter(app: App) {
  app.use(router)
}
// 设置路由
export const setRoute = (list: RouteRecordRaw[]) => {
  if (list && list.length > 0) {
    list.forEach((item: any) => {
      if (item.level === 1) {
        router.addRoute(item)
      }
      if (item.children && item.children.length > 0) {
        setRoute(item.children)
      }
    })
  }
}

export default router
