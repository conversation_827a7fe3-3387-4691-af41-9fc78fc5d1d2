// 全局 CSS 变量
@import "./variables.css";
// Transition
@import "./transition.scss";
// Element Plus
@import "./element-plus.css";
@import "./element-plus.scss";
// Vxe Table
@import "./vxe-table.css";
@import "./vxe-table.scss";
// 注册多主题
@import "./theme/register.scss";
// Mixins
@import "./mixins.scss";
// View Transition
@import "./view-transition.scss";

// 业务页面几乎都应该在根元素上挂载 class="app-container"，以保持页面美观
.app-container {
  padding: 0px 20px 20px;
}

html {
  height: 100%;
}

body {
  height: 100%;
  color: var(--v3-body-text-color);
  background-color: var(--v3-body-bg-color);
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial,
    sans-serif;
  @extend %scrollbar;
}

#app {
  height: 100%;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

a,
a:focus,
a:hover {
  color: inherit;
  outline: none;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.pointer {
  cursor: pointer;
}

.m-auto {
  margin: 0 auto;
}

.ps-inline {
  display: inline;
}

.ps-flex {
  display: flex;
}

.flex-inline {
  display: inline-flex;
}

.flex-col {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-align-center {
  align-items: center;
}

.flex-none {
  flex: none;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.col-baseline {
  align-items: baseline;
}

.col-center {
  align-items: center;
}

.col-top {
  align-items: flex-start;
}

.col-bottom {
  align-items: flex-end;
}

.col-stretch {
  align-items: stretch;
}

.row-center {
  justify-content: center;
}

.row-left {
  justify-content: flex-start;
}

.row-right {
  justify-content: flex-end;
}

.row-between {
  justify-content: space-between;
}

.row-around {
  justify-content: space-around;
}

.ps-dialog {
  .el-dialog__body {
    padding: 0;
  }
  border-radius: 10px !important;
  .el-dialog__title {
    font-weight: 600;
  }
  .el-dialog__close {
    color: #cccccc;
    font-size: 20px;
    font-weight: 600;
  }
}
.block-label {
  .el-form-item {
    display: block;
  }
}
@for $i from 0 through 100 {
  .font-size-#{$i} {
    font-size: $i + px;
  }
}

@for $i from 0 through 1920 {
  .w-#{$i} {
    width: $i + px;
  }
}

@for $i from 0 through 1080 {
  .h-#{$i} {
    height: $i + px;
  }
}

@for $i from 0 through 300 {
  .m-#{$i} {
    margin: $i + px;
  }
}

@for $i from 0 through 300 {
  .m-t-#{$i} {
    margin-top: $i + px;
  }
}

@for $i from 0 through 300 {
  .m-l-#{$i} {
    margin-left: $i + px;
  }
}

@for $i from 0 through 300 {
  .m-r-#{$i} {
    margin-right: $i + px;
  }
}

@for $i from 0 through 300 {
  .m-b-#{$i} {
    margin-bottom: $i + px;
  }
}

@for $i from 0 through 300 {
  .p-#{$i} {
    padding: $i + px;
  }
}

@for $i from 0 through 100 {
  .p-t-#{$i} {
    padding-top: $i + px;
  }
}

@for $i from 0 through 100 {
  .p-b-#{$i} {
    padding-bottom: $i + px;
  }
}

@for $i from 0 through 100 {
  .p-l-#{$i} {
    padding-left: $i + px;
  }
}

@for $i from 0 through 100 {
  .p-r-#{$i} {
    padding-right: $i + px;
  }
}

@for $i from 0 through 100 {
  .b-r-#{$i} {
    border-radius: $i + px;
  }
}

@for $i from 0 through 300 {
  .l-h-#{$i} {
    line-height: $i + px;
    height: $i + px;
  }
}

.ps-drawer {
  border-radius: 10px 0 0 10px;

  .el-drawer__title {
    font-weight: bold;
    color: #1e2224;
    font-size: 18px;
  }

  .el-drawer__header {
    margin-bottom: 0;
  }

  .dialog-footer {
    position: sticky;
    bottom: -20px;
    padding: 20px 0px;
    background-color: #fff;
    display: flex;
    z-index: 99;
    .canel-btn .confirm-btn {
      width: 96px;
      height: 32px;
      border-radius: 4px;
    }
  }
}

.ps-notification-custom-class {
  .el-notification__group {
    width: 100%;
  }
}

.ps-origin-btn-light {
  color: #fff;
  background-color: var(--el-btn-color-light) !important;
  border: 1px solid var(--el-btn-color-light) !important;

  &:hover {
    color: #fff !important;
    background-color: var(--el-color-primary) !important;
  }

  &:active {
    color: #fff !important;
    background-color: var(--el-color-primary) !important;
  }

  &.is-disabled {
    opacity: 0.5;
  }
}

.ps-origin-btn-plain {
  color: var(--el-color-primary) !important;
  background: #fff;
  border: 1px solid var(--el-color-primary) !important;

  &:hover {
    color: #fff !important;
    background-color: var(--el-color-primary) !important;
  }

  &:active {
    color: #fff !important;
    background-color: var(--el-color-primary) !important;
  }

  &.is-disabled {
    opacity: 0.5;
  }
}
.ps-red-btn {
  color: var(--el-color-red) !important;
  background: #fff;
  border: 1px solid var(--el-color-red) !important;

  &:hover {
    color: #fff !important;
    background-color: var(--el-color-red) !important;
  }

  &:active {
    color: #fff !important;
    background-color: var(--el-color-red) !important;
  }

  &.is-disabled {
    opacity: 0.5;
  }
}

.container-wrapper {
  .table-wrapper {
    margin-top: 20px;
    // padding: $mainPadding;
    background-color: #fff;
  }

  .search-form-wrapper {
    // padding: 0 20px;
    border-radius: 12px;
    box-shadow:
      6px 6px 10px 0 rgb(202 210 221 / 30%),
      inset 2px 2px 0 0 #fff;
  }

  .table-wrapper,
  .box-wrapper {
    overflow: hidden;
    background-color: #fff;
    border-radius: 12px;
    box-shadow:
      6px 6px 10px 0 rgb(202 210 221 / 30%),
      inset 2px 2px 0 0 #fff;
  }

  .box-header {
    position: relative;
    padding: 15px 0;
    margin-bottom: 15px;
    background-color: #f8f9fa;

    &::after {
      position: absolute;
      right: 24px;
      bottom: 0;
      left: 24px;
      height: 1px;
      content: "";
      background-color: #e7ecf2;
    }

    .box-title {
      padding-left: 18px;
      font-size: 20px;
      color: #23282d;
      border-left: 4px solid #ff9b45;
    }
  }

  .box-content {
    padding-right: 20px;
    padding-bottom: 20px;
    padding-left: 20px;
  }

  &.has-organization {
    .organization-tree {
      border-radius: 12px 0 0 12px;
      box-shadow: inset 2px 2px 0 0 #fff;
    }

    .search-form-wrapper {
      border-radius: 0 12px 12px 0;
      box-shadow:
        6px 6px 10px 0 rgb(202 210 221 / 30%),
        inset 2px 2px 0 0px#ffffff;
    }

    .table-wrapper {
      border-radius: 0 12px 12px 0;
      box-shadow:
        6px 6px 10px 0 rgb(202 210 221 / 30%),
        inset 2px 2px 0 0 #fff;
    }
  }

  .table-header {
    position: relative;
    padding: 15px 20px;
    // margin-bottom: 15px;
    background-color: #fff;
    min-height: 72px;

    // display: flex;
    // justify-content: space-between;
    .table-button {
      position: absolute;
      top: 15px;
      right: 20px;
      display: flex;
      justify-content: flex-end;
    }

    &::after {
      position: absolute;
      right: 24px;
      bottom: 0;
      left: 24px;
      height: 1px;
      content: "";
      background-color: #e7ecf2;
    }

    .table-title {
      position: relative;
      flex: 1;
      min-width: 250px;
      height: 34px;
      // padding-left: 30px;
      font-size: 20px;
      font-weight: bold;
      line-height: 34px;
      color: #23282d;
      // background-color: rgb(241 246 247 / 92.1%);
    }

    .table-title::before {
      position: absolute;
      top: 7px;
      left: -20px;
      height: 20px;
      content: "";
      border-left: 6px solid var(--el-color-primary);
      border-radius: 0 3px 3px 0;
      // box-shadow: 4px 5px 7px 0 rgb(0 0 0 / 75%);
    }
  }

  .align-r {
    padding: 0 20px;
    text-align: right;

    // display: flex;
    // justify-content: flex-end;
    .el-button {
      &:not(:last-child) {
        margin-right: 10px;
      }
    }
  }

  .table-content {
    padding: 0 20px;

    .el-table__empty-block {
      display: flex;
      justify-content: center;

      .el-table__empty-text {
        box-sizing: border-box;
        display: block;
        width: 130px;
        height: 160px;
        padding-top: 110px;
        margin-top: 25px;
        color: #909399;
        text-align: center;
      }
    }
  }
  .ps-pagination {
    display: flex;
    justify-content: end;
    padding: 20px;
    text-align: right;
  }
  .ps-table-header-row {
    color: #fff;
    background-color: #eef1f6;
    border-radius: 4px 4px 0 0;

    th.el-table__cell {
      padding: 10px 0;
      color: #000;
      // background-color: #f7f9fa !important;
    }
  }
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.ps-red-txt {
  color: var(--el-color-red);
}
.ps-origin-txt {
  color: var(--el-color-primary);
}
/** S 文本行数限制 **/

.line-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-2 {
  -webkit-line-clamp: 2;
}

.line-3 {
  -webkit-line-clamp: 3;
}

.line-4 {
  -webkit-line-clamp: 4;
}

.line-2,
.line-3,
.line-4 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-box-orient: vertical;
}

.header-bg-blue {
  background-color: #c8e4fb !important; /* 蓝色背景 */
}

.header-bg-green {
  background-color: #81c784 !important; /* 绿色背景 */
}
.hor-line-black {
  border-bottom: 1px dashed #000;
}
