/**
 * 主题模式下的 Element Plus CSS 变量
 * 在此查阅所有可自定义的变量：https://github.com/element-plus/element-plus/blob/dev/packages/theme-chalk/src/common/var.scss
 * 也可以打开浏览器控制台选择元素，查看要覆盖的变量名
 */

/** ------------------- 默认主题 start ----------------- */
/** 基础颜色 */
html.normal {
  --el-color-primary: #2f77ff;
  --el-color-primary-light-3: #ebf2ff;
  --el-color-gray: #e6e8eb;
  --el-color-white: #ffffff;
  --el-color-black: #363636;
  --el-color-menu-bg: #242424;
  --el-menu-liner-bg: linear-gradient(90deg, rgba(47, 119, 255, 0.2) 0%, rgba(47, 119, 255, 0) 100%);
  --el-btn-color-light: #00aaff;
  /** echart */
  --el-color-echart-1: #2f77ff;
  --el-color-echart-2: #15c8dc;
  --el-color-echart-3: #ff9f13;
  --el-color-echart-4: #6b767e;
  --el-color-echart-5: #b8c0c6;
  --el-color-red: #ff5656;
}
/** button */
html.normal .el-button {
  --el-button-disabled-text-color: rgba(255, 255, 255, 0.5);
}

html.normal .el-button--primary {
  --el-button-hover-bg-color: #2f77ff;
  --el-button-hover-text-color: #fff;
  --el-button-hover-border-color: #fff;
}

/** ------------------- 默认主题 end ----------------- */

/** ------------------- 蓝色主题 start ----------------- */
html.blue {
  --el-color-primary: #2f77ff;
  --el-color-primary-light-3: #ebf2ff;
  --el-color-menu-bg: #242424;
  --el-menu-liner-bg: linear-gradient(90deg, rgba(47, 119, 255, 0.2) 0%, rgba(47, 119, 255, 0) 100%);
  --el-btn-color-light: #00aaff;
  /** echart */
  --el-color-echart-1: #2f77ff;
  --el-color-echart-2: #15c8dc;
  --el-color-echart-3: #ff9f13;
  --el-color-echart-4: #6b767e;
  --el-color-echart-5: #b8c0c6;
  --el-color-red: #ff5656;
  --el-table-border-color: "#6b767e";
}

/** button */
html.blue .el-button {
  --el-button-disabled-text-color: rgba(255, 255, 255, 0.5);
}

html.blue .el-button--primary {
  --el-button-hover-bg-color: #2f77ff;
  --el-button-hover-text-color: #fff;
  --el-button-hover-border-color: #fff;
}

/** ------------------- 蓝色主题 end ----------------- */

/** ------------------- 绿色主题 start ----------------- */
html.green {
  --el-color-primary: #00b482;
  --el-color-primary-light-3: #e6f8f3;
  --el-color-menu-bg: #242424;
  --el-menu-liner-bg: linear-gradient(90deg, rgba(0, 180, 130, 0.2) 0%, rgba(0, 180, 130, 0) 100%);
  --el-btn-color-light: #09f0c6;
  /** echart */
  --el-color-echart-1: #00b482;
  --el-color-echart-2: #15c8dc;
  --el-color-echart-3: #ff9f13;
  --el-color-echart-4: #6b767e;
  --el-color-echart-5: #b8c0c6;
  --el-color-red: #ff5656;
}

/** button */
html.green .el-button {
  --el-button-disabled-text-color: rgba(255, 255, 255, 0.5);
}

html.green .el-button--primary {
  --el-button-hover-bg-color: #00b482;
  --el-button-hover-text-color: #fff;
  --el-button-hover-border-color: #fff;
}

/** ------------------- 绿色主题 end ----------------- */

/** ------------------- 紫色主题 start ----------------- */
html.purple {
  --el-color-primary: #692bfa;
  --el-color-primary-light-3: #e7f9f5;
  --el-color-menu-bg: #242424;
  --el-menu-liner-bg: linear-gradient(90deg, rgba(47, 119, 255, 0.2) 0%, rgba(47, 119, 255, 0) 100%);
  --el-btn-color-light: #00aaff;
  --el-color-red: #ff5656;
}

/** button */
html.purple .el-button {
  --el-button-disabled-text-color: rgba(255, 255, 255, 0.5);
}

html.purple .el-button--primary {
  --el-button-hover-bg-color: #692bfa;
  --el-button-hover-text-color: #fff;
  --el-button-hover-border-color: #fff;
}

/** ------------------- 紫色主题 end ----------------- */

/** ------------------- 橙色主题 start ----------------- */
html.orange {
  --el-color-primary: #ff8b1e;
  --el-color-primary-light-3: #fff4e9;
  --el-color-menu-bg: #242424;
  --el-menu-liner-bg: linear-gradient(90deg, rgba(255, 139, 30, 0.2) 0%, rgba(255, 139, 30, 0) 100%);
  --el-btn-color-light: #ff6a00;
  /** echart */
  --el-color-echart-1: #ff8b1e;
  --el-color-echart-2: #ffcb3a;
  --el-color-echart-3: #7d7064;
  --el-color-echart-4: #6b767e;
  --el-color-echart-5: #b8c0c6;
  --el-color-red: #ff5656;
}

/** button */
html.orange .el-button {
  --el-button-disabled-text-color: rgba(255, 255, 255, 0.5);
}

html.orange .el-button--primary {
  --el-button-hover-bg-color: #ff8b1e;
  --el-button-hover-text-color: #fff;
  --el-button-hover-border-color: #fff;
}

/** ------------------- 橙色主题 end ----------------- */

/** button */
html.dark-blue .el-button {
  --el-button-disabled-text-color: rgba(255, 255, 255, 0.5);
}

/** 基础颜色 */
html.dark-blue {
  /** color-primary */
  --el-color-primary: #00bb99;
  --el-color-primary-light-3: #00bb99b3;
  --el-color-primary-light-5: #00bb9980;
  --el-color-primary-light-7: #00bb994d;
  --el-color-primary-light-8: #00bb9933;
  --el-color-primary-light-9: #00bb991a;
  --el-color-primary-dark-2: #00bb99;
  /** color-success */
  --el-color-success: #67c23a;
  --el-color-success-light-3: #67c23ab3;
  --el-color-success-light-5: #67c23a80;
  --el-color-success-light-7: #67c23a4d;
  --el-color-success-light-8: #67c23a33;
  --el-color-success-light-9: #67c23a1a;
  --el-color-success-dark-2: #67c23a;
  /** color-warning */
  --el-color-warning: #e6a23c;
  --el-color-warning-light-3: #e6a23cb3;
  --el-color-warning-light-5: #e6a23c80;
  --el-color-warning-light-7: #e6a23c4d;
  --el-color-warning-light-8: #e6a23c33;
  --el-color-warning-light-9: #e6a23c1a;
  --el-color-warning-dark-2: #e6a23c;
  /** color-danger */
  --el-color-danger: #f56c6c;
  --el-color-danger-light-3: #f56c6cb3;
  --el-color-danger-light-5: #f56c6c80;
  --el-color-danger-light-7: #f56c6c4d;
  --el-color-danger-light-8: #f56c6c33;
  --el-color-danger-light-9: #f56c6c1a;
  --el-color-danger-dark-2: #f56c6c;
  /** color-error */
  --el-color-error: #f56c6c;
  --el-color-error-light-3: #f56c6cb3;
  --el-color-error-light-5: #f56c6c80;
  --el-color-error-light-7: #f56c6c4d;
  --el-color-error-light-8: #f56c6c33;
  --el-color-error-light-9: #f56c6c1a;
  --el-color-error-dark-2: #f56c6c;
  /** color-info */
  --el-color-info: #909399;
  --el-color-info-light-3: #909399b3;
  --el-color-info-light-5: #90939980;
  --el-color-info-light-7: #9093994d;
  --el-color-info-light-8: #90939933;
  --el-color-info-light-9: #9093991a;
  --el-color-info-dark-2: #909399;
  /** text-color */
  --el-text-color-primary: #e5eaf3;
  --el-text-color-regular: #cfd3dc;
  --el-text-color-secondary: #a3a6ad;
  --el-text-color-placeholder: #8d9095;
  --el-text-color-disabled: #6c6e72;
  /** border-color */
  --el-border-color-darker: #003380;
  --el-border-color-dark: #003380;
  --el-border-color: #003380;
  --el-border-color-light: #003380;
  --el-border-color-lighter: #003380;
  --el-border-color-extra-light: #003380;
  /** fill-color */
  --el-fill-color-darker: #002b6b;
  --el-fill-color-dark: #002b6b;
  --el-fill-color: #002b6b;
  --el-fill-color-light: #002359;
  --el-fill-color-lighter: #002359;
  --el-fill-color-blank: #001b44;
  --el-fill-color-extra-light: #001b44;
  /** bg-color */
  --el-bg-color-page: #001535;
  --el-bg-color: #001b44;
  --el-bg-color-overlay: #002359;
  /** mask-color */
  --el-mask-color: rgba(0, 0, 0, 0.5);
  --el-mask-color-extra-light: rgba(0, 0, 0, 0.3);
  --el-color-menu-bg: #242424;
  --el-btn-color-light: #00aaff;
  --el-color-red: #ff5656;
}

/** button */
html.dark-blue .el-button {
  --el-button-disabled-text-color: rgba(255, 255, 255, 0.5);
}
