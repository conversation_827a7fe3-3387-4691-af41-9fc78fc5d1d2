import { graphic } from "echarts/core"

export const SERIES_ITEM = {
  type: "line",
  name: "退款",
  smooth: true,
  showSymbol: false,
  lineStyle: {
    width: 3,
    type: "solid",
    color: "#2f77ff"
  },
  areaStyle: {
    opacity: 0.5,
    color: new graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: "#2f77ff"
      },
      {
        offset: 1,
        color: "rgba(255,255,255,0)"
      }
    ])
  },
  itemStyle: {
    color: "#FFFFFF"
  },
  data: [140, 232, 101, 264, 90, 340, 250]
}

export const LINE_ECHART_OPTION = {
  tooltip: {
    show: true,
    trigger: "axis",
    axisPointer: {
      type: "line"
    }
  },
  xAxis: {
    show: true,
    type: "category",
    axisTick: {
      show: false // 不显示坐标轴刻度
    },
    axisLine: {
      show: true, // 不显示坐标轴轴线
      lineStyle: {
        color: "#E9ECF1"
      }
    },
    axisLabel: {
      show: true,
      color: "#737373"
    },
    data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
  },
  yAxis: {
    show: true,
    type: "value",
    axisLine: {
      show: false // 不显示坐标轴轴线
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: "dashed", // 横轴虚线
        color: "#E9ECF1"
      }
    },
    axisLabel: {
      show: true,
      color: "#737373"
    }
  },
  grid: {
    top: "10%",
    left: "2%",
    right: "4%",
    bottom: "10%",
    containLabel: true
  },
  legend: {
    show: false,
    data: ["消费"]
  },
  series: [SERIES_ITEM]
}

export const PIE_ECHART_OPTION = {
  color: ["#57C3FF", "#377AFA", "#864DFF", "#FF627E"],
  title: {
    text: "￥307882",
    subtext: "合计（万元）",
    left: "center",
    top: "43%",
    textStyle: {
      fontSize: 25,
      color: "#000"
    },
    subtextStyle: {
      fontSize: 14,
      color: "#00000099"
    },
    show: true
  },
  tooltip: {
    trigger: "item",
    position: function (point: any, params: any, dom: any, rect: any, size: any) {
      // 例如，将 tooltip 移动到鼠标的左侧或右侧，根据实际需求调整
      return [point[0] + 10, point[1]] // 修改为需要的坐标，例如向右偏移10px
    }
  },
  legend: {
    orient: "vertical",
    top: "30%",
    right: "5%",
    show: false
  },
  series: [
    {
      name: "Access From",
      type: "pie",
      radius: ["50%", "70%"],
      avoidLabelOverlap: false,
      padAngle: 2,
      itemStyle: {
        borderRadius: 1,
        borderWidth: 2
      },
      label: {
        show: false,
        position: "center"
      },
      emphasis: {
        label: {
          show: false, // 隐藏emphasis下的label 会与中间的东西重叠喔
          fontSize: 20,
          fontWeight: "bold"
        }
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 3, name: "车辆黑名单" },
        { value: 6, name: "陌生车辆" },
        { value: 2, name: "人员黑名单" },
        { value: 10, name: "陌生人人员" }
        // { value: 0, name: '危险区域' }
      ]
    }
  ]
}
