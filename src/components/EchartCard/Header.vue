<template>
  <div class="echart-card-header">
    <div class="ps-flex col-center">
      <div class="title">{{ title }}</div>
      <!-- <el-tooltip
        effect="dark"
        placement="top-start"
        content="统计每个项目的收支数据，每个类型按天单独汇总，当天凌晨更新前一天的数据。"
      >
        <img :src="IcQuestionBlack" class="w-28px h-28px" />
      </el-tooltip> -->
    </div>
    <!-- 日期选择-->

    <div class="ps-flex col-center" v-if="props.dataType === 'monthYear'">
      <div class="w-120px m-r-10px" v-if="timeType === 'month'">
        <!-- <el-select v-model="monthValue" placeholder="请选择" @change="changDate">
          <el-option v-for="(item, index) in monthList" :key="index" :label="`${item}月`" :value="item" />
        </el-select> -->
        <el-date-picker
          v-model="monthValue"
          type="month"
          @change="changDate"
          style="width: 120px"
          format="YYYY-MM"
          value-format="YYYY-MM"
          :clearable="false"
        />
      </div>
      <div class="w-120px m-r-10px" v-if="timeType === 'year'">
        <!-- <el-select v-model="yearValue" placeholder="请选择" @change="changDate">
          <el-option v-for="(item, index) in yearList" :key="index" :label="`${item}年`" :value="item" />
        </el-select> -->
        <el-date-picker
          v-model="yearValue"
          type="year"
          @change="changDate"
          style="width: 120px"
          format="YYYY"
          value-format="YYYY"
          :clearable="false"
        />
      </div>
      <el-radio-group v-model="timeType" size="small" @change="changDate">
        <el-radio-button value="month">按月</el-radio-button>
        <el-radio-button value="year">按年</el-radio-button>
      </el-radio-group>
    </div>
    <div class="ps-flex col-center" v-if="props.dataType === 'year'">
      <div class="w-100px m-r-10px">
        <!-- <el-select v-model="yearValue" placeholder="请选择" @change="changDate">
          <el-option v-for="(item, index) in yearList" :key="index" :label="`${item}年`" :value="item" />
        </el-select> -->
        <el-date-picker
          v-model="yearValue"
          type="year"
          @change="changDate"
          style="width: 120px"
          format="YYYY"
          value-format="YYYY"
          :clearable="false"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, toRefs } from "vue"
import IcQuestionBlack from "@/assets/layouts/ic_question_black.png"

const emit = defineEmits(["change", "changeYearDate"])

const props = defineProps({
  title: {
    type: String,
    required: false,
    default: ""
  },
  dataType: {
    type: String,
    required: false,
    default: "monthYear"
  }
})

const currentYear = new Date().getFullYear() + "" // 当前年
console.log("currentYear", currentYear, typeof currentYear)

const preMonth =
  new Date().getFullYear() + "-" + (new Date().getMonth() > 10 ? new Date().getMonth() : "0" + new Date().getMonth()) // 上个月
const currentMonth = new Date().getFullYear() + "-" + new Date().getMonth() + 1

const data = reactive({
  timeType: "month",
  monthValue: preMonth,
  yearValue: currentYear,
  monthList: 12,
  yearList: [2023, 2024, 2025]
})
let { timeType, monthValue, yearValue, monthList, yearList } = toRefs(data)

const changDate = () => {
  let time
  let type
  if (props.dataType === "year") {
    type = props.dataType
    time = yearValue
  } else {
    type = timeType.value === "month" ? "month" : "year"
    time = timeType.value === "month" ? monthValue : yearValue
  }
  emit("change", {
    time,
    type
  })
}
</script>

<style scoped lang="scss">
.echart-card-header {
  display: flex;
  justify-content: space-between;
  .title {
    font-size: 18px;
    font-weight: bold;
    margin-right: 5px;
  }
}
</style>
