<template>
  <div class="line-gradients">
    <div class="collect-data" v-if="props.showCollect">
      <div v-for="(item, index) in collectData" :key="index" class="collect-data-item">
        <div class="title">{{ item.title }}</div>
        <div class="fee">￥{{ item.fee }}</div>
      </div>
    </div>
    <div class="chart-wrap">
      <div class="chart-pie" :style="`height:280px;`">
        <v-chart ref="vChartRef" :option="option.value" autoresize></v-chart>
      </div>
      <div v-if="props.showLegend" class="legend-data">
        <div v-for="(item, index) in props.dataList" :key="index" class="legend-item">
          <div class="legend-item-color" :style="`background: ${props.colorList[index]}`"></div>
          <div class="ps-flex col-center">
            <div class="legend-item-name">{{ item.name }}</div>
            <div class="legend-item-value">￥{{ item.value }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch, onMounted } from "vue"
import { cloneDeep } from "lodash"
import VChart from "vue-echarts"
import { use, graphic } from "echarts/core"
import { CanvasRenderer } from "echarts/renderers"
import { PieChart } from "echarts/charts"
import { DatasetComponent, GridComponent, TooltipComponent, LegendComponent, TitleComponent } from "echarts/components"
import { PIE_ECHART_OPTION } from "./constants"

const props = defineProps({
  type: {
    type: String,
    required: false,
    default: ""
  },
  name: {
    // 饼图名字
    type: String,
    required: false,
    default: ""
  },
  subtext: {
    // 饼图中间的数
    type: String,
    required: false,
    default: ""
  },
  text: {
    // 饼图中间的数
    type: String,
    required: false,
    default: ""
  },
  colorList: {
    type: Array<string>,
    required: false,
    default: []
  },
  dataList: {
    type: Array<any>,
    required: false,
    default: []
  },
  showLegend: {
    type: Boolean,
    required: false,
    default: true
  },
  legendData: {
    type: Array<any>,
    required: false,
    default: []
  },
  showCollect: {
    type: Boolean,
    required: false,
    default: false
  },
  collectData: {
    type: Array<any>,
    required: false,
    default: []
  }
})

use([DatasetComponent, CanvasRenderer, PieChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent])

const option = reactive({
  value: cloneDeep(PIE_ECHART_OPTION)
})

// 初始化数据
const initData = async () => {
  option.value.series[0].data = props.dataList
  option.value.series[0].name = props.name
  option.value.color = props.colorList
  option.value.title.text = props.text
  option.value.title.subtext = props.subtext
}

watch(
  () => props.colorList,
  (newVal) => {
    initData()
  }
)

watch(
  () => props.dataList,
  (newVal) => {
    initData()
  }
)

onMounted(() => {
  initData()
})
</script>

<style scoped lang="scss">
.line-gradients {
  .chart-wrap {
    display: flex;
    align-items: center;
    .chart-pie {
      flex: 3;
    }
  }
  .legend-data {
    flex: 2;
    .legend-item {
      display: flex;
      align-items: center;
      margin-bottom: 30px;
    }
    .legend-item:last-child {
      margin-bottom: 0 !important;
    }
    .legend-item-color {
      width: 14px;
      height: 14px;
      border-radius: 4px;
      margin-right: 5px;
    }
    .legend-item-value {
      font-size: 24px;
      font-weight: bold;
      margin-left: 10px;
    }
  }
}
</style>
