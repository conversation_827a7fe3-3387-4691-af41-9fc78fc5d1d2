<template>
  <div class="line-gradients">
    <div class="collect-data" v-if="props.showCollect">
      <div v-for="(item, index) in collectData" :key="index" class="collect-data-item">
        <div class="title">{{ item.title }}</div>
        <div class="fee">￥{{ item.fee }}</div>
      </div>
    </div>
    <div :style="`width:100%;height:200px;`">
      <v-chart ref="vChartRef" :option="option.value" autoresize></v-chart>
    </div>
    <div v-if="props.showLegend" class="legend-data">
      <div v-for="(item, index) in props.legendData" :key="index" class="legend-item">
        <div class="legend-item-color" :style="`background: ${props.colorList[index]}`"></div>
        <div class="legend-item-name">{{ item }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch, onMounted } from "vue"
import { cloneDeep } from "lodash"
import VChart from "vue-echarts"
import { use, graphic } from "echarts/core"
import { CanvasRenderer } from "echarts/renderers"
import { LineChart } from "echarts/charts"
import { DatasetComponent, GridComponent, TooltipComponent, LegendComponent } from "echarts/components"
import { LINE_ECHART_OPTION, SERIES_ITEM } from "./constants"

const props = defineProps({
  type: {
    type: String,
    required: false,
    default: ""
  },
  colorList: {
    type: Array<string>,
    required: false,
    default: []
  },
  dataList: {
    type: Array<any>,
    required: false,
    default: []
  },
  xAxisData: {
    type: Array<any>,
    required: false,
    default: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"]
  },
  showLegend: {
    type: Boolean,
    required: false,
    default: true
  },
  legendData: {
    type: Array<any>,
    required: false,
    default: []
  },
  showCollect: {
    type: Boolean,
    required: false,
    default: true
  },
  collectData: {
    type: Array<any>,
    required: false,
    default: []
  }
})

use([DatasetComponent, CanvasRenderer, LineChart, GridComponent, TooltipComponent, LegendComponent])

const option = reactive({
  value: cloneDeep(LINE_ECHART_OPTION)
})

// 初始化数据
const initData = async () => {
  option.value.series = []
  props.dataList.forEach((dataItem: any, index: number) => {
    let seriesItem = cloneDeep(SERIES_ITEM)
    seriesItem.name = props.legendData[index]
    seriesItem.data = dataItem
    seriesItem.lineStyle.color = props.colorList[index]
    seriesItem.areaStyle.color = new graphic.LinearGradient(0, 0, 0, 1, [
      {
        offset: 0,
        color: props.colorList[index]
      },
      {
        offset: 1,
        color: "rgba(255,255,255,0)"
      }
    ])
    option.value.series.push(seriesItem)
  })
  option.value.legend.data = props.legendData
  option.value.xAxis.data = props.xAxisData
}

watch(
  () => props.colorList,
  (newVal) => {
    initData()
  }
)

watch(
  () => props.dataList,
  (newVal) => {
    initData()
  }
)

onMounted(() => {
  initData()
})
</script>

<style scoped lang="scss">
.line-gradients {
  .collect-data {
    margin: 15px 0 10px;
    display: flex;
    justify-content: space-between;
    .collect-data-item {
      background: #fff;
      padding: 15px;
      border-radius: 10px;
      margin-right: 20px;
      display: flex;
      flex: 1;
      align-items: center;
      .title {
        font-size: 18px;
      }
      .fee {
        font-size: 28px;
        font-weight: bold;
      }
    }
    .collect-data-item:last-child {
      margin-right: 0px !important;
    }
  }
  .legend-data {
    margin: 0 200px;
    display: flex;
    justify-content: space-around;
  }
  .legend-item {
    display: flex;
  }
  .legend-item-color {
    width: 14px;
    height: 14px;
    border-radius: 4px;
    margin-right: 5px;
  }
  .legend-item-name {
    line-height: 15px;
  }
}
</style>
