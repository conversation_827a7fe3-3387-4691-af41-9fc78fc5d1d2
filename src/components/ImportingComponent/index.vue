<script lang="ts" setup>
import { sleep } from "@/utils"
import { ElMessage } from "element-plus"
import { computed, ref, onBeforeUnmount } from "vue"
import { apiBackgroundFileChannelUploadFile } from "@/api/index"
import { apiBackgroundBaseTasksExportQueryPost } from "@/api/super"
import { request } from "@/utils/service"
const reDownload = ref<boolean>(false)
const isLoading = ref<boolean>(false)
const excelStatus = ref<string>("processing")
const emit = defineEmits(["showMemberImportDrawer", "showMemberImportDrawerCancel", "update:show", "update:loading"])

type TableSetting = {
  key: string
  label: string
}
interface Props {
  importType?: string
  show: boolean
  title?: string
  size?: string
  loading?: boolean
  templateUrl: string
  tableSetting?: TableSetting[]
  openExcelType?: string
  params?: any
  url?: string
}

const props = withDefaults(defineProps<Props>(), {
  importType: "excel",
  show: false,
  title: "导入",
  size: "600",
  loading: false,
  templateUrl: "",
  url: "",
  tableSetting: () => [] as TableSetting[],
  openExcelType: "",
  params: {}
})
const requestFun = (params: any, url: string) => {
  return request({
    url: url,
    method: "post",
    data: params
  })
}
const isShow = computed({
  get() {
    return props.show
  },
  set(val) {
    emit("update:show", val)
  }
})
// const isLoading = computed({
//   get() {
//     return props.loading
//   },
//   set(val) {
//     emit("update:loading", val)
//   }
// })

const tableData = ref<any>([])

const download = () => {
  if (props.templateUrl) {
    window.open(props.templateUrl)
  } else {
    ElMessage.error("请确认下载地址是否正确")
  }
}

const excelObj = ref<any>({})
const requestData = ref<any>({})
const fileData = ref<any>(null)
const timer = ref<NodeJS.Timeout | null>(null)
// const fileData = null as any
const getXlsxData = (json: any, file: any) => {
  fileData.value = file
  excelObj.value = {}
  props.tableSetting.forEach((item: any) => {
    excelObj.value[item.label] = item.key
  })
  // json.splice(0, 1)
  console.log("excelObj", excelObj.value)

  const keyObj = excelObj.value
  const result = json.map((item: any) => {
    const data: any = {}
    for (const key in item) {
      if (keyObj[key]) {
        data[keyObj[key]] = item[key]
      }
    }
    return data
  })
  console.log("result", result)
  tableData.value = []
  result.map((item: any) => {
    let flag = true
    for (const key in item) {
      console.log("item[key]", item[key])
      if (typeof item[key] !== "string") {
        if (item[key].match(/^[ ]*$/)) {
          flag = false
        }
      } else {
        if (item[key].toString().match(/^[ ]*$/)) {
          flag = false
        }
      }
    }
    if (flag) {
      tableData.value.push(item)
    }
  })
}
const uploadParams = ref<any>({
  prefix: "xlsx"
})
const uploadStartHandle = () => {
  if (tableData.value.length === 0) {
    return ElMessage.error("暂无数据，请导入数据")
  }
  uploadParams.value.key = new Date().getTime() + "-" + Math.floor(Math.random() * 10) + getSuffix(fileData.value.name)
  getFileUploadPost(fileData.value)
  // console.log(tableData.value, file.value, "mmm")
  // emit("showMemberImportDrawer", false)
}
const clickConfirm = () => {
  uploadStartHandle()
  // emit("showMemberImportDrawer", false)
}
const clickConfirmCancel = () => {
  emit("showMemberImportDrawerCancel", false)
}
const getFileUploadPost = (file: any) => {
  console.log(file, "你好你好")
  isLoading.value = true
  apiBackgroundFileChannelUploadFile({
    ...uploadParams.value,
    file: file
  }).then((res: any) => {
    if (res.code === 0) {
      if (props.url) {
        requestFun({ url: res.data.public_url }, props.url).then((res: any) => {
          if (res.code === 0) {
            requestData.value = res.data
            start()
          } else {
            ElMessage.error(res.msg)
          }
        })
      }
      console.log(1)
    } else {
      ElMessage.error(res.msg)
    }
    // isLoading.value = false
  })
}
const start = () => {
  if (timer.value) {
    clearTimeout(timer.value)
  }
  getExcelUrl(requestData.value.query_id)
  timer.value = setTimeout(() => {
    getExcelUrl(requestData.value.query_id)
  }, 5000)
}
const getExcelUrl = async (id: string) => {
  try {
    isLoading.value = true
    excelStatus.value = "processing"
    reDownload.value = false
    await sleep(2000)
    apiBackgroundBaseTasksExportQueryPost({
      query_id: id
    })
      .then((res: any) => {
        if (res.code === 0) {
          excelStatus.value = { ...res.data.status }
          if (res.data.status === "processing") {
            // 记录当前进度的时间
            // if (progress.value !== res.data.progress) {
            //   progressTime.value = Date.now()
            // }
            start()
          } else {
            // progressTime.value = 0
          }
          // progress.value = res.data.progress

          if (res.data.status === "success") {
            ElMessage.success("导入成功")
            // downLoadExcelUrl.value = res.data.url
            window.location.href = res.data.url
            // if (asyncType.value !== "asyncTypeState") {
            //   // downloadExcel()
            // }
            if (timer.value) {
              clearTimeout(timer.value)
            }
            reDownload.value = false
          } else if (res.data.status === "failure") {
            ElMessage.error("导入失败")
            if (timer.value) {
              clearTimeout(timer.value)
            }
            reDownload.value = true
            excelStatus.value = "failure"
          }
        } else {
          ElMessage.error(res.msg)
          if (timer.value) {
            clearTimeout(timer.value)
          }
          reDownload.value = true
          excelStatus.value = "failure"
        }
      })
      .finally(() => {
        isLoading.value = false
      })
  } catch (error) {
    // progress = 0
    ElMessage.error("内部服务错误")
    if (timer.value) {
      clearTimeout(timer.value)
    }
    reDownload.value = true
    excelStatus.value = "failure"
    // remainder.value = 0
  }
}

// 获取文件后缀名
const getSuffix = (filename: any) => {
  const pos = filename.lastIndexOf(".")
  let suffix = ""
  if (pos !== -1) {
    suffix = filename.substring(pos)
  }
  return suffix
}
onBeforeUnmount(() => {
  if (timer.value) {
    clearTimeout(timer.value)
  }
})
</script>

<template>
  <el-drawer v-model="isShow" :size="props.size" :close-on-click-modal="false" :show-close="false">
    <template #header>
      <span>导入成员</span>
    </template>
    <template #default>
      <div class="mb-20px" v-loading="isLoading">
        <div class="flex justify-start items-center mb-20px">
          <span class="mr-20px">导入请下载数据模板</span>
          <NewButton :type="'colorful'" @click="download">
            <template #text>
              <span style="color: #fff; font-size: 14px">下载模板</span>
            </template>
          </NewButton>
        </div>
        <div class="flex justify-start items-center mb-20px">
          <span class="color-red"
            >注意：导入的数据需要按照平台提供的模板进行！导入前，请检查导入文件的格式和内容。</span
          >
        </div>
        <div class="flex justify-start items-center mb-20px">
          <span class="mr-20px">上传文件</span>
          <parse-excel @excel="getXlsxData" />
          <!-- <NewButton :type="'colorful'" @click='getXlsxData'>
            <template #text>
              <span style="color: #fff; font-size: 14px">选择文件</span>
            </template>
          </NewButton> -->
        </div>
        <div class="flex justify-start items-center mb-20px">
          <el-table :data="tableData" :header-cell-style="{ background: '#F7F9FA' }" stripe border height="280px">
            <el-table-column
              v-for="(item, index) in props.tableSetting"
              :key="index"
              :prop="item.key"
              :label="item.label"
              align="center"
            />
            <template #empty>
              <span class="flex flex-center">暂无内容</span>
            </template>
          </el-table>
        </div>
        <div class="flex justify-start items-center">
          <NewButton @click="clickConfirmCancel" :type="'default'" class="mr-20px">
            <template #text>
              <span style="color: #bbbdbf; font-size: 14px">取消</span>
            </template>
          </NewButton>
          <NewButton @click="clickConfirm" :type="'colorful'">
            <template #text>
              <span style="color: #fff; font-size: 14px">保存</span>
            </template>
          </NewButton>
        </div>
      </div>
    </template>
  </el-drawer>
</template>

<style lang="scss" scoped></style>
