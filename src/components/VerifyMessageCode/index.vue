<template>
  <div class="verificationCode">
    <el-input
      v-model="verificationCode"
      :placeholder="placeHolder"
      clearable
      class="ps-input"
      size="large"
      style="width: 214px; margin-right: 8px"
      @input="changeCode"
    />
    <el-button :disabled="disabled" v-if="sendAuthCode" class="phone-code-btn" @click="getPhoneCode">{{
      codeTipText
    }}</el-button>
    <el-button
      v-if="!sendAuthCode"
      class="ps-plain-btn"
      style="padding: 12px 0; font-size: 12px; width: 118px; height: 40px"
      >{{ countDownText }}后重新获取</el-button
    >
  </div>
</template>

<script lang="ts" setup>
import { ref, watchEffect, onMounted, nextTick, onUnmounted, watch } from "vue"

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  sendAuthCode: {
    type: Boolean,
    default: true
  },
  counNum: {
    type: Number,
    default: 60
  },
  placeHolder: {
    type: String,
    default: "请输入验证码"
  }
})

const emit = defineEmits(["sendAuthCode", "getPhoneCode", "reset", "input"])
const countDown = ref(0)
const countDownText = ref("")
const verificationCode = ref("")
const authTimetimer = ref()
const codeTipText = ref("获取验证码")

// 获取验证码点击
const getPhoneCode = async () => {
  console.log("getPhoneCode")

  emit("getPhoneCode")
}
// 倒计时
const countDownHandle = () => {
  console.log("倒计时")
  countDown.value = props.counNum
  setCountDownText()
  authTimetimer.value = setInterval(() => {
    countDown.value--
    setCountDownText()
    if (countDown.value <= 0) {
      codeTipText.value = "重新获取"
      emit("reset") // 重置下
      countDownText.value = ""
      if (authTimetimer.value) {
        clearInterval(authTimetimer.value)
      }
    }
  }, 1000)
}
// 设置倒计时的文字
const setCountDownText = () => {
  const ONE_MINUTE = 60
  const minute = Math.floor(countDown.value / ONE_MINUTE)
  let countText = ""
  if (minute > 1) {
    // 1 分钟就不要显示这个啦，显示60秒多好
    countText = `${minute}分`
    const second = Math.floor(countDown.value % ONE_MINUTE)
    if (second > 0) {
      countText += `${second}秒`
    } else {
      countText += `钟`
    }
    countDownText.value = countText
  } else {
    countDownText.value = `${countDown.value}秒`
  }
  console.log("countDownText", countDownText.value)
}
// 点击倒计时按钮
const changeCode = () => {
  emit("input", verificationCode)
}

watch(
  () => props.sendAuthCode,
  (val) => {
    if (!val) {
      countDownHandle()
    }
  }
)

onUnmounted(() => {
  clearInterval(authTimetimer.value)
})
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.verificationCode {
  display: flex;
}

.phone-code-btn {
  width: 118px;
  height: 40px;
  color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  &:hover {
    background-color: var(--el-color-primary);
    color: var(--el-color-white);
  }

  &:focus {
    background-color: var(--el-color-primary);
    color: var(--el-color-white);
  }

  &:active {
    background-color: var(--el-color-primary);
    color: var(--el-color-white);
  }

  &.is-disabled,
  &.is-disabled:focus,
  &.is-disabled:hover {
    color: #c0c4cc;
    cursor: not-allowed;
    background-image: none;
    background-color: #fff;
    border-color: #ebeef5;
  }
}
</style>
