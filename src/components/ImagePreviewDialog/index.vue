<template>
  <el-dialog
    :title="title"
    v-model="state.visible"
    class="ps-dialog"
    width="fit-content"
    @close="close"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <div class="ps-dialog-img">
      <div class="cursor-pointer" @click="handlerLeft">
        <el-icon :size="40">
          <ArrowLeft />
        </el-icon>
      </div>
      <div class="min-w-400px">
        <el-image :src="imgs[state.index]" :lazy="true" class="ps-dialog-img-item" fit="scale-down" />
        <div class="m-auto text-center">{{ state.index + 1 + "/" + imgs.length }}</div>
      </div>
      <div class="cursor-pointer" @click="handlerRight">
        <el-icon :size="40"><ArrowRight /></el-icon>
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ElMessage } from "element-plus"
import { reactive, watch } from "vue"
import { useVModel } from "@vueuse/core"
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue"
const props = defineProps<{
  title: string
  modelValue: boolean
  imgs: string[]
  currentIndex: number
}>()
const emits = defineEmits(["update:modelValue"])
const state = reactive({
  index: 0,
  // 相当于是set 与 get
  visible: useVModel(props, "modelValue", emits)
})
watch(
  () => props.currentIndex,
  (newValue) => {
    state.index = newValue
  }
)
// 点击关闭的时候，连同小图一起关闭
function close() {
  state.visible = false
  state.index = 0
}
// 点击左边
const handlerLeft = () => {
  if (state.index === 0) {
    return ElMessage.warning("已经是第一张了")
  }
  state.index = state.index - 1
}

// 点击右边
const handlerRight = () => {
  if (state.index === props.imgs.length - 1) {
    return ElMessage.warning("已经是最后一张了")
  }
  state.index = state.index + 1
  console.log("handlerRight", state.index, props.imgs.length)
}
</script>
<style lang="scss" scoped>
.ps-dialog {
  min-width: 600px;
  max-width: 1100px;
  .ps-dialog-img {
    display: flex;
    justify-content: center;
    align-items: center;
    .ps-dialog-img-item {
      width: 800px;
      height: 600px;
    }
  }
}
</style>
