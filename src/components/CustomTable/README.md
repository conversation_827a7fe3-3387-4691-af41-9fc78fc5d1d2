## 参数介绍

#### Table 属性

| 参数      | 说明                 | 类型          | 是否必填 | 默认值 |
| --------- | -------------------- | ------------- | -------- | ------ |
| tableData | table 表格的数据     | Array<object> | 是       | —      |
| options   | 自定义配置           | object        | 否       | —      |
| columns   | 列 column 的配置数组 | Array<object> | 是       | —      |

####

```
本项目中rowStyle需默认设置为cursor:pointer，不需要可删除此默认选项
```

#### Column 配置项

| 参数                | 说明                                                                                                                                                                                                                                     | 类型                                                                  | 是否必填 | 默认值     |
| ------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------- | -------- | ---------- |
| type                | 对应列的类型。 如果设置了 selection 则显示多选框； 如果设置了 index 则显示该行的索引（从 1 开始计算）； 如果设置了 expand 则显示为一个可展开的按钮；如果设置 image，则显示图片; 如是设置 date，则显示格式化后的日期;设置了money就会除100 | selection / index / expand / image / money /date                      | 否       | —          |
| label               | 每一列的标题                                                                                                                                                                                                                             | string                                                                | 否       | —          |
| prop                | 字段名称 对应列内容的字段名                                                                                                                                                                                                              | string                                                                | 否       | —          |
| slot                | 插槽名称，自定义列的内容 作用域参数为 `{ row, $index }`                                                                                                                                                                                  | string                                                                | 否       | —          |
| width               | 对应列的宽度                                                                                                                                                                                                                             | string / number                                                       | 否       | —          |
| align               | 对齐方式                                                                                                                                                                                                                                 | left / center / right                                                 | 否       | left       |
| dateFormat          | 显示在页面中的日期格式，当 type === date 时，可更改日期显示格式                                                                                                                                                                          | ‘YYYY-MM-DD’ / ‘YYYY-MM-DD HH:mm:ss’ / ‘YYYY-MM-DD HH:mm’ / ‘YYYY-MM’ | 否       | YYYY-MM-DD |
| showOverflowTooltip | 当内容过长被隐藏时显示 tooltip                                                                                                                                                                                                           | boolean                                                               | 否       | false      |
| buttons             | 按钮组的内容                                                                                                                                                                                                                             | Array<object>                                                         | 否       | —          |
| render              | 渲染函数，渲染这一列的每一行的单元格                                                                                                                                                                                                     | (row:object, index:number) => VNodeChild                              | 否       | —          |
| headerRender        | 渲染函数，渲染列表头                                                                                                                                                                                                                     | ({ column, index }) => VNodeChild                                     | 否       | —          |
| headerSlot          | 自定义表头插槽名字                                                                                                                                                                                                                       | string                                                                | 否       | —          |
| sortable            | 对应列是否可以排序， 如果设置为 ‘custom’，则代表用户希望远程排序，需要监听 Table 的 sort-change 事件                                                                                                                                     | boolean / ‘custom’                                                    | 否       | false      |
| children            | 配置多级表头的数据集合, 具体用法可参考多级表头使用示例。                                                                                                                                                                                 | Array<object>                                                         | 否       | —          |

#### paginationConfig 配置项

| 参数        | 说明                                    | 类型     | 是否必填 | 默认值                                    |
| ----------- | --------------------------------------- | -------- | -------- | ----------------------------------------- |
| total       | 总条目数                                | number   | 是       | 0                                         |
| currentPage | 当前页数，支持 v-model 双向绑定         | number   | 是       | 1                                         |
| pageSize    | 每页显示条目个数，支持 v-model 双向绑定 | number   | 是       | 10                                        |
| pageSizes   | 每页显示个数选择器的选项设置            | number[] | 否       | [10, 20, 30, 40, 50, 100]                 |
| layout      | 组件布局，子组件名用逗号分隔            | string   | 否       | ‘total, sizes, prev, pager, next, jumper’ |
| background  | 是否为分页按钮添加背景色                | boolean  | 否       | false                                     |

#### Buttons 配置项

| 参数    | 说明                              | 类型                                        | 是否必填 | 默认值 |
| ------- | --------------------------------- | ------------------------------------------- | -------- | ------ |
| name    | 按钮显示的名称                    | string                                      | 是       | —      |
| command | 派发到 command 回调函数的指令参数 | string/number                               | 是       | —      |
| size    | 用于控制该按钮组内按钮的大小      | large / default / small                     | 是       | —      |
| type    | 用于控制该按钮组内按钮的类型      | primary / success / warning / danger / info | 否       | —      |
| color   | 按钮颜色                          | string                                      | 否       | —      |

#### Table 插槽

| 插槽名 | 说明                                               | 插槽作用域     |
| ------ | -------------------------------------------------- | -------------- |
| expand | 当列的 type 等于 expand 时，自定义展开行区域的内容 | { row, index } |

#### Table 事件

| 事件名            | 说明                                       | 回调参数                 |
| ----------------- | ------------------------------------------ | ------------------------ |
| selection-change  | 当选择项发生变化时会触发该事件             | selection                |
| row-click         | 当某一行被点击时会触发该事件               | row, column, event       |
| cell-click        | 当某个单元格被点击时会触发该事件           | row, column, cell, event |
| command           | 点击按钮组某个按钮触发的事件回调           | command, row             |
| size-change       | pageSize 改变时触发                        | pageSize                 |
| current-change    | currentPage 改变时触发                     | currentPage              |
| pagination-change | currentPage 或者 pageSize 改变时触发       | currentPage ,pageSize    |
| sort-change       | 当表格的排序条件发生变化的时候会触发该事件 | { column, prop, order }  |
