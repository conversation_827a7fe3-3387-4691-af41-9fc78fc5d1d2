<script setup lang="ts">
defineProps({
  // 百分比
  percentage: {
    type: Number,
    default: 10
  },
  // 环形进度条宽度
  strokeWidth: {
    type: Number,
    default: 10
  },
  // 环形颜色
  color: {
    type: String,
    default: "#000"
  },
  // 环形宽高
  width: {
    type: Number,
    default: 120
  },
  // 是否显示圆环中间内容
  isShowStatistic: {
    type: Boolean,
    default: true
  },
  // 显示数值
  value: {
    type: [Number, String],
    default: "10%"
  },
  // 显示提示
  tip: {
    type: String,
    default: "tip"
  }
})
</script>

<template>
  <div class="progressBar-content">
    <el-progress
      type="circle"
      :percentage="percentage"
      :stroke-width="strokeWidth"
      :color="color"
      :width="width"
      :show-text="false"
    />
    <div class="progressBar-content-text" v-if="isShowStatistic">
      <span>{{ value }}</span>
      <span>{{ tip }}</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.progressBar-content {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  &-text {
    width: 116px;
    height: 116px;
    border-radius: 50%;
    border: 2px solid #e1ffaf;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    & span:first-child {
      font-size: 32px;
      font-weight: 600;
    }
    & span:last-child {
      font-size: 14px;
      color: #808488;
    }
  }
}
</style>
