<template>
  <div class="income-situation-card">
    <div class="income-situation-card-box">
      <span class="income-situation-card-box-text">{{ text }}</span>
      <el-statistic
        :precision="2"
        class="income-situation-card-box-value"
        :value="amountValue"
        :value-style="valueStyle"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  text: {
    type: String,
    default: "tip"
  },
  amountValue: {
    type: Number,
    default: 1000
  }
})
const valueStyle = {
  fontSize: "28px",
  fontWeight: "600"
}
</script>

<style lang="scss" scoped>
* {
  // outline: 1px solid red;
}
.income-situation-card {
  background-color: #fff;
  width: 18%;
  height: 120px;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  justify-content: start;
  align-items: center;
  &-box {
    &-text {
      font-size: 12px;
      color: #1e2224;
    }
    &-value {
      margin-top: 10px;
    }
  }
}
</style>
