<template>
  <div class="evaluate-card">
    <div class="evaluate-card-content">
      <div class="evaluate-card-content-top">
        <div class="evaluate-card-content-top-rate">
          <span>评分：{{ props.rate.toFixed(1) }}</span>
          <el-rate v-model="rateValue" disabled size="default" />
        </div>
        <div class="evaluate-card-content-top-date">
          <span>{{ evaluateTime }}</span>
        </div>
      </div>
      <div class="evaluate-card-content-tag">
        <div class="evaluate-card-content-tag-item" v-for="(item, index) in props.tag" :key="index">
          {{ item }}
        </div>
      </div>
      <div class="evaluate-card-content-text">
        {{ props.text }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs"
import { computed } from "vue"

const props = defineProps({
  rate: {
    type: Number,
    default: 0
  },
  time: {
    type: Number,
    default: 1000000000000
  },
  tag: {
    type: Array,
    default: () => {
      return []
    }
  },
  text: {
    type: String,
    default: ""
  }
})
const rateValue = props.rate
const evaluateTime = computed(() => {
  return dayjs(props.time).format("YYYY-MM-DD HH:mm:ss")
})
</script>

<style lang="scss" scoped>
* {
  // outline: 1px solid red;
}
.evaluate-card {
  background-color: #f7f9fa;
  border-radius: 8px;
  padding: 6px 10px;
  margin-bottom: 10px;
  &-content {
    display: flex;
    flex-direction: column;
    align-items: start;
    &-top {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &-rate {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        font-weight: 600;
        & span {
          margin-right: 5px;
        }
      }
      &-date {
        font-size: 12px;
        color: #808488;
      }
    }
    &-tag {
      display: flex;
      &-item {
        font-size: 12px;
        padding: 4px 8px;
        color: #46d277;
        border: 1px solid #46d277;
        border-radius: 4px;
        background-color: #e6f6ed;
        margin-right: 6px;
        margin-bottom: 6px;
      }
    }
    &-text {
      font-size: 12px;
      max-width: 470px;
      color: #808488;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
