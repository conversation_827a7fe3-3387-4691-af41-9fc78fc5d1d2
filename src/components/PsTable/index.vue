<template>
  <div>
    <el-table
      :data="tableData"
      style="width: 100%"
      ref="tableRef"
      stripe
      header-row-class-name="ps-table-header-row"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
      @cell-click="handleCellClick"
      :span-method="isCustom ? objectSpanMethod : customSpanMethod !== undefined ? customSpanMethod : undefined"
      row-key="id"
      :border="isBorder"
      @sort-change="handleSortChange"
      :max-height="maxHeight"
      :height="maxHeight"
      :default-expand-all="defaultExpandAll"
      :load="loadChildrenData"
      :tree-props="treeProps"
      :lazy="lazyLoad"
      v-loading="tableLoading"
      :tooltip-effect="tooltipEffect"
      @expand-change="expandChange"
      :key="tableKey"
    >
      <template v-for="(slot, index) in Object.keys($slots as Record<string, any>)" #[slot] :key="index">
        <slot :name="slot" />
      </template>
    </el-table>
    <!-- 分页器 -->
    <div v-if="showPagination" class="ps-pagination">
      <el-pagination
        v-bind="paginationConfig"
        @size-change="pageSizeChange"
        @current-change="currentPageChange"
        :page-sizes="paginationConfig.pageSizes"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted, PropType, watch } from "vue"
import type { TableColumnCtx } from "element-plus"
import { ElTable } from "element-plus" // 引入 ElTable 组件
import { nextTick } from "process"

export type SortParams<T> = {
  column: TableColumnCtx<T | any>
  prop: string
  order: Table.Order
}
type SpanMethod = (data: {
  row: any
  rowIndex: number
  column: TableColumnCtx<any>
  columnIndex: number
}) => { rowspan: number; colspan: number } | undefined

// 添加defineOptions以确保组件名称被正确识别
defineOptions({
  name: "PsTable"
})

const props = defineProps({
  tableData: {
    type: Array<any>,
    default: () => []
  },
  showPagination: {
    type: Boolean,
    default: false
  },
  pageConfig: {
    type: Object,
    default: () => {}
  },
  isBorder: {
    type: Boolean,
    default: true
  },
  maxHeight: {
    type: Number,
    default: null
  },
  treeProps: {
    type: Object,
    default: () => {
      return {
        children: "children",
        hasChildren: "hasChildren"
      }
    }
  },
  lazyLoad: {
    type: Boolean,
    default: true
  },
  defaultExpandAll: {
    type: Boolean,
    default: false
  },
  isCustom: {
    type: Boolean,
    default: false
  },
  tooltipEffect: {
    type: String,
    default: "dark"
  },
  customSpanMethod: {
    type: Function as PropType<SpanMethod>,
    default: () => undefined
  }
})
interface nodeTree {
  id: string
  isExpanded?: boolean
  children?: nodeTree[]
  hasChildren?: boolean
}

const tableRef = ref<InstanceType<typeof ElTable> | null>(null)
const tableKey = ref(0)
// 合并分页配置
const paginationConfig = computed(() => {
  const config = {
    total: 0,
    currentPage: 1,
    pageSize: 10,
    pageSizes: [10, 20, 30, 40, 50],
    layout: "total, prev, pager, next,sizes, jumper"
  }
  return Object.assign(config, props.pageConfig)
})
const tableLoading = ref(false)
const emit = defineEmits([
  "selection-change", // 当选择项发生变化时会触发该事件
  "row-click", // 当某一行被点击时会触发该事件
  "cell-click", // 当某个单元格被点击时会触发该事件
  "command", // 按钮组事件
  "size-change", // pageSize事件
  "current-change", // currentPage按钮组事件
  "pagination-change", // currentPage或者pageSize改变触发
  "sort-change" // 列排序发生改变触发
])
// 自定义索引
const indexMethod = (index: number) => {
  const tabIndex = index + (paginationConfig.value.currentPage - 1) * paginationConfig.value.pageSize + 1
  return tabIndex
}
// 切换pageSize
const pageSizeChange = (pageSize: number) => {
  emit("size-change", pageSize)
  emit("pagination-change", 1, pageSize)
}
// 切换currentPage
const currentPageChange = (currentPage: number) => {
  emit("current-change", currentPage)
  emit("pagination-change", currentPage, paginationConfig.value.pageSize)
}
// 按钮组事件
const handleAction = (command: Table.Command, row: any, index: number) => {
  emit("command", command, row, index)
}
// 多选事件
const handleSelectionChange = (val: any) => {
  emit("selection-change", val)
}
// 当某一行被点击时会触发该事件
const handleRowClick = (row: any, column: any, event: MouseEvent) => {
  emit("row-click", row, column, event)
}
// 当某一行触发展开点击的时候
const expandChange = (row: any) => {
  console.log("toggleRowExpansion", row)
  row.isExpanded = !row.isExpanded
}
// 当某个单元格被点击时会触发该事件
const handleCellClick = (row: any, column: any, cell: any, event: MouseEvent) => {
  console.log("handleCellClick")

  emit("cell-click", row, column, cell, event)
}
/**
 *  当表格的排序条件发生变化的时候会触发该事件
 * 在列中设置 sortable 属性即可实现以该列为基准的排序， 接受一个 Boolean，默认为 false。
 * 可以通过 Table 的 default-sort 属性设置默认的排序列和排序顺序。
 * 如果需要后端排序，需将 sortable 设置为 custom，同时在 Table 上监听 sort-change 事件，
 * 在事件回调中可以获取当前排序的字段名和排序顺序，从而向接口请求排序后的表格数据。
 */
const handleSortChange = ({ column, prop, order }: SortParams<any>) => {
  emit("sort-change", { column, prop, order })
}
// 获取当前页
const getCurrentPage = () => {
  return paginationConfig.value.currentPage
}
// 获取pageSize
const getPageSize = () => {
  return paginationConfig.value.pageSize
}
// 设置已选的行
const setSelection = (ids: Array<any>) => {
  if (tableRef.value && ids.length > 0) {
    setTimeout(() => {
      tableLoading.value = true
      console.log("id", props.tableData, ids)
      props.tableData.forEach((item) => {
        const id = item.id || ""
        if (ids.includes(id) && tableRef.value) {
          console.log("有id", id)
          // @ts-ignore
          tableRef.value.toggleRowSelection(item)
        }
      })
      tableLoading.value = false
    }, 300)
  }
}

// 异步加载子节点数据，防止卡顿
const loadChildrenData = (tree: nodeTree, treeNode: unknown, resolve: (data: nodeTree[]) => void) => {
  console.log("loadChildrenData", tree, treeNode)
  setTimeout(() => {
    if (tree.children && tree.children.length > 0) {
      resolve(tree.children)
      return
    }
    const childrenItem = props.tableData.find((item) => item.id === tree.id)
    if (childrenItem && childrenItem.children && childrenItem.children.length > 0) {
      resolve(childrenItem.children)
    } else {
      resolve([])
    }
  }, 100)
}

export interface SpanMethodProps {
  row: any
  column: TableColumnCtx<any>
  rowIndex: number
  columnIndex: number
}

const objectSpanMethod = ({ row, column, rowIndex, columnIndex }: SpanMethodProps) => {
  // 循环
  const key = column.property
  const currentRowValue = row[key]

  if (rowIndex === 0) {
    return { rowspan: 1, colspan: 1 }
  }

  const prevRowValue = props.tableData[rowIndex - 1][key]

  if (currentRowValue === prevRowValue) {
    return { rowspan: 0, colspan: 0 }
  }

  let count = 1
  for (let i = rowIndex + 1; i < props.tableData.length; i++) {
    if (props.tableData[i][key] === currentRowValue) {
      count++
    } else {
      break
    }
  }

  return { rowspan: count, colspan: 1 }
}

onMounted(() => {})
watch(
  () => props.tableData,
  (newVal) => {
    nextTick(() => {
      // 强制重新渲染表格
      if (tableRef.value) {
        tableRef.value.doLayout()
        tableKey.value = tableKey.value + 1
      }
    })
  },
  { deep: true } // 深度监听 tableData 的变化
)

// 暴露给父组件参数和方法，如果外部需要更多的参数或者方法，都可以从这里暴露出去。
defineExpose({ element: tableRef, getCurrentPage, getPageSize, setSelection })
</script>
