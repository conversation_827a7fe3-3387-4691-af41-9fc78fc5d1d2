<template>
  <div>
    <el-drawer
      v-model="dialogVisible"
      :title="dialogTitle"
      size="618px"
      direction="rtl"
      @close="closeDialog"
      class="ps-drawer"
      :close-on-click-modal="false"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="ruleFormRef"
        label-width="auto"
        class="from-container"
        label-position="top"
        v-loading="loading"
      >
        <el-form-item label="请求类型" prop="type" class="m-t-10px">
          <el-select v-model="formData.type" placeholder="请选择请求类型" style="width: 100%" @change="handlerChange">
            <el-option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="请求地址" prop="url" class="m-t-10px">
          <el-input v-model="formData.url" placeholder="请输入" maxlength="100" show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDialog">取消</el-button>
          <el-button type="primary" @click="confirmDialog">确定</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, reactive, onMounted } from "vue"
import to from "await-to-js"
import { FormInstance } from "element-plus"
import { useAppStore } from "@/store/modules/app"

const appStore = useAppStore()
const prop = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dialogData: {
    type: Object,
    default: () => {
      return {}
    }
  },
  dialogTitle: {
    type: String,
    default: "编辑"
  },
  direction: {
    type: String,
    default: "rtl"
  },
  dialogType: {
    type: String,
    default: "add"
  }
})
// 类型
const typeList = ref([
  {
    label: "测试",
    value: "http://*************:80"
  },
  {
    label: "自定义",
    value: "自定义"
  }
])
// 表单ref
const ruleFormRef = ref<FormInstance>()
// 表单loading
const loading = ref(false)
// 表单数据
const formData = reactive<any>({
  type: "",
  url: ""
})
// 表单校验
const rules = reactive<any>({
  type: [{ required: false, message: "请选择类型", trigger: "change" }],
  url: [{ required: false, message: "请输入url", trigger: "blur" }]
})
// 是否显示弹窗
const dialogVisible = ref(false)
// 弹窗事件
const emit = defineEmits([
  "closeDialog", // 弹窗关闭
  "confirmDialog" // 弹窗确认
])
// 关闭弹窗
const closeDialog = () => {
  console.log("closeDialog 111111")
  if (ruleFormRef.value) {
    ruleFormRef.value.resetFields()
  }
  formData.areaLevelList = []
  dialogVisible.value = false
  emit("closeDialog")
}
// 取消
const cancelDialog = () => {
  closeDialog()
}
// 确认弹窗
const confirmDialog = async () => {
  console.log("confirmDialog")

  if (ruleFormRef.value) {
    ruleFormRef.value.validate(async (valid) => {
      if (valid) {
        appStore.setUrl(formData.url)
        emit("confirmDialog", formData)
        window.location.reload()
      }
    })
  }
}
// 类型改变
const handlerChange = (val: any) => {
  console.log("handlerChange", val)
  formData.url = val === "自定义" ? "" : val
}

onMounted(() => {})
watch([() => prop.visible, () => prop.dialogData], ([newVisible, newData]) => {
  dialogVisible.value = newVisible
  if (newVisible) {
    console.log("newVisible", appStore.url, newData)

    formData.type = appStore.url === "http://*************:80" ? appStore.url : "自定义"
    formData.url = appStore.url
  }
})
</script>
