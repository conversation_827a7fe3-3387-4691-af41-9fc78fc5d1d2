import { defineAsyncComponent, AsyncComponentLoader } from "vue"

// 获取所有组件，该方法返回一个对象
const components = import.meta.glob("./**/index.vue")

export function installComponents(app: any) {
  // 遍历对象并注册异步组件
  for (const [key, value] of Object.entries(components)) {
    const name = key.split("/")[1]
    console.log("name", name)
    app.component(name, defineAsyncComponent(value as AsyncComponentLoader))
  }
}
