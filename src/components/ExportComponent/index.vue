<script lang="ts" setup>
import { onBeforeUnmount, onMounted, ref, watch, computed } from "vue"
import { sleep, download } from "@/utils"
import { apiBackgroundBaseTasksExportQueryPost } from "@/api/super"
import { ElMessage } from "element-plus"

const props = defineProps({
  queryType: {
    type: String,
    required: false,
    default: ""
  },
  queryId: {
    type: String,
    required: true,
    default: ""
  }
})
const emit = defineEmits(["update:queryId"])

const downLoadExcelUrl = ref<string>("")
const excelStatus = ref<string>("processing")
const timer = ref<NodeJS.Timeout | null>(null)
const reDownload = ref<boolean>(false)
const asyncType = ref<string>("")
const progress = ref<number>(0)
const startTime = ref<number>(0)
const progressTime = ref<number>(0)
const remainder = ref<number>(0)

watch(progressTime, (newVal, oldVal) => {
  if (!newVal) {
    remainder.value = 0
    return
  }
  if (progress.value) {
    // 计算下当前进度下的平均时间
    const averageTime = (newVal - startTime.value) / progress.value
    const diff = (100 - progress.value) * averageTime + 5000
    remainder.value = diff // + parseInt(diff / 5000) * 5000 + 5000 // 加上当前每次请求时的等待时间
  }
})

const query_id = computed({
  get() {
    return props.queryId
  },
  set(value: string | undefined) {
    emit("update:queryId", value)
  }
})

const startQueryHandle = (restart = false) => {
  // restart 重试需要看是否需要重置当前的开始时间
  // 如果当前的进度也是0的时候需要重置开始时间了
  if (restart && progress.value === 0) {
    startTime.value = Date.now()
  }
  getExcelUrl(props.queryId)
  start()
}
watch(
  () => props.queryId,
  (newVal, oldVal) => {
    if (newVal) {
      startQueryHandle()
    }
  }
)
const start = () => {
  if (timer.value) {
    clearTimeout(timer.value)
  }
  timer.value = setTimeout(() => {
    getExcelUrl(props.queryId)
  }, 5000)
}
const getExcelUrl = async (id: string) => {
  try {
    excelStatus.value = "processing"
    reDownload.value = false
    await sleep(2000)
    apiBackgroundBaseTasksExportQueryPost({
      query_id: id
    }).then((res: any) => {
      if (res.code === 0) {
        excelStatus.value = { ...res.data.status }
        if (res.data.status === "processing") {
          // 记录当前进度的时间
          if (progress.value !== res.data.progress) {
            progressTime.value = Date.now()
          }
          start()
        } else {
          progressTime.value = 0
        }
        progress.value = res.data.progress

        if (res.data.status === "success") {
          downLoadExcelUrl.value = res.data.url
          // window.location.href = res.url;
          if (asyncType.value !== "asyncTypeState") {
            downloadExcel()
          }
          if (timer.value) {
            clearTimeout(timer.value)
          }
          reDownload.value = false
        } else if (res.data.status === "failure") {
          if (timer.value) {
            clearTimeout(timer.value)
          }
          reDownload.value = true
          excelStatus.value = "failure"
        }
      } else {
        ElMessage.error(res.msg)
        if (timer.value) {
          clearTimeout(timer.value)
        }
        reDownload.value = true
        excelStatus.value = "failure"
      }
    })
  } catch (error) {
    // progress = 0
    ElMessage.error("内部服务错误")
    if (timer.value) {
      clearTimeout(timer.value)
    }
    reDownload.value = true
    excelStatus.value = "failure"
    remainder.value = 0
  }
}

const downloadExcel = () => {
  //   let spliturl = downLoadExcelUrl.split('/')
  //   let filsename = spliturl[spliturl.length - 1]
  // FileSaver.saveAs(downLoadExcelUrl, filsename)
  download(downLoadExcelUrl.value)
  emit("update:queryId", "")
}
const downloadExcela = () => {
  emit("update:queryId", "")
}

onMounted(() => {
  startTime.value = Date.now()
  progress.value = 0
})

onBeforeUnmount(() => {
  if (timer.value) {
    clearTimeout(timer.value)
  }
})
</script>

<template>
  <div class="export-excel">
    <div class="await-tip-wrapper">
      <div v-if="asyncType == 'asyncTypeState' && excelStatus == 'success'" class="sync-box">
        <i class="el-icon-check icon-success" />
        <div class="tip-text">处理已完成</div>
      </div>
      <div v-else>
        <div class="tip-text" v-if="excelStatus == 'processing'">
          正在生成中，请勿关闭和刷新本页面。
          <div class="percentage-box">
            <el-progress :percentage="progress" />
          </div>
        </div>
        <div v-if="remainder" class="remainder">
          预估剩余时间:
          <count-down v-model="remainder" />
        </div>
        <div class="tip-text" v-if="excelStatus == 'failure' || reDownload">生成失败，请点击下面按钮重试。</div>
        <div class="tip-text" v-if="excelStatus == 'success'">已生成，请点击下面按钮进行下载。</div>
        <div class="wran-text" v-if="excelStatus == 'processing'">数据量比较多，请耐心等待</div>
        <div class="wran-text" v-if="excelStatus == 'failure' || reDownload" />
        <div class="wran-text" v-if="excelStatus == 'success'">
          如果没有自动下载或者取消下载后，请点击下面按钮重新下载。
        </div>
      </div>
      <el-button size="small" @click="downloadExcela"> 关闭 </el-button>
      <el-button type="primary" size="small" :loading="excelStatus == 'processing'" v-if="excelStatus == 'processing'">
        正在生成
      </el-button>
      <el-button
        type="primary"
        size="small"
        v-if="excelStatus == 'failure' || (reDownload && asyncType != 'asyncTypeState')"
        @click="startQueryHandle(true)"
      >
        重新生成
      </el-button>
      <el-button
        type="primary"
        size="small"
        v-if="excelStatus == 'success' && asyncType != 'asyncTypeState'"
        @click="downloadExcel"
      >
        点击下载
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.export-excel {
  position: relative;
  width: 100%;
  height: 100%;
  .await-tip-wrapper {
    position: absolute;
    top: 20%;
    left: 0;
    right: 0;
    // bottom: 0;
    margin: 0 auto;
    width: 360px;
    // height: 170px;
    padding-bottom: 20px;
    border: 2px solid #27adf4;
    text-align: center;
    background-color: #fff;
    .tip-text {
      padding: 20px 0;
      color: #27adf4;
      font-size: 16px;
    }
    .wran-text {
      height: 20px;
      color: #f56c6c;
      font-size: 12px;
      padding-bottom: 20px;
    }
    .remainder {
      margin-top: -10px;
      margin-bottom: 10px;
    }
  }
  .sync-box {
    padding-top: 30px;
  }
  .icon-success {
    font-size: 50px;
    color: #27adf4;
  }
  .percentage-box {
    width: 82%;
    margin: 20px auto 0;
  }
}
</style>
