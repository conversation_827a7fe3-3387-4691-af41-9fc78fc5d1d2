<template>
  <div class="container-wrapper">
    <el-drawer
      v-model="dialogVisible"
      :title="dialogTitle"
      size="928px"
      :direction="direction"
      @close="closeDialog"
      class="ps-drawer"
      :close-on-click-modal="false"
    >
      <import-excel-data
        ref="importExcelDataRef"
        :import-type="importType"
        :template-url="templateUrl"
        :header-len="headerLen"
        :import-api="importApi"
        :is-delete-top-tips="isDeleteTopTips"
        @close="handlerCloseImportExcelData"
        :del-example-len="delExampleLen"
        :btn-confirm-txt="btnConfirmTxt"
        :tip-step-txt1="tipStepTxt1"
        :tip-step-txt2="tipStepTxt2"
        :is-return-json="isReturnJson"
        @confirm="handlerConfirm"
      />
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, onMounted, PropType } from "vue"
import ImportExcelData from "../ImportExcelData/index.vue"

type DirectionDialog = "rtl" | "ltr" | "ttb" | "btt"

const prop = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dialogData: {
    type: Object,
    default: () => {
      return {}
    }
  },
  dialogTitle: {
    type: String,
    default: "导入"
  },
  direction: {
    type: String as PropType<DirectionDialog>,
    default: "rtl"
  },
  dialogType: {
    type: String,
    default: "import"
  },
  templateUrl: {
    // 下载模板链接
    type: String,
    default: ""
  },
  importType: {
    // 导入类型
    type: String,
    default: ""
  },
  headerLen: {
    // 表头长度
    type: Number,
    default: 0
  },
  isDeleteTopTips: {
    // 是否删除第一行提示
    type: Boolean,
    default: false
  },
  delExampleLen: {
    type: Number,
    default: 1
  },
  importApi: Function,
  btnConfirmTxt: {
    type: String,
    default: "保存"
  },
  tipStepTxt1: {
    type: String,
    default: "下载模板数据"
  },
  tipStepTxt2: {
    type: String,
    default: "导入文件"
  },
  isReturnJson: {
    type: Boolean,
    default: false
  },
  id: {
    type: Number,
    default: 0
  }
})
// 是否显示弹窗
const dialogVisible = ref(false)
// 弹窗ref
const importExcelDataRef = ref()

// 弹窗事件
const emit = defineEmits([
  "closeDialog", // 弹窗关闭
  "confirmDialog", // 弹窗确认
  "choosePerson" // 选择人员
])
// 关闭弹窗
const closeDialog = () => {
  console.log("closeDialog 111111")

  dialogVisible.value = false
  emit("closeDialog")
}
// 确认导入数据
const handlerConfirm = (json: any) => {
  console.log("handlerConfirm", json)
  emit("confirmDialog", json, prop.id)
}
// 关闭导入数据
const handlerCloseImportExcelData = () => {
  console.log("handlerCloseImportExcelData")
  emit("closeDialog", true)
}
const clearTableData = () => {
  // 清空表格数据
  // tableData.value = []
  if (importExcelDataRef.value) {
    importExcelDataRef.value.clearTable()
  }
}

defineExpose({
  clearTableData
})
onMounted(() => {})
watch([() => prop.visible, () => prop.dialogData], ([newVisible, newData]) => {
  console.log("importExcelDilaog", newVisible, newData)
  dialogVisible.value = newVisible
  if (newData) {
    // formData.signTime = newData.create_time;
  }
  console.log("AddCardReplacementDialog", newVisible)
})
</script>
