<template>
  <el-dialog
    :model-value="isshow"
    title="咨询记录"
    width="30%"
    class="record-message-dialog ps-dialog"
    center
    align-center
    :lock-scroll="false"
    @close="handleClose"
  >
    <!-- 聊天内容 -->
    <div class="consult-chat-main">
      <div class="consult-chat-main-content">
        <div class="consult-chat-main-content-time">咨询时间： {{ formatTime }}</div>
        <div class="consult-chat-main-content-list" ref="messageList" @scroll="handleScroll" v-loading="isLoading">
          <div :class="['consult-chat-main-content-list-item']" v-for="(item, index) in chatData" :key="index">
            <!-- 用户 -->
            <div
              :class="['consult-chat-main-content-list-item-box', 'gtl']"
              v-if="
                item.direction === 'user_to_nutritionist' &&
                (item.message_type === 'txt' || item.message_type === 'image')
              "
            >
              <div :class="['consult-chat-main-content-list-item-box-img', 'f-l']">
                <el-avatar :size="42" :src="item.headimgurl ? item.headimgurl : ''" />
              </div>
              <div
                :class="['consult-chat-main-content-list-item-box-textarea', 'f-l']"
                v-if="item.message_type === 'txt'"
              >
                <div :class="['consult-chat-main-content-list-item-box-textarea-time', 'f-l']">
                  {{ item.create_time }}
                </div>
                <!-- item.type === 'left' ? 'bg-grey' : 'bg-green' -->
                <div :class="['consult-chat-main-content-list-item-box-textarea-text', 'bg-grey']">
                  {{ item.content }}
                </div>
              </div>
              <div v-else-if="item.message_type === 'image'" class="f-l">
                <el-image style="width: 150px; height: 150px" :src="item.content" />
              </div>
            </div>
            <!-- 营养师 -->
            <div
              :class="['consult-chat-main-content-list-item-box', 'gtr']"
              v-if="
                item.direction === 'nutritionist_to_user' &&
                (item.message_type === 'txt' || item.message_type === 'image')
              "
            >
              <div :class="['consult-chat-main-content-list-item-box-img', 'f-r']">
                <el-avatar :size="42" :src="item.images_url.length ? item.images_url[0] : ''" />
              </div>
              <div
                :class="['consult-chat-main-content-list-item-box-textarea', 'f-r']"
                v-if="item.message_type === 'txt'"
              >
                <div :class="['consult-chat-main-content-list-item-box-textarea-time', 'f-r']">
                  {{ item.create_time }}
                </div>
                <div :class="['consult-chat-main-content-list-item-box-textarea-text', 'bg-green']">
                  {{ item.content }}
                </div>
              </div>
              <div v-else-if="item.message_type === 'image'" class="f-r">
                <el-image style="width: 150px; height: 150px" :preview-src-list="[item.content]" :src="item.content" />
              </div>
            </div>
            <!-- 加时间 -->
            <div
              class="card-wrapper"
              v-if="item.message_type === 'sys_n_popup' && item.message_popup_type === 'add_time'"
            >
              <el-card style="max-width: 480px">
                <div class="card-content">
                  <div class="card-wrapper-title">加时申请</div>
                  <div>客户向您发起了咨询加时10分钟，是否同意加时。</div>
                  <div class="card-btn">
                    <el-button type="info" :disabled="true" plain>拒绝</el-button>
                    <el-button type="primary" plain :disabled="true">确定</el-button>
                  </div>
                </div>
              </el-card>
            </div>
            <!-- 系统推送 -->
            <div class="system-news-wrapper" v-if="item.message_type === 'sys_n_txt'">
              <div class="system-news">{{ item.content }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, toRefs, ref, onBeforeMount, nextTick } from "vue"
import { getApiSessionMessageList } from "@/api/healthy"
import { formatDateTimeByType } from "@/utils/index"
const emit = defineEmits(["clickClose"])
const props = defineProps({
  //子组件接收父组件传递过来的值
  isshow: {
    type: Boolean,
    default: true
  },
  item: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const data = reactive({
  isLoading: false, // 加载数据
  iconLoading: false, // 控制滚动的
  currentPage: 1, // 第几页
  pageSize: 20, // 每页数量
  totalCount: 0, // 总条数
  scrollHeight: 0,
  formatTime: "2022-11-11"
})
const { currentPage, pageSize, totalCount, isLoading, iconLoading, scrollHeight, formatTime } = toRefs(data)
const messageList = ref<any>(null)

// 聊天数据
const chatData = ref([] as any)
const handleClose = () => {
  emit("clickClose")
}
// const clickDetermine = () => {
//   emit('determine', 'define')
// }
const getScrollTop = () => {
  let scrollTop = 0
  let bodyScrollTop = 0
  let documentScrollTop = 0
  if (document.body) {
    bodyScrollTop = document.body.scrollTop
  }
  if (document.documentElement) {
    documentScrollTop = messageList.value.scrollTop
  }
  scrollTop = bodyScrollTop - documentScrollTop > 0 ? bodyScrollTop : documentScrollTop
  return scrollTop
}
const handleScroll = () => {
  const getScrollTopData = getScrollTop()
  if (getScrollTopData === 0 && !iconLoading.value && chatData.value.length < totalCount.value) {
    //   // 到达顶部执行
    iconLoading.value = true
    // 记录高度
    scrollHeight.value = messageList.value.scrollHeight
    getSessionMessageList().then(() => {
      nextTick(() => {
        messageList.value.scrollTo({ top: scrollHeight.value })
        messageList.value.scrollTop = messageList.value.scrollHeight - scrollHeight.value
      })
    })
  }
}
// 获取历史
const getSessionMessageList = () => {
  return new Promise((resove, reject) => {
    try {
      isLoading.value = true
      getApiSessionMessageList({
        page: currentPage.value,
        page_size: pageSize.value,
        consult_session_id: props.item.id,
        message_type: ["txt", "image", "sys_n_txt", "sys_n_popup"]
      })
        .then((res: any) => {
          if (res.code === 0) {
            resove(res)
            const arr: any[] = []
            res.data.results.forEach((item: any) => {
              arr.unshift(item)
            })
            currentPage.value++
            chatData.value = [...arr, ...chatData.value]
            totalCount.value = res.data.count
            iconLoading.value = false
            isLoading.value = false
          } else {
            isLoading.value = false
          }
        })
        .finally(() => {})
    } catch (error) {
      reject(error)
    }
  })
}
onBeforeMount(async () => {
  getSessionMessageList().then(() => {
    // 首次渲染后获取scrollHeight并滑动到底部。
    nextTick(() => {
      scrollHeight.value = messageList.value.scrollHeight
      messageList.value.scrollTo({ top: scrollHeight.value })
    })
  })
  formatTime.value = formatDateTimeByType(new Date(props.item.create_time), "YYYY-MM-DD")
})
</script>

<style lang="scss" scope>
.record-message-dialog {
  .consult-chat-main {
    display: flex;
    height: 100%;

    &-content {
      width: 100%;
      // border-right: 1px solid #e6e8eb;

      &-time {
        background-color: #787a7c;
        border-radius: 80px;
        width: 200px;
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0px auto 10px auto;
        color: #fff;
      }

      &-list {
        min-height: 507px;
        max-height: 507px;
        overflow-y: auto;

        &-item {
          width: 100%;
          margin-bottom: 24px;
          display: flex;

          &-box {
            width: 100%;
            padding: 10px;

            .f-l {
              margin-right: 10px;
              float: left;
            }

            .f-r {
              float: right;
              align-items: flex-end;
              margin-right: 5px;
            }

            &-img {
              margin-right: 12px;
            }

            &-textarea {
              max-width: 220px;
              font-size: 14px;
              display: flex;
              flex-direction: column;

              &-time {
                margin-bottom: 8px;
                color: #808488;
                font-size: 12px;
                display: flex;
              }

              &-text {
                width: fit-content;
                border-radius: 8px;
                padding: 5px 10px;
                word-break: break-all;
                // background-color: red !important;
              }

              .bg-grey {
                background-color: #f7f9fa;
                color: #1e2224;
              }

              .bg-green {
                background-color: #0dc195;
                color: #fff;
              }
            }
          }
        }
      }

      .gtl {
        justify-content: flex-start;
      }

      .gtr {
        justify-content: flex-end;
      }

      .card-wrapper {
        width: 100%;
        display: flex;
        justify-content: center;

        .card-content {
          width: 300px;
          font-size: 15px;

          .card-wrapper-title {
            text-align: center;
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 10px;
          }

          .card-btn {
            margin-top: 10px;
            text-align: right;
          }
        }
      }

      .system-news-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;

        .system-news {
          padding: 5px 10px;
          color: #5a5a5a;
          background-color: #e5e7e9;
          border-radius: 3px;
        }
      }
    }
  }
}
</style>
