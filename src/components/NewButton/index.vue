<template>
  <div :class="['button', props.type === 'colorful' ? 'colorful' : 'default']">
    <div class="button-content">
      <slot name="icon" />
      <slot name="text" />
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps(["type"])
</script>

<style lang="scss" scoped>
.button {
  cursor: pointer;
  width: 80px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  // margin: 0px 5px;
  &-content {
    width: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.colorful {
  background-color: #0dc195;
  &:hover {
    background-color: #00b388;
  }
}
.default {
  border: 1px solid #dcdfe6;
  background-color: transparent;
  &:hover {
    background-color: #dfe3e8;
  }
}
</style>
