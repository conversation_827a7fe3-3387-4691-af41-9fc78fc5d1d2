<template>
  <div class="ps-dialog-doublefactor">
    <el-dialog
      v-model="isShowMainDialog"
      custom-class="ps-dialog"
      :title="title"
      :width="width"
      :show-close="false"
      @closed="handleClose"
      :close-on-click-modal="false"
    >
      <div class="content" v-loading="isLoading">
        <el-form :model="checkFormData" :rules="rules" ref="doubleFormRef" class="form" label-width="0">
          <el-form-item prop="phone" class="phone"> 手机号: {{ userInfo.mobile }} </el-form-item>
          <el-form-item prop="smsCode" class="phone-code">
            <verify-message-code
              :sendAuthCode="sendAuthCode"
              :disabled="sendCodeDisabled"
              @reset="resetHandle"
              @getPhoneCode="getPhoneCode"
              v-model="checkFormData.smsCode"
              @input="inputCodeChange"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button :disabled="isLoading" type="primary" @click="clickConfirmHandle">{{ confirmText }}</el-button>
          <el-button :disabled="isLoading" plain class="m-l-10px" @click="clickCancleHandle">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 登录过期弹窗，过期弹窗 -->
    <el-dialog width="450px" :title="messageTitle" :show-close="false" v-model="showDialog">
      <span>{{ messageContent }}</span>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeTimeDialogHandle('close')">取消</el-button>
          <el-button type="primary" @click="closeTimeDialogHandle('2')">
            {{ massageConfirmText ? massageConfirmText : "确 定" }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from "vue"
import { useUserStore } from "@/store/modules/user"
import { useRouter } from "vue-router"
import { ElMessage } from "element-plus"
import { setSessionStorage, getSessionStorage } from "@/utils/storage"
import { apiBackgroundVerificationCodeAutoPost, apiBackgroundCheckVerificationCodePost } from "@/api/index"
import { useVModel } from "@vueuse/core"
import VerifyMesssageCode from "../VerifyMessageCode/index.vue"

interface CheckFormData {
  phone: string
  smsCode: string
}

const props = defineProps({
  isShow: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: "验证登录"
  },
  width: {
    type: String,
    default: "430px"
  },

  confirmText: {
    type: String,
    default: "验 证"
  },
  customClass: {
    type: String,
    default: "ps-dialog-doublefactor"
  },
  userInfo: {
    type: Object,
    default: () => {
      return {}
    }
  },
  center: Boolean
})

const emit = defineEmits(["close", "doubleConfirm", "doubleCancel", "update:isShow"])

const userStore = useUserStore()
const router = useRouter()

const isLoading = ref(false)
const isShowMainDialog = ref(useVModel(props, "isShow", emit))
const sendAuthCode = ref(true)
const sendCodeDisabled = ref(false)
const checkFormData = ref<CheckFormData>({ phone: "", smsCode: "" })
const rules = ref({
  smsCode: [{ required: true, message: "请输入验证码", trigger: "change" }]
})
const showDialog = ref(false)
const countDownHandle = ref<NodeJS.Timeout | null>(null)
const messageType = ref<string>("")
const messageTitle = ref<string>("")
const messageContent = ref<string>("")
const massageConfirmText = ref<string>("")

const doubleFormRef = ref()

onMounted(() => {
  document.addEventListener("keydown", enterKeydowHandler)
})

onBeforeUnmount(() => {
  console.log("onBeforeUnmount 销毁")
  document.removeEventListener("keydown", enterKeydowHandler)
  if (countDownHandle.value) {
    clearTimeout(countDownHandle.value)
  }
})
// 监听回车
const enterKeydowHandler = (e: KeyboardEvent) => {
  if (e.keyCode === 13 && isShowMainDialog.value) {
    clickConfirmHandle()
  }
}
// 获取验证码
const getPhoneCode = async () => {
  try {
    isLoading.value = true
    const res: any = await apiBackgroundVerificationCodeAutoPost({})
    isLoading.value = false
    if (res.code === 0) {
      sendAuthCode.value = false
      ElMessage.success("发送成功")
    } else {
      ElMessage.error(res.msg)
    }
  } catch (err: any) {
    isLoading.value = false
    ElMessage.error(err.message)
  }
}
// 重置倒计时
const resetHandle = () => {
  sendAuthCode.value = true
}
// 提交
const clickConfirmHandle = () => {
  doubleFormRef.value?.validate((valid: any) => {
    if (valid) {
      checkSmsCode()
    }
  })
}
// 校验
const checkSmsCode = async () => {
  if (isLoading.value) return
  try {
    isLoading.value = true
    const res: any = await apiBackgroundCheckVerificationCodePost({ sms_code: checkFormData.value.smsCode })
    isLoading.value = false
    if (res && res.code === 0) {
      isShowMainDialog.value = false
      setSessionStorage("CHECKDOUBLEFACTOR", "1")
      userStore.updateAllUserInfo(props.userInfo)
      ElMessage.success("验证成功")
      // showChangePwdHandle()
      emit("doubleConfirm")
    } else {
      ElMessage.error(res.msg)
    }
  } catch (err: any) {
    isLoading.value = false
    ElMessage.error(err.message)
  }
}
// 取消
const clickCancleHandle = () => {
  isShowMainDialog.value = false
  userStore.resetUserData()
  emit("close")
}
// 关闭
const handleClose = () => {
  isLoading.value = false
  checkFormData.value.smsCode = ""
  setTimeout(() => {
    if (doubleFormRef.value) {
      doubleFormRef.value.clearValidate()
    }
  }, 100)
  emit("close")
}
// 关闭校验有效期弹窗
const closeTimeDialogHandle = (type: string) => {
  showDialog.value = false
  layoutOutHandle(type)
}
// 确认有效期弹窗
const layoutOutHandle = (type: string) => {
  console.log("layoutOutHandle", type)
  isShowMainDialog.value = false
  // store.dispatch("user/logout")
  emit("doubleCancel")
  if (messageType.value === "2" && type !== "close") {
    router.push({ path: "/account/setting" })
  } else {
    router.push({ path: "/login" })
  }
}
// 跟产品郑鸿宇说这个没有有效期，后面加了再走这个流程
const showChangePwdHandle = () => {
  if (props.userInfo.last_change_pwd_time && props.userInfo.is_expire_change_pwd) {
    const diffTime =
      new Date().getTime() - new Date(props.userInfo.last_change_pwd_time.replace(new RegExp(/-/gm), "/")).getTime()
    if (diffTime <= 90 * 24 * 60 * 60 * 1000) {
      if (diffTime >= 87 * 24 * 60 * 60 * 1000) {
        messageType.value = "2"
        massageConfirmText.value = "修改密码"
        messageTitle.value = "提示"
        const day = 90 - Math.floor(diffTime / (24 * 60 * 60 * 1000))
        messageContent.value = day
          ? `您的登录密码${day}天后即将过期，为了不影响您正常使用，建议及时修改。`
          : `您的登录密码1天后即将过期，为了不影响您正常使用，建议及时修改。`
        if (getSessionStorage("ISEXPIRECHANGEPWD") !== "1") {
          showDialog.value = true
        }
      }
    } else {
      messageType.value = "3"
      massageConfirmText.value = "确 定"
      messageContent.value = "帐号失效，无法登陆"
      showDialog.value = true
    }
  }
}
// 输入验证码监听
const inputCodeChange = (value: any) => {
  checkFormData.value.smsCode = value
}
</script>

<style lang="scss" scoped>
.ps-dialog-doublefactor {
  .el-dialog__header {
    text-align: center;
    .el-dialog__title {
      font-weight: 500;
    }
  }
  .el-dialog__body {
    padding: 15px 20px;
  }
  .content {
    // text-align: center;
  }
  .dialog-footer {
    text-align: right;
  }
}
</style>
