export type OrganizationParams = {
  page: number
  page_size: number
  id?: number
  status?: string
  create_time?: string
  parent?: string
  company?: string
  name?: string
  level?: string
  status__in?: string[]
  parent__in?: number
  parent__is_null?: string
  name__contains?: string
}

// 监管组织树类型
export type OrgsForm = {
  id: number
  status?: string
  create_time?: string
  update_time?: string
  parent?: number
  name: string
  parent_names?: any
  color?: string
  supervision_organizations?: any[]
  tree_id?: number
  level?: number
  status_alias?: string
  parent_alias?: string
  children_list?: any[]
  has_children?: boolean
  get_login_token?: string
  binded_org_info?: IBindedOrgInfo[]
  organizations_data?: IOrganizationsData[]
  isJian?: boolean
}
