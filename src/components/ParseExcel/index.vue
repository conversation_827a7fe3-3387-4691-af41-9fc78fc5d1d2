<script setup lang="ts">
import { ref } from "vue"
import * as XLSX from "xlsx"
import { trim } from "@/utils"
import { ElMessage } from "element-plus"

const fileName = ref<string>("")
const isLoading = ref<boolean>(false)

const props = defineProps({
  type: {
    type: String,
    default: "button"
  },
  name: {
    type: String,
    default: "file"
  },
  data: Object,
  accept: {
    // 默认可选择的文件类型
    type: String,
    default: () => {
      return ".xlsx, .xls, .csv"
    }
  },
  showLoading: {
    type: Boolean,
    default: true
  },
  disabled: Boolean,
  btnType: {
    type: String,
    default: "primary"
  },
  initial: {
    // 是否返回原始数据
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(["excel"])
const input = ref()
const triggerFileInput = () => {
  input.value.click()
}
// const handleClick = () => {
//   if (!props.disabled) {
//     props.showLoading && (isLoading.value = true)
//     input.value = null
//     input.value.click()
//     // 加个focus 的监听，如果用户取消或者关闭就关闭loading
//     window.addEventListener(
//       "focus",
//       () => {
//         console.log("focus")
//         // 取消逻辑处理
//         isLoading.value = false
//       },
//       { once: true }
//     )
//   }
// }
const handleChange = (ev: any) => {
  const file = ev.target.files[0]
  console.log(input.value, 999)
  if (!file) return
  // 设置下文件名
  fileName.value = file.name
  const fileType = getSuffix(file.name)
  if (!props.accept.split(", ").includes(fileType)) {
    isLoading.value = false
    return ElMessage.error("请上传excel相关格式的文件！")
  }
  if (props.initial) {
    loadFile(file)
  } else {
    importData(file)
  }
  ev.target.value = ""
}
// 获取文件后缀名
const getSuffix = (filename: any) => {
  const pos = filename.lastIndexOf(".")
  let suffix = ""
  if (pos !== -1) {
    suffix = filename.substring(pos)
  }
  return suffix
}
const loadFile = (obj: any) => {
  /**
   * 合并单元格元素(decode_range方法解析数据格式)
   {
      s: { //s start 开始
        c: 1,//cols 开始列
        r: 0 //rows 开始行
      },
      e: {//e end  结束
        c: 4,//cols 结束列
        r: 0 //rows 结束行
      }
    }
  */
  const reader = new FileReader()
  reader.onload = async (e) => {
    const data = e.target?.result
    const workbook = XLSX.read(data, { type: "array" })
    console.log(workbook)
    const firstSheetName = workbook.SheetNames[0]
    if (props.initial) {
      // 直接返回workbook
      emit("excel", workbook, obj)
    } else {
      // 返回转json的格式，仅适用于无合并表格的
      // 以字符串形式读取 { raw: false }, 如果是金额的需要去除前后空格
      // trim
      let results = XLSX.utils.sheet_to_json(workbook.Sheets[firstSheetName], { raw: false })
      console.log("json", results)
      // 发现金额这些会多个空格结尾，现在需要去除首尾空格
      results = results.map((v: any) => {
        Object.keys(v).forEach((key) => {
          v[key] = trim(v[key], 2)
        })
        return v
      })
      emit("excel", results, obj)
    }
    isLoading.value = false
  }
  reader.readAsArrayBuffer(obj)
}
// 导入数据
const importData = (obj: any) => {
  const reader = new FileReader()
  let binary = ""
  let wb // Read completed data
  let outData
  reader.onload = async function (e: any) {
    const bytes = new Uint8Array(e.currentTarget?.result)
    const length = bytes.byteLength
    for (let i = 0; i < length; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    wb = XLSX.read(binary, {
      type: "binary"
    })
    console.log("wb", wb)
    outData = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]], { raw: false })
    // { raw: false } 需要去除首尾空格
    outData = outData.map((v: any) => {
      Object.keys(v).forEach((key) => {
        v[key] = trim(v[key], 2)
      })
      return v
    })
    console.log("outData", outData)
    emit("excel", outData, obj)
    isLoading.value = false
  }
  reader.readAsArrayBuffer(obj)
}
</script>

<template>
  <div
    class="parse-excel bg-[#0DC195] color-white"
    b="1 solid color-[#0DC195] rd-4px"
    p="r-10px l-10px t-6px b-6px"
    @click="triggerFileInput"
  >
    选择文件
    <input class="parse-excel_input" type="file" ref="input" @change="handleChange" :accept="props.accept" />
  </div>
</template>

<style lang="scss">
.parse-excel {
  display: block;
  text-align: center;
  cursor: pointer;
  outline: none;
  .parse-excel_input {
    display: none;
  }
}
</style>
