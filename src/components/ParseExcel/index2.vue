<template>
  <!-- 模板部分略，因为原始代码中没有提供 -->
  <div class="parse-excel">
    <el-button size="small" @click="handleClick" :disabled="disabled" type="primary">选择文件</el-button>
    <input ref="fileInput" type="file" @change="handleChange" :accept="accept" class="parse-excel-input" />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue"
import * as XLSX from "xlsx"
// import { trim } from '@/utils'; // 确保这个路径正确

const emit = defineEmits(["excel"])

const props = defineProps({
  type: {
    type: String,
    default: "button"
  },
  name: {
    type: String,
    default: "file"
  },
  data: Object as () => any, // 假设data可以是任何类型，具体应根据实际情况调整
  accept: {
    // 默认可选择的文件类型
    type: String,
    default: ".xlsx, .xls, .csv"
  },
  showloading: {
    type: Boolean,
    default: true
  },
  disabled: <PERSON><PERSON>an,
  initial: {
    // 是否返回原始数据
    type: Boolean,
    default: false
  }
})

const fileName = ref("")
const isLoading = ref(false)
const fileInput = ref<HTMLInputElement | null>(null)

const handleClick = () => {
  console.log("handleClick", fileInput.value)

  if (!props.disabled && fileInput.value) {
    fileInput.value.click()
    if (props.showloading) {
      isLoading.value = true
      // 监听窗口焦点变化来尝试检测文件选择对话框的关闭（可选）
      // 注意：这通常不是处理文件上传的最佳方式，因为焦点变化可能由多种原因触发
      // 这里仅作为示例
      const handleFocus = () => {
        isLoading.value = false
        window.removeEventListener("focus", handleFocus)
      }
      window.addEventListener("focus", handleFocus, { once: true })
    }
  }
}

const handleChange = (ev: Event) => {
  console.log(666)
  const target = ev.target as HTMLInputElement
  const file = target.files?.[0]
  if (!file) return
  fileName.value = file.name
  const fileType = "." + getSuffix(file.name)
  if (!props.accept.split(", ").includes(fileType)) {
    isLoading.value = false
    // 假设这里有一个全局的message函数，实际项目中可能需要通过emit或provide/inject来处理
    console.error("请上传excel相关格式的文件！")
    return
  }

  if (props.initial) {
    loadFile(file)
  } else {
    importData(file)
  }
}

const getSuffix = (filename: String) => {
  const pos = filename.lastIndexOf(".")
  if (pos !== -1) {
    return filename.substring(pos + 1)
  }
  return ""
}

// 注意：这些方法应该根据实际需求来处理文件
const loadFile = (obj: File) => {
  // 实现加载文件的逻辑
  /**
   * 合并单元格元素(decode_range方法解析数据格式)
   {
      s: { //s start 开始
        c: 1,//cols 开始列
        r: 0 //rows 开始行
      },
      e: {//e end  结束
        c: 4,//cols 结束列
        r: 0 //rows 结束行
      }
    }
  */
  const reader = new FileReader()
  reader.onload = (e: ProgressEvent<FileReader>) => {
    if (e.target?.result) {
      const data = e.target.result
      const workbook = XLSX.read(data, { type: "array" })
      const firstSheetName = workbook.SheetNames[0]
      if (props.initial) {
        // 直接返回workbook
        emit("excel", workbook, fileName)
        if (fileInput.value) {
          fileInput.value.value = ""
        }
      } else {
        // 返回转json的格式，仅适用于无合并表格的
        // 以字符串形式读取 { raw: false }, 如果是金额的需要去除前后空格
        // trim
        let results = XLSX.utils.sheet_to_json(workbook.Sheets[firstSheetName], { raw: false })
        console.log("json", results)
        // 发现金额这些会多个空格结尾，现在需要去除首尾空格
        // results = results.map(v => {
        //   Object.keys(v).forEach(key => {
        //     v[key] = trim(v[key], 2)
        //   })
        //   return v
        // })
        console.log(results)
        emit("excel", results, fileName)
        if (fileInput.value) {
          fileInput.value.value = ""
        }
      }
    }

    isLoading.value = false
  }
  reader.readAsArrayBuffer(obj)
}

const importData = (obj: File) => {
  // 实现导入数据的逻辑
  var reader = new FileReader()
  var binary = ""
  var workbook // Read completed data
  var outdata
  reader.onload = (e: ProgressEvent<FileReader>) => {
    const fileResult = (e.currentTarget as FileReader).result
    if (fileResult instanceof ArrayBuffer) {
      var bytes = new Uint8Array(fileResult)
      var length = bytes.byteLength
      for (var i = 0; i < length; i++) {
        binary += String.fromCharCode(bytes[i])
      }
    }
    workbook = XLSX.read(binary, { type: "binary" })
    console.log("workbook", workbook)
    outdata = XLSX.utils.sheet_to_json(workbook.Sheets[workbook.SheetNames[0]], { raw: false })
    // { raw: false } 需要去除首尾空格
    // outdata = outdata.map(v => {
    //   Object.keys(v).forEach(key => {
    //     v[key] = trim(v[key], 2)
    //   })
    //   return v
    // })
    console.log("outdata", outdata)
    emit("excel", outdata, fileName)
    isLoading.value = false
  }
  reader.readAsArrayBuffer(obj)
}

onMounted(() => {
  // 可以在这里初始化一些状态或监听事件
})
</script>

<style lang="scss" scoped>
/* 样式部分 */
.parse-excel {
  display: inline-block;
  text-align: center;
  cursor: pointer;
  outline: none;

  .parse-excel-input {
    display: none;
  }
}
</style>
