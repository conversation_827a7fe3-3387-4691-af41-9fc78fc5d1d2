<script setup>
defineProps({
  statistics: Array
})
</script>
<template>
  <!-- 报表底下的统计 start -->
  <ul class="total ps-flex">
    <li v-for="item in statistics" :key="item.key" :class="[item.block ? 'block' : '', item.class, 'm-r-4']">
      {{ item.label }}
      <span v-if="item.type === 'money'">{{ item.value }}</span>
      <span v-else-if="item.type === 'moneyKey'">¥{{ item.value }}</span>
      <span v-else>{{ item.value + (item.unit ? item.unit : "") }}</span>
    </li>
  </ul>
  <!-- end -->
</template>

<style scoped lang="scss">
.total {
  padding: 0 20px;
  color: #606266;

  li {
    display: inline-block;
    margin-right: 20px;
    font-size: 14px;
    font-weight: 600;

    span {
      font-weight: 600;
    }
  }

  .block {
    display: block;
  }
}
</style>
