<template>
  <div :class="['search-form-wrapper', searchMode == 'normal' ? 'mini-h-80' : 'mini-h-130']">
    <div class="search-header" v-if="searchMode == 'advance'">
      <div class="search-h-l">
        <span>{{ title }}</span>
      </div>
      <div v-if="isShowRightBtn" class="search-right flex">
        <!--展开收起-->
        <div v-if="isShowMoreLayout" class="flex flex-center m-r-4 cursor-pointer" @click="handlerMore">
          <div class="color">{{ !isShowMore ? "展开" : "收起" }}</div>
          <el-icon size="20" class="m-l-2">
            <arrow-up v-if="isShowMore" />
            <arrow-down v-else />
          </el-icon>
        </div>
        <el-button class="w-82px h-32px" type="primary" @click="handlerSearch('search')">搜索</el-button>
        <el-button class="w-82px h-32px" plain @click="handlerReset(searchFormRef)"> 重置</el-button>
      </div>
    </div>

    <div
      class="collapse-wrapper"
      :class="[isShowMore || searchMode == 'normal' ? 'h-auto' : 'h-50px', searchMode == 'normal' ? 'm-t-20px' : '']"
    >
      <el-form :model="formSetting" inline ref="searchFormRef" :label-width="labelWidth" class="search-form-collapse">
        <slot name="perv" />
        <slot>
          <el-form-item
            v-for="(item, key) in formSetting"
            :key="key"
            :label="item.label"
            :prop="key + '.value'"
            :label-width="item.labelWidth"
          >
            <el-checkbox
              v-if="item.type === 'checkbox'"
              class="search-item-w"
              v-model="item.value"
              :label="item.labelText"
              @change="handlerSearch"
            />
            <el-input
              v-if="item.type === 'input'"
              class="search-item-w ps-input"
              :style="{ width: item.maxWidth }"
              v-model="model[key].value"
              :placeholder="item.placeholder"
              :clearable="item.clearable"
              :maxlength="item.maxlength"
              @input="handlerSearch"
            />
            <el-select
              v-if="item.type === 'select'"
              class="search-item-w"
              v-model="model[key].value"
              :placeholder="item.placeholder"
              :multiple="item.multiple"
              :collapse-tags="item.collapseTags"
              :collapse-tags-tooltip="item.collapseTagsTooltip"
              :clearable="item.clearable"
              :filterable="item.filterable"
              :style="{ width: item.maxWidth }"
              :disabled="item.disabled"
              @change="handlerSearch(item.linkage)"
            >
              <el-option
                v-for="(option, i) in item.dataList"
                :key="i"
                :label="item.listNameKey ? option[item.listNameKey] : option.label"
                :value="item.listValueKey ? option[item.listValueKey] : option.value"
                :disabled="option.disabled"
              />
            </el-select>
            <el-date-picker
              v-if="item.type === 'daterange'"
              :style="{ width: item.maxWidth }"
              v-model="model[key].value"
              type="daterange"
              :clearable="item.clearable"
              :format="item.format ? item.format : 'YYYY-MM-DD'"
              :value-format="item.format ? item.format : 'YYYY-MM-DD'"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :disabled-date="item.disabledDate || undefined"
              @change="handlerSearch"
            />
            <el-date-picker
              v-if="item.type === 'datetimerange'"
              :style="{ width: item.maxWidth }"
              :disabled-date="item.disabledDate || undefined"
              v-model="model[key].value"
              type="datetimerange"
              :clearable="item.clearable"
              :format="item.format ? item.format : 'yyyy-MM-dd HH:mm'"
              :value-format="item.format ? item.format : 'yyyy-MM-dd HH:mm'"
              align="left"
              unlink-panels
              range-separator="⇀"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="ps-picker"
              popper-class="ps-poper-picker"
              @change="handlerSearch"
            />
            <el-date-picker
              v-if="item.type === 'monthrange'"
              v-model="model[key].value"
              type="monthrange"
              :clearable="item.clearable"
              :format="item.format ? item.format : 'YYYY-MM'"
              :value-format="item.format ? item.format : 'YYYY-MM'"
              unlink-panels
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              @change="handlerSearch"
              :disabled-date="item.disabledDate || undefined"
            />
            <el-date-picker
              v-if="item.type === 'week'"
              v-model="model[key].value"
              type="week"
              :clearable="item.clearable"
              :format="item.format ? item.format : `YYYY年 第ww周`"
              unlink-panels
              placeholder="请选择范围"
              @change="handlerSearch"
            />
            <el-date-picker
              v-if="item.type === 'year'"
              v-model="model[key].value"
              type="year"
              :clearable="item.clearable"
              :format="item.format ? item.format : 'YYYY'"
              :value-format="item.format ? item.format : 'YYYY'"
              unlink-panels
              placeholder="请选择年份"
              @change="handlerSearch"
              :disabled-date="item.disabledDate || undefined"
            />
            <el-tree-select
              v-if="item.type === 'treeSelect'"
              style="width: 220px"
              v-model="model[key].value"
              :data="model[key].dataList"
              :clearable="item.clearable"
              :check-strictly="item.checkStrictly"
              :show-checkbox="item.showCheckbox"
              :default-expand-all="item.defaultExpandAll"
              :filterable="item.filterable"
              :multiple="item.multiple"
              :collapse-tags="item.collapseTags"
              :render-after-expand="false"
              @change="handlerSearch"
            />
            <user-group
              v-if="item.type === 'userGroup'"
              v-model="model[key].value"
              :multiple="model[key].multiple"
              :collapse-tags="model[key].collapseTags"
              @change="handlerSearch"
              class="search-item-w"
            />
            <user-department
              v-if="item.type === 'userDepartment'"
              v-model="model[key].value"
              :multiple="model[key].multiple"
              :collapse-tags="model[key].collapseTags"
              @change="handlerSearch"
              class="search-item-w"
            />
            <!-- <logistics-scene
              v-if="item.type === 'logisticsScene'"
              v-model="model[key].value"
              :multiple="model[key].multiple"
              :collapse-tags="model[key].collapseTags"
              @change="handlerSearch"
              class="search-item-w"
            /> -->
            <organization-select
              v-if="item.type === 'organization'"
              v-model="model[key].value"
              :multiple="model[key].multiple"
              :collapse-tags="model[key].collapseTags"
              @change="handlerSearch"
              class="search-item-w"
            />
            <supervision-orgs
              v-if="item.type === 'supervisionOrgs'"
              v-model="item.value"
              :multiple="item.multiple"
              :collapse-tags="item.collapseTags"
              :disabled="item.disabled"
              :clearable="item.clearable"
              :placeholder="item.placeholder"
              :width="item.width"
              :isShowChildOrs="item.isShowChildOrs"
              @change="handlerSearch($event, key)"
            />
            <canteen-orgs
              v-if="item.type === 'canteenOrgs'"
              v-model="item.value"
              :multiple="item.multiple"
              :collapse-tags="item.collapseTags"
              :disabled="item.disabled"
              :clearable="item.clearable"
              :placeholder="item.placeholder"
              :width="item.width"
              :orgs-id="item.orgsId"
              @change="handlerSearch($event, key)"
            />
            <el-cascader
              v-if="item.type === 'cascader'"
              :options="item.dataList"
              v-model="item.value"
              :props="item.props"
              :collapse-tags="item.collapseTags"
              :clearable="item.clearable"
              :filterable="item.filterable"
              :show-all-levels="item.showAllLevels"
              :style="{ width: item.width }"
              popper-class="ps-popper-cascader"
              @change="handlerSearch($event, key)"
            />
          </el-form-item>
        </slot>
        <el-button
          class="w-82px h-32px m-b-18px"
          type="primary"
          @click="handlerSearch('search')"
          v-if="searchMode == 'normal'"
          >筛选</el-button
        >
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { FormInstance } from "element-plus"
import { reactive, toRefs, ref } from "vue"
import { watchEffect } from "vue"
import { ArrowDown, ArrowUp } from "@element-plus/icons-vue"
import OrganizationSelect from "@/components/OrganizationSelect/index.vue"
import SupervisionOrgs from "@/components/SupervisionOrgs/index.vue"
import CanteenOrgs from "@/components/CanteenOrgs/index.vue"
const emit = defineEmits(["changeSearch", "reset"])
import { debounce } from "lodash"

// 添加defineOptions以确保组件名称被正确识别
defineOptions({
  name: "SearchForm"
})

// type Size = "small" | "default" | "large";
const props = defineProps({
  title: {
    type: String,
    default: "筛选查询"
  },
  labelWidth: {
    type: String,
    default: ""
  },
  formSetting: {
    type: Object,
    default() {}
  },
  isShowRightBtn: {
    type: Boolean,
    default: true
  },
  isShowMoreLayout: {
    //是否显示展开收起 这个默认不显示 控件就只显示一行，要显示多行
    type: Boolean,
    default: false
  },
  searchMode: {
    // 搜索模式，normal:普通模式，advance:高级模式
    type: String,
    default: "normal"
  }
})
const canteenOrgsRef = ref()
const searchFormRef = ref<FormInstance>()
const isShowMore = ref(false)
const model = ref<Record<string, any>>({})
watchEffect(() => {
  model.value = props.formSetting
})
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]
const handlerSearch = debounce((type?: any, itemType?: string) => {
  console.log("itemType", itemType, type)
  emit("changeSearch", model, type, itemType)
}, 500)
const handlerReset = (formEl?: FormInstance | undefined) => {
  if (formEl) {
    formEl.resetFields()
  } else {
    if (searchFormRef.value) {
      searchFormRef.value.resetFields()
    }
  }
  emit("reset", {})
}
defineExpose({
  handlerSearch,
  handlerReset
})
// 查看更多
const handlerMore = () => {
  isShowMore.value = !isShowMore.value
}
</script>

<style lang="scss" scope>
.mini-h-80 {
  min-height: 80px;
}
.mini-h-130 {
  min-height: 133px;
}
.search-form-wrapper {
  overflow: hidden;
  background: #fff;
  border-radius: 12px;

  .search-header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
    margin-bottom: 20px;

    // &::after {
    //   position: absolute;
    //   right: 24px;
    //   bottom: 0;
    //   left: 24px;
    //   height: 1px;
    //   content: "";
    //   background-color: #e7ecf2;
    // }

    .search-h-l {
      position: relative;
      flex: 1;
      min-width: 250px;
      height: 34px;
      padding-left: 20px;
      font-size: 20px;
      font-weight: bold;
      line-height: 34px;
      color: var(--el-color-black);
      // background-color: rgb(241 246 247 / 92.1%);
    }

    .search-h-l::before {
      position: absolute;
      top: 7px;
      left: 0;
      height: 20px;
      content: "";
      border-left: 6px solid var(--el-color-primary);
      border-radius: 0 3px 3px 0;
      // box-shadow: 4px 5px 7px 0 rgb(0 0 0 / 75%);
    }

    .search-right {
      margin-right: 20px;
    }
  }

  .collapse-wrapper {
    padding: 0 20px;
    // overflow: hidden;
  }

  .search-item-w {
    width: 200px;
  }
}
</style>
