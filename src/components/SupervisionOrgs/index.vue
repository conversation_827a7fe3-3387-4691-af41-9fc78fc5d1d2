<template>
  <div v-loading="isLoading">
    <el-select
      v-model="selectId"
      :multiple="multiple"
      :disabled="disabled"
      :collapse-tags="collapseTags"
      :clearable="clearable"
      :placeholder="placeholder"
      :filterable="filterable"
      :style="{ width: width }"
      @change="changeSelect"
    >
      <el-option v-for="item in selectData" :key="item.id" :label="item.name" :value="item.id" />
    </el-select>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue"
import { apiBackgroundFundSupervisionSupervisionChannelChannelRoleOrgsInofPost } from "@/api/user"
import to from "await-to-js"
import { useVModel } from "@vueuse/core"
// import { useUserStore } from "@/store/modules/user"
import { cloneDeep } from "lodash"

// 获取用户信息
// const userStore = useUserStore()
// 弹窗事件
const emit = defineEmits([
  "change", // 数据改变
  "update:modelValue",
  "callBackFunc"
])
// 加载中
const isLoading = ref(false)

const prop = defineProps({
  modelValue: {
    type: [String, Number, Array]
  },
  multiple: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  collapseTags: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: "请选择"
  },
  width: {
    type: String,
    default: "200px"
  },
  isShowChildOrs: {
    type: Boolean,
    default: false
  },
  filterable: {
    type: Boolean,
    default: false
  }
})
const selectId = ref(useVModel(prop, "modelValue", emit))
// 组织列表
const selectData = ref<Array<any>>([])

onMounted(() => {
  // 初始化数据
  getOrgsData()
})
// 获取数据
const getOrgsData = async () => {
  isLoading.value = true
  console.log("organvalue")
  let params = prop.isShowChildOrs ? { view_sub: true } : {}

  isLoading.value = true
  const [err, res]: any[] = await to(apiBackgroundFundSupervisionSupervisionChannelChannelRoleOrgsInofPost(params))
  isLoading.value = false
  if (err) {
    return
  }
  if (res && res.code === 0) {
    let data: any = res.data || {}
    let results: Array<any> = data.results || []
    selectData.value = cloneDeep(results)
    console.log("data", data)

    emit("callBackFunc", results.map((item) => item.id) || [])
  }
}

// 监听数据改变
const changeSelect = (val: any) => {
  emit("change", val)
}

defineExpose({
  selectData
})
</script>
<style lang="less" scoped></style>
