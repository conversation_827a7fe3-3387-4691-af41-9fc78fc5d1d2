<script lang="ts" setup>
import to from "await-to-js"
import { apiCardGroupListPost } from "@/api/user"
import { ElMessage } from "element-plus"
import { onMounted, defineEmits, defineProps, watch, ref } from "vue"

const emit = defineEmits(["update:modelValue", "change"])

const props = defineProps({
  modelValue: {
    type: [String, Number, Array]
  },
  multiple: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  collapseTags: {
    type: Boolean,
    default: true
  }
})

const groupValue = ref(props.modelValue)
const groupList = ref<any[]>([])

watch(
  () => ({
    modelValue: props.modelValue
  }),
  (newValue) => {
    groupValue.value = newValue.modelValue
  }
)

const changeValue = async (e: Event) => {
  emit("update:modelValue", e)
  emit("change", e)
}
const getGroupList = async () => {
  const [err, res] = await to(
    apiCardGroupListPost({
      page: 1,
      page_size: 999999
    })
  )
  const { code, msg } = res!
  if (err) {
    return
  }
  if (code === 0) {
    groupList.value = res.data.results
  } else {
    ElMessage.error(msg)
  }
}

onMounted(() => {
  getGroupList()
})
</script>
<template>
  <div>
    <el-select
      class="w-full"
      v-model="groupValue"
      :multiple="multiple"
      :disabled="disabled"
      :collapse-tags="collapseTags"
      @change="changeValue"
    >
      <el-option v-for="item in groupList" :key="item.id" :label="item.group_name" :value="item.id" />
    </el-select>
  </div>
</template>
