div
<template>
  <div class="questionnaire">
    <!-- 左边选择 -->
    <div class="questionnaire-left" v-if="isShowLeft">
      <div class="questionnaire-left-title">
        <span>题型</span>
      </div>
      <div class="questionnaire-left-content">
        <div
          class="questionnaire-left-content-item pointer"
          v-for="(item, index) in questionType"
          :key="index"
          @click="selectThisTopic(item)"
        >
          <span>{{ item.name }}</span>
        </div>
      </div>
    </div>
    <!-- 右边预览 -->
    <div class="questionnaire-right" :class="{ 'disabled-view': disabled }">
      <VueDraggable
        v-model="localQuestionsList"
        :animation="300"
        target=".sort-target"
        v-if="localQuestionsList.length"
        @update="handleDragChange"
        :disabled="disabled"
      >
        <TransitionGroup type="transition" tag="div" name="fade" class="sort-target">
          <div class="questionnaire-right-item" v-for="(element, index) in localQuestionsList" :key="index">
            <choice-topic
              :ref="(el) => setItemRef(el, index, 'choice')"
              v-if="element.question_type === 0 || element.question_type === 1"
              :topic="element"
              :index="index"
              @update:topic="updateTopic(index, $event)"
              @delete="deleteTopic"
              :disabled="disabled"
            />
            <evaluate-topic
              :ref="(el) => setItemRef(el, index, 'evaluate')"
              v-if="element.question_type === 3"
              :topic="element"
              :index="index"
              @update:topic="updateTopic(index, $event)"
              @delete="deleteTopic"
              :disabled="disabled"
            />
            <file-upload-topic
              :ref="(el) => setItemRef(el, index, 'fileUpload')"
              v-if="element.question_type === 5 || element.question_type === 6"
              :topic="element"
              :index="index"
              @update:topic="updateTopic(index, $event)"
              @delete="deleteTopic"
              :disabled="disabled"
            />
            <gap-filling-topic
              :ref="(el) => setItemRef(el, index, 'gapFilling')"
              v-if="element.question_type === 4"
              :topic="element"
              :index="index"
              @update:topic="updateTopic(index, $event)"
              @delete="deleteTopic"
              :disabled="disabled"
            />
            <mark-topic
              :ref="(el) => setItemRef(el, index, 'mark')"
              v-if="element.question_type === 2"
              :topic="element"
              :index="index"
              @update:topic="updateTopic(index, $event)"
              @delete="deleteTopic"
              :disabled="disabled"
            />
          </div>
        </TransitionGroup>
      </VueDraggable>
      <div v-else>
        <el-empty description="请点击左侧新增题型" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, onBeforeUpdate } from "vue"
import { VueDraggable } from "vue-draggable-plus"
import ChoiceTopic from "./components/ChoiceTopic.vue"
import EvaluateTopic from "./components/EvaluateTopic.vue"
import FileUploadTopic from "./components/FileUploadTopic.vue"
import GapFillingTopic from "./components/GapFillingTopic.vue"
import MarkTopic from "./components/MarkTopic.vue"
import _, { cloneDeep } from "lodash"
import { ElMessage } from "element-plus"

const props = defineProps({
  questionsList: {
    type: Array,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  },
  drawerShow: {
    type: Boolean,
    default: false
  },
  isShowLeft: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(["update:questionsList", "update:disabled", "update:drawerShow"])

const questionType = ref([
  {
    type: 0,
    name: "单选题"
  },
  {
    type: 1,
    name: "多选题"
  },
  {
    type: 2,
    name: "分数题"
  },
  {
    type: 3,
    name: "评价题"
  },
  {
    type: 4,
    name: "填空题"
  },
  {
    type: 5,
    name: "图片上传"
  },
  {
    type: 6,
    name: "文件上传"
  }
])

// 动态refs处理 - 改用对象结构而不是数组
const choiceTopicFormRefs = ref({})
const evaluateTopicFormRefs = ref({})
const fileUploadTopicFormRefs = ref({})
const gapFillingTopicFormRefs = ref({})
const markTopicFormRefs = ref({})

// 更新ref设置函数
const setItemRef = (el, index, type) => {
  if (el) {
    switch (type) {
      case "choice":
        choiceTopicFormRefs.value[index] = el
        break
      case "evaluate":
        evaluateTopicFormRefs.value[index] = el
        break
      case "fileUpload":
        fileUploadTopicFormRefs.value[index] = el
        break
      case "gapFilling":
        gapFillingTopicFormRefs.value[index] = el
        break
      case "mark":
        markTopicFormRefs.value[index] = el
        break
    }
  }
}

// Vue 3中使用onBeforeUpdate来重置refs对象
onBeforeUpdate(() => {
  choiceTopicFormRefs.value = {}
  evaluateTopicFormRefs.value = {}
  fileUploadTopicFormRefs.value = {}
  gapFillingTopicFormRefs.value = {}
  markTopicFormRefs.value = {}
})

const result = ref([]) // 接一下每个问题类型的返回结果
// 使用深度拷贝确保数据隔离
const localQuestionsList = ref(cloneDeep(props.questionsList || []))

// 监听 props 变化更新本地数据
watch(
  () => props.questionsList,
  (newVal, oldVal) => {
    if (!_.isEqual(newVal, oldVal)) {
      localQuestionsList.value = cloneDeep(newVal || [])
    }
  },
  { immediate: true, deep: true }
)

// 监听disabled属性变化并通知父组件
watch(
  () => props.disabled,
  (newVal) => {
    emit("update:disabled", newVal)
  }
)

// 监听drawerShow属性变化并通知父组件
watch(
  () => props.drawerShow,
  (newVal) => {
    emit("update:drawerShow", newVal)
  }
)

// 监听本地数据变化向父组件发送更新
watch(
  localQuestionsList,
  (newVal) => {
    if (!_.isEqual(newVal, props.questionsList)) {
      updateQuestionOrder()
      emit("update:questionsList", cloneDeep(newVal))
    }
  },
  { deep: true }
)

// 处理拖拽排序事件
const handleDragChange = () => {
  updateQuestionOrder()
}

// 更新问题序号
const updateQuestionOrder = () => {
  localQuestionsList.value.forEach((item, index) => {
    item.order_in_list = index + 1
  })
}
const selectThisTopic = (item) => {
  if (props.disabled) {
    return
  }
  let obj = {
    question_type: item.type,
    order_in_list: localQuestionsList.value.length + 1
  }
  switch (item.type) {
    case 0: {
      Object.assign(obj, {
        caption: "",
        choices: [
          {
            type: "default",
            description: "",
            other_content: "",
            multi_choice: false,
            order_in_list: 0
          }
        ],
        required: true
      })
      break
    }
    case 1: {
      Object.assign(obj, {
        caption: "",
        choices: [
          {
            type: "default",
            description: "",
            other_content: "",
            multi_choice: true,
            order_in_list: 0
          }
        ],
        least_choose_count: 1,
        required: true
      })
      break
    }
    case 2: {
      Object.assign(obj, {
        caption: "",
        top_score: 10,
        required: true
      })
      break
    }
    case 3: {
      Object.assign(obj, {
        caption: "",
        choices: [
          {
            description: "",
            order_in_list: 0
          }
        ],
        top_score: 5,
        required: true
      })
      break
    }
    case 4: {
      Object.assign(obj, {
        caption: "",
        required: true
      })
      break
    }
    case 5: {
      Object.assign(obj, {
        caption: "图片上传",
        upload_max_num: 1,
        required: true
      })
      break
    }
    case 6: {
      Object.assign(obj, {
        caption: "文件上传",
        upload_max_num: 1,
        required: true
      })
      break
    }
  }
  if (localQuestionsList.value.length >= 50) {
    return ElMessage.error("最多创建50个问题")
  } else {
    localQuestionsList.value.push(obj)
  }
  console.log("localQuestionsList.value", localQuestionsList.value)
  updateQuestionOrder()
}

const deleteTopic = (index) => {
  if (props.disabled) {
    return
  }
  localQuestionsList.value.splice(index, 1)
  updateQuestionOrder()
}

const checkQuestionnaire = async () => {
  result.value = []

  if (localQuestionsList.value.length === 0) {
    return [false]
  }

  for (let index = 0; index < localQuestionsList.value.length; index++) {
    const item = localQuestionsList.value[index]
    let flag = false

    try {
      switch (item.question_type) {
        case 0:
        case 1:
          // 在Vue 3中使用ref对象访问
          if (choiceTopicFormRefs.value[index]) {
            flag = await choiceTopicFormRefs.value[index].handleValidate()
          }
          break
        case 2:
          if (markTopicFormRefs.value[index]) {
            flag = await markTopicFormRefs.value[index].handleValidate()
          }
          break
        case 3:
          if (evaluateTopicFormRefs.value[index]) {
            flag = await evaluateTopicFormRefs.value[index].handleValidate()
          }
          break
        case 4:
          if (gapFillingTopicFormRefs.value[index]) {
            flag = await gapFillingTopicFormRefs.value[index].handleValidate()
          }
          break
        case 5:
        case 6:
          if (fileUploadTopicFormRefs.value[index]) {
            flag = await fileUploadTopicFormRefs.value[index].handleValidate()
          }
          break
      }
    } catch (error) {
      console.error(`题目${index + 1}验证出错:`, error)
      flag = false
    }

    result.value.push(flag)
  }

  return result.value
}

// 新增方法用于更新topic
const updateTopic = (index, newValue) => {
  if (index >= 0 && index < localQuestionsList.value.length) {
    localQuestionsList.value[index] = newValue
  }
}

// 向父组件暴露验证方法
defineExpose({
  checkQuestionnaire
})
</script>

<style lang="scss" scoped>
.questionnaire {
  width: 100%;
  border-top: 1px solid #e7ecf2;
  display: flex;

  &-left {
    border-right: 1px solid #e7ecf2;
    padding: 15px 15px 15px 0px;
    width: 20%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;

    &-title {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f2f2f2;
      border-radius: 6px;
      padding: 8px 0;
      margin-bottom: 10px;
      font-weight: bold;
    }

    &-content {
      width: 100%;
      padding: 10px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;

      &-item {
        width: 100%;
        padding: 10px;
        margin: 5px;
        display: flex;
        align-items: stretch;
        justify-content: center;
        border-radius: 16px;
        font-size: 14px;
        border: 1px solid #f2f2f2;
        transition: all 0.3s;

        &:hover {
          background-color: #f2f2f2;
          border-color: #409eff;
          color: #409eff;
        }

        &.disabled {
          cursor: not-allowed;
          opacity: 0.6;
          &:hover {
            background-color: #f2f2f2;
            border-color: #f2f2f2;
            color: inherit;
          }
        }
      }
    }
  }

  &-right {
    padding: 15px;
    width: 80%;
    display: flex;
    flex-direction: column;
    position: relative;

    &.disabled-view {
      background-color: #fafafa;
    }

    &-item {
      border: 1px solid #e7ecf2;
      border-radius: 6px;
      margin: 10px;
      position: relative;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      .draggable-handle {
        position: absolute;
        top: 5px;
        right: 5px;
        z-index: 10;
        padding: 5px;
        cursor: move;
        color: #909399;

        &:hover {
          color: #409eff;
        }
      }
    }
  }

  .ghost-item {
    opacity: 0.5;
    background: #c8ebfb;
  }
}
</style>
