<template>
  <div class="choice-topic">
    <div class="delete-icon pointer" @click="deleteTopic">
      <el-icon><Delete /></el-icon>
    </div>
    <el-form :model="topicModel" ref="topicRef">
      <div class="choice-topic-title">
        <div class="choice-topic-title-left">
          <span class="m-b-5">{{ index + 1 }}.</span>
          <el-form-item
            prop="caption"
            :rules="[
              { required: true, message: '请输入标题', trigger: ['blur', 'change'] },
              { pattern: /^$|^.*\S.*$/, message: '请输入标题', trigger: ['blur', 'change'] }
            ]"
          >
            <el-input
              v-model="topicModel.caption"
              type="textarea"
              class="m-l-10 w-350"
              :autosize="{ minRows: 1 }"
              placeholder="请输入标题"
              resize="vertical"
              :disabled="disabled"
              maxlength="30"
              @keydown.enter.prevent
              @input="filterNewlines"
            />
          </el-form-item>
        </div>
      </div>
      <div class="choice-topic-content">
        <div class="upload">
          <div style="color: #ff9b45; font-size: 32px">
            <el-icon v-if="topicModel.question_type === 6"><Upload /></el-icon>
            <el-icon v-else><CirclePlus /></el-icon>
          </div>
          <div class="upload-text">
            <div style="line-height: 14px">{{ uploadTypeText }}</div>
            <div style="line-height: 14px">[{{ fileSizeLimit }}]</div>
          </div>
        </div>
      </div>
      <div class="choice-topic-footer">
        <div class="m-r-10">最多可上传</div>
        <el-select class="w-60 m-r-10" size="small" v-model="topicModel.upload_max_num" :disabled="disabled">
          <el-option v-for="item in 10" :key="item" :label="item" :value="item" />
        </el-select>
        <el-checkbox v-model="topicModel.required" :disabled="disabled">必填</el-checkbox>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, watch, reactive } from "vue"
import { Delete, CirclePlus, Upload } from "@element-plus/icons-vue"

const props = defineProps({
  topic: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(["update:topic", "delete"])

// 深度复制一份 topic 以避免直接修改 props
const topicModel = reactive(JSON.parse(JSON.stringify(props.topic)))
import _ from "lodash"

// 监听 props 变化更新本地数据
watch(
  () => props.topic,
  (newVal, oldVal) => {
    if (!_.isEqual(newVal, oldVal)) {
      Object.assign(topicModel, JSON.parse(JSON.stringify(newVal)))
    }
  },
  { deep: true }
)

// 监听本地数据变化向父组件发送更新
watch(
  topicModel,
  (newVal) => {
    // 使用深拷贝避免对象引用问题
    emit("update:topic", JSON.parse(JSON.stringify(newVal)))
  },
  { deep: true }
)

const topicRef = ref(null)

// 计算文件大小限制描述文字
const fileSizeLimit = computed(() => {
  return topicModel.question_type === 5 ? "不超过5M" : "不超过10M"
})

// 计算上传类型描述文字
const uploadTypeText = computed(() => {
  return topicModel.question_type === 5 ? "上传图片" : "选择文件"
})

const deleteTopic = () => {
  emit("delete", props.index)
}

const handleValidate = () => {
  return new Promise((resolve) => {
    topicRef.value.validate((valid) => {
      if (valid) {
        resolve(true)
      } else {
        resolve(false)
      }
    })
  })
}

const filterNewlines = (value) => {
  inputText.value = value.replace(/\n/g, "") // 清除所有换行符
}

// 向父组件暴露验证方法
defineExpose({
  handleValidate
})
</script>

<style lang="scss" scoped>
.choice-topic {
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  .delete-icon {
    position: absolute;
    top: 10px;
    right: 10px;
  }
  &-title {
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    &-left {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
    }
    &-right {
      margin-left: 10px;
      color: #e7ecf2;
    }
  }
  &-content {
    margin-left: 23px;
    margin-top: 10px;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    .upload {
      width: 120px;
      height: 120px;
      border: 1px solid #e7ecf2;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;
      position: relative;
      &-text {
        font-size: 12px;
        position: absolute;
        bottom: 5px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
    }
  }
  &-footer {
    position: absolute;
    right: 10px;
    bottom: 10px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>
