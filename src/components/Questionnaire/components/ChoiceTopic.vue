<template>
  <div class="choice-topic">
    <div class="delete-icon pointer" @click="deleteTopic">
      <el-icon><Delete /></el-icon>
    </div>
    <el-form :model="topicModel" ref="topicRef">
      <div class="choice-topic-title">
        <div class="choice-topic-title-left">
          <span class="m-b-5px">{{ index + 1 }}.</span>
          <el-form-item
            prop="caption"
            :rules="[
              { required: true, message: '请输入标题', trigger: ['blur', 'change'] },
              { pattern: /^$|^.*\S.*$/, message: '请输入标题', trigger: ['blur', 'change'] }
            ]"
          >
            <div class="m-l-10px w-350px">
              <el-input
                v-model="topicModel.caption"
                type="textarea"
                autosize
                placeholder="请输入标题"
                resize="vertical"
                :disabled="disabled"
                maxlength="30"
                @keydown.enter.prevent
                @input="filterNewlines"
              />
            </div>
          </el-form-item>
        </div>
      </div>
      <div class="choice-topic-content">
        <div class="choice-topic-content-item" v-for="(item, index) in topicModel.choices" :key="index">
          <div :class="[topicModel.question_type === 0 ? 'singleStyle' : 'multipleStyle', 'm-r-5px', 'm-t-15px']" />
          <div class="choice-topic-content-item-input">
            <el-form-item
              :prop="`choices.` + index + `.description`"
              :rules="[
                { required: true, message: '请输入选项内容', trigger: ['blur', 'change'] },
                { pattern: /^$|^.*\S.*$/, message: '请输入选项内容', trigger: ['blur', 'change'] }
              ]"
            >
              <div class="w-350px">
                <el-input
                  v-model="item.description"
                  size="small"
                  :placeholder="computedPlaceholder(item)"
                  type="textarea"
                  autosize
                  resize="vertical"
                  :disabled="disabled"
                  maxlength="15"
                  @keydown.enter.prevent
                  @input="filterNewlines"
                />
              </div>
            </el-form-item>
            <el-form-item
              :prop="`choices.` + index + `.other_content`"
              v-if="item.type !== 'default'"
              :rules="[
                { required: true, message: '请输入填空内容', trigger: ['blur', 'change'] },
                { pattern: /^$|^.*\S.*$/, message: '请输入填空内容', trigger: ['blur', 'change'] }
              ]"
            >
              <div class="w-350px m-t-5px">
                <el-input
                  size="small"
                  v-model="item.other_content"
                  placeholder="请输入内容"
                  type="textarea"
                  autosize
                  resize="vertical"
                  :disabled="disabled"
                  maxlength="20"
                  @keydown.enter.prevent
                  @input="filterNewlines"
                />
              </div>
            </el-form-item>
          </div>
          <div class="choice-topic-content-item-icon m-t-13px">
            <div class="icon-style pointer" @click="addSelect(index)">
              <el-icon><CirclePlusFilled /></el-icon>
            </div>
            <div class="icon-style pointer" v-if="topicModel.choices.length > 1" @click="deleteSelect(index)">
              <el-icon><RemoveFilled /></el-icon>
            </div>
          </div>
        </div>
      </div>
      <div class="choice-topic-footer">
        <el-button type="text" size="small" class="m-r-10px" @click="changeItemType">添加填空选项</el-button>
        <div class="w-110px m-r-10px" v-if="topicModel.question_type === 1">
          <el-select size="small" v-model="topicModel.least_choose_count" placeholder="至少选" :disabled="disabled">
            <el-option v-for="(item, index) in selectList" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <el-checkbox v-model="topicModel.required" :disabled="disabled">必填</el-checkbox>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { cloneDeep } from "lodash"
import { ref, computed, defineProps, defineEmits, watch, reactive } from "vue"

const props = defineProps({
  topic: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(["update:topic", "delete"])

// 深度复制一份 topic 以避免直接修改 props
const topicModel = reactive(cloneDeep(props.topic))
import _ from "lodash"

// 监听 props 变化更新本地数据
watch(
  () => props.topic,
  (newVal, oldVal) => {
    if (!_.isEqual(newVal, oldVal)) {
      Object.assign(topicModel, cloneDeep(newVal))
    }
  },
  { deep: true }
)

// 监听本地数据变化向父组件发送更新
watch(
  topicModel,
  (newVal) => {
    // 使用深拷贝避免对象引用问题
    emit("update:topic", cloneDeep(newVal))
  },
  { deep: true }
)

const topicRef = ref(null)
const selectList = ref([])

// 初始化选择列表
const initSelectList = () => {
  selectList.value = []
  if (topicModel.question_type === 1) {
    const len = Math.min(topicModel.choices.length, 10) // 限制最多10项
    for (let i = 1; i <= len; i++) {
      selectList.value.push({
        label: `至少选${i}项`,
        value: i
      })
    }
  }
}

// 在组件加载和题目类型变化时初始化选择列表
watch(() => topicModel.question_type, initSelectList, { immediate: true })
watch(() => topicModel.choices.length, initSelectList)

const computedPlaceholder = computed(() => {
  return (d) => {
    if (d.type !== "default") {
      return "其他"
    } else {
      return "请输入选项内容"
    }
  }
})

const addSelect = (index) => {
  if (props.disabled) {
    return
  }
  // 确保每个选项的order_in_list值正确
  const newItem = {
    type: "default",
    description: "",
    other_content: "",
    multi_choice: topicModel.question_type === 1,
    order_in_list: index + 1
  }

  // 使用splice添加到指定位置
  topicModel.choices.splice(index + 1, 0, newItem)

  // 重新排序所有选项
  topicModel.choices.forEach((item, idx) => {
    item.order_in_list = idx
  })
}

const deleteSelect = (index) => {
  if (props.disabled || topicModel.choices.length <= 1) {
    return
  }

  // 删除指定位置的选项
  topicModel.choices.splice(index, 1)

  // 重新排序所有选项
  topicModel.choices.forEach((item, idx) => {
    item.order_in_list = idx
  })
}

const changeItemType = () => {
  if (props.disabled) {
    return
  }

  // 添加一个"其他"类型的选项
  const newItem = {
    type: "other",
    description: "其他",
    other_content: "请输入内容",
    multi_choice: topicModel.question_type === 1,
    order_in_list: topicModel.choices.length
  }

  topicModel.choices.push(newItem)
}

const deleteTopic = () => {
  emit("delete", props.index)
}

const handleValidate = () => {
  return new Promise((resolve) => {
    topicRef.value.validate((valid) => {
      if (valid) {
        resolve(true)
      } else {
        resolve(false)
      }
    })
  })
}

const filterNewlines = (value) => {
  inputText.value = value.replace(/\n/g, "") // 清除所有换行符
}

// 向父组件暴露验证方法
defineExpose({
  handleValidate
})
</script>

<style lang="scss" scoped>
.choice-topic {
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  .delete-icon {
    position: absolute;
    top: 10px;
    right: 10px;
  }
  &-title {
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    &-left {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
    }
    &-right {
      margin-left: 10px;
      color: #e7ecf2;
    }
  }
  &-content {
    margin-top: 10px;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    &-item {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      .singleStyle {
        width: 18px;
        height: 18px;
        border: 1px solid #e7ecf2;
        border-radius: 50%;
      }
      .multipleStyle {
        width: 18px;
        height: 18px;
        border: 1px solid #e7ecf2;
      }
      &-input {
        display: flex;
        flex-direction: column;
        margin: 10px 0px;
        margin-right: 10px;
        :deep(.el-form-item__content) {
          line-height: 30px;
        }
      }
      &-icon {
        width: 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .icon-style {
          font-size: 22px;
        }
      }
    }
  }
  &-footer {
    position: absolute;
    right: 10px;
    bottom: 10px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>
