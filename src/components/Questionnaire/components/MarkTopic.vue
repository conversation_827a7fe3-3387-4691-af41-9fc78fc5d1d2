<template>
  <div class="mark-topic">
    <div class="delete-icon pointer" @click="deleteTopic">
      <el-icon><Delete /></el-icon>
    </div>
    <el-form :model="topicModel" ref="topicRef">
      <div class="mark-topic-title">
        <div class="mark-topic-title-left">
          <span class="m-b-5">{{ index + 1 }}.</span>
          <el-form-item
            prop="caption"
            :rules="[
              { required: true, message: '请输入标题', trigger: ['blur', 'change'] },
              { pattern: /^$|^.*\S.*$/, message: '请输入标题', trigger: ['blur', 'change'] }
            ]"
          >
            <el-input
              v-model="topicModel.caption"
              type="textarea"
              class="m-l-10 w-350"
              :autosize="{ minRows: 1 }"
              placeholder="请输入标题"
              resize="vertical"
              :disabled="disabled"
              maxlength="30"
              @keydown.enter.prevent
              @input="filterNewlines"
            />
          </el-form-item>
        </div>
      </div>
      <div class="mark-topic-content">
        <div class="mark-topic-content-item">
          <div class="mark-topic-content-item-top">
            <div class="point">1</div>
            <div class="point">{{ topicModel.top_score }}</div>
          </div>
          <div class="mark-topic-content-item-bottom">
            <div
              v-for="score in scoreArray"
              :key="score"
              class="mark"
              :class="{ active: score <= selectedScore }"
              @click="updateSelectedScore(score)"
            >
              {{ score }}
            </div>
          </div>
        </div>
      </div>
      <div class="mark-topic-footer">
        <div class="m-r-10">最高分</div>
        <el-select class="w-60 m-r-10" size="small" v-model="topicModel.top_score" :disabled="disabled">
          <el-option v-for="item in [2, 3, 4, 5, 6, 7, 8, 9, 10]" :key="item" :label="item" :value="item" />
        </el-select>
        <el-checkbox v-model="topicModel.required" :disabled="disabled">必填</el-checkbox>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, reactive, computed } from "vue"
import { Delete } from "@element-plus/icons-vue"

const props = defineProps({
  topic: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(["update:topic", "delete"])

// 深度复制一份 topic 以避免直接修改 props
const topicModel = reactive(JSON.parse(JSON.stringify(props.topic)))
import _ from "lodash"

// 监听 props 变化更新本地数据
watch(
  () => props.topic,
  (newVal, oldVal) => {
    if (!_.isEqual(newVal, oldVal)) {
      Object.assign(topicModel, JSON.parse(JSON.stringify(newVal)))
    }
  },
  { deep: true }
)

// 监听本地数据变化向父组件发送更新
watch(
  topicModel,
  (newVal) => {
    emit("update:topic", JSON.parse(JSON.stringify(newVal)))
  },
  { deep: true }
)

const topicRef = ref(null)

// 计算生成评分数组
const scoreArray = computed(() => {
  return Array.from({ length: topicModel.top_score }, (_, i) => i + 1)
})

// 当前选中的分数（仅用于预览）
const selectedScore = ref(Math.ceil(topicModel.top_score))

// 更新选中分数
const updateSelectedScore = (score) => {
  if (props.disabled) return
  selectedScore.value = score
}

// 监听最高分变化，调整选中分数
watch(
  () => topicModel.top_score,
  (newVal) => {
    if (selectedScore.value > newVal) {
      selectedScore.value = newVal
    }
  }
)

const deleteTopic = () => {
  emit("delete", props.index)
}

const handleValidate = () => {
  return new Promise((resolve) => {
    topicRef.value.validate((valid) => {
      if (valid) {
        resolve(true)
      } else {
        resolve(false)
      }
    })
  })
}

const filterNewlines = (value) => {
  inputText.value = value.replace(/\n/g, "") // 清除所有换行符
}

// 向父组件暴露验证方法
defineExpose({
  handleValidate
})
</script>

<style lang="scss" scoped>
.mark-topic {
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  .delete-icon {
    position: absolute;
    top: 10px;
    right: 10px;
  }
  &-title {
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    &-left {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
    }
    &-right {
      margin-left: 10px;
      color: #e7ecf2;
    }
  }
  &-content {
    margin-left: 23px;
    margin-top: 10px;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    &-item {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      border: 1px solid #e7ecf2;
      border-radius: 6px;
      padding: 5px;
      &-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 5px;
        border-bottom: 1px solid #e7ecf2;
        .point {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      &-bottom {
        margin-top: 5px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        .mark {
          width: 32px;
          height: 32px;
          margin: 5px;
          border: 1px solid #e7ecf2;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            border-color: #ff9b45;
            color: #ff9b45;
          }

          &.active {
            background-color: #ff9b45;
            color: white;
            border-color: #ff9b45;
          }
        }
      }
    }
  }
  &-footer {
    position: absolute;
    right: 10px;
    bottom: 10px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>
