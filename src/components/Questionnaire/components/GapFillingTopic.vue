<template>
  <div class="gap-filling-topic">
    <div class="delete-icon pointer" @click="deleteTopic">
      <el-icon><Delete /></el-icon>
    </div>
    <el-form :model="topicModel" ref="topicRef">
      <div class="gap-filling-topic-title">
        <div class="gap-filling-topic-title-left">
          <span class="m-b-5">{{ index + 1 }}.</span>
          <el-form-item
            prop="caption"
            :rules="[
              { required: true, message: '请输入标题', trigger: ['blur', 'change'] },
              { pattern: /^$|^.*\S.*$/, message: '请输入标题', trigger: ['blur', 'change'] }
            ]"
          >
            <el-input
              v-model="topicModel.caption"
              type="textarea"
              class="m-l-10 w-350"
              :autosize="{ minRows: 1 }"
              placeholder="请输入标题"
              resize="vertical"
              :disabled="disabled"
              maxlength="30"
              @keydown.enter.prevent
              @input="filterNewlines"
            />
          </el-form-item>
        </div>
      </div>
      <div class="gap-filling-topic-content">
        <el-input
          v-model="placeholderText"
          type="textarea"
          class="m-l-10 w-350"
          :autosize="{ minRows: 4 }"
          placeholder="请输入内容"
          resize="vertical"
          disabled
          style="opacity: 0.6"
          maxlength="100"
        />
      </div>
      <div class="gap-filling-topic-footer">
        <el-checkbox v-model="topicModel.required" :disabled="disabled">必填</el-checkbox>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, reactive } from "vue"
import { Delete } from "@element-plus/icons-vue"

const props = defineProps({
  topic: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(["update:topic", "delete"])

// 深度复制一份 topic 以避免直接修改 props
const topicModel = reactive(JSON.parse(JSON.stringify(props.topic)))
import _ from "lodash"

// 监听 props 变化更新本地数据
watch(
  () => props.topic,
  (newVal, oldVal) => {
    if (!_.isEqual(newVal, oldVal)) {
      Object.assign(topicModel, JSON.parse(JSON.stringify(newVal)))
    }
  },
  { deep: true }
)

// 监听本地数据变化向父组件发送更新
watch(
  topicModel,
  (newVal) => {
    emit("update:topic", JSON.parse(JSON.stringify(newVal)))
  },
  { deep: true }
)

const topicRef = ref(null)

// 示例文本，预览用
const placeholderText = ref("这里是用户填写的内容")

const deleteTopic = () => {
  emit("delete", props.index)
}

const handleValidate = () => {
  return new Promise((resolve) => {
    topicRef.value.validate((valid) => {
      if (valid) {
        resolve(true)
      } else {
        resolve(false)
      }
    })
  })
}

const filterNewlines = (value) => {
  inputText.value = value.replace(/\n/g, "") // 清除所有换行符
}

// 向父组件暴露验证方法
defineExpose({
  handleValidate
})
</script>

<style lang="scss" scoped>
.gap-filling-topic {
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  .delete-icon {
    position: absolute;
    top: 10px;
    right: 10px;
  }
  &-title {
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    &-left {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
    }
    &-right {
      margin-left: 10px;
      color: #e7ecf2;
    }
  }
  &-content {
    margin-left: 14px;
    margin-top: 10px;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    position: relative;

    .placeholder-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 5px;
      margin-left: 10px;
    }
  }
  &-footer {
    position: absolute;
    right: 10px;
    bottom: 10px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>
