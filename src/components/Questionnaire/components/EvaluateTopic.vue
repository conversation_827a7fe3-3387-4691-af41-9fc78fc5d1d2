<template>
  <div class="evaluate-topic">
    <div class="delete-icon pointer" @click="deleteTopic">
      <el-icon><Delete /></el-icon>
    </div>
    <el-form :model="topicModel" ref="topicRef">
      <div class="evaluate-topic-title">
        <div class="evaluate-topic-title-left">
          <span class="m-b-5">{{ index + 1 }}.</span>
          <el-form-item
            prop="caption"
            :rules="[
              { required: true, message: '请输入标题', trigger: ['blur', 'change'] },
              { pattern: /^$|^.*\S.*$/, message: '请输入标题', trigger: ['blur', 'change'] }
            ]"
          >
            <div class="m-l-10 w-350">
              <el-input
                v-model="topicModel.caption"
                type="textarea"
                :autosize="{ minRows: 1 }"
                placeholder="请输入标题"
                resize="vertical"
                :disabled="disabled"
                maxlength="30"
                @keydown.enter.prevent
                @input="filterNewlines"
              />
            </div>
          </el-form-item>
        </div>
      </div>
      <div class="evaluate-topic-content">
        <div class="evaluate-topic-content-item" v-for="(item, idx) in topicModel.choices" :key="idx">
          <div class="evaluate-topic-content-item-input">
            <el-form-item
              :prop="`choices.${idx}.description`"
              :rules="[
                { required: true, message: '请输入选项内容', trigger: ['blur', 'change'] },
                { pattern: /^$|^.*\S.*$/, message: '请输入选项内容', trigger: ['blur', 'change'] }
              ]"
            >
              <div class="w-110 m-r-10">
                <el-input
                  v-model="item.description"
                  size="small"
                  :placeholder="`选项${idx + 1}`"
                  type="textarea"
                  :autosize="{ minRows: 1 }"
                  resize="vertical"
                  :disabled="disabled"
                  maxlength="15"
                  @keydown.enter.prevent
                  @input="filterNewlines"
                />
              </div>
              <el-rate v-model="score" disabled text-color="#FF9B45" />
            </el-form-item>
          </div>
          <div class="evaluate-topic-content-item-icon m-t-5">
            <div class="icon-style pointer" @click="addSelect(idx)">
              <el-icon><CirclePlusFilled /></el-icon>
            </div>
            <div class="icon-style pointer" v-if="topicModel.choices.length > 1" @click="deleteSelect(idx)">
              <el-icon><RemoveFilled /></el-icon>
            </div>
          </div>
        </div>
      </div>
      <div class="evaluate-topic-footer">
        <el-checkbox v-model="topicModel.required" :disabled="disabled">必填</el-checkbox>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, reactive } from "vue"

const props = defineProps({
  topic: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(["update:topic", "delete"])

// 深度复制一份 topic 以避免直接修改 props
const topicModel = reactive(JSON.parse(JSON.stringify(props.topic)))
import _ from "lodash"

// 监听 props 变化更新本地数据
watch(
  () => props.topic,
  (newVal, oldVal) => {
    if (!_.isEqual(newVal, oldVal)) {
      Object.assign(topicModel, JSON.parse(JSON.stringify(newVal)))
    }
  },
  { deep: true }
)

// 监听本地数据变化向父组件发送更新
watch(
  topicModel,
  (newVal) => {
    emit("update:topic", JSON.parse(JSON.stringify(newVal)))
  },
  { deep: true }
)

const topicRef = ref(null)
const score = ref(topicModel.top_score || 5)

// 监听评分变化
watch(score, (newVal) => {
  topicModel.top_score = newVal
})

const deleteTopic = () => {
  emit("delete", props.index)
}

const addSelect = (index) => {
  if (props.disabled) {
    return
  }

  // 添加一个新的评价选项
  const newItem = {
    description: "",
    order_in_list: index + 1
  }

  // 插入到指定位置
  topicModel.choices.splice(index + 1, 0, newItem)

  // 重新排序
  topicModel.choices.forEach((item, idx) => {
    item.order_in_list = idx
  })
}

const deleteSelect = (index) => {
  if (props.disabled || topicModel.choices.length <= 1) {
    return
  }

  // 删除指定位置选项
  topicModel.choices.splice(index, 1)

  // 重新排序
  topicModel.choices.forEach((item, idx) => {
    item.order_in_list = idx
  })
}

const handleValidate = () => {
  return new Promise((resolve) => {
    topicRef.value.validate((valid) => {
      if (valid) {
        resolve(true)
      } else {
        resolve(false)
      }
    })
  })
}

const filterNewlines = (value) => {
  inputText.value = value.replace(/\n/g, "") // 清除所有换行符
}

// 向父组件暴露验证方法
defineExpose({
  handleValidate
})
</script>

<style lang="scss" scoped>
.evaluate-topic {
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  .delete-icon {
    position: absolute;
    top: 10px;
    right: 10px;
  }
  &-title {
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    &-left {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
    }
    &-right {
      margin-left: 10px;
      color: #e7ecf2;
    }
  }
  &-content {
    margin-left: 23px;
    margin-top: 10px;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    &-item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      &-input {
        display: flex;
        align-items: baseline;
        justify-content: flex-start;
        margin-right: 10px;
        :deep(.el-form-item__content) {
          line-height: 30px;
        }
      }
      &-icon {
        width: 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .icon-style {
          font-size: 22px;
        }
      }
    }
  }
  &-footer {
    position: absolute;
    right: 10px;
    bottom: 10px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  :deep(.el-rate__icon) {
    font-size: 32px;
    margin-right: 10px;
  }
}
</style>
