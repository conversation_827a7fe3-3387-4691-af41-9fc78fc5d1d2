<template>
  <div class="verify-main" v-show="visible">
    <div placement="bottom" title="">
      <div class="verify-container" :style="{ width: `${width}px` }">
        <div class="verify-head">请完成安全认证</div>
        <div class="refresh" @click="refresh">
          <svg
            t="1637315258145"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="2420"
            width="20"
            height="20"
          >
            <path
              d="M960 416V192l-73.056 73.056a447.712 447.712 0 0 0-373.6-201.088C265.92 63.968 65.312 264.544 65.312 512S265.92 960.032 513.344 960.032a448.064 448.064 0 0 0 415.232-279.488 38.368 38.368 0 1 0-71.136-28.896 371.36 371.36 0 0 1-344.096 231.584C308.32 883.232 142.112 717.024 142.112 512S308.32 140.768 513.344 140.768c132.448 0 251.936 70.08 318.016 179.84L736 416h224z"
              p-id="2421"
              fill="#8a8a8a"
            />
          </svg>
        </div>
        <div class="pic">
          <img :src="bgImg" />
          <canvas class="canvas" ref="canvas" :width="width" :height="height" @click="createPointer" />
          <span
            class="pointer"
            v-for="(item, index) in pointer"
            :style="{ left: `${item.x}px`, top: `${item.y}px` }"
            :key="index"
          >
            <i>{{ index + 1 }}</i>
          </span>
        </div>
        <div :class="['toolbar', state]">
          <p v-if="state === 'fail'">验证失败</p>
          <p v-else-if="state === 'success'">验证通过</p>
          <p v-else>
            请顺序点击<span v-for="(item, index) in tips" :key="index">{{ "【" + item.character + "】" }}</span>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { cloneDeep } from "lodash"
import { onMounted, onUnmounted, ref, watch } from "vue"
import IcBgImg1 from "@/assets/login/ic_bg_img1.png"
import IcBgImg2 from "@/assets/login/ic_bg_img2.png"
import IcBgImg3 from "@/assets/login/ic_bg_img3.png"
import IcBgImg4 from "@/assets/login/ic_bg_img4.png"
import { nextTick } from "process"

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 320
  },
  height: {
    type: Number,
    default: 160
  },
  fontStrContent: {
    type: String,
    default:
      "赵钱孙李周吴郑王朱秦尤许何吕施张孔曹严华金魏陶姜戚谢邹喻柏水窦章云苏潘葛奚范彭郎鲁韦昌马苗凤花方俞任袁柳酆鲍史唐"
  },
  fontNum: {
    // 显示几个
    type: Number,
    default: 4
  },
  checkNum: {
    // 点击验证数
    type: Number,
    default: 2
  },
  accuracy: {
    // 精度
    type: Number,
    default: 15
  },
  isNumber: {
    type: Boolean,
    default: false
  },
  images: {
    type: Array,
    default: () => {
      return []
    }
  }
})
const emit = defineEmits(["success", "refresh"])

const bgImg = ref("") // 背景图
const ctx = ref() // 背景画笔
const fontArr = ref<Array<any>>([]) // 显示的字符
const tips = ref<Array<any>>([]) // 提示文字
const pointer = ref<Array<any>>([]) // 点击序号
const state = ref("") // success fail active
const timeIns = ref()
const imagesList = ref<Array<any>>([]) // 图片列表
const fontStr = ref("")
const answerList = ref<Array<any>>([])
const canvas = ref()

// 初始化
const init = () => {
  console.log("init", ctx.value, canvas.value)
  nextTick(() => {
    if (canvas.value) {
      ctx.value = canvas.value.getContext("2d")
    }
  })
  getImg()
}
const getImg = () => {
  const imagesLen = imagesList.value.length
  const randomIndex = Math.floor(Math.random() * imagesLen)
  bgImg.value = imagesList.value[randomIndex]
  draw()
  console.log(bgImg.value)
}
const draw = () => {
  fontStr.value = props.isNumber ? "0123456789" : props.fontStrContent
  // 设置结果集
  let resultList: any[] = []
  // 如果有答案，直接使用答案
  if (answerList.value.length > 0) {
    resultList = cloneDeep(answerList.value)
  }
  // 获取答案以外的随机字符
  for (let i = 0; i < props.fontNum - answerList.value.length; i++) {
    resultList.push(getRandomCharacter(resultList))
  }
  console.log("resultList", resultList, answerList.value)
  // 打乱结果集
  resultList = shuffleArray(resultList)
  // 给结果集增加随机xy坐标
  for (let i = 0; i < resultList.length; i++) {
    const character = resultList[i]
    console.log(character)
    const fontSize = randomNum(36, (props.height * 1) / 4)
    const fontWeight = "bold"
    const fontStyle = "italic"
    const fontFamily = "sans-serif"
    const x = (props.width / props.fontNum) * i + 10
    const y = Math.random() * (props.height - fontSize)
    if (ctx.value) {
      ctx.value.fillStyle = randomColor(0, 255)
      ctx.value.font = `${fontStyle} ${fontWeight} ${fontSize}px ${fontFamily}`
      ctx.value.textBaseline = "top"
      ctx.value.fillText(character, x, y)
    }
    fontArr.value.push({
      character,
      // fontSize,
      x,
      y
    })
  }

  console.log("this.fontArr", fontArr.value)
  // 展示结果集内容
  if (answerList.value.length > 0) {
    for (let i = 0; i < answerList.value.length; i++) {
      const randomIndex = fontArr.value.findIndex((item) => {
        return item.character === answerList.value[i]
      })
      const character = fontArr.value?.splice(randomIndex, 1)[0]
      tips.value.push(character)
      // console.log(character,fontArr.value)
    }
  } else {
    for (let i = 0; i < props.checkNum; i++) {
      const randomIndex = Math.floor(Math.random() * fontArr.value.length)
      const character = fontArr.value.splice(randomIndex, 1)[0]
      tips.value.push(character)
      // console.log(character,fontArr.value)
    }
  }
  console.log(tips.value)
}
// 获取随机字符
const getRandomCharacter = (list: Array<any>): string => {
  const fontStrLen = fontStr.value.length
  const randomIndex = Math.floor(Math.random() * fontStrLen)
  const character = fontStr.value.charAt(randomIndex)
  // debugger
  const isSome = list.some((item) => {
    console.log("isSome", item, character)
    return parseInt(item) === parseInt(character)
  })
  if (isSome) {
    console.log(`>>>${character}已存在>>>`)
    return getRandomCharacter(list)
  } else {
    return character
  }
}
const randomColor = (min: number, max: number) => {
  // let r = this.randomNum(min, max)
  // let g = this.randomNum(min, max)
  // let b = this.randomNum(min, max)
  const colors = ["#FF3030", "#FB32FF", "#FFFFFF", "#4AFF67", "#150F0D", "#9D50FF", "#12FFD4", "#FFF500", "#2238FF"]
  const color = colors[Math.floor(Math.random() * colors.length)]
  // return 'rgb(' + r + ',' + g + ',' + b + ')'
  return color
}
const randomNum = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min) + min)
}
const createPointer = (e: MouseEvent) => {
  // console.log(e)
  // const canvasRect = this.$refs.canvas.getBoundingClientRect();
  const x = e.offsetX - 15
  const y = e.offsetY - 15

  if (pointer.value.length < tips.value.length) {
    pointer.value.push({ x, y })
    // console.log(pointer.value.length)
    // this.verify()
    state.value = "active"
  }
  if (pointer.value.length === tips.value.length) {
    const isPass = verify()
    if (isPass) {
      state.value = "success"
      timeIns.value = setTimeout(() => {
        reset()
        emit("success", tips.value)
      }, 500)
    } else {
      state.value = "fail"
      // 如果失败则1000毫秒后重置
      timeIns.value = setTimeout(() => {
        reset()
      }, 1000)
    }
  }
}
// 判断精度
const verify = () => {
  console.log("验证")
  const result = pointer.value.every((item, index) => {
    const _left = item.x > tips.value[index].x - props.accuracy
    const _right = item.x < tips.value[index].x + props.accuracy
    const _top = item.y > tips.value[index].y - props.accuracy
    const _bottom = item.y < tips.value[index].y + props.accuracy
    return _left && _right && _top && _bottom
  })
  console.log(result)
  return result
}
// 重置
const reset = () => {
  fontArr.value = []
  tips.value = []
  pointer.value = []
  state.value = ""
  if (ctx.value) {
    ctx.value.clearRect(0, 0, props.width, props.height)
  }
  getImg()
}
// 打乱数据
const shuffleArray = (array: Array<any>) => {
  for (let i = array.length - 1; i > 0; i--) {
    // 生成一个从0到i的随机索引
    const j = Math.floor(Math.random() * (i + 1))
    // 交换当前元素与随机索引处的元素
    ;[array[i], array[j]] = [array[j], array[i]]
  }
  return array
}
// 刷新
const refresh = () => {
  emit("refresh")
}
// 设置答案
const setAnswerList = (list: Array<any>) => {
  if (!list || !Array.isArray(list)) {
    return
  }
  answerList.value = cloneDeep(list)
}
watch(
  () => props.visible,
  (newValue: any) => {
    console.log("newValue", newValue)
    if (newValue) {
      reset()
    }
  }
)

onMounted(() => {
  imagesList.value = [IcBgImg1, IcBgImg2, IcBgImg3, IcBgImg4]
  init()
})

onUnmounted(() => {
  reset()
  clearTimeout(timeIns.value)
})
defineExpose({ setAnswerList, reset })
</script>
<style lang="scss" scoped>
.verify-main {
  position: relative;

  :deep(.el-popover) {
    background-color: transparent;
    border: none !important;
    padding: 0 !important;
  }
}

.verify-container {
  /*border: 1px solid #e4e4e4;*/
  position: relative;
  overflow: hidden;
  user-select: none;
  z-index: 997;
  box-shadow: 0 0 11px 0 #999;
  border-radius: 5px;
  background-color: white;
}

.pic {
  position: relative;

  & img {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    width: 320px;
    height: 160px;
  }
}

.canvas {
  display: block;
}

.pointer {
  background: #1abd6c;
  border-radius: 50%;
  padding: 15px;
  position: absolute;
}

.pointer i {
  color: #fff;
  font-style: normal;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}

.toolbar {
  width: 100%;
  height: 40px;
  border: 1px solid #e4e4e4;
  background: #f7f7f7;
  color: #666;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.toolbar.active {
  color: #fff;
  background: #1991fa;
  border: 1px solid #1991fa;
}

.toolbar.success {
  color: #fff;
  background: #52ccba;
  border: 1px solid #52ccba;
}

.toolbar.fail {
  color: #fff;
  background: #f57a7a;
  border: 1px solid #f57a7a;
}

.refresh {
  display: flex;
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 2;
  cursor: pointer;
}

.verify-head {
  padding: 10px;
  background-color: white;
}
</style>
