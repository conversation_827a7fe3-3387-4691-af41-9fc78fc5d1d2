<template>
  <div class="img-viewer-box">
    <el-image-viewer v-if="state.visible" :url-list="props.imgs" @close="close" :initial-index="currentIndex" />
  </div>
</template>
<script lang="ts" setup>
import { reactive } from "vue"
import { useVModel } from "@vueuse/core"
const props = defineProps<{
  modelValue: boolean
  imgs: string[]
  currentIndex: number
}>()
const emits = defineEmits(["update:modelValue"])
const state = reactive({
  imgList: [],
  // 相当于是set 与 get
  visible: useVModel(props, "modelValue", emits)
})
// 点击关闭的时候，连同小图一起关闭
function close() {
  state.visible = false
}
</script>
