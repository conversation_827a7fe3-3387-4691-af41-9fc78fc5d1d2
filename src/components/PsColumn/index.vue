<template>
  <el-table-column v-for="(item, index) in tableHeaders" :key="index" v-bind="item">
    <template #default="{ row, $index }">
      <!-- 如果有配置多级表头的数据，则递归该组件 -->
      <template v-if="item.children && item.children.length > 0">
        <!--多级表头-->
        <!-- <el-table-column v-for="(subItem, subIndex) in item.children" :key="subIndex" v-bind="subItem" /> -->
        <!--占位空div 去掉多表头无法显示正常-->
        <div />
        <PsColumn :table-headers="item.children && item.children.length > 0 ? item.children : []" :key="$index" />
      </template>
      <div v-else-if="item.type == 'price'">
        <span>{{ row[item.prop!] ? "￥" + formatPrice(row[item.prop!]) : "￥0" }}</span>
      </div>
      <div v-else-if="typeof item.inner == 'string'" v-dompurify-html="item.inner">
        <!--html-->
      </div>
      <component v-else-if="item.render" :is="item.render" :row="row" :index="$index" />
      <slot v-else-if="item.slot" :row="row" :index="$index" :name="item.slot">
        <!--slot-->
      </slot>
      <span v-else-if="row.isExpanded" class="inline-flex items-center">
        <!--有展开数据-->
        <img :src="row.isExpanded ? IcArrowUP : IcArrowDown" v-if="getTreeImg(row, item, index)" class="m-r-5px" />
        <div>
          {{ row[item.prop!] || row[item.prop!] === 0 ? row[item.prop!] : "--" }}
        </div>
      </span>
      <!--没有数据-->
      <span v-else>{{
        row[item.prop!] || row[item.prop!] === 0 ? row[item.prop!] + (item.unit ? item.unit : "") : "--"
      }}</span>
    </template>
  </el-table-column>
</template>
<script lang="ts">
import { defineComponent } from "vue"
import { divide } from "@/utils/index"
export default defineComponent({
  name: "PsColumn"
})
export interface TableColumn {
  key?: string
  label?: string
  prop?: string
  children?: TableColumn[]
  inner?: string
  render?: any
  slot?: any
  type?: string
  width?: string | number
  unit?: string
}
export type Mapper<T> = {
  [P in keyof T as string]?: string | object
}
</script>
<script lang="ts" setup>
import { ref, onMounted, onBeforeUpdate, useSlots, watch, defineOptions } from "vue"
import IcArrowUP from "@/assets/images/ic_arrow_up.png"
import IcArrowDown from "@/assets/images/ic_arrow_down.png"
// 定义名字
defineOptions({
  name: "PsColumn"
})

// 格式化价格
const formatPrice = (val: any) => {
  let price = divide(val)
  return divide(val)
}

const slots = useSlots()

const props = defineProps<{
  tableHeaders: TableColumn[]
}>()
const newTableHeaders = ref<any>({})
const getNewTableHeaders = () => {
  newTableHeaders.value = { ...props.tableHeaders }

  const rawAttr = props.tableHeaders
  for (const key in rawAttr) {
    const column = rawAttr[key]
    if (typeof column === "string") {
      Reflect.set(newTableHeaders.value, key, {
        label: column,
        prop: key,
        key: key
      })
    }
    // 其实此时一定是对象了，此处判断是用于ts类型收窄
    if (typeof column === "object") {
      // 设置默认的key
      if (!Reflect.has(column, "key")) {
        Reflect.set(column, "key", key)
      }
      if (!Reflect.has(column, "label")) {
        Reflect.set(column, "label", key)
      }
      // 设置默认的prop, 如果该列是多选项，则不需要prop
      if (
        !Reflect.has(column, "prop") &&
        !(Reflect.has(column, "type") && Reflect.get(column, "type") === "selection")
      ) {
        Reflect.set(column, "prop", key)
      }
      // 处理插槽
      const slotKeys = Object.keys(slots)

      for (const key of slotKeys) {
        const res = key.match(/^(\S+)-(\S+)/)
        // 查找不到则为null
        // 这里做了一个处理，定位到slot里面，将原来的slot 配置成{}
        if (res && res[2] === Reflect.get(column, "slot")) {
          Reflect.set(column, "slot", {})
          if (column.slot) {
            Reflect.set(column.slot, res[1], res[0])
          }
        }
      }
    }
  }
}
// 获取树形表格图标展示
const getTreeImg = (row: any, item: any, index: number) => {
  // 第一个列
  if (index === 0) {
    const hasChildren = Reflect.has(row, "hasChildren") ? row.hasChildren : false
    return hasChildren
  }
  return false
}

onMounted(() => {
  // 获取表格的列
  // getNewTableHeaders();
  console.log("noMounted")
})
onBeforeUpdate(() => {
  // 获取表格的列
  // getNewTableHeaders();
})

watch(
  () => props.tableHeaders,
  (newValue, oldValue) => {
    console.log("newValue", newValue, oldValue)
  }
)
</script>

<style lang="scss">
.el-table__expand-icon {
  position: absolute;
  left: 0;
  width: 30px !important;
  height: 24px !important;
  line-height: 24px !important;

  .el-icon svg {
    display: none;
  }
}
</style>
