<script lang="ts" setup>
import to from "await-to-js"
import { apiDepartmentListPost } from "@/api/user"
import { ref, watch, watchEffect } from "vue"

const emit = defineEmits(["update:modelValue", "change"])

const props = defineProps({
  modelValue: {
    type: [String, Number, Array]
  },
  multiple: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  collapseTags: {
    type: <PERSON>olean,
    default: true
  },
  checkStrictly: {
    type: Boolean,
    default: true
  }
})

const defaultProps = {
  isLeaf: "has_children",
  label: "group_name"
}

const departmentValue = ref(props.modelValue)

watch(
  () => ({
    modelValue: props.modelValue
  }),
  (newValue) => {
    console.log(newValue.modelValue, "newValue.modelValue")
    departmentValue.value = newValue.modelValue
  }
)

watchEffect(() => {
  emit("update:modelValue", departmentValue.value)
  // emit("change", departmentValue.value);
})

// const changeValue = async (e: Event) => {
//   emit("update:modelValue", departmentValue.value);
//   emit("change", departmentValue.value);
// };

// 动态加载远程数据
const loadDepartment = async (node: any, resolve: any) => {
  console.log(node, "node666")
  const params: any = {
    status: "enable",
    page: 1,
    page_size: 99999
  }
  if (node.level) {
    params.parent = node.data.id
  } else {
    params.level = node.level
  }
  const [err, res]: any[] = await to(apiDepartmentListPost(params))
  if (err) {
    resolve([])
    return
  }
  if (res.code === 0) {
    const data: any = res.data.results.map((item: any) => {
      item.has_children = !item.has_children // 不知道为啥官方是反着来的，true是没有节点。。。
      return item
    })
    console.log(data, "data")
    resolve(data)
  } else {
    resolve([])
  }
}
// 节点点击监听
const treeHandleNodeClick = (e: any) => {
  console.log(e, "treeHandleNodeClick")
}
// 节点选中监听
const checkChange = (checkedNodesData: any, checkedKeysData: any) => {
  console.log("checkChange", checkedNodesData, "checkedKeys", checkedKeysData)
  const checkedKeys = Reflect.has(checkedKeysData, "checkedKeys") ? checkedKeysData.checkedKeys : []
  emit("change", props.multiple ? checkedKeys : checkedKeys[0] || null)
}
</script>
<template>
  <div>
    <el-tree-select
      v-model="departmentValue"
      :props="defaultProps"
      :load="loadDepartment"
      :multiple="multiple"
      :disabled="disabled"
      :collapse-tags="collapseTags"
      node-key="id"
      lazy
      show-checkbox
      class="w-full"
      v-on="$attrs"
      :check-strictly="checkStrictly"
      @node-click="treeHandleNodeClick"
      @check="checkChange"
    />
  </div>
</template>
