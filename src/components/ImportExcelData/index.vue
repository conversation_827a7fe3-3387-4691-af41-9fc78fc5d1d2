<script lang="ts" setup>
import { ElMessage } from "element-plus"
import * as XLSX from "xlsx"
import to from "await-to-js"
import { exportHandle } from "@/utils/exportExcel"
import { apiBackgroundFileChannelUploadFile } from "@/api"
import { reactive, ref, toRefs, computed } from "vue"
import { cloneDeep } from "lodash"
import ParseExcel from "../ParseExcel/index2.vue"

const emit = defineEmits(["close", "confirm"])

const props = defineProps({
  importType: {
    type: String
  },
  templateUrl: {
    type: String
  },
  headerLen: {
    // 设置模板头部有多少行，必须设，不然没法算, 目前只支持三级合并表头，即tableSetting的children的deep为3
    type: Number,
    default: 0
  },
  delExampleLen: {
    // 删除前几行示例数据，一般都是不传给后端的，可以自己沟通
    type: Number,
    default: 0
  },
  isDeleteTopTips: {
    // 是否删除第一行的提示，这种情况下，一般第二行是表头，每个表可能都不一样，要重点关注配置这个参数
    type: Boolean,
    default: false
  },
  // 是否显示操作栏
  showOperation: {
    type: Boolean,
    default: false
  },
  importApi: Function,
  // 下面保存按钮的名字
  btnConfirmTxt: {
    type: String,
    default: "保存"
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true
  },
  tipStepTxt1: {
    type: String,
    default: "下载模板数据"
  },
  tipStepTxt2: {
    type: String,
    default: "导入文件"
  },
  isReturnJson: {
    // 是否返回json
    type: Boolean,
    default: false
  }
})

// 变量定义
const data = reactive({
  loading: false,
  uploadFileName: ""
})

let { loading, uploadFileName } = toRefs(data)

const tableData = ref<Array<object>>([])
const uploadParamsKey = ref<String>()

const importConfig = reactive({
  allData: ref<Array<object>>([]),
  currentPageData: ref<Array<object>>([]),
  totalCount: 0,
  currentPage: 1,
  pageSize: 10
})

// interface ExcelValue {
//   [key: string]: any // 使用索引签名来允许任意键
// }
// const excelObj = reactive({
//   value: {} as ExcelValue // 明确指定value的类型
// })

const xlsxAllData = ref<any>([])
const tableSetting = ref<any>([])
const mergesList = ref<any>([])

const getXlsxData = (workbook: any, name: any) => {
  console.log("name._value", name._value.split(".")[1])
  uploadFileName.value = props.importType + "." + name._value.split(".")[1]
  // excelObj.value = {};
  // tableSetting.value.forEach((item: any) => {
  //   excelObj.value[item.label] = item.key;
  // });
  // json.splice(0, 1);
  // let result = json.map((item: any) => {
  //   let data = {} as ExcelValue;
  //   for (let key in item) {
  //     if (excelObj.value[key]) {
  //       data[excelObj.value[key]] = item[key];
  //     }
  //   }
  //   return data;
  // });
  // // 处理数据
  // importConfig.allData = [];
  // result.map((item: any) => {
  //   let flag = true;
  //   for (let key in item) {
  //     if (item[key].toString().match(/^[ ]*$/)) {
  //       flag = false;
  //     }
  //   }
  //   if (flag) {
  //     importConfig.allData.push(item);
  //   }
  // });
  tableSetting.value = []
  importConfig.allData = []
  const firstSheetName = workbook.SheetNames[0]
  const sheet = workbook.Sheets[firstSheetName]
  console.log("sheetsheetsheet", sheet)
  let result = getHeaderRows(sheet)
  setHeaderOperation(result.headerSetting)
  console.log("result", result)
  tableSetting.value = result.headerSetting
  setTableData(result)
  setXlsxCurrentPageData()
}

// 读取xlsx内容并初始化
const getHeaderRows = (sheet: any) => {
  const range = XLSX.utils.decode_range(sheet["!ref"])
  console.log("range", range)
  let xlsxData: any = []
  for (let R = range.s.r; R <= range.e.r; ++R) {
    xlsxData[R] = []
    for (let C = range.s.c; C <= range.e.c; ++C) {
      let cellAddress = { c: C, r: R }
      /* if an A1-style address is needed, encode the address */
      let cellRef = XLSX.utils.encode_cell(cellAddress)
      let dd = {
        cell_ddress: cellAddress,
        cell_ref: cellRef,
        data: sheet[cellRef] ? sheet[cellRef].w : null
      }
      xlsxData[R][C] = dd
    }
  }
  if (props.delExampleLen) {
    xlsxData.splice(props.headerLen + 1, props.delExampleLen)
  }
  console.log(1111111122222, xlsxData)
  xlsxAllData.value = cloneDeep(xlsxData)
  let mergeObj: any = {}
  let headerSetting = [] // 表格头
  mergesList.value = sheet["!merges"] ? sheet["!merges"] : []
  console.log(mergesList.value)
  if (mergesList.value.length > 0) {
    // 有合并表头的
    range.e.r = Math.min(range.e.r, props.headerLen) // 重新设下当前行哪个小拿哪个
    for (let R = range.s.r; R <= range.e.r; ++R) {
      mergeObj[R] = []
      mergesList.value.forEach((item: any) => {
        if (item.e.r === R) {
          mergeObj[R].push(item)
        }
      })
    }
    for (let R = range.s.r; R <= range.e.r; ++R) {
      let last = range.e.r - R // 倒序进行赋值
      if (!headerSetting.length) {
        headerSetting = xlsxData[last].map((v: any, i: any) => {
          return { label: v.data, key: "item-" + i }
        })
        let setting = cloneDeep(headerSetting)
        mergeObj[last].forEach((k: any) => {
          if (k.s.c === k.e.c) {
            // 处理合并列 col
            let start = k.s.c
            let end = k.e.c
            let len = end - start + 1
            setting.splice(k.s.c, len, {
              label: xlsxData[k.s.r][k.s.c].data,
              key: "item-" + k.s.c
            })
          }
        })
        headerSetting = cloneDeep(setting)
      } else {
        let setting: any = cloneDeep(headerSetting)
        let reduceLen = 0
        mergeObj[last].forEach((k: any) => {
          if (k.s.r === k.e.r) {
            // 处理合并行 row
            let start = k.s.c - reduceLen
            let end = k.e.c - reduceLen
            let len = end - start + 1
            reduceLen += len - 1
            let mmm = setting.slice(start, end + 1)
            if (!props.isDeleteTopTips) {
              // 这里的情况是表格第一行是文字提示，第二行是表头，并且要删除提示行，目前后端都默认删除提示行进行读取
              setting.splice(start, len, {
                label: xlsxData[last][k.s.c].data,
                key: "merge-" + k.s.c,
                children: mmm
              })
            }
          } else if (k.s.c === k.e.c) {
            // 这种情况没考虑好，先不做
            // let start = k.s.c
            // let end = k.e.c
            // let len = end - start + 1
            // setting.splice(k.s.c, len, { label: xlsxData[k.s.r][k.s.c].data, key: k.s.c })
          }
        })
        headerSetting = cloneDeep(setting)
      }
    }
    return {
      headerSetting: headerSetting,
      data: xlsxData.slice(props.headerLen + 1, xlsxData.length)
    }
  } else {
    // 没有合并表头的
    headerSetting = xlsxData.splice(0, 1)[0].map((item: any, index: any) => {
      let key = "item-" + index
      return {
        label: item.data,
        key: key
      }
    })
    return {
      headerSetting: headerSetting,
      data: xlsxData
    }
  }
}

// 添加操作栏
const setHeaderOperation = (data: any) => {
  // 统一加
  if (props.showOperation) {
    data.push({
      label: "操作",
      key: "operation",
      type: "operation",
      align: "center",
      fixed: "right"
    })
  }
}

// 设置table数据
const setTableData = (result: any, type?: any) => {
  if (type) {
    // console.log("type", type);
    // this.updateTableData(result)
  } else {
    // nextTick(() => {
    result.data.map((v: any) => {
      let obj: any = {}
      let isSet = false
      v.map((k: any, j: any) => {
        if (k.data !== null) {
          isSet = true
        }
        obj["item-" + j] = k.data
      })
      if (isSet) {
        importConfig.allData.push(obj)
      }
    })
    // })
    // if (this.uploadStep === 'read') {
    //   this.tableSetting = result.headerSetting
    //   this.$nextTick(_ => {
    //     result.data.map((v:any, index:any) => {
    //       let obj = {}
    //       let isSet = false
    //       v.map((k, j) => {
    //         if (k.data !== null) {
    //           isSet = true
    //         }
    //         obj['item-' + j] = k.data
    //       })
    //       if (isSet) {
    //         importConfig.allData.push(obj)
    //       }
    //     })
    //   })
    // } else {
    //   // result.headerSetting[0].key = 'result'
    //   // result.headerSetting.splice(this.resultIndex, 1) // 删除第一个结果的表头
    //   this.$nextTick(_ => {
    //     let resultIndex = 0
    //     for (let index = 0; index < result.headerSetting.length; index++) {
    //       const item = result.headerSetting[index];
    //       if (item.label === '结果') {
    //         resultIndex = index
    //         break
    //       }
    //     }
    //     result.headerSetting.splice(resultIndex, 1)
    //     this.tableSetting = result.headerSetting
    //     this.resultData = result.data.map((v:any) => {
    //       let obj = {}
    //       let vlen = v.length
    //       v.map((k, j) => {
    //         if (resultIndex === -1) { // -1是特殊情况需要特别处理
    //           if (j === (vlen - 1)) {
    //             obj.result = k.data
    //           } else {
    //             obj['item-' + j] = k.data
    //           }
    //         } else {
    //           if (j === resultIndex) {
    //             obj.result = k.data
    //           } else {
    //             obj['item-' + j] = k.data
    //           }
    //         }
    //       })
    //       return obj
    //     })
    //     this.clickTabHandler(this.tabType)
    //   })
    // }
  }
}

// 根据导入的数据进行分页显示
const setXlsxCurrentPageData = () => {
  console.log("allExcelData", importConfig.allData, importConfig.allData)
  let start = (importConfig.currentPage - 1) * importConfig.pageSize
  let end =
    importConfig.allData.length > importConfig.pageSize
      ? (importConfig.currentPage - 1) * importConfig.pageSize + importConfig.pageSize
      : importConfig.allData.length
  console.log("(start, end)", start, end)
  importConfig.totalCount = importConfig.allData.length
  importConfig.currentPageData = importConfig.allData.slice(start, end)
  tableData.value = importConfig.currentPageData
  console.log("tableData.value", importConfig)
}

// 翻页
const handleSizeChange = (val: number) => {
  importConfig.pageSize = val
  setXlsxCurrentPageData()
}
const handleCurrentChange = (val: number) => {
  importConfig.currentPage = val
  setXlsxCurrentPageData()
}

// 移除
const deleteImportData = (row: any, index: number) => {
  let deleteIndex = importConfig.currentPage * importConfig.pageSize - importConfig.pageSize + index
  importConfig.allData.splice(deleteIndex, 1)
  // 重置分页数据
  // checkedCurrentPage(importConfig.allData, importConfig);
  if (importConfig.currentPage > 1) {
    let lit = importConfig.allData.length % importConfig.pageSize
    if (lit < 1) {
      importConfig.currentPage--
    }
  }
  let headLenth = props.headerLen ? props.headerLen : 1
  let deleteCurIndex = deleteIndex + headLenth
  if (deleteCurIndex < xlsxAllData.value.length) {
    xlsxAllData.value.splice(deleteCurIndex, 1)
  }
  setXlsxCurrentPageData()
}

// 检查当前页码是否满足分页
// const checkedCurrentPage = (data: any, opts: any) => {
//   if (opts.currentPage > 1) {
//     let lit = data.length % opts.pageSize;
//     if (lit < 1) {
//       opts.currentPage--;
//     }
//   }
// };

// 导入
const importHandle = () => {
  if (loading.value) return
  if (importConfig.allData.length === 0) {
    return ElMessage.error("暂无数据，请导入数据")
  }
  loading.value = true
  let result: any = []
  xlsxAllData.value.forEach((item: any) => {
    let obj: any = []
    let isSet = false
    item.forEach((v: any) => {
      if (v.data !== null) {
        isSet = true
      }
      obj.push(v.data)
    })
    if (isSet) {
      result.push(obj)
    }
  })
  jsonToXlsx(aoaToXlsx(result, mergesList.value), result)
}

const aoaToXlsx = (json: any, merges: any) => {
  let wsname = "Sheet1"
  let wb = XLSX.utils.book_new()
  let ws = XLSX.utils.aoa_to_sheet(json)
  XLSX.utils.book_append_sheet(wb, ws, wsname)
  if (merges) ws["!merges"] = merges
  let opts: any = {
    bookType: "xlsx", // 要生成的文件类型
    bookSST: false,
    type: "binary" // 二进制格式
  }
  let wbout = XLSX.write(wb, opts) // 生成xlsx格式的数据
  let xlsxblob = new Blob([s2ab(wbout)], {
    // 生成数据流格式
    type: "application/octet-stream"
  })
  // 字符串转ArrayBuffer
  return xlsxblob
}
const s2ab = (s: any) => {
  var buf = new ArrayBuffer(s.length)
  var view = new Uint8Array(buf)
  for (var i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xff
  return buf
}

const jsonToXlsx = async (json: any, result: any) => {
  console.log(
    "jsonToXlsx",
    json,
    "result",
    result,
    "importConfig.allData",
    importConfig.allData,
    "xlsxAllData",
    xlsxAllData.value
  )

  if (props.isReturnJson) {
    loading.value = false
    emit("confirm", result)
    return
  }
  uploadParamsKey.value = String(new Date().getTime()) + uploadFileName.value
  loading.value = true
  const [err, res]: any = await to(
    apiBackgroundFileChannelUploadFile({
      key: uploadParamsKey.value,
      file: json
    })
  )
  let { code, msg } = res!
  loading.value = false
  if (err) {
    return
  }
  if (code === 0) {
    checkUploadResult(res.data.public_url)
  } else {
    ElMessage.error(msg)
  }
}
const checkUploadResult = (publicUrl: any) => {
  if (!props.importType || !publicUrl) {
    ElMessage.error("请设置打开的页面参数！")
    return
  }
  const option = {
    type: props.importType,
    api: props.importApi,
    params: {
      oss_url: publicUrl
    }
  }
  loading.value = false
  exportHandle(option)
  emit("close", false)
}

const downloadTemplate = () => {
  window.open(props.templateUrl)
}
const maxHeight = computed(() => {
  return window.innerHeight / 2 + 100 + "px"
})

const cancelDrawerHandle = () => {
  emit("close", false)
}
// 清除表格数据
const clearTable = () => {
  tableData.value = []
  importConfig.totalCount = 0
  importConfig.currentPage = 1
  importConfig.pageSize = 10
  importConfig.allData = []
}

defineExpose({
  clearTable
})

// watchEffect(() => {
//   emit("importData", importConfig.allData);
// });
</script>
<template>
  <div class="import-excel-data">
    <div class="header-info info-item">
      <div class="header-info-item header-title yellow-tips" v-if="isShowTip">
        注意：导入的数据模板，需要按照平台提供的模板进行！导入前，请检查导入表格的格式和内容
      </div>
      <div class="header-info-item line-num">
        <div class="line-num-item m-t-3">
          <div class="active-num num">1</div>
          <div class="active-text-color m-l-5 m-r-5 w-150px">{{ tipStepTxt1 }}</div>
          <el-button type="primary" size="small" @click="downloadTemplate">下载模板</el-button>
        </div>
        <!--<div class="line" />-->
        <div class="line-num-item m-t-30">
          <!--<div :class="['num', tableData.length ? 'active-num' : '']">2</div>
          <div :class="[tableData.length ? 'active-text-color' : 'text-color']" class="m-l-5 m-r-5 w-150px">
            {{ tipStepTxt2 }}
          </div>-->
          <div class="active-num num">2</div>
          <div class="active-text-color m-l-5 m-r-5 w-150px">
            {{ tipStepTxt2 }}
          </div>
          <parse-excel titleName="选择本地文件" :initial="true" @excel="getXlsxData">选择本地文件</parse-excel>
        </div>
      </div>
    </div>
    <el-table ref="tableRef" :data="tableData" header-row-class-name="ps-table-header-row" :max-height="maxHeight">
      <template v-for="item in tableSetting" :key="item.key">
        <el-table-column :label="item.label" :prop="item.key" :fixed="item.fixed" align="center">
          <template v-if="item.children">
            <template v-for="child in item.children" :key="child.key">
              <el-table-column :label="child.label" :prop="child.key" align="center" />
            </template>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" width="60" align="center" v-if="tableSetting.length">
        <template #default="scope">
          <el-button link plain size="small" color="#00ca79" @click="deleteImportData(scope.row, scope.$index)"
            >移除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="ps-pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="importConfig.currentPage"
        :page-sizes="[10, 20, 50, 100, 500]"
        :page-size="importConfig.pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="importConfig.totalCount"
        background
        class="ps-text"
      />
    </div>
    <div class="import-footer">
      <el-button plain @click="cancelDrawerHandle">取消</el-button>
      <el-button type="primary" v-loading="loading" @click="importHandle">{{ btnConfirmTxt }}</el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.import-excel-data {
  .header-info {
    .header-info-item {
      padding: 6px 10px;
      font-size: 14px;
      border-right: 1px solid #e6e8eb;
      border-bottom: 1px solid #e6e8eb;
      border-left: 1px solid #e6e8eb;
    }

    .header-title {
      background-color: #f7f9fa;
    }

    .line-num {
      .line-num-item {
        display: flex;
        align-items: center;

        .num {
          width: 30px;
          height: 30px;
          line-height: 30px;
          color: #fff;
          text-align: center;
          background-color: #dfe5eb;
          border-radius: 20px;
        }

        .active-num {
          background-color: var(--el-color-primary);
        }

        .text-color {
          color: #bbbdbf;
        }

        .active-text-color {
          color: #1e2224;
        }
      }

      .line {
        height: 40px;
        margin: 8px 15px;
        border-left: 1px #dae1eb solid;
      }
    }
  }

  .import-footer {
    position: absolute;
    left: 20px;
    bottom: 20px;
    background-color: #fff;
    z-index: 99;
  }
}
</style>
