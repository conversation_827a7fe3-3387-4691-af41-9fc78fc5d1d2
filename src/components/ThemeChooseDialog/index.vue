<template>
  <div>
    <el-drawer
      v-model="dialogVisible"
      :title="dialogTitle"
      size="408px"
      direction="rtl"
      @close="closeDialog"
      class="ps-drawer"
      :close-on-click-modal="false"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="ruleFormRef"
        label-width="auto"
        class="from-container"
        label-position="right"
        v-loading="loading"
      >
        <!-- 单位-->
        <el-form-item label="使用单位：" prop="name">
          <div>{{ formData.name }}</div>
        </el-form-item>
        <el-form-item label="系统颜色：" prop="themeColor" class="m-t-10px">
          <div class="color-item">
            <div
              v-for="(item, index) in themeColorList"
              :class="['', index > 0 ? 'm-l-20px' : '', formData.themeColor == item.themeColor ? 'active' : '']"
              :key="index"
              @click="handlerThemeChange(item)"
            >
              <div class="color-item-box" :style="{ background: item.color }">
                <img
                  :src="getImgByName('ic_theme_active', formData.themeColor)"
                  v-if="item.themeColor == formData.themeColor"
                />
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="LoGo：" prop="logo">
          <el-upload
            v-model:file-list="formData.picList"
            :http-request="handleUpload"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :before-upload="beforeUpload"
            :show-file-list="false"
            class="avatar-uploader"
          >
            <img v-if="formData.logo" :src="formData.logo" class="avatar" />
            <div v-else>
              <el-icon class="avatar-uploader-icon">
                <Plus />
              </el-icon>
              <span class="pic-tip">提示：logo建议上传png格式，尺寸为200*60，不大于1M</span>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDialog">取消</el-button>
          <el-button
            type="primary"
            @click="
              (e: MouseEvent) => {
                confirmDialog(e)
              }
            "
            >保存</el-button
          >
        </div>
      </template>
    </el-drawer>
    <el-dialog v-model="dialogVisiblePreview">
      <img w-full :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, reactive } from "vue"
import type { ThemeForm } from "./index.d"
import { apiBackgroundFundSupervisionAuditAccountAccountSetPost } from "@/api/login"
import to from "await-to-js"
import type { FormRules, UploadUserFile, UploadRequestOptions } from "element-plus"
import { ElMessage } from "element-plus"
import { getSuffix } from "@/utils"
import { apiBackgroundFileChannelUploadFile } from "@/api"
import { getImgByName, useTheme, type ThemeName } from "@/hooks/useTheme"
import { useUserStore } from "@/store/modules/user"
import { Plus } from "@element-plus/icons-vue"

const userStore = useUserStore()

const prop = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dialogData: {
    type: Object,
    default: () => {
      return {}
    }
  },
  dialogTitle: {
    type: String,
    default: "系统设置"
  },
  direction: {
    type: String,
    default: "rtl"
  },
  dialogType: {
    type: String,
    default: "add"
  }
})
// 颜色列表
const themeColorList = ref<Array<ThemeForm>>([
  {
    name: "蓝色",
    color: "#2F77FF",
    themeColor: "blue"
  },
  {
    name: "橙色",
    color: "#FF8B1E",
    themeColor: "orange"
  },
  {
    name: "绿色",
    color: "#00B482",
    themeColor: "green"
  }
])
// 表单ref
const ruleFormRef = ref()
// 表单数据
const formData = reactive<ThemeForm>({
  name: "中国银行",
  themeColor: "blue",
  picList: [],
  logo: ""
})
// 表单校验
const rules = reactive<FormRules>({
  name: [
    {
      required: true,
      message: "请选择用户",
      trigger: "change"
    }
  ]
})
// 加载中
const loading = ref(false)
// 是否显示弹窗
const dialogVisible = ref(false)
// 弹窗事件
const emit = defineEmits([
  "closeDialog", // 弹窗关闭
  "confirmDialog" // 弹窗确认
])
// 图片列表
const picList = ref<UploadUserFile[]>([])
// 预览图片
const dialogImageUrl = ref("")
// 是否显示预览
const dialogVisiblePreview = ref(false)
// 关闭弹窗
const closeDialog = () => {
  console.log("closeDialog 111111")
  dialogVisible.value = false
  emit("closeDialog")
}
// 取消
const cancelDialog = () => {
  closeDialog()
}
// 确认弹窗
const confirmDialog = async (e: MouseEvent) => {
  console.log("confirmDialog", ruleFormRef.value)
  if (ruleFormRef.value) {
    ruleFormRef.value.validate(async (valid: any) => {
      if (valid) {
        saveTheme(formData, e)
      }
    })
  }
}

// 删除图片
const handleRemove = (file: any, fileList: any) => {
  console.log("handleRemove", file, fileList)
  // 如果要调用接口删除使用这里
}

// 预览图片
const handlePictureCardPreview = (uploadFile: any) => {
  dialogImageUrl.value = uploadFile.url!
  dialogVisiblePreview.value = true
  console.log("handlePictureCardPreview", uploadFile)
}

const beforeUpload = (file: File) => {
  console.log("beforeUpload", file)
  const uploadType = [".jpeg", ".jpg", ".png", ".bmp"]
  if (!uploadType.includes(getSuffix(file.name))) {
    ElMessage.error("请检查上传图片格式文件！")
    return false
  }

  if (file.size > 2 * 1048 * 1048) {
    ElMessage.warning("上传图片不能大于2M")
    return false
  }
  return true
}
// 上传文件
const handleUpload = async (options: UploadRequestOptions) => {
  console.log("handleUpload", options)
  const name = options.file.name
  const [err, res]: any[] = await to(
    apiBackgroundFileChannelUploadFile({
      file: options.file,
      key: name,
      prefix: "logo"
    })
  )
  if (err) {
    picList.value.splice(picList.value.length - 1, 1)
    return
  }
  if (res && res.code === 0) {
    const url = res.data?.public_url || ""
    formData.logo = url
    console.log("handleUpload", url, picList.value)
    // 上传成功需手动替换文件路径为远程URL，否则图片地址为预览地址 blob:http://
    const fileIndex = picList.value.findIndex((file) => file.uid == (options.file as any).uid)
    picList.value.splice(fileIndex, 1, {
      name: name,
      url: url,
      uid: options.file.uid,
      status: "success"
    } as UploadUserFile)
  } else {
    ElMessage.error("上传失败")
    picList.value.splice(picList.value.length - 1, 1)
  }
}

// 保存主题
const saveTheme = async (data: any, e: MouseEvent) => {
  console.log("saveTheme", data)
  const params = {
    color: data.color,
    logo_url: data.logo
  }
  console.log("params", params)
  const [err, res] = await to(apiBackgroundFundSupervisionAuditAccountAccountSetPost(params))
  if (err) {
    emit("confirmDialog", false)
    return
  }
  if (res && res.code === 0) {
    ElMessage.success("保存成功")
    confirmChangeTheme(e, data.themeColor)
    emit("confirmDialog", true)
  } else {
    ElMessage.error(res.msg || "保存失败")
    //
    emit("confirmDialog", false)
  }
}

// 颜色选择
const handlerThemeChange = (val: any) => {
  console.log("handlerThemeChange", val)
  formData.themeColor = val.themeColor
  formData.color = val.color
}
// 初始化数据
const initData = () => {
  const themeName = useTheme().activeThemeName
  const userInfo = userStore.getUserInfo
  formData.color = userInfo?.color
  formData.logo = userInfo?.logo_url
  formData.name = userInfo?.supervision_channel_name
  formData.themeColor = themeName.value === "normal" ? "blue" : themeName.value
  formData.picList = userInfo?.logo_url ? [{ url: userInfo?.logo_url }] : []
  console.log("userInfo", userInfo)
}
// 切换主题
const confirmChangeTheme = ({ clientX, clientY }: MouseEvent, themeName: ThemeName) => {
  const maxRadius = Math.hypot(
    Math.max(clientX, window.innerWidth - clientX),
    Math.max(clientY, window.innerHeight - clientY)
  )
  const style = document.documentElement.style
  style.setProperty("--v3-theme-x", clientX + "px")
  style.setProperty("--v3-theme-y", clientY + "px")
  style.setProperty("--v3-theme-r", maxRadius + "px")
  const handler = () => {
    const userInfo = userStore.getUserInfo
    userInfo.logo_url = formData.logo || userInfo.logo_url
    userInfo.color = formData.color || ""
    userStore.setUserInfo(userInfo)
    useTheme().setTheme(themeName)
  }
  if (document && "startViewTransition" in document) {
    // @ts-expect-error
    document.startViewTransition(handler)
  } else {
    handler()
  }
}

watch([() => prop.visible, () => prop.dialogData], ([newVisible, newData]) => {
  console.log("themeChooseDialog", newVisible, newData)
  dialogVisible.value = newVisible
  if (newVisible) {
    // 如果显示获取一下最新的数据
    initData()
  }
  console.log("themeChooseDialog", newVisible)
})
</script>
<style lang="scss">
.avatar-uploader .avatar {
  display: block;
  width: 280px;
  height: 160px;
}

.avatar-uploader .el-upload {
  width: 280px;
  height: 160px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 280px;
  font-size: 64px;
  color: #e9ecf1;
}
</style>

<style lang="scss" scoped>
:deep(.el-upload--picture-card) {
  --el-upload-picture-card-size: 280px !important;
  height: 160px !important;
}

:deep(.el-upload-list--picture-card) {
  --el-upload-list-picture-card-size: 280px !important;
  height: 160px !important;
}

.pic-tip {
  // position: absolute;
  // bottom: 10px;
  // left: 25px;
  text-align: center;
  font-size: 12px;
  color: #737373;
  width: 170px;
  margin: 0 auto;
  display: block;
}

.color-item {
  display: flex;

  .color-item-box {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
  }
}

.avatar {
  width: 280px;
  height: 160px;
}
</style>
