<script setup lang="ts">
defineProps({
  title: {
    type: String,
    default: "数据列表"
  },
  showTitle: {
    type: Boolean,
    default: true
  }
})
</script>

<template>
  <div class="page-content">
    <CardTitle class="page-content-header" :showTitle="showTitle">
      <template #title v-if="showTitle"> {{ title }} </template>
      <template #right>
        <slot name="contentHeaderRight" />
      </template>
    </CardTitle>
    <div :class="['page-content-main', showTitle ? 'border-top-show' : '']">
      <slot name="main" />
      <slot name="footer" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-content {
  background-color: #fff;
  border-radius: 12px;
  padding-top: 14px;
  &-main {
    margin: 14px 20px 0px;
    padding-top: 14px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .border-top-show {
    border-top: 1px solid #e6e8eb;
  }
}
</style>
