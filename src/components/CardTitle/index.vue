<script setup lang="ts">
defineProps({
  showTitle: {
    type: Boolean,
    default: true
  }
})
</script>

<template>
  <div class="card-title">
    <div class="card-title-left">
      <div class="card-title-left-line" v-if="showTitle" />
      <div class="card-title-left-text">
        <slot name="title" />
        <slot name="left" />
      </div>
    </div>
    <div class="card-title-right">
      <slot name="right" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.card-title {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  &-left {
    display: flex;
    align-items: center;
    justify-content: start;
    &-line {
      width: 6px;
      height: 20px;
      border-radius: 0px 3px 3px 0px;
      background-color: #0dc195;
      margin-right: 20px;
    }
    &-text {
      font-size: 18px;
      font-weight: 600;
      color: #1e2224;
    }
  }
  &-right {
    display: flex;
    align-items: center;
    justify-content: end;
    margin-right: 20px;
  }
}
</style>
