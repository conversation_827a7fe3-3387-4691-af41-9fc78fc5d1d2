<template>
  <el-drawer
    v-model="dialogVisible"
    :title="dialogTitle"
    size="600px"
    direction="rtl"
    @close="closeDialog"
    class="ps-drawer"
    :close-on-click-modal="false"
  >
    <div class="ps-tree-checkbox">
      <el-tree
        :data="settingData"
        show-checkbox
        default-expand-all
        node-key="prop"
        ref="printTree"
        highlight-current
        :check-on-click-node="true"
        :default-checked-keys="tableCheckedSetting"
        @check="checkHandle"
        :props="propsOption"
      />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelDialog">取消</el-button>
        <el-button type="primary" @click="confirmDialog">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, watch, defineProps, defineEmits } from "vue"
import { getTreeDeepkeyList, findChildSetting } from "@/utils/index"
import { useVModel } from "@vueuse/core"

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  dialogTitle: {
    type: String,
    default: "设置"
  },
  tableSetting: {
    // tree 数据
    type: Array,
    default: () => []
  },
  defaultCheckedSetting: {
    // 打开弹窗时，默认选中的数据
    type: Array,
    default: () => []
  },
  width: {
    // dialog宽度
    type: String,
    default: "450px"
  }
})
// 弹窗事件
const emits = defineEmits(["update:modelValue", "closeDialog", "confirmDialog"])
// 是否显示弹出窗
const dialogVisible = useVModel(props, "modelValue", emits)
// tree 配置项
const propsOption = {
  children: "children",
  label: "label",
  value: "prop"
}
const settingData = ref<any[]>()
const tableCheckedSetting = ref<any[]>() // 默认选中的数据
// const halfCheckedKeys = ref<any[]>() // 子级没有全选的key
const settings = ref<any[]>()
const printTree = ref()
// 获取tree数据
const getSetting = (data: Array<any>) => {
  let res = []
  for (let i = 0; i < data.length; i++) {
    if (!data[i].hidden && !data[i].children) {
      res.push(data[i])
    } else if (!data[i].hidden && data[i].children) {
      let parent = JSON.parse(JSON.stringify(data[i]))
      parent.children = getSetting(data[i].children)
      res.push(parent)
    }
  }
  return res
}
// 初始化默认参数
const initDefaultSettingCheck = () => {
  // 所有的key值
  const allKeys = getTreeDeepkeyList(props.defaultCheckedSetting, "prop", "children", 1)
  let keysList: any = getAllChildrenKeyList(props.tableSetting) // 获取子级的key list
  allKeys.forEach((key, i) => {
    // tree 树中如果子级不是全选的话需要把父级key去掉
    if (keysList[key]) {
      let isAll = keysList[key].every((v: any) => allKeys.includes(v))
      if (!isAll) {
        allKeys.splice(i, 1)
      }
    }
  })
  tableCheckedSetting.value = allKeys
  settingData.value = getSetting(props.tableSetting)
  settings.value = props.defaultCheckedSetting
  console.log(
    "initDefaultSettingCheck",
    allKeys,
    "tableCheckedSetting",
    tableCheckedSetting.value,
    "settings",
    settings.value,
    "settingData",
    settingData.value
  )
}
const getAllChildrenKeyList = (list: Array<any>) => {
  let resultObj = {}
  function mapSetting(data: Array<any>, resultObj: any) {
    data.forEach((item) => {
      if (item.children && item.children.length) {
        resultObj[item.key] = item.children.map((v: any) => {
          if (v.children && v.children.length) {
            mapSetting(v.children, resultObj)
          }
          return v.key
        })
      }
    })
    return resultObj
  }
  mapSetting(list, resultObj)
  return resultObj
}
// tree check事件
const checkHandle = (row: any, data: any) => {
  let list = printTree.value ? printTree.value.getCheckedNodes() : []
  tableCheckedSetting.value = list.map((item: any) => {
    return item.prop
  })
  // 子级没有全选的key
  let halfCheckedKeys = data.halfCheckedKeys
  const allChecks = tableCheckedSetting.value?.concat(halfCheckedKeys)
  settings.value = findChildSetting(settingData.value ? settingData.value : [], allChecks || [], "prop")
  console.log("setting.value", settings.value, settingData.value, allChecks)
}
// 取消事件
const cancelDialog = () => {
  dialogVisible.value = false
}
// 确定事件
const confirmDialog = () => {
  dialogVisible.value = false
  emits("confirmDialog", settings.value)
}
const closeDialog = () => {
  dialogVisible.value = false
  emits("closeDialog")
}

watch([() => props.modelValue], ([newVal]) => {
  console.log(newVal, "printSetting")
  if (newVal) {
    initDefaultSettingCheck()
  }
})
</script>

<style lang="scss">
.print-setting {
  .el-dialog__body {
    max-height: 60vh;
    overflow-y: auto;
  }
}
</style>
