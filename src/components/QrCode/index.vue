<template>
  <div>
    <img :src="qrCodeDataUrl" alt="QR Code" :style="{ width: `${props.size}px`, height: 'auto' }" />
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue"
import QRCode from "qrcode"

const props = defineProps({
  value: {
    type: String,
    required: false,
    default: ""
  },
  size: {
    type: Number,
    required: false,
    default: 200
  },
  color: {
    type: String,
    required: false,
    default: "#000000"
  }
})
const qrCodeDataUrl = ref("")

onMounted(async () => {
  const text = props.value // 要生成二维码的文本
  console.log(text)
  try {
    // 根据传入的size和默认的scale（这里假设为4）计算最终的二维码大小
    const scale = 8 // 这个值可以根据需要进行调整，以得到合适的二维码大小
    const qrSize = props.size / scale

    const dataUrl = await QRCode.toDataURL(text, {
      type: "image/png",
      errorCorrectionLevel: "H",
      color: {
        dark: props.color
      },
      margin: 0, // 根据需要调整边距
      scale: scale, // 使用scale来放大或缩小二维码
      width: qrSize // 设置二维码的宽度，注意这里已经是放大后的像素值
    })
    qrCodeDataUrl.value = dataUrl
  } catch (error) {
    console.error("Failed to generate QR Code", error)
  }
})
</script>
