<template>
  <div>
    <el-tree-select
      v-model="organValue"
      :props="defaultProps"
      :data="organData"
      :multiple="multiple"
      :disabled="disabled"
      :collapse-tags="collapseTags"
      node-key="id"
      lazy
      show-checkbox
      class="w-full"
      v-on="$attrs"
      :check-strictly="checkStrictly"
      @check="checkChange"
    />
  </div>
</template>
<script lang="ts" setup>
import to from "await-to-js"
import { useVModel } from "@vueuse/core"
import { apiBackgroundLogisticsManageLogisticsOrganizationParentTreeListPost } from "@/api/user"
import { ref, onMounted } from "vue"
import { useUserStoreHook } from "@/store/modules/user"
import { cloneDeep } from "lodash"

const props = defineProps({
  modelValue: {
    type: [String, Number, Array]
  },
  multiple: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  collapseTags: {
    type: Boolean,
    default: true
  },
  checkStrictly: {
    type: Boolean,
    default: true
  }
})
const userStore = useUserStoreHook()
// 父组件监听
const emit = defineEmits(["update:modelValue", "change"])
// 选择的自组织id
const organValue = ref(useVModel(props, "modelValue", emit))
// 组织数据
const organData = ref([])
// 默认配置
const defaultProps = {
  isLeaf: "has_children",
  label: "name",
  value: "id"
}

// 节点选中监听
const checkChange = (checkedNodesData: any, checkedKeysData: any) => {
  console.log("checkChange", checkedNodesData, "checkedKeys", checkedKeysData)
  const checkedKeys = Reflect.has(checkedKeysData, "checkedKeys") ? checkedKeysData.checkedKeys : []
  emit("change", props.multiple ? checkedKeys : checkedKeys[0] || null)
}
// 获取组织
const getOrganData = async () => {
  const userInfo: any = userStore.getUserInfo
  console.log("userInfo getOrganData", userInfo)
  const [err, res]: any[] = await to(
    apiBackgroundLogisticsManageLogisticsOrganizationParentTreeListPost({
      organization_id: userInfo.use_org
    })
  )
  if (err) {
    console.log(err)
    return
  }
  if (res && res.code === 0) {
    const data = res.data || []
    if (data) {
      organData.value = cloneDeep(data)
    }
  }
}

onMounted(() => {
  console.log("organvalue")
  getOrganData()
})
</script>
