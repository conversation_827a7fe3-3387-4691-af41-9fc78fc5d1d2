<template>
  <div class="count-down">
    <slot v-bind="timeData">
      <span>{{ formattedTime }}</span>
    </slot>
  </div>
</template>

<script lang="ts" setup>
import { parseTimeData, parseFormat, isSameSecond } from "@/utils/date"
import { onBeforeUnmount, onMounted, ref, watch } from "vue"

const props = defineProps({
  value: {
    type: [Number, String],
    default: 0
  },
  // 是否显示毫秒
  isMilliSecond: {
    type: Boolean,
    default: false
  },
  // 结束时间
  end: {
    type: [Number, String],
    default: 0
  },
  // 时间格式，DD-日，HH-时，mm-分，ss-秒，SSS-毫秒
  format: {
    type: String,
    default: () => "HH:mm:ss"
  },
  // 是否自动开始倒计时
  autoStart: {
    type: Boolean,
    default: true
  }
})
const emit = defineEmits(["input", "change", "finish"])

const timer = ref<NodeJS.Timeout | null>(null)
const timeData = ref(parseTimeData(0))
const formattedTime = ref("0")
const running = ref(false)
const endTime = ref(0)
const remainTime = ref<number>(0)

const reset = () => {
  pause()
  if (props.value) {
    remainTime.value = +props.value
  } else if (props.end) {
    remainTime.value = new Date(replaceTime(props.end)).getTime() - Date.now()
  }
  setRemainTime(remainTime.value)
  if (props.autoStart) {
    start()
  }
}
// 开始倒计时
const start = () => {
  if (running.value) return
  // 标识为进行中
  running.value = true
  // 结束时间戳 = 此刻时间戳 + 剩余的时间
  endTime.value = Date.now() + remainTime.value
  toTick()
}
// 根据是否展示毫秒，执行不同操作函数
const toTick = () => {
  if (props.isMilliSecond) {
    microTick()
  } else {
    macroTick()
  }
}
const macroTick = () => {
  clearTimer()
  // 每隔一定时间，更新一遍定时器的值
  // 同时此定时器的作用也能带来毫秒级的更新
  timer.value = setTimeout(() => {
    // 获取剩余时间
    const remain = getRemainTime()
    // 重设剩余时间
    if (!isSameSecond(remain, remainTime.value) || remain === 0) {
      setRemainTime(remain)
    }
    // 如果剩余时间不为0，则继续检查更新倒计时
    if (remainTime.value !== 0) {
      macroTick()
    }
  }, 30)
}
const microTick = () => {
  clearTimer()
  timer.value = setTimeout(() => {
    setRemainTime(getRemainTime())
    if (remainTime.value !== 0) {
      microTick()
    }
  }, 50)
}
// 获取剩余的时间
const getRemainTime = () => {
  // 取最大值，防止出现小于0的剩余时间值
  return Math.max(endTime.value - Date.now(), 0)
}
// 设置剩余的时间
const setRemainTime = (remain: number) => {
  remainTime.value = remain
  // 根据剩余的毫秒时间，得出该有天，小时，分钟等的值，返回一个对象
  const timeData = parseTimeData(remain)
  emit("input", remain)
  emit("change", timeData)
  // 得出格式化后的时间
  formattedTime.value = parseFormat(props.format, timeData)
  // 如果时间已到，停止倒计时
  if (remain <= 0) {
    pause()
    emit("finish")
  }
}
const replaceTime = (date: number | string): string => {
  let str = ref("")
  if (typeof date === "number") {
    date.toString()
  }
  if (typeof date === "string") {
    str.value = date.replace(new RegExp(/-/gm), "/").replace(new RegExp(/T/gm), " ")
  }
  return str.value
}
// 暂停倒计时
const pause = () => {
  running.value = false
  clearTimer()
}
// 清空定时器
const clearTimer = () => {
  if (timer.value) {
    clearTimeout(timer.value)
    timer.value = null
  }
}

watch(
  () => props.value,
  (newVal, oldVal) => {
    reset()
  }
)
onMounted(() => {
  reset()
})
onBeforeUnmount(() => {
  clearTimer()
})
</script>

<style scoped lang="scss">
.count-down {
  display: inline-block;
}
</style>
