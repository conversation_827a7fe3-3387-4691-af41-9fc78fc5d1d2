// import * as mqtt from "mqtt";
import * as mqtt from "mqtt/dist/mqtt.min"
import type { IClientOptions, ISubscriptionGrant, MqttClient, Packet, IConnackPacket } from "mqtt"
import type { QoS } from "mqtt-packet"
import type IMqttService from "./IMqttService.d"
import mqttConfigDefault from "@/config/mqtt.d"
import { useMqttClientStore } from "@/store/modules/mqttClientStore"

//  定义一个默认的配置
let defaultOptions: IClientOptions = {
  defaultProtocol: "wss",
  protocol: "wss",
  protocolVersion: 4, //MQTT连接协议版本
  clientId: "mqtt_" + Math.random().toString(16), // 客户端ID，如果不传进去的话mqtt 会自动给你生成一个
  keepalive: 60, // 心跳间隔（秒）。客户端会定期发送心跳消息以保持连接 默认是60秒
  clean: false, // 当客户端断开连接时，它将清除所有相关的会话信息（默认true）
  username: mqttConfigDefault.username, // 用户名
  password: mqttConfigDefault.password, // 密码
  reconnectPeriod: 1000, //1000毫秒，两次重新连接之间的间隔
  connectTimeout: 30 * 1000, //1000毫秒，两次重新连接之间的间隔
  resubscribe: true //如果连接断开并重新连接，则会再次自动订阅已订阅的主题（默认true）
}

//  创建一个class 客户端，里面有一个mqttClient对象
class MqttService implements IMqttService {
  private client: MqttClient | undefined
  private store = useMqttClientStore()

  // 初始化构造方法的时候，将配置传入进行保存
  constructor(options: IClientOptions) {
    if (options && typeof options === "object") {
      defaultOptions = { ...defaultOptions, ...options }
    }
  }
  // 连接方法
  public async connect(url: string, options: IClientOptions): Promise<IConnackPacket> {
    return new Promise((resove, reject) => {
      if (options && typeof options === "object") {
        defaultOptions = { ...defaultOptions, ...options }
        console.log("defaultOptions 123", defaultOptions)
      }
      try {
        this.client = mqtt.connect(defaultOptions.protocol + "://" + url + "/mqtt", defaultOptions)
        this.client?.on("connect", (e) => {
          console.log("connect", e)
          this.store.addReconnentMessage({
            message: e,
            time: new Date(),
            isReconnet: this.store.getReconnect
          })
          resove(e)
        })
      } catch (error) {
        reject(error)
      }
    })
  }
  // 断开连接
  public disconnect(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.client?.end(true, {}, (error?: Error) => {
        if (error) reject()
        resolve()
      })
    })
  }
  //  订阅主题
  public subscribe(topic: string): Promise<ISubscriptionGrant[]> {
    return new Promise((resolve, reject) => {
      try {
        this.client?.subscribe(topic, (err: Error | null, granted: ISubscriptionGrant[]) => {
          console.log(err, granted)
          if (err) reject(err)
          resolve(granted)
        })
      } catch (error) {
        reject(error)
      }
    })
  }
  // 取消订阅
  public unSubscribe(topic: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.client?.unsubscribe(topic, (error?: Error, packet?: Packet) => {
          console.log("unSubscribe", error, packet)
          if (error) reject(error)
          resolve()
        })
      } catch (error) {
        reject(error)
      }
    })
  }
  // 发布消息
  public publish(topic: string, message: string, qos: QoS): Promise<Packet | undefined> {
    return new Promise((resolve, reject) => {
      if (this.client === undefined) throw new Error("client is undefined")

      this.client.publish(topic, message, { qos }, (error?: Error, packet?: Packet) => {
        if (error) reject(error)
        resolve(packet)
      })
    })
  }
  // 监听订阅
  public addListeners(topic: any, callback: any) {
    this.client?.on(topic, callback)
  }
}

export default MqttService
