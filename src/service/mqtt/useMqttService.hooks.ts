import type IMqttService from "./IMqttService.d"
import mqttServeic from "./MqttService"
import type { IPublishPacket, IClientOptions, ISubscriptionGrant } from "mqtt"

import { useMqttClientStore } from "@/store/modules/mqttClientStore"
import { QoS } from "mqtt-packet"
const options: IClientOptions = {
  protocol: "wss",
  protocolVersion: 4, //MQTT连接协议版本
  clientId: "mqtt_" + Math.random().toString(16).substr(2, 8), // 客户端ID，如果不传进去的话mqtt 会自动给你生成一个
  keepalive: 60, // 心跳间隔（秒）。客户端会定期发送心跳消息以保持连接 默认是60秒
  clean: false, // 当客户端断开连接时，它将清除所有相关的会话信息（默认true）
  reconnectPeriod: 1000, //1000毫秒，两次重新连接之间的间隔
  connectTimeout: 30 * 1000, //1000毫秒，两次重新连接之间的间隔
  resubscribe: true //如果连接断开并重新连接，则会再次自动订阅已订阅的主题（默认true）
}

export default function useMqttClient() {
  const store = useMqttClientStore()

  const mqttService: IMqttService = new mqttServeic(options)

  const connect = async (url: string, topic?: string, options?: IClientOptions) => {
    try {
      console.log(888888888888)
      store.setReConnected(false)
      await mqttService?.connect(url, options)
      store.setConnected(true)
      console.log("Connected", 888888888888)
      if (topic) {
        subscribe(topic)
      }
      attachListeners()
    } catch (error: any) {
      console.log(error)
    }
  }

  const disconnect = async () => {
    await mqttService?.disconnect()
    store.setConnected(false)
    console.log("Disconnected")
  }

  const subscribe = async (topic: string) => {
    const result = await mqttService.subscribe(topic)
    console.log("subscribe", result)
    result.forEach((item: ISubscriptionGrant) => {
      store.addToSubscribedTopics(item)
      console.log(`Successfully subscribed to ${item.topic}`)
    })
  }

  const unSubscribe = async (topic: string) => {
    await mqttService.unSubscribe(topic)
    store.removeSubscribedTopic(topic)
  }

  const publish = async (topic: string, message: string, qos: QoS) => {
    await mqttService.publish(topic, message, qos)
    console.log(`Successfully published to ${topic}`)
  }

  function attachListeners() {
    mqttService.addListeners("disconnect", () => {
      console.log("Disconnected")
      store.clearSubscribedTopics()
    })

    mqttService.addListeners("end", () => {
      console.log("mqtt client was ended")
      store.clearSubscribedTopics()
    })

    mqttService.addListeners("offline", () => {
      console.log("mqtt client is offline")
    })

    mqttService.addListeners("error", (error: Error) => {
      console.log("Error", error)
    })

    mqttService.addListeners("reconnect", (message: any) => {
      console.log("reconnect")
      store.setReConnected(true)
    })
    mqttService.addListeners("message", (topic: string, payload: Buffer, packet: IPublishPacket) => {
      console.log("Message", topic, payload.toString(), packet, 888777)
      store.addMessage({
        body: payload.toString(),
        topic,
        qos: packet.qos
      })
    })
  }

  return {
    connect,
    disconnect,
    subscribe,
    unSubscribe,
    publish
  }
}
