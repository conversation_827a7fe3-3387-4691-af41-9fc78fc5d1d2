import type { ISubscriptionGrant, Packet, IConnackPacket } from "mqtt"
import type { QoS } from "mqtt-packet"

interface IMqttService {
  // 连接(url 地址)
  connect(url: string, options?: IClientOptions): Promise<IConnackPacket>
  // 取消连接
  disconnect(): Promise<void>
  // 订阅
  subscribe(topic: string): Promise<ISubscriptionGrant[]>
  // 取消订阅
  unSubscribe(topic: string): Promise<void>
  // 发布
  publish(topic: string, message: string, qos: QoS): Promise<Packet | undefined>
  // 添加监听器
  addListeners(topic: string, callback: any): void
}

export default IMqttService
