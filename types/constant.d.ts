export interface TBreadCrumbDate {
  dateValue: Array<string> | null
  dateType?:
    | "date"
    | "year"
    | "datetime"
    | "years"
    | "month"
    | "dates"
    | "week"
    | "datetimerange"
    | "daterange"
    | "monthrange"
    | null
  startPlaceholder?: string
  endPlaceholder?: string
  rangeSeparator?: string
  defaultTime?: Array<any>
}
// 登录返回用户信息
export interface TUserInfo {
  account_id?: number
  color?: string
  has_first_login?: boolean
  is_channel_account?: boolean
  is_double_factor?: boolean
  last_change_pwd_time?: string
  last_login_time?: string
  logo_url?: string
  mobile?: string
  must_change_password?: boolean
  supervision_channel_id?: number
  supervision_channel_name?: string
  token?: string
  username?: string
  password?: string
  name?: string
  vrcode?: string
  newPass?: string
  confirmPass?: string
  newMobile?: string
  code?: string
  is_expire_change_pwd?: boolean
}
