## 运行与安装

"volta": {
"node": "18.0.0",
"pnpm": "8.15.6"
}

pnpm install

pnpm run dev

## 关于屏幕设备适配

1.此框架使用的屏幕适配方案是rem，使用rem适配，需要使用rem适配器，rem适配器是针对vue3的
需要安装的依赖：

```
pnpm i postcss-pxtorem autoprefixer amfe-flexible --save-dev

```

### 配置文件

postcss.config.js

### 具体配置

```
 "postcss-pxtorem": {
      rootValue: 192, // 根元素字体大小，用于将像素转换为rem的基准值,一般1920*1080基础字体是16px，这边就是设置192
      propList: ["*", "!border"], // 除 border 外所有px 转 rem
      selectorBlackList: [".el-"], // 过滤掉.el-开头的class，不进行rem转换
      replace: true,
      minPixelValue: 1
    }
```

## mqtt 连接

### mqtt

服务文件放到 service/mqtt 目录下
mqtt 配置文件放到 config/mqtt 目录下

```
const mqttConfigDefault: mqttConfig = {
  host: "emqx.debug.packertec.com",
  port: "28084",
  username: "admin",
  password: "P@ssword"
}
```

### 使用 在App.vue 中初始化连接

```
import useMqttClient from "@/service/mqtt/useMqttService.hooks";
import mqttConfigDefault from "./config/mqtt.d";
const { connect, disconnect } = useMqttClient();
/** 初始化mqtt连接 */
const mqttUrl = mqttConfigDefault.host + ":" + mqttConfigDefault.port;
console.log("mqttUrl", mqttUrl);

onMounted(() => {
  console.log("App onUnmounted");
  connect(mqttUrl)
})
onUnmounted(() => {
  console.log("App onUnmounted");
  disconnect()
})
```

### 订阅

```
import useMqttClient from "../service/useMqttService.hooks";
const { connect, disconnect, subscribe, unSubscribe, publish } = useMqttClient();
 subscribe('你的主题名字')
```

### 发布

```
import useMqttClient from "../service/useMqttService.hooks";
import { QosEnum } from "../enums/qosEnum";
const { connect, disconnect, subscribe, unSubscribe, publish } = useMqttClient();
 // publish('你的主题名字', "你要发布的内容",qos的等级)
 const messageStr = ref("")
 publish('liangzz', messageStr.value, QosEnum.Qos1)
```

### 监听消息

```
import { useMqttClientStore } from "../store/modules/mqttClientStore"
const useMqttStore = useMqttClientStore()

watch(() => useMqttStore.getReceivedMessages, (newValue: any) => {
 console.log("newValue", newValue);
 var list = newValue
 console.log("list", list,list[0]);
}, {
 immediate: true,
 deep: true
})

```
