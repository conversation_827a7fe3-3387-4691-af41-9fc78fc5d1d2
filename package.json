{"name": "ps-healthy-background", "version": "1.0.0", "author": {"name": "ps"}, "type": "module", "scripts": {"serve": "vite", "dev": "vite", "build:stage": "vue-tsc --noEmit && vite build --mode staging", "build:test": "vue-tsc --noEmit && vite build --mode test", "build:prod": "vue-tsc --noEmit && vite build", "preview:stage": "pnpm build:stage && vite preview", "preview:prod": "pnpm build:prod && vite preview", "lint:eslint": "eslint --cache --max-warnings 0 \"{src,tests,types}/**/*.{vue,js,jsx,ts,tsx}\" --fix", "lint:prettier": "prettier --write \"{src,tests,types}/**/*.{vue,js,jsx,ts,tsx,json,css,less,scss,html,md}\"", "lint": "pnpm lint:eslint && pnpm lint:prettier", "prepare": "husky install", "test": "vitest"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@tinymce/tinymce-vue": "5", "@types/echarts": "^4.9.22", "await-to-js": "^3.0.0", "axios": "1.6.8", "dayjs": "1.11.10", "echarts": "^5.5.0", "element-china-area-data": "^6.1.0", "element-plus": "2.8.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "js-base64": "^3.7.7", "js-cookie": "3.0.5", "lodash": "4.17.21", "lodash-es": "4.17.21", "markdown-it": "^14.1.0", "MD5": "^1.3.0", "mitt": "3.0.1", "mqtt": "5.0.5", "normalize.css": "8.0.1", "nprogress": "0.2.0", "number-precision": "^1.6.0", "path-browserify": "1.0.1", "path-to-regexp": "6.2.1", "pinia": "2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "qrcode": "^1.5.3", "screenfull": "6.0.2", "ts-md5": "^1.3.1", "vue": "3.3.8", "vue-clipboard3": "^2.0.0", "vue-dompurify-html": "^5.1.0", "vue-draggable-plus": "^0.6.0", "vue-echarts": "^7.0.3", "vue-i18n": "9.2.2", "vue-router": "4.3.0", "vue3-print-nb": "^0.1.4", "vue3-seamless-scroll": "^2.0.1", "vxe-table": "4.5.21", "vxe-table-plugin-element": "4.0.1", "xe-utils": "3.5.22", "xlsx": "^0.18.5"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/js-cookie": "3.0.6", "@types/lodash-es": "4.17.12", "@types/node": "20.11.30", "@types/nprogress": "0.2.3", "@types/path-browserify": "1.0.2", "@typescript-eslint/eslint-plugin": "6.0.0", "@typescript-eslint/parser": "6.0.0", "@vitejs/plugin-vue": "4.0.0", "@vitejs/plugin-vue-jsx": "3.1.0", "@vue/eslint-config-prettier": "9.0.0", "@vue/eslint-config-typescript": "12.0.0", "@vue/test-utils": "2.4.5", "amfe-flexible": "^2.2.1", "autoprefixer": "^10.4.19", "eslint": "8.57.0", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-vue": "9.23.0", "husky": "8.0.0", "jsdom": "22.0.0", "lint-staged": "14.0.0", "postcss-pxtorem": "^6.1.0", "prettier": "3.2.5", "sass": "1.72.0", "typescript": "5.4.3", "unocss": "0.58.6", "vite": "4.0.0", "vite-plugin-svg-icons": "2.0.1", "vite-svg-loader": "5.1.0", "vitest": "0.9.0", "vue-eslint-parser": "9.4.2", "vue-tsc": "2.0.7"}, "lint-staged": {"*.{vue,js,jsx,ts,tsx}": ["prettier --write"], "*.{css,less,scss,html,md}": ["prettier --write"], "package.json": ["prettier --write"]}, "keywords": ["vue", "vue3", "admin", "vue-admin", "vue3-admin", "vite", "vite-admin", "element-plus", "element-plus-admin", "ts", "typescript"], "license": "MIT", "volta": {"node": "16.16.0", "pnpm": "8.15.6", "yarn": "1.22.21"}}