version: "3"
services:
    web:
        # image: registry.cn-shenzhen.aliyuncs.com/packer/frontend-background:latest
        image: registry.cn-shenzhen.aliyuncs.com/packer-devops/node:yarn
        restart: always
        command: ./deploy/run_service.sh
        volumes:
            - /root/app/frontend/background:/app
            - node_module:/app/node_modules:rw
        deploy:
            replicas: 2
            resources:
                limits:
                    cpus: "200"
                    memory: 256M
            restart_policy:
                condition: on-failure
        ports:
            - "4000:8080"
        networks:
            - webnet
networks:
    webnet:

volumes:
    node_module:
        driver_opts:
            type: bind
            o: bind
            device: /root/volume/background_node_modules/
